{"name": "ethercat-webui", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@types/multer": "^1.4.12", "@viz-js/viz": "^3.11.0", "@vueuse/core": "^9.12.0", "ant-design-vue": "^3.2.20", "axios": "^1.3.4", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.7", "echarts": "^5.4.2", "esbuild-loader": "^4.2.2", "monaco-editor": "^0.34.0", "monaco-editor-webpack-plugin": "^7.0.1", "multer": "^1.4.5-lts.2", "nunjucks": "^3.2.4", "spark-md5": "^3.0.2", "vue": "^3.2.13", "vue-router": "^4.0.3", "vuex": "^4.0.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xml2js": "^0.6.2"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^22.9.0", "@types/nunjucks": "^3.2.6", "@types/spark-md5": "^3.0.5", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "buffer": "^6.0.3", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "events": "^3.3.0", "less": "^4.2.0", "less-loader": "^11.1.0", "stream-browserify": "^3.0.0", "timers-browserify": "^2.0.12", "typescript": "^5.6.3", "util": "^0.12.5"}}