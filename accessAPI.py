import requests
import json
import sys

def start_middleware(program_name: str, use_sdk: bool = False):
    """
    启动中间件程序
    
    Args:
        program_name: 程序名称
        use_sdk: 是否使用SDK模式，默认False
    
    Returns:
        dict: 接口返回的数据
    """
    url = "http://192.168.1.174:3000/api/programs/start-middleware"
    
    # 准备请求数据
    data = {
        "programName": program_name,
        "sdk": use_sdk
    }
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data)
        
        # 确保响应成功
        response.raise_for_status()
        
        # 解析响应数据
        result = response.json()
        
        # 打印完整响应
        print("\n响应数据:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 检查响应状态
        if result.get("code") == 0:
            print(f"\n成功: {result.get('msg')}")
            print(f"共享内存文件: {result.get('data', {}).get('shmFile')}")
            print(f"SDK模式: {result.get('data', {}).get('type')}")
        else:
            print(f"\n错误: {result.get('msg')}")
            if 'errorData' in result.get('data', {}):
                print("可用程序列表:")
                for prog in result['data']['errorData'].get('availablePrograms', []):
                    print(f"  - {prog['name']} (ID: {prog['id']})")
        
        return result
        
    except requests.exceptions.RequestException as e:
        print(f"\n请求错误: {str(e)}")
        sys.exit(1)
    except json.JSONDecodeError:
        print("\n响应格式错误: 无法解析JSON数据")
        sys.exit(1)
    except Exception as e:
        print(f"\n未知错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    # 启动LS_L5N程序，使用SDK模式
    start_middleware("HX_16I16O", True)
