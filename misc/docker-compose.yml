version: '3.8'

services:
  pocketbase:
    image: ghcr.io/muchobien/pocketbase:latest
    ports:
      - "8090:8090"
    volumes:
      - pocketbase-data:/pb_data
    restart: unless-stopped

  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - POCKETBASE_URL=http://pocketbase:8090
    depends_on:
      - pocketbase
    restart: unless-stopped

volumes:
  pocketbase-data: 