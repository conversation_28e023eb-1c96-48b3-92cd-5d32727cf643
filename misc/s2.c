#include <ecrt.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>

// 过程数据偏移量
static unsigned int off_dig_out;  // 数字输出偏移量
static unsigned int off_status;   // 状态字偏移量
static unsigned int off_err_num;  // 错误号偏移量

// PDO条目定义
static ec_pdo_entry_info_t ctl_ect_pdo_entries[] = {
    // ModuleStatus TxPDO
    {0x8002, 0x00, 16}, // Module State
    {0x8003, 0x00, 32}, // Module Err Num
    
    // DQ-16 Outputs RxPDO
    {0x7000, 0x01, 8},  // OutByte0
    {0x7000, 0x02, 8}   // OutByte1
};

// PDO定义
static ec_pdo_info_t ctl_ect_pdos[] = {
    // TxPDO 0x1A00
    {0x1A00, 2, &ctl_ect_pdo_entries[0]}, // ModuleStatus
    // RxPDO 0x1600
    {0x1600, 2, &ctl_ect_pdo_entries[2]}  // DQ-16 Outputs
};

// 同步管理器配置
static ec_sync_info_t ctl_ect_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT,  0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, &ctl_ect_pdos[1], EC_WD_ENABLE},
    {3, EC_DIR_INPUT,  1, &ctl_ect_pdos[0], EC_WD_DISABLE},
    {0xff}
};

// PDO条目注册列表
static ec_pdo_entry_reg_t domain_regs[] = {
    {0, 0, 0x99, 0x00020310, 0x7000, 0x01, &off_dig_out},  // OutByte0
    {0, 0, 0x99, 0x00020310, 0x8002, 0x00, &off_status},   // Module State
    {0, 0, 0x99, 0x00020310, 0x8003, 0x00, &off_err_num},  // Module Err Num
    {}
};

// SDO配置数据
static uint32_t sdo_1_data = 0x00000372; // 32769/1
static uint8_t sdo_2_data = 0xff;        // 28674/1
static uint8_t sdo_3_data = 0xff;        // 28674/2

// 域寄存器
static ec_domain_t *domain = NULL;
static ec_domain_state_t domain_state = {};

// 过程数据
static uint8_t *domain_pd = NULL;

static int run = 1;

// 信号处理函数
void signal_handler(int sig)
{
    run = 0;
}

int main(void)
{
    ec_master_t *master = NULL;
    ec_slave_config_t *sc;
    int ret = 0;

    // 注册信号处理
    signal(SIGTERM, signal_handler);
    signal(SIGINT, signal_handler);

    master = ecrt_request_master(0);
    if (!master) {
        fprintf(stderr, "Failed to request master.\n");
        return -1;
    }

    // 创建域
    domain = ecrt_master_create_domain(master);
    if (!domain) {
        fprintf(stderr, "Failed to create domain.\n");
        ret = -1;
        goto out_release_master;
    }

    // 配置从站
    sc = ecrt_master_slave_config(master, 0, 0, 0x99, 0x00020310);
    if (!sc) {
        fprintf(stderr, "Failed to configure slave.\n");
        ret = -1;
        goto out_release_master;
    }

    // 配置PDOs
    if (ecrt_slave_config_pdos(sc, EC_END, ctl_ect_syncs)) {
        fprintf(stderr, "Failed to configure PDOs.\n");
        ret = -1;
        goto out_release_master;
    }

    // 注册PDO条目
    if (ecrt_domain_reg_pdo_entry_list(domain, domain_regs)) {
        fprintf(stderr, "PDO entry registration failed!\n");
        ret = -1;
        goto out_release_master;
    }

    // 配置SDOs (从ENI文件的CoE InitCmds)
    if (ecrt_slave_config_sdo32(sc, 0x8001, 0x01, sdo_1_data)) {
        fprintf(stderr, "Failed to configure SDO 0x8001:01.\n");
        ret = -1;
        goto out_release_master;
    }

    if (ecrt_slave_config_sdo8(sc, 0x7002, 0x01, sdo_2_data)) {
        fprintf(stderr, "Failed to configure SDO 0x7002:01.\n");
        ret = -1;
        goto out_release_master;
    }

    if (ecrt_slave_config_sdo8(sc, 0x7002, 0x02, sdo_3_data)) {
        fprintf(stderr, "Failed to configure SDO 0x7002:02.\n");
        ret = -1;
        goto out_release_master;
    }

    // 配置DC (从ENI文件的InitCmds)
    //ecrt_slave_config_dc(sc, 0x0300, 1000000, 0, 0, 0);

    // 激活主站
    if (ecrt_master_activate(master)) {
        fprintf(stderr, "Failed to activate master.\n");
        ret = -1;
        goto out_release_master;
    }

    // 获取过程数据指针
    domain_pd = ecrt_domain_data(domain);
    if (!domain_pd) {
        fprintf(stderr, "Failed to get process data pointer.\n");
        ret = -1;
        goto out_release_master;
    }

    printf("Starting cyclic operation...\n");
    
    // 循环任务
    while (run) {
        // 接收过程数据
        ecrt_master_receive(master);
        ecrt_domain_process(domain);

        // 检查域状态
        ecrt_domain_state(domain, &domain_state);

        // 读取状态
        uint16_t status = EC_READ_U16(domain_pd + off_status);
        uint32_t err_num = EC_READ_U32(domain_pd + off_err_num);

        // LED全亮/全灭控制 - 每秒切换一次
        static int led_state = 0;
        static int counter = 0;
        if (++counter >= 1000) {
            counter = 0;
            led_state = !led_state;
            
            // 写入数字输出 - 0xFF(255)为全亮，0x00(0)为全灭
            EC_WRITE_U8(domain_pd + off_dig_out, led_state ? 0xFF : 0x00);
            printf("LED state: %s\n", led_state ? "ON (255)" : "OFF (0)");
        }

        // 发送过程数据
        ecrt_domain_queue(domain);
        ecrt_master_send(master);

        usleep(1000); // 1ms周期
    }

    // 关闭所有输出
    EC_WRITE_U8(domain_pd + off_dig_out, 0);
    ecrt_domain_queue(domain);
    ecrt_master_send(master);

    printf("\nCyclic operation completed.\n");

out_release_master:
    ecrt_release_master(master);
    return ret;
} 