import { ApiError } from '../utils/errors.js';
import { ErrorCode } from '../utils/error-codes.js';
import fs from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { EthercatService } from './ethercat.service.js';
const execAsync = promisify(exec);

interface SyncInfo {
  index: number;
  syncInfo: string;
}

interface SlaveConfig {
  index: number;
  name: string;
  vid: string;
  pid: string;
  rx_pdo: string;
  tx_pdo: string;
  rx_pdos?: PDOConfig[];
  tx_pdos?: PDOConfig[];
  dc_config?: {
    assign_activate: string;  // 如 "0x0300"
    sync0_cycle: number;      // 可选，默认使用 PERIOD_NS
    sync0_shift: number;      // 可选，默认使用 PERIOD_NS/2
    sync1_cycle: number;      // 可选，默认为 0
    sync1_shift: number;      // 可选，默认为 0
  };
}

interface PDOConfig {
  name: string;
  index: string;
  type: string;
  subindex?: string;
}

interface TemplateConfig {
  masterIndex: number;
  taskFrequency: number;
  ethercatDir: string;
  slaves: SlaveConfig[];
}

export class TemplateService {
  static async generateCTemplate(config: TemplateConfig): Promise<string> {
    try {
      // 验证从站配置
      const validation = await this.validateSlaves(config);
      if (!validation.isValid) {
        throw new ApiError(
          ErrorCode.SLAVE_VID_PID_MISMATCH,
          `从站配置验证失败:\n${validation.errors.join('\n')}`
        );
      }

      // 首先获取每个从站的同步管理器信息
      const syncInfos = await Promise.all(
        config.slaves.map(async (slave: SlaveConfig, index: number) => {
          try {
            const { stdout } = await execAsync(`ethercat cstruct | sed -n '/ec_sync_info_t slave_${slave.index}_syncs\\[\\]/,/};/p'`);
            if (!stdout.trim()) {
              throw new Error(`No sync info found for slave ${index} (position ${slave.index})`);
            }

            // 解析原始同步管理器配置
            let syncInfo = stdout.trim();
            
            // 检查是否有 RxPDOs 和 TxPDOs
            const hasRxPDOs = slave.rx_pdos && slave.rx_pdos.length > 0;
            const hasTxPDOs = slave.tx_pdos && slave.tx_pdos.length > 0;

            // 修改 SM2 (输出) 配置
            if (hasRxPDOs) {
              syncInfo = syncInfo.replace(
                /\{2,\s*EC_DIR_OUTPUT,\s*[0-9]+,\s*(?:NULL|slave_[0-9]+_pdos\s*\+\s*[0-9]+),/,
                `{2, EC_DIR_OUTPUT, 1, slave${index}_pdos + 0,`
              );
            }

            // 修改 SM3 (输入) 配置
            if (hasTxPDOs) {
              syncInfo = syncInfo.replace(
                /\{3,\s*EC_DIR_INPUT,\s*[0-9]+,\s*(?:NULL|slave_[0-9]+_pdos\s*\+\s*[0-9]+),/,
                `{3, EC_DIR_INPUT, 1, slave${index}_pdos + 1,`
              );
            }

            // 修改变量名以匹配命名规则
            syncInfo = syncInfo
              .replace(
                `ec_sync_info_t slave_${slave.index}_syncs[]`,
                `ec_sync_info_t slave${index}_syncs[]`
              )
              .replace(
                new RegExp(`slave_${slave.index}_pdos`, 'g'),
                `slave${index}_pdos`
              );

            return {
              index,
              syncInfo
            } as SyncInfo;
          } catch (error) {
            // 如果是ApiError，直接往上抛
            if (error instanceof ApiError) {
              throw error;
            }
            throw new ApiError(
              ErrorCode.TEMPLATE_ERROR,
              `获取从站${index}同步信息失败: ${error.message}`
            );
          }
        })
      );

      let template = '';
      template += this.generateHeaders();
      template += this.generateShmStructure(config);
      template += this.generateAppParams(config);
      template += this.generateEtherCATConfig(config);
      template += this.generatePDOMapping(config, syncInfos);  // 传入 syncInfos
      template += this.generateShmFunctions();
      template += this.generateCyclicTask(config);
      template += this.generateMainFunction(config);
      
      return template;
    } catch (error) {
      // 如果是ApiError，直接往上抛，保持原始错误码和消息
      if (error instanceof ApiError) {
        throw error;
      }
      // 其他错误包装成模板错误
      throw new ApiError(
        ErrorCode.TEMPLATE_ERROR,
        `生成模板失败: ${error.message}`
      );
    }
  }

  static async generateCTemplateWithoutValidation(config: TemplateConfig): Promise<string> {
    try {
      let template = '';
      template += this.generateHeaders();
      template += this.generateShmStructure(config);
      template += this.generateAppParams(config);
      template += this.generateEtherCATConfig(config);

      // 创建空的 syncInfos 数组
      const syncInfos: SyncInfo[] = config.slaves.map((slave, index) => ({
        index,
        syncInfo: `
ec_sync_info_t slave${index}_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave${index}_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave${index}_pdos + 1, EC_WD_DISABLE},
    {0xff}
};`
      }));

      template += this.generatePDOMapping(config, syncInfos);  // 传入 syncInfos
      template += this.generateShmFunctions();
      template += this.generateCyclicTask(config);
      template += this.generateMainFunction(config);
      
      return template;
    } catch (error) {
      // 如果是ApiError，直接往上抛，保持原始错误码和消息
      if (error instanceof ApiError) {
        throw error;
      }
      // 其他错误包装成模板错误
      throw new ApiError(
        ErrorCode.TEMPLATE_ERROR,
        `生成模板失败: ${error.message}`
      );
    }
  }

  /**
   * 验证实际从站数量与配置是否一致
   * @param config JSON配置
   * @returns Promise<{isValid: boolean, actualCount: number, configuredCount: number}>
   */
  private static async validateSlaves(config: TemplateConfig): Promise<{isValid: boolean, errors: string[]}> {
    try {
      // 获取实际从站
      const actualSlaves = await EthercatService.getSlaves();
      const errors: string[] = [];

      // 检查每个配置的从站
      for (let i = 0; i < config.slaves.length; i++) {
        const configuredSlave = config.slaves[i];
        // 将配置的索引转换为数字进行比较
        const configIndex = parseInt(configuredSlave.index.toString());
        const actualSlave = actualSlaves.find(s => s.index === configIndex);

        if (!actualSlave) {
          errors.push(`从站 ${i} (位置 ${configuredSlave.index}): 未找到实际从站，请检查从站JSON和连接状态`);
          continue;
        }

        // 验证 VID
        const configVid = configuredSlave.vid.toLowerCase().replace('0x', '');
        const actualVid = actualSlave.vendorId.toLowerCase().replace('0x', '');
        if (configVid !== actualVid) {
          errors.push(
            `从站 ${i} (位置 ${configuredSlave.index}): VID不匹配 ` +
            `(配置: 0x${configVid}, 实际: 0x${actualVid})`
          );
        }

        // 验证 PID
        const configPid = configuredSlave.pid.toLowerCase().replace('0x', '');
        const actualPid = actualSlave.productCode.toLowerCase().replace('0x', '');
        if (configPid !== actualPid) {
          errors.push(
            `从站 ${i} (位置 ${configuredSlave.index}): PID不匹配 ` +
            `(配置: 0x${configPid}, 实际: 0x${actualPid})`
          );
        }
      }

      // 添加调试日志
      console.log('Validation results:', {
        actualSlaves: actualSlaves.map(s => ({
          index: s.index,
          vendorId: s.vendorId,
          productCode: s.productCode
        })),
        configuredSlaves: config.slaves.map(s => ({
          index: s.index,
          vid: s.vid,
          pid: s.pid
        })),
        errors
      });

      return {
        isValid: errors.length === 0,
        errors
      };
    } catch (error) {
      console.error('Failed to validate slaves:', error);
      throw error;
    }
  }

  private static generateHeaders(): string {
    return `
#include <errno.h>
#include <signal.h>
#include <stdio.h>
#include <string.h>
#include <sys/resource.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <sched.h>
#include <sys/stat.h>
#include <sys/shm.h>
#include <time.h>
#include <pthread.h>  // 添加pthread头文件

#include "ecrt.h"

// Forward declarations
void cleanup_shm(void);
void signal_handler(int sig);
void check_slave_config_states(void);

// 定义PDO配置线程的参数结构
typedef struct {
    ec_slave_config_t *slave_config;
    ec_sync_info_t *sync_info;
    int *result;
} config_thread_param_t;

// PDO配置线程函数
void* config_slave_thread(void *arg) {
    config_thread_param_t *param = (config_thread_param_t *)arg;
    *(param->result) = ecrt_slave_config_pdos(param->slave_config, EC_END, param->sync_info);
    return NULL;
}

// 定义DC配置线程的参数结构
typedef struct {
    ec_slave_config_t *slave_config;
    uint16_t assign_activate;
    uint32_t sync0_cycle;
    uint32_t sync0_shift;
    uint32_t sync1_cycle;
    uint32_t sync1_shift;
    int *result;
} dc_config_thread_param_t;

// DC配置线程函数
void* dc_config_thread(void *arg) {
    dc_config_thread_param_t *param = (dc_config_thread_param_t *)arg;
    int ret = ecrt_slave_config_dc(
        param->slave_config,
        param->assign_activate,
        param->sync0_cycle,
        param->sync0_shift,
        param->sync1_cycle,
        param->sync1_shift
    );
    *(param->result) = ret;
    return NULL;
}

// Global control flags
static int run = 1;  // 控制主循环的标志
static int last_cycle = 0;  // 标记最后一个循环
static int all_slaves_op = 0;  // Add this flag to track ALL OP state


// Signal handler implementation
void signal_handler(int sig) {
    printf("\\nSignal %d received, will exit after next cycle...\\n", sig);
    last_cycle = 1;  // 设置最后一个循环标志
}

/* Time definitions */
#define NSEC_PER_SEC (1000000000L)
#define TIMESPEC2NS(T) ((uint64_t) (T).tv_sec * NSEC_PER_SEC + (T).tv_nsec)
#define CLOCK_TO_USE CLOCK_MONOTONIC
`;
  }

  private static generateShmStructure(config: any): string {
    const shmFileName = `${config.id}_${config.name}_shm`;

    let struct = `
/* Shared memory configuration */
#define ETHERCAT_SHM_FILE "${shmFileName}"
#define ETHERCAT_SHM_SIZE (sizeof(ethercat_shm_t))

/* Shared memory structure */
typedef struct {
`;

    // 为每个从站添加状态变量
    config.slaves.forEach((slave: any, index: number) => {
        struct += `    int shm_slave${index}_online_status; /* 从站${index}在线状态 */\n`;
        struct += `    int shm_slave${index}_operational_status; /* 从站${index}运行状态 */\n`;
        struct += `    int shm_slave${index}_al_state; /* 从站${index}AL状态 */\n\n`;
        
        // 添加PDO变量
        slave.rx_pdos?.forEach((pdo: any) => {
            const varName = this.sanitizeVariableName(pdo, index, 'rx');
            const comment = pdo.comment ? ` /* ${pdo.comment} */` : '';
            struct += `    int shm_slave${index}_rx_${varName};${comment}\n`;
        });
        slave.tx_pdos?.forEach((pdo: any) => {
            const varName = this.sanitizeVariableName(pdo, index, 'tx');
            const comment = pdo.comment ? ` /* ${pdo.comment} */` : '';
            struct += `    int shm_slave${index}_tx_${varName};${comment}\n`;
        });
    });

    struct += `} ethercat_shm_t;\n\nstatic ethercat_shm_t *ethercat_shm = NULL;\n`;
    return struct;
  }

  private static sanitizeVariableName(pdo: any, slaveIndex: number, direction: 'rx' | 'tx'): string {
    if (!pdo || !pdo.index || !pdo.name) {
      throw new Error('Invalid PDO data');
    }
    // 构造变量名: shm_slave{index}_rx/tx_index_name
    const baseName = `shm_slave${slaveIndex}_${direction}_${pdo.index.toLowerCase()}_${pdo.name.toLowerCase()}`;
    return baseName.replace(/[^a-z0-9_]/g, '_');
  }

  private static generateShmFunctions(): string {
    return `
void create_shm() {
    // Create and open shared memory
    int fd = shm_open(ETHERCAT_SHM_FILE, O_CREAT | O_RDWR, 0666);
    if (fd < 0) {
        perror("shm_open failed");
        exit(EXIT_FAILURE);
    }

    // Set the size of shared memory
    if (ftruncate(fd, ETHERCAT_SHM_SIZE) < 0) {
        perror("ftruncate failed");
        exit(EXIT_FAILURE);
    }

    // Map shared memory
    ethercat_shm = (ethercat_shm_t *)mmap(NULL, ETHERCAT_SHM_SIZE, 
                                         PROT_READ | PROT_WRITE, 
                                         MAP_SHARED, fd, 0);
    if (ethercat_shm == MAP_FAILED) {
        perror("mmap failed");
        exit(EXIT_FAILURE);
    }

    // Initialize shared memory to zero
    memset(ethercat_shm, 0, ETHERCAT_SHM_SIZE);
    
    // Close file descriptor (mapping remains valid)
    close(fd);
}

void cleanup_shm() {
    if (ethercat_shm != NULL) {
        munmap(ethercat_shm, ETHERCAT_SHM_SIZE);
        shm_unlink(ETHERCAT_SHM_FILE);
    }
}
`;
  }

  private static generateAppParams(config: any): string {
    return `
/* Application Parameters */
#define TASK_FREQUENCY ${config.taskFrequency || 1000} /*Hz*/
#define PERIOD_NS (NSEC_PER_SEC / TASK_FREQUENCY)
`;
  }

  private static generateEtherCATConfig(config: TemplateConfig): string {
    let conf = `\n/* EtherCAT configurations */\n`;
    conf += `#define MASTER_INDEX ${config.masterIndex || 0}\n\n`;
    conf += `static ec_master_t *master = NULL;\n`;
    conf += `static ec_master_state_t master_state = {};\n`;
    conf += `static ec_domain_t *domain1 = NULL;\n`;
    conf += `static ec_domain_state_t domain1_state = {};\n`;
    
    // 为每个从站创建独立的配置和状态对象
    config.slaves.forEach((slave: any, index: number) => {
        conf += `static ec_slave_config_t *sc_slave${index} = NULL;\n`;
        conf += `static ec_slave_config_state_t sc_slave${index}_state = {};\n`;
    });
    
    conf += `static uint8_t *domain1_pd = NULL;\n\n`;

    // 为每个从站生成位置和VID_PID定义
    config.slaves.forEach((slave: any, index: number) => {
        conf += `#define slave${index}_POS 0,${slave.index}\n`;
        conf += `#define slave${index}_VID_PID ${slave.vid},${slave.pid}\n`;
    });

    return conf;
  }

  private static generatePDOMapping(config: any, syncInfos: SyncInfo[]): string {
    let mapping = `\n/* PDO mapping */\n`;
    
    // 生成 PDO 偏移结构，使用 pdo_ 前缀和 rx/tx 区分
    mapping += `static struct {\n`;
    config.slaves.forEach((slave: any, index: number) => {
      slave.rx_pdos?.forEach((pdo: any) => {
        const varName = this.sanitizeVariableName(pdo, index, 'rx');
        if (pdo.type === 'bool') {
          mapping += `    unsigned int pdo_slave${index}_rx_${varName}_off;\n`;
          mapping += `    unsigned int pdo_slave${index}_rx_${varName}_bit;\n`;
        } else {
          mapping += `    unsigned int pdo_slave${index}_rx_${varName};\n`;
        }
      });
      slave.tx_pdos?.forEach((pdo: any) => {
        const varName = this.sanitizeVariableName(pdo, index, 'tx');
        if (pdo.type === 'bool') {
          mapping += `    unsigned int pdo_slave${index}_tx_${varName}_off;\n`;
          mapping += `    unsigned int pdo_slave${index}_tx_${varName}_bit;\n`;
        } else {
          mapping += `    unsigned int pdo_slave${index}_tx_${varName};\n`;
        }
      });
    });
    mapping += `} offset;\n\n`;

    // 生成 PDO 条目注册，使相应的前缀
    mapping += `const static ec_pdo_entry_reg_t domain1_regs[] = {\n`;
    config.slaves.forEach((slave: any, index: number) => {
      slave.rx_pdos?.forEach((pdo: any) => {
        const varName = this.sanitizeVariableName(pdo, index, 'rx');
        if (pdo.type === 'bool') {
          mapping += `    {slave${index}_POS, slave${index}_VID_PID, ${pdo.index}, ${pdo.subindex}, &offset.pdo_slave${index}_rx_${varName}_off, &offset.pdo_slave${index}_rx_${varName}_bit},\n`;
        } else {
          mapping += `    {slave${index}_POS, slave${index}_VID_PID, ${pdo.index}, ${pdo.subindex}, &offset.pdo_slave${index}_rx_${varName}},\n`;
        }
      });
      slave.tx_pdos?.forEach((pdo: any) => {
        const varName = this.sanitizeVariableName(pdo, index, 'tx');
        if (pdo.type === 'bool') {
          mapping += `    {slave${index}_POS, slave${index}_VID_PID, ${pdo.index}, ${pdo.subindex}, &offset.pdo_slave${index}_tx_${varName}_off, &offset.pdo_slave${index}_tx_${varName}_bit},\n`;
        } else {
          mapping += `    {slave${index}_POS, slave${index}_VID_PID, ${pdo.index}, ${pdo.subindex}, &offset.pdo_slave${index}_tx_${varName}},\n`;
        }
      });
    });
    mapping += `    {}\n};\n\n`;

    // 为每个从站生成 PDO 条目信息
    config.slaves.forEach((slave: any, index: number) => {
      mapping += `static ec_pdo_entry_info_t slave${index}_pdo_entries[] = {\n`;
      slave.rx_pdos?.forEach((pdo: any) => {
        mapping += `    {${pdo.index}, ${pdo.subindex}, ${this.getTypeBits(pdo)}},  /* ${pdo.name} */\n`;
      });
      slave.tx_pdos?.forEach((pdo: any) => {
        mapping += `    {${pdo.index}, ${pdo.subindex}, ${this.getTypeBits(pdo)}},  /* ${pdo.name} */\n`;
      });
      mapping += `};\n\n`;

      // 生成 PDO 信息
      mapping += `static ec_pdo_info_t slave${index}_pdos[] = {\n`;
      const rxCount = slave.rx_pdos?.length || 0;
      const txCount = slave.tx_pdos?.length || 0;
      mapping += `    {${slave.rx_pdo}, ${rxCount}, slave${index}_pdo_entries + 0},  /* RxPDO */\n`;
      mapping += `    {${slave.tx_pdo}, ${txCount}, slave${index}_pdo_entries + ${rxCount}},  /* TxPDO */\n`;
      mapping += `};\n\n`;

      // 使用从 ethercat cstruct 获取的同步管理器信息
      const syncInfo = syncInfos.find(si => si.index === index);
      if (syncInfo && syncInfo.syncInfo) {
        mapping += syncInfo.syncInfo + '\n\n';
      } else {
        throw new Error(`Missing sync info for slave ${slave.index}`);
      }
    });

    return mapping;
  }

  private static getTypeBits(pdo: any): number {
    // 如果有 bitlen，直接使用
    if (pdo.bitlen !== undefined) {
      return pdo.bitlen;
    }

    // 否则根据类型提供默认值
    switch (pdo.type.toLowerCase()) {
      case 'bool':
        return 1;
      case 'int8':
      case 'uint8':
        return 8;
      case 'uint16':
      case 'int16':
        return 16;
      case 'uint32':
      case 'int32':
        return 32;
      case 'uint64':
      case 'int64':
      case 'double':
        return 64;
      default:
        return 16;  // 默认使用 16 位
    }
  }

  private static getPDOReadFunction(type: string): string {
    switch (type.toLowerCase()) {
      case 'bool':
        return 'EC_READ_BIT';
      case 'uint8':
        return 'EC_READ_U8';
      case 'int8':
        return 'EC_READ_S8';
      case 'uint16':
        return 'EC_READ_U16';
      case 'int16':
        return 'EC_READ_S16';
      case 'uint32':
        return 'EC_READ_U32';
      case 'int32':
        return 'EC_READ_S32';
      case 'uint64':
        return 'EC_READ_U64';
      case 'int64':
        return 'EC_READ_S64';
      case 'double':
        return 'EC_READ_LREAL';
      default:
        return 'EC_READ_U16';
    }
  }

  private static getPDOWriteFunction(type: string): string {
    switch (type.toLowerCase()) {
      case 'bool':
        return 'EC_WRITE_BIT';
      case 'uint8':
        return 'EC_WRITE_U8';
      case 'int8':
        return 'EC_WRITE_S8';
      case 'uint16':
        return 'EC_WRITE_U16';
      case 'int16':
        return 'EC_WRITE_S16';
      case 'uint32':
        return 'EC_WRITE_U32';
      case 'int32':
        return 'EC_WRITE_S32';
      case 'uint64':
        return 'EC_WRITE_U64';
      case 'int64':
        return 'EC_WRITE_S64';
      case 'double':
        return 'EC_WRITE_LREAL';
      default:
        return 'EC_WRITE_U16';
    }
  }

  private static generateCyclicTask(config: any): string {
    let task = `
void check_slave_config_states(void)
{
    static ec_slave_config_state_t slaves_state[${config.slaves.length}];  // 从站数从配置获取
    static int first_check = 1;  // 第一次检查的标志
    int all_op = 1;  // 所有从站是否都在OP状态的标志
    
    // 检查所有从站状态
    for (int i = 0; i < ${config.slaves.length}; i++) {
        ec_slave_config_state_t s;
        ec_slave_config_t *sc = NULL;
        
        // 根据索引获取对应的从站配置
        switch(i) {
${config.slaves.map((slave: any, index: number) => 
            `            case ${index}: sc = sc_slave${index}; break;`
).join('\n')}
        }
        
        ecrt_slave_config_state(sc, &s);
        
        // 检查是否所有从站都在OP状态
        if (!s.operational) {
            all_op = 0;
        }
        
        // 只在状态发生变化时打印
        if (!first_check) {
            if (s.al_state != slaves_state[i].al_state)
                printf("slave %d: State 0x%02X.\\n", i, s.al_state);
            if (s.online != slaves_state[i].online)
                printf("slave %d: %s.\\n", i, s.online ? "online" : "offline");
            if (s.operational != slaves_state[i].operational)
                printf("slave %d: %soperational.\\n", i, s.operational ? "" : "Not ");
        }
        
        slaves_state[i] = s;
    }
    
    // 修改 ALL OP 检测和文件操作部分
    static int op_printed = 0;
    char op_file[64];
    snprintf(op_file, sizeof(op_file), "/tmp/MASTER%d_ALL_OP", MASTER_INDEX);
    
    if (all_op && !op_printed) {
        printf("ALL OP\\n");
        op_printed = 1;
        all_slaves_op = 1;  // Set the flag to indicate all slaves are in OP state
        // 创建 ALL OP 标记文件
        FILE *fp = fopen(op_file, "w");
        if (fp) {
            fclose(fp);
        } else {
            fprintf(stderr, "Failed to create ALL OP file: %s\\n", op_file);
        }
    } else if (!all_op && op_printed) {
        op_printed = 0;
        // 删除 ALL OP 标记文件
        // if (remove(op_file) != 0) {
        //     fprintf(stderr, "Failed to remove ALL OP file: %s\\n", op_file);
        // }
    }
    
    first_check = 0;
}

void cyclic_task(void)
{
    static int cycle_counter = 0;
    static struct timespec wakeupTime;
    static int initialized = 0;

    if (!initialized) {
        clock_gettime(CLOCK_TO_USE, &wakeupTime);
        initialized = 1;
    }

    wakeupTime.tv_nsec += PERIOD_NS;
    while (wakeupTime.tv_nsec >= NSEC_PER_SEC) {
        wakeupTime.tv_nsec -= NSEC_PER_SEC;
        wakeupTime.tv_sec++;
    }

    clock_nanosleep(CLOCK_TO_USE, TIMER_ABSTIME, &wakeupTime, NULL);

    ecrt_master_application_time(master, TIMESPEC2NS(wakeupTime));

    ecrt_master_receive(master);
    
    // 添加错误检查
    if (ecrt_master_state(master, &master_state)) {
        run = 0;  // 如果出现错误，触发程序退出
        fprintf(stderr, "Failed to get master state.\\n");
        return;
    }
    
    ecrt_domain_process(domain1);

    if (cycle_counter) {
        cycle_counter--;
    } else {
          cycle_counter = TASK_FREQUENCY;
        // Only check states if not all slaves are in OP state
        if (!all_slaves_op) {
            check_slave_config_states();
        }
    }

    // 更新从站状态
`;

    // 为每个从站添加状态更新代码
    config.slaves.forEach((slave: any, index: number) => {
        task += `
    // 获取从站${index}状态
    ecrt_slave_config_state(sc_slave${index}, &sc_slave${index}_state);
    
    // 更新从站${index}状态到共享内存
    ethercat_shm->shm_slave${index}_online_status = sc_slave${index}_state.online;
    ethercat_shm->shm_slave${index}_operational_status = sc_slave${index}_state.operational;
    ethercat_shm->shm_slave${index}_al_state = sc_slave${index}_state.al_state;
`;
    });

    // 添加PDO更新代码
    task += `
    // Update shared memory with status
${config.slaves.map((slave: any, index: number) => 
    slave.tx_pdos?.map((pdo: any) => {
        const varName = this.sanitizeVariableName(pdo, index, 'tx');
        if (pdo.type === 'bool') {
            return `    ethercat_shm->shm_slave${index}_tx_${varName} = EC_READ_BIT(domain1_pd + offset.pdo_slave${index}_tx_${varName}_off, offset.pdo_slave${index}_tx_${varName}_bit);`;
        } else {
            const readFunc = this.getPDOReadFunction(pdo.type);
            return `    ethercat_shm->shm_slave${index}_tx_${varName} = ${readFunc}(domain1_pd + offset.pdo_slave${index}_tx_${varName});`;
        }
    }).join('\n')
).join('\n')}

    // Write to EtherCAT
${config.slaves.map((slave: any, index: number) => 
    slave.rx_pdos?.map((pdo: any) => {
        const varName = this.sanitizeVariableName(pdo, index, 'rx');
        if (pdo.type === 'bool') {
            return `    EC_WRITE_BIT(domain1_pd + offset.pdo_slave${index}_rx_${varName}_off, offset.pdo_slave${index}_rx_${varName}_bit, ethercat_shm->shm_slave${index}_rx_${varName});`;
        } else {
            const writeFunc = this.getPDOWriteFunction(pdo.type);
            return `    ${writeFunc}(domain1_pd + offset.pdo_slave${index}_rx_${varName}, ethercat_shm->shm_slave${index}_rx_${varName});`;
        }
    }).join('\n')
).join('\n')}

    // Send process data
    ecrt_domain_queue(domain1);
    ecrt_master_sync_slave_clocks(master);
    ecrt_master_sync_reference_clock(master);
    ecrt_master_send(master);

    if (last_cycle) {
        printf("Executing final cycle...\\n");
        
        // 将从站切换到预运行状态
        printf("Switching slaves to PREOP state...\\n");
        ecrt_master_deactivate(master);
        
        // 释放EtherCAT主站
        if (master) {
            printf("Releasing master...\\n");
            ecrt_release_master(master);
        }
        
        // 清理共享内存
        cleanup_shm();
        
        printf("Shutdown complete\\n");
        run = 0;  // 这将导致主循环退出
    }
}`;

    return task;
  }

  private static generateMainFunction(config: TemplateConfig): string {
    return `
int main(int argc, char **argv) {
    // 启动时先删除可能存在的 ALL OP 标记文件
    char op_file[64];
    snprintf(op_file, sizeof(op_file), "/tmp/MASTER%d_ALL_OP", MASTER_INDEX);
    remove(op_file);

    // Set up signal handler for cleanup
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // Lock memory to prevent paging
    if (mlockall(MCL_CURRENT | MCL_FUTURE) == -1) {
        perror("mlockall failed");
        return -1;
    }

    // Create shared memory
    create_shm();
    
    // Initialize EtherCAT master
    printf("Requesting master...\\n");
    master = ecrt_request_master(MASTER_INDEX);
    if (!master) exit(EXIT_FAILURE);
    
    domain1 = ecrt_master_create_domain(master);
    if (!domain1) exit(EXIT_FAILURE);

    // Configure slaves
    printf("Configuring all slaves...\\n");
    
    // 创建从站配置数组
    ec_slave_config_t *slave_configs[${config.slaves.length}];
    
    // 批量获取所有从站配置
    ${config.slaves.map((slave: any, index: number) => `
    slave_configs[${index}] = ecrt_master_slave_config(master, slave${index}_POS, slave${index}_VID_PID);
    if (!slave_configs[${index}]) {
        fprintf(stderr, "Failed to get slave${index} configuration!\\n");
        exit(EXIT_FAILURE);
    }`).join('\n')}
    
    // 保存配置引用
    ${config.slaves.map((slave: any, index: number) => `
    sc_slave${index} = slave_configs[${index}];`).join('\n')}
    
    // 创建PDO配置线程
    pthread_t threads[${config.slaves.length}];
    int config_results[${config.slaves.length}] = {0};
    config_thread_param_t thread_params[${config.slaves.length}];
    
    printf("Starting parallel PDO configuration...\\n");
    
    // 初始化PDO配置线程参数
    ${config.slaves.map((slave: any, index: number) => `
    thread_params[${index}].slave_config = slave_configs[${index}];
    thread_params[${index}].sync_info = slave${index}_syncs;
    thread_params[${index}].result = &config_results[${index}];`).join('\n')}
    
    // 启动PDO配置线程
    for(int i = 0; i < ${config.slaves.length}; i++) {
        if (pthread_create(&threads[i], NULL, config_slave_thread, &thread_params[i])) {
            fprintf(stderr, "Failed to create PDO configuration thread %d\\n", i);
            exit(EXIT_FAILURE);
        }
    }
    
    // 等待所有PDO配置线程完成
    for(int i = 0; i < ${config.slaves.length}; i++) {
        pthread_join(threads[i], NULL);
        if (config_results[i]) {
            fprintf(stderr, "Failed to configure PDOs for slave %d\\n", i);
            exit(EXIT_FAILURE);
        }
    }
    
    printf("Parallel PDO configuration completed\\n");

    // 配置 DC
    printf("Starting parallel DC configuration...\\n");
    
    // 创建DC配置线程数组和参数
    pthread_t dc_threads[${config.slaves.length}];
    int dc_results[${config.slaves.length}] = {0};
    dc_config_thread_param_t dc_params[${config.slaves.length}];
    
    // 初始化DC配置参数
    ${config.slaves.map((slave: any, index: number) => `
    ${typeof slave.dc_config === 'object' ? `
    dc_params[${index}].slave_config = slave_configs[${index}];
    dc_params[${index}].assign_activate = ${slave.dc_config.assign_activate || '0x0300'};
    dc_params[${index}].sync0_cycle = ${slave.dc_config.sync0_cycle === '' ? 'PERIOD_NS' : slave.dc_config.sync0_cycle || 'PERIOD_NS'};
    dc_params[${index}].sync0_shift = ${slave.dc_config.sync0_shift === '' ? 'PERIOD_NS/2' : slave.dc_config.sync0_shift || 'PERIOD_NS/2'};
    dc_params[${index}].sync1_cycle = ${slave.dc_config.sync1_cycle === '' ? '0' : slave.dc_config.sync1_cycle || '0'};
    dc_params[${index}].sync1_shift = ${slave.dc_config.sync1_shift === '' ? '0' : slave.dc_config.sync1_shift || '0'};
    dc_params[${index}].result = &dc_results[${index}];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[${index}], NULL, dc_config_thread, &dc_params[${index}])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\\n", ${index});
        exit(EXIT_FAILURE);
    }` : 
    '// No DC configuration needed for this slave'}`).join('\n')}
    
    // 等待所有DC配置线程完成
    ${config.slaves.map((slave: any, index: number) => 
        typeof slave.dc_config === 'object' ? 
        `pthread_join(dc_threads[${index}], NULL);
    if (dc_results[${index}]) {
        fprintf(stderr, "Failed to configure DC for slave %d\\n", ${index});
        exit(EXIT_FAILURE);
    }` : 
        '// Skip waiting for non-DC slave'
    ).join('\n')}
    
    printf("Parallel DC configuration completed\\n");

    if (ecrt_domain_reg_pdo_entry_list(domain1, domain1_regs)) {
        fprintf(stderr, "PDO entry registration failed!\\n");
        exit(EXIT_FAILURE);
    }

    printf("Activating master...\\n");
    if (ecrt_master_activate(master)) {
        exit(EXIT_FAILURE);
    }

    if (!(domain1_pd = ecrt_domain_data(domain1))) {
        exit(EXIT_FAILURE);
    }

    // Set real-time priority
    struct sched_param param = {};
    param.sched_priority = sched_get_priority_max(SCHED_FIFO);
    printf("Using priority %i.\\n", param.sched_priority);
    if (sched_setscheduler(0, SCHED_FIFO, &param) == -1) {
        perror("sched_setscheduler failed");
    }

    printf("Started.\\n");
    printf("Shared memory interface created at %s\\n", ETHERCAT_SHM_FILE);
    
    // 修改后的主循环
    while (run) {
        cyclic_task();
    }

    return EXIT_SUCCESS;
}`;
  }

  private static generateCStruct(config: any): string {
    const lines: string[] = [];
    
    if (config.slaves && config.slaves.length > 0) {
      // 遍历所有从站
      config.slaves.forEach((slave: any, index: number) => {
        // 处理 RxPDOs
        if (slave.rx_pdos) {
          slave.rx_pdos.forEach((pdo: any) => {
            if (pdo && pdo.index && pdo.name) {  // 添加空值检查
              const varName = this.sanitizeVariableName(pdo, index, 'rx');
              const comment = pdo.comment ? ` /* ${pdo.comment} */` : '';
              lines.push(`    int ${varName};${comment}`);
            }
          });
        }

        // 处理 TxPDOs
        if (slave.tx_pdos) {
          slave.tx_pdos.forEach((pdo: any) => {
            if (pdo && pdo.index && pdo.name) {  // 添加空值检查
              const varName = this.sanitizeVariableName(pdo, index, 'tx');
              const comment = pdo.comment ? ` /* ${pdo.comment} */` : '';
              lines.push(`    int ${varName};${comment}`);
            }
          });
        }
      });
    }

    return lines.join('\n');
  }

  public static generateProgramServiceScript(
    config: TemplateConfig, 
    middleLayerPath: string, 
    programPath: string, 
    shmFileName: string,
    programCpu: string = "2"  // 新增参数，默认值为"2"
  ): string {
    const workingDir = path.dirname(programPath);
    return `
[Unit]
StartLimitIntervalSec=0
StartLimitBurst=0
Description=EtherCAT Control Service
After=network.target
[Service]
Type=simple
WorkingDirectory=${workingDir}
ExecStartPre=/bin/bash -c 'echo 1 > /proc/sys/kernel/printk && echo 1 > /proc/sys/vm/overcommit_memory'
ExecStart=/bin/bash -c 'taskset -c ${programCpu} ${programPath}'
#ExecStop=/bin/bash -c 'pkill -INT  "${path.basename(programPath)}"'
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
`;
  }

  public static generateMiddlewareScript(
    config: TemplateConfig, 
    middleLayerPath: string, 
    programPath: string, 
    shmFileName: string,
    middlewareCpu: string = "3"  // 新增参数，默认值为"3" 
  ): string {
    const workingDir = path.dirname(programPath);
    return `[Unit]
StartLimitIntervalSec=0
StartLimitBurst=0
Description=EtherCAT Middleware Service
After=network.target
[Service]
Type=simple
WorkingDirectory=${workingDir}
ExecStartPre=/bin/bash -c 'echo 1 > /proc/sys/kernel/printk && echo 1 > /proc/sys/vm/overcommit_memory'
ExecStart=/bin/bash -c 'taskset -c ${middlewareCpu} chrt -f 99 ${middleLayerPath}'
#ExecStop=/bin/bash -c 'pkill -INT  "${path.basename(middleLayerPath)}"'
Restart=always
RestartSec=5
#Environment=ETHERCAT_MASTER=${config.masterIndex}

[Install]
WantedBy=multi-user.target
`;
  }
} 
