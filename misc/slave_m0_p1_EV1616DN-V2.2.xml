<?xml version="1.0" ?>
<EtherCATInfo>
  <!-- Slave 1 -->
  <Vendor>
    <Id>3383</Id>
  </Vendor>
  <Descriptions>
    <Devices>
      <Device>
        <Type ProductCode="#x000f0001" RevisionNo="#x00000100">EV1616DN-V2.2</Type>
        <Name><![CDATA[EV1616DN-V2.2 ]]></Name>
        <Sm Enable="1" StartAddress="#x1000" ControlByte="#x26" DefaultSize="128" />
        <Sm Enable="1" StartAddress="#x1080" ControlByte="#x22" DefaultSize="128" />
        <Sm Enable="0" StartAddress="#x1100" ControlByte="#x64" DefaultSize="0" />
        <Sm Enable="0" StartAddress="#x1400" ControlByte="#x20" DefaultSize="0" />
        <RxPdo Sm="2" Fixed="1" Mandatory="1">
          <Index>#x1601</Index>
          <Name>RxPDO-Map</Name>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>1</SubIndex>
            <BitLen>1</BitLen>
            <Name>Output_U16</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>2</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 002</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>3</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 003</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>4</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 004</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>5</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 005</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>6</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 006</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>7</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 007</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>8</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 008</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>9</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 009</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>10</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 010</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>11</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 011</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>12</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 012</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>13</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 013</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>14</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 014</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>15</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 015</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x7010</Index>
            <SubIndex>16</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 016</Name>
            <DataType>BOOL</DataType>
          </Entry>
        </RxPdo>
        <TxPdo Sm="3" Fixed="1" Mandatory="1">
          <Index>#x1a00</Index>
          <Name>TxPDO-Map</Name>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>1</SubIndex>
            <BitLen>1</BitLen>
            <Name>Input_U16</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>2</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 002</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>3</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 003</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>4</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 004</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>5</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 005</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>6</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 006</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>7</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 007</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>8</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 008</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>9</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 009</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>16</SubIndex>
            <BitLen>1</BitLen>
            <Name>SubIndex 016</Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>17</SubIndex>
            <BitLen>1</BitLen>
            <Name></Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>18</SubIndex>
            <BitLen>1</BitLen>
            <Name></Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>19</SubIndex>
            <BitLen>1</BitLen>
            <Name></Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>20</SubIndex>
            <BitLen>1</BitLen>
            <Name></Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>21</SubIndex>
            <BitLen>1</BitLen>
            <Name></Name>
            <DataType>BOOL</DataType>
          </Entry>
          <Entry>
            <Index>#x6000</Index>
            <SubIndex>22</SubIndex>
            <BitLen>1</BitLen>
            <Name></Name>
            <DataType>BOOL</DataType>
          </Entry>
        </TxPdo>
      </Device>
    </Devices>
  </Descriptions>
</EtherCATInfo>
