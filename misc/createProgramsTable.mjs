import PocketBase from 'pocketbase';

async function createProgramsTable() {
  const pb = new PocketBase('http://127.0.0.1:8090');

  // 登录管理员账户
  await pb.admins.authWithPassword('<EMAIL>', 'qs2022qs2022');

  // 检查是否已经存在名为 'programs' 的集合
  const existingCollection = await pb.collections.getOne('programs').catch(() => null);

  if (existingCollection) {
    console.log('Collection "programs" already exists.');
    return;
  }

  // 创建 'programs' 集合
  const collection = await pb.collections.create({
    name: 'programs',
    type: 'base',
    schema: [
      {
        name: 'name',
        type: 'text',
        required: true,
        unique: true
      },
      {
        name: 'status',
        type: 'select',
        options: {
          values: ['running', 'stopped', 'error']
        },
        required: true
      },
      {
        name: 'uploadTime',
        type: 'datetime',
        required: true
      },
      {
        name: 'masterIndex',
        type: 'number',
        required: true,
        options: {
          min: 0,
          max: 10,
          default: 0
        }
      },
      {
        name: 'taskFrequency',
        type: 'number',
        required: true,
        options: {
          min: 100,
          max: 10000,
          default: 1000
        }
      },
      {
        name: 'ethercatDir',
        type: 'text',
        required: true,
        options: {
          default: '/dev/shm/ethercat'
        }
      },
      {
        name: 'config',
        type: 'json',
        required: true
      },
      {
        name: 'filePath',
        type: 'text',
        required: true
      }
    ],
    listRule: null,
    viewRule: null,
    createRule: null,
    updateRule: null,
    deleteRule: null
  });

  console.log('Collection "programs" created:', collection);
}

createProgramsTable().catch(console.error); 