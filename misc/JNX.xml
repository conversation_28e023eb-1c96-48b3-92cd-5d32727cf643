<?xml version="1.0"?>
<EtherCATConfig xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="EtherCATConfig.xsd" Version="1.3">
	<Config>
		<Master>
			<Info>
				<Name><![CDATA[Device 1 (EtherCAT)]]></Name>
				<Destination>010105010000</Destination>
				<Source>047c167b18a7</Source>
				<EtherType>a488</EtherType>
			</Info>
			<MailboxStates>
				<StartAddr>150994944</StartAddr>
				<Count>2</Count>
			</MailboxStates>
			<InitCmds>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[read slave count]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>7</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>0</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[read slave count]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>7</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>0</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[enable ECAT IRQ]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>512</Ado>
					<Data>0400</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear configured addresses]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>16</Ado>
					<Data>0000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear crc register]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>768</Ado>
					<Data>0000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>PI</Transition>
					<Transition>BI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear fmmu]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>1536</Ado>
					<DataLength>256</DataLength>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear sm]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2048</Ado>
					<DataLength>256</DataLength>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear dc system time]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2320</Ado>
					<DataLength>32</DataLength>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear dc cycle cfg]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2433</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[reset dc speed]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2352</Ado>
					<Data>0010</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[configure dc filter]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2356</Ado>
					<Data>000c</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[en/disable second physical address]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>259</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
			</InitCmds>
		</Master>
		<Slave>
			<Info>
				<Name><![CDATA[Box 1 (AU7 523S-1BL)]]></Name>
				<PhysAddr>1001</PhysAddr>
				<AutoIncAddr>0</AutoIncAddr>
				<Physics>YY</Physics>
				<VendorId>2546</VendorId>
				<ProductCode>86</ProductCode>
				<RevisionNo>0</RevisionNo>
				<SerialNo>0</SerialNo>
			</Info>
			<ProcessData>
				<Send>
					<BitStart>312</BitStart>
					<BitLength>32</BitLength>
				</Send>
				<Recv>
					<BitStart>312</BitStart>
					<BitLength>32</BitLength>
				</Recv>
				<Sm2>
					<Type>Outputs</Type>
					<StartAddress>4352</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>1</Enable>
					<Pdo>5632</Pdo>
					<Pdo>5633</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<StartAddress>5888</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>1</Enable>
					<Pdo>6656</Pdo>
					<Pdo>6657</Pdo>
				</Sm3>
				<TxPdo Fixed="true" Mandatory="true" Sm="3">
					<Index>#x1a00</Index>
					<Name>ID</Name>
					<Entry>
						<Index>#x6000</Index>
						<SubIndex>1</SubIndex>
						<BitLen>16</BitLen>
						<Name>ID</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="true" Mandatory="true" Sm="3">
					<Index>#x1a01</Index>
					<Name>Digital Inputs</Name>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>1</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 1</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>2</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 2</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>3</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 3</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>4</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 4</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>5</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 5</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>6</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 6</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>7</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 7</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>8</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 8</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>9</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 9</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>10</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 10</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>11</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 11</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>12</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 12</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>13</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 13</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>14</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 14</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>15</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 15</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x6010</Index>
						<SubIndex>16</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 16</Name>
						<DataType>BIT</DataType>
					</Entry>
				</TxPdo>
				<RxPdo Fixed="true" Mandatory="true" Sm="2">
					<Index>#x1600</Index>
					<Name>Filter Setting</Name>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>1</SubIndex>
						<BitLen>8</BitLen>
						<Name>Filter Time</Name>
						<DataType>USINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<BitLen>8</BitLen>
						<Name>E0000:00</Name>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="true" Mandatory="true" Sm="2">
					<Index>#x1601</Index>
					<Name>Digital Outputs</Name>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>1</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 1</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>2</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 2</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>3</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 3</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>4</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 4</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>5</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 5</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>6</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 6</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>7</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 7</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>8</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 8</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>9</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 9</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>10</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 10</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>11</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 11</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>12</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 12</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>13</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 13</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>14</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 14</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>15</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 15</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7010</Index>
						<SubIndex>16</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 16</Name>
						<DataType>BIT</DataType>
					</Entry>
				</RxPdo>
			</ProcessData>
			<Mailbox DataLinkLayer="true">
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>4224</Start>
					<Length>128</Length>
					<StatusBitAddr>0</StatusBitAddr>
				</Recv>
				<Protocol>CoE</Protocol>
			</Mailbox>
			<InitCmds>
				<InitCmd>
					<Transition>PI</Transition>
					<Transition>BI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[set device state to INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>288</Ado>
					<Data>1100</Data>
					<Retries>3</Retries>
					<Timeout>5000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>PI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[check device state for INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>0100</Data>
						<DataMask>0f00</DataMask>
						<Timeout>5000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>BI</Transition>
					<Comment><![CDATA[check device state for INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>0100</Data>
						<DataMask>0f00</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set device state to INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>288</Ado>
					<Data>1100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>3000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[check device state for INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>0100</Data>
						<DataMask>0f00</DataMask>
						<Timeout>3000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[assign EEPROM to ECAT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1280</Ado>
					<Data>00</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check vendor id]]></Comment>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1282</Ado>
					<Data>000108000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check vendor id]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>1288</Ado>
					<Data>00000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>f2090000</Data>
						<Timeout>100</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check product code]]></Comment>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1282</Ado>
					<Data>00010a000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check product code]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>1288</Ado>
					<Data>00000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>56000000</Data>
						<Timeout>100</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set physical address]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>16</Ado>
					<Data>e903</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Transition>PI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear sm 0/1 (mailbox out/in)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2048</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>BI</Transition>
					<Comment><![CDATA[clear sm 0/1 (mailbox out/in)]]></Comment>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>2048</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set sm 0 (mailbox out)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2048</Ado>
					<Data>0010800026000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set sm 1 (mailbox in)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2056</Ado>
					<Data>8010800022000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>OP</Transition>
					<Comment><![CDATA[set device state to PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>1200</Data>
					<Retries>300</Retries>
					<Timeout>200</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>SP</Transition>
					<Transition>SI</Transition>
					<Transition>OP</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear sms]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2064</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set sm 2 (outputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2064</Ado>
					<Data>0011040064000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set sm 3 (inputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2072</Ado>
					<Data>0017040020000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set fmmu 0 (outputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1536</Ado>
					<Data>00000001040000070011000201000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set fmmu 1 (inputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1552</Ado>
					<Data>00000001040000070017000101000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set fmmu 2 (mailbox state)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1568</Ado>
					<Data>00000009010000000d08000101000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>OS</Transition>
					<Comment><![CDATA[set device state to SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>0400</Data>
					<Retries>3</Retries>
					<Timeout>200</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>SI</Transition>
					<Transition>OP</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear fmmu 0]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1536</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>SI</Transition>
					<Transition>OP</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear fmmu 1]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1552</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PI</Transition>
					<Transition>BI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear fmmu 2]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1568</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>OP</Transition>
					<Comment><![CDATA[check device state for PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>020000000000</Data>
						<DataMask>0f0000000000</DataMask>
						<Timeout>5000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[assign EEPROM to PDI]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1280</Ado>
					<Data>01</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>II</Transition>
					<Comment><![CDATA[assign EEPROM back to ECAT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1280</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[set device state to PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>1200</Data>
					<Cnt>1</Cnt>
					<Retries>300</Retries>
					<Timeout>3000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check device state for PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>020000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>3000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>BI</Transition>
					<Comment><![CDATA[assign EEPROM back to ECAT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1280</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IB</Transition>
					<Comment><![CDATA[set device state to BOOT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>1300</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>3000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IB</Transition>
					<Comment><![CDATA[check device state for BOOT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>030000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>3000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set device state to SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>0400</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>10000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[check device state for SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>040000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>OS</Transition>
					<Comment><![CDATA[check device state for SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>040000000000</Data>
						<DataMask>0f0000000000</DataMask>
						<Timeout>200</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>SO</Transition>
					<Comment><![CDATA[set device state to OP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>0800</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>10000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>SO</Transition>
					<Comment><![CDATA[check device state for OP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>080000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
			</InitCmds>
		</Slave>
		<Slave>
			<Info>
				<Name><![CDATA[Box 2 (SM522S-1BL)]]></Name>
				<PhysAddr>1002</PhysAddr>
				<AutoIncAddr>65535</AutoIncAddr>
				<Physics>YY</Physics>
				<VendorId>2546</VendorId>
				<ProductCode>84</ProductCode>
				<RevisionNo>0</RevisionNo>
				<SerialNo>0</SerialNo>
			</Info>
			<ProcessData>
				<Send>
					<BitStart>344</BitStart>
					<BitLength>32</BitLength>
				</Send>
				<Recv>
					<BitStart>344</BitStart>
					<BitLength>16</BitLength>
				</Recv>
				<Sm2>
					<Type>Outputs</Type>
					<StartAddress>4352</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>1</Enable>
					<Pdo>5632</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<StartAddress>5888</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>1</Enable>
					<Pdo>6656</Pdo>
				</Sm3>
				<TxPdo Fixed="true" Mandatory="true" Sm="3">
					<Index>#x1a00</Index>
					<Name>ID</Name>
					<Entry>
						<Index>#x6000</Index>
						<SubIndex>1</SubIndex>
						<BitLen>16</BitLen>
						<Name>ID</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<RxPdo Fixed="true" Mandatory="true" Sm="2">
					<Index>#x1600</Index>
					<Name>Digital Outputs</Name>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>1</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 1</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>2</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 2</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>3</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 3</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>4</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 4</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>5</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 5</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>6</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 6</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>7</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 7</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>8</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 8</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>9</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 9</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>10</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 10</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>11</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 11</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>12</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 12</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>13</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 13</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>14</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 14</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>15</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 15</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>16</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 16</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>17</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 17</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>18</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 18</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>19</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 19</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>20</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 20</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>21</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 21</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>22</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 22</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>23</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 23</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>24</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 24</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>25</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 25</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>26</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 26</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>27</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 27</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>28</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 28</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>29</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 29</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>30</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 30</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>31</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 31</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index>#x7000</Index>
						<SubIndex>32</SubIndex>
						<BitLen>1</BitLen>
						<Name>Channel 32</Name>
						<DataType>BIT</DataType>
					</Entry>
				</RxPdo>
			</ProcessData>
			<Mailbox DataLinkLayer="true">
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>4224</Start>
					<Length>128</Length>
					<StatusBitAddr>1</StatusBitAddr>
				</Recv>
				<Protocol>CoE</Protocol>
			</Mailbox>
			<InitCmds>
				<InitCmd>
					<Transition>PI</Transition>
					<Transition>BI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[set device state to INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>65535</Adp>
					<Ado>288</Ado>
					<Data>1100</Data>
					<Retries>3</Retries>
					<Timeout>5000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>PI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[check device state for INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>65535</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>0100</Data>
						<DataMask>0f00</DataMask>
						<Timeout>5000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>BI</Transition>
					<Comment><![CDATA[check device state for INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>65535</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>0100</Data>
						<DataMask>0f00</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set device state to INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>65535</Adp>
					<Ado>288</Ado>
					<Data>1100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>3000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[check device state for INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>65535</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>0100</Data>
						<DataMask>0f00</DataMask>
						<Timeout>3000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[assign EEPROM to ECAT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>65535</Adp>
					<Ado>1280</Ado>
					<Data>00</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check vendor id]]></Comment>
					<Cmd>2</Cmd>
					<Adp>65535</Adp>
					<Ado>1282</Ado>
					<Data>000108000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check vendor id]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>65535</Adp>
					<Ado>1288</Ado>
					<Data>00000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>f2090000</Data>
						<Timeout>100</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check product code]]></Comment>
					<Cmd>2</Cmd>
					<Adp>65535</Adp>
					<Ado>1282</Ado>
					<Data>00010a000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check product code]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>65535</Adp>
					<Ado>1288</Ado>
					<Data>00000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>54000000</Data>
						<Timeout>100</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set physical address]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>65535</Adp>
					<Ado>16</Ado>
					<Data>ea03</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Transition>PI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear sm 0/1 (mailbox out/in)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>2048</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>BI</Transition>
					<Comment><![CDATA[clear sm 0/1 (mailbox out/in)]]></Comment>
					<Cmd>2</Cmd>
					<Adp>65535</Adp>
					<Ado>2048</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set sm 0 (mailbox out)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>2048</Ado>
					<Data>0010800026000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set sm 1 (mailbox in)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>2056</Ado>
					<Data>8010800022000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>OP</Transition>
					<Comment><![CDATA[set device state to PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>288</Ado>
					<Data>1200</Data>
					<Retries>300</Retries>
					<Timeout>200</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>SP</Transition>
					<Transition>SI</Transition>
					<Transition>OP</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear sms]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>2064</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set sm 2 (outputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>2064</Ado>
					<Data>0011040064000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set sm 3 (inputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>2072</Ado>
					<Data>0017020020000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set fmmu 0 (outputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>1536</Ado>
					<Data>04000001040000070011000201000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set fmmu 1 (inputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>1552</Ado>
					<Data>04000001020000070017000101000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set fmmu 2 (mailbox state)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>1568</Ado>
					<Data>00000009010001010d08000101000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>OS</Transition>
					<Comment><![CDATA[set device state to SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>288</Ado>
					<Data>0400</Data>
					<Retries>3</Retries>
					<Timeout>200</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>SI</Transition>
					<Transition>OP</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear fmmu 0]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>1536</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>SI</Transition>
					<Transition>OP</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear fmmu 1]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>1552</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PI</Transition>
					<Transition>BI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear fmmu 2]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>1568</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>OP</Transition>
					<Comment><![CDATA[check device state for PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1002</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>020000000000</Data>
						<DataMask>0f0000000000</DataMask>
						<Timeout>5000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[assign EEPROM to PDI]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>1280</Ado>
					<Data>01</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>II</Transition>
					<Comment><![CDATA[assign EEPROM back to ECAT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>65535</Adp>
					<Ado>1280</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[set device state to PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>288</Ado>
					<Data>1200</Data>
					<Cnt>1</Cnt>
					<Retries>300</Retries>
					<Timeout>3000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check device state for PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1002</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>020000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>3000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>BI</Transition>
					<Comment><![CDATA[assign EEPROM back to ECAT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>65535</Adp>
					<Ado>1280</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IB</Transition>
					<Comment><![CDATA[set device state to BOOT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>288</Ado>
					<Data>1300</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>3000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IB</Transition>
					<Comment><![CDATA[check device state for BOOT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1002</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>030000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>3000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set device state to SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>288</Ado>
					<Data>0400</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>10000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[check device state for SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1002</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>040000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>OS</Transition>
					<Comment><![CDATA[check device state for SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1002</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>040000000000</Data>
						<DataMask>0f0000000000</DataMask>
						<Timeout>200</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>SO</Transition>
					<Comment><![CDATA[set device state to OP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1002</Adp>
					<Ado>288</Ado>
					<Data>0800</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>10000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>SO</Transition>
					<Comment><![CDATA[check device state for OP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1002</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>080000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
			</InitCmds>
			<PreviousPort Selected="true">
				<Port>B</Port>
				<PhysAddr>1001</PhysAddr>
			</PreviousPort>
		</Slave>
		<Cyclic>
			<Frame>
				<Cmd>
					<State>PREOP</State>
					<State>SAFEOP</State>
					<State>OP</State>
					<Comment><![CDATA[cyclic cmd]]></Comment>
					<Cmd>10</Cmd>
					<Addr>150994944</Addr>
					<DataLength>1</DataLength>
					<InputOffs>16</InputOffs>
					<OutputOffs>16</OutputOffs>
				</Cmd>
				<Cmd>
					<State>SAFEOP</State>
					<State>OP</State>
					<Comment><![CDATA[cyclic cmd]]></Comment>
					<Cmd>12</Cmd>
					<Addr>16777216</Addr>
					<DataLength>8</DataLength>
					<Cnt>6</Cnt>
					<InputOffs>29</InputOffs>
					<OutputOffs>29</OutputOffs>
				</Cmd>
				<Cmd>
					<State>PREOP</State>
					<State>SAFEOP</State>
					<State>OP</State>
					<Comment><![CDATA[cyclic cmd]]></Comment>
					<Cmd>7</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<DataLength>2</DataLength>
					<Cnt>2</Cnt>
					<InputOffs>49</InputOffs>
					<OutputOffs>49</OutputOffs>
				</Cmd>
			</Frame>
		</Cyclic>
		<ProcessImage>
			<Inputs>
				<ByteSize>1536</ByteSize>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).ID.ID</Name>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>312</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 1</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>328</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 2</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>329</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 3</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>330</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 4</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>331</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 5</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>332</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 6</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>333</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 7</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>334</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 8</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>335</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 9</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>336</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 10</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>337</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 11</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>338</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 12</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>339</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 13</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>340</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 14</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>341</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 15</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>342</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Inputs.Channel 16</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>343</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).ID.ID</Name>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>344</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.Frm0State</Name>
					<Comment><![CDATA[0x0001 = 1. EtherCAT command not sent (NOP requested)
0x0002 = 2. EtherCAT command not sent (NOP requested)
0x0004 = 3. EtherCAT command not sent (NOP requested)
...
0x4000 = 15. EtherCAT command not sent (NOP requested)
0x8000 = complete frame not sent
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12160</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.Frm0WcState</Name>
					<Comment><![CDATA[0x0001 = wrong working counter of 1. EtherCAT command received
0x0002 = wrong working counter of 2. EtherCAT command received
0x0004 = wrong working counter of 3. EtherCAT command received
...
0x4000 = wrong working counter of 15. EtherCAT command received
0x8000 = complete frame missing
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12176</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).WcState.WcState</Name>
					<Comment><![CDATA[0 = Data valid
1 = Data invalid
]]></Comment>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>12177</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).WcState.WcState</Name>
					<Comment><![CDATA[0 = Data valid
1 = Data invalid
]]></Comment>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>12177</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.Frm0InputToggle</Name>
					<Comment><![CDATA[0x0001 = Toggle Bit: 1. EtherCAT command received new inputs
0x0002 = Toggle Bit: 2. EtherCAT command received new inputs
0x0004 = Toggle Bit: 3. EtherCAT command received new inputs
...
0x4000 = Toggle Bit: 15. EtherCAT command received new inputs
0x8000 = Old frame - not from the actual cycle - received
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12192</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).WcState.InputToggle</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>12193</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).WcState.InputToggle</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>12193</BitOffs>
				</Variable>
				<Variable>
					<Name>SyncUnits.&lt;default&gt;.&lt;unreferenced&gt;.WcState.WcState</Name>
					<Comment><![CDATA[0 = Data valid
1 = Data invalid
]]></Comment>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>12208</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.SlaveCount</Name>
					<Comment><![CDATA[Actual count of EtherCAT slaves received]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12240</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.DevState</Name>
					<Comment><![CDATA[0x0001 = Link error
0x0002 = I/O locked after link error (I/O reset required)
0x0004 = Link error (redundancy adapter)
0x0008 = Missing one frame (redundancy mode)
0x0010 = Out of send resources (I/O reset required)
0x0020 = Watchdog triggered
0x0040 = Ethernet driver (miniport) not found
0x0080 = I/O reset active
0x0100 = At least one device in 'INIT' state
0x0200 = At least one device in 'PRE-OP' state
0x0400 = At least one device in 'SAFE-OP' state
0x0800 = At least one device indicates an error state
0x1000 = DC not in sync
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12272</BitOffs>
				</Variable>
				<Variable>
					<Name>InfoData.ChangeCount</Name>
					<Comment><![CDATA[Info data change counter]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12288</BitOffs>
				</Variable>
				<Variable>
					<Name>InfoData.DevId</Name>
					<Comment><![CDATA[DeviceId of EtherCAT device]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12304</BitOffs>
				</Variable>
				<Variable>
					<Name>InfoData.AmsNetId</Name>
					<Comment><![CDATA[AmsNetId of EtherCAT device]]></Comment>
					<DataType>AMSNETID</DataType>
					<BitSize>48</BitSize>
					<BitOffs>12320</BitOffs>
				</Variable>
				<Variable>
					<Name>InfoData.CfgSlaveCount</Name>
					<Comment><![CDATA[Count of configured EtherCAT slaves]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12368</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).InfoData.State</Name>
					<Comment><![CDATA[0x___1 = Slave in 'INIT' state
0x___2 = Slave in 'PREOP' state
0x___3 = Slave in 'BOOT' state
0x___4 = Slave in 'SAFEOP' state
0x___8 = Slave in 'OP' state
0x001_ = Slave signals error
0x002_ = Invalid vendorId, productCode... read
0x004_ = Initialization error occurred
0x008_ = Slave disabled
0x010_ = Slave not present
0x020_ = Slave signals link error
0x040_ = Slave signals missing link
0x080_ = Slave signals unexpected link
0x100_ = Communication port A
0x200_ = Communication port B
0x400_ = Communication port C
0x800_ = Communication port D
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12384</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).InfoData.AdsAddr</Name>
					<Comment><![CDATA[Complete ADS address to access the slave (mailbox)]]></Comment>
					<DataType>AMSADDR</DataType>
					<BitSize>64</BitSize>
					<BitOffs>12400</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).InfoData.State</Name>
					<Comment><![CDATA[0x___1 = Slave in 'INIT' state
0x___2 = Slave in 'PREOP' state
0x___3 = Slave in 'BOOT' state
0x___4 = Slave in 'SAFEOP' state
0x___8 = Slave in 'OP' state
0x001_ = Slave signals error
0x002_ = Invalid vendorId, productCode... read
0x004_ = Initialization error occurred
0x008_ = Slave disabled
0x010_ = Slave not present
0x020_ = Slave signals link error
0x040_ = Slave signals missing link
0x080_ = Slave signals unexpected link
0x100_ = Communication port A
0x200_ = Communication port B
0x400_ = Communication port C
0x800_ = Communication port D
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12464</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).InfoData.AdsAddr</Name>
					<Comment><![CDATA[Complete ADS address to access the slave (mailbox)]]></Comment>
					<DataType>AMSADDR</DataType>
					<BitSize>64</BitSize>
					<BitOffs>12480</BitOffs>
				</Variable>
				<Variable>
					<Name>SyncUnits.&lt;default&gt;.&lt;unreferenced&gt;.InfoData.ObjectId</Name>
					<DataType>OTCID</DataType>
					<BitSize>32</BitSize>
					<BitOffs>12544</BitOffs>
				</Variable>
				<Variable>
					<Name>SyncUnits.&lt;default&gt;.&lt;unreferenced&gt;.InfoData.State</Name>
					<Comment><![CDATA[0x___1 = At least one slave in 'INIT' state
0x___2 = At least one slave in 'PREOP' state
0x___3 = At least one slave in 'BOOT' state
0x___4 = At least one slave in 'SAFEOP' state
0x___8 = At least one slave in 'OP' state
0x001_ = At least one slave signals error
0x002_ = Invalid vendorId, productCode... read
0x004_ = Initialization error occurred
0x008_ = At least one slave disabled
0x010_ = At least one slave not present
0x020_ = At least one slave signals link error
0x040_ = At least one slave signals missing link
0x080_ = At least one slave signals unexpected link
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12576</BitOffs>
				</Variable>
				<Variable>
					<Name>SyncUnits.&lt;default&gt;.&lt;unreferenced&gt;.InfoData.SlaveCount</Name>
					<Comment><![CDATA[Info data slave counter]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12592</BitOffs>
				</Variable>
			</Inputs>
			<Outputs>
				<ByteSize>1536</ByteSize>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Filter Setting.Filter Time</Name>
					<DataType>USINT</DataType>
					<BitSize>8</BitSize>
					<BitOffs>312</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 1</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>328</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 2</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>329</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 3</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>330</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 4</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>331</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 5</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>332</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 6</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>333</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 7</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>334</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 8</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>335</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 9</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>336</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 10</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>337</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 11</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>338</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 12</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>339</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 13</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>340</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 14</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>341</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 15</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>342</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (AU7 523S-1BL).Digital Outputs.Channel 16</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>343</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 1</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>344</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 2</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>345</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 3</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>346</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 4</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>347</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 5</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>348</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 6</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>349</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 7</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>350</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 8</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>351</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 9</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>352</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 10</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>353</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 11</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>354</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 12</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>355</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 13</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>356</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 14</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>357</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 15</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>358</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 16</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>359</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 17</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>360</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 18</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>361</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 19</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>362</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 20</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>363</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 21</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>364</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 22</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>365</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 23</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>366</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 24</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>367</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 25</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>368</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 26</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>369</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 27</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>370</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 28</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>371</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 29</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>372</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 30</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>373</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 31</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>374</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 2 (SM522S-1BL).Digital Outputs.Channel 32</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>375</BitOffs>
				</Variable>
				<Variable>
					<Name>Outputs.Frm0Ctrl</Name>
					<Comment><![CDATA[0x0001 = prevent 1. EtherCAT command from sending (request NOP)
0x0002 = prevent 2. EtherCAT command from sending (request NOP)
0x0004 = prevent 3. EtherCAT command from sending (request NOP)
...
0x4000 = prevent 15. EtherCAT command from sending (request NOP)
0x8000 = prevent complete frame from sending
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12160</BitOffs>
				</Variable>
				<Variable>
					<Name>Outputs.Frm0WcCtrl</Name>
					<Comment><![CDATA[0x0001 = copy data with wrong working counter of 1. EtherCAT command
0x0002 = copy data with wrong working counter of 2. EtherCAT command
0x0004 = copy data with wrong working counter of 3. EtherCAT command
...
0x4000 = copy data with wrong working counter of 15. EtherCAT command
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12176</BitOffs>
				</Variable>
				<Variable>
					<Name>Outputs.DevCtrl</Name>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12272</BitOffs>
				</Variable>
			</Outputs>
		</ProcessImage>
	</Config>
</EtherCATConfig>
