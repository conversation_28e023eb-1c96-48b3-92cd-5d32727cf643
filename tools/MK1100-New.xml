<?xml version="1.0"?>
<EtherCATConfig xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="EtherCATConfig.xsd" Version="1.3">
	<Config>
		<Master>
			<Info>
				<Name><![CDATA[Device 1 (EtherCAT)]]></Name>
				<Destination>010105010000</Destination>
				<Source>047c167b18a7</Source>
				<EtherType>a488</EtherType>
			</Info>
			<MailboxStates>
				<StartAddr>150994944</StartAddr>
				<Count>1</Count>
			</MailboxStates>
			<InitCmds>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[read slave count]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>7</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>0</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[read slave count]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>7</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>0</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[enable ECAT IRQ]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>512</Ado>
					<Data>0400</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear configured addresses]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>16</Ado>
					<Data>0000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear crc register]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>768</Ado>
					<Data>0000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>PI</Transition>
					<Transition>BI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear fmmu]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>1536</Ado>
					<DataLength>256</DataLength>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear sm]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2048</Ado>
					<DataLength>256</DataLength>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear dc system time]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2320</Ado>
					<DataLength>32</DataLength>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear dc cycle cfg]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2433</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[reset dc speed]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2352</Ado>
					<Data>0010</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[configure dc filter]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2356</Ado>
					<Data>000c</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[en/disable second physical address]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>259</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
			</InitCmds>
		</Master>
		<Slave>
			<Info>
				<Name><![CDATA[Box 1 (MK1100)]]></Name>
				<PhysAddr>1001</PhysAddr>
				<AutoIncAddr>0</AutoIncAddr>
				<Physics>YY</Physics>
				<VendorId>280</VendorId>
				<ProductCode>4352</ProductCode>
				<RevisionNo>853</RevisionNo>
				<SerialNo>0</SerialNo>
			</Info>
			<ProcessData>
				<Send>
					<BitStart>312</BitStart>
					<BitLength>32</BitLength>
				</Send>
				<Recv>
					<BitStart>312</BitStart>
					<BitLength>16</BitLength>
				</Recv>
				<Sm2>
					<Type>Outputs</Type>
					<StartAddress>5632</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>1</Enable>
					<Pdo>5632</Pdo>
					<Pdo>5648</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<StartAddress>6912</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>1</Enable>
					<Pdo>6672</Pdo>
				</Sm3>
				<RxPdo Fixed="true" Sm="2">
					<Index DependOnSlot="true">#x1600</Index>
					<Name>8 CH Digital Output PNP RxPDO-Mapping</Name>
					<Exclude DependOnSlot="true">#x1601</Exclude>
					<Exclude DependOnSlot="true">#x1602</Exclude>
					<Entry>
						<Index DependOnSlot="true">#x3000</Index>
						<SubIndex>1</SubIndex>
						<BitLen>1</BitLen>
						<Name>DO1</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x3000</Index>
						<SubIndex>2</SubIndex>
						<BitLen>1</BitLen>
						<Name>DO2</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x3000</Index>
						<SubIndex>3</SubIndex>
						<BitLen>1</BitLen>
						<Name>DO3</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x3000</Index>
						<SubIndex>4</SubIndex>
						<BitLen>1</BitLen>
						<Name>DO4</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x3000</Index>
						<SubIndex>5</SubIndex>
						<BitLen>1</BitLen>
						<Name>DO5</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x3000</Index>
						<SubIndex>6</SubIndex>
						<BitLen>1</BitLen>
						<Name>DO6</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x3000</Index>
						<SubIndex>7</SubIndex>
						<BitLen>1</BitLen>
						<Name>DO7</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x3000</Index>
						<SubIndex>8</SubIndex>
						<BitLen>1</BitLen>
						<Name>DO8</Name>
						<DataType>BIT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="true">
					<Index DependOnSlot="true">#x1601</Index>
					<Name>8 CH Digital Output PNP RxPDO-Mapping</Name>
					<Exclude DependOnSlot="true">#x1600</Exclude>
					<Exclude DependOnSlot="true">#x1602</Exclude>
					<Entry>
						<Index DependOnSlot="true">#x3001</Index>
						<SubIndex>1</SubIndex>
						<BitLen>8</BitLen>
						<Name>DO8N</Name>
						<DataType>BITARR8</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="true">
					<Index DependOnSlot="true">#x1602</Index>
					<Name>8 CH Digital Output PNP RxPDO-Mapping</Name>
					<Exclude DependOnSlot="true">#x1600</Exclude>
					<Exclude DependOnSlot="true">#x1601</Exclude>
					<Entry>
						<Index DependOnSlot="true">#x3002</Index>
						<SubIndex>1</SubIndex>
						<BitLen>8</BitLen>
						<Name>DO8N</Name>
						<DataType>USINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="true" Sm="3">
					<Index DependOnSlot="true">#x1a10</Index>
					<Name>Alarm Module TxPDO-Mapping</Name>
					<Entry>
						<Index DependOnSlot="true">#x2001</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Alarm Stateword</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<RxPdo Fixed="true" Sm="2">
					<Index DependOnSlot="true">#x1610</Index>
					<Name>Alarm Module RxPDO-Mapping</Name>
					<Entry>
						<Index DependOnSlot="true">#x3001</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Alarm Ctrlword</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
			</ProcessData>
			<Mailbox DataLinkLayer="true">
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>4864</Start>
					<Length>128</Length>
					<StatusBitAddr>0</StatusBitAddr>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE>
					<InitCmds>
						<InitCmd Fixed="true" CompleteAccess="true">
							<Transition>PS</Transition>
							<Comment><![CDATA[download slot cfg]]></Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>61488</Index>
							<SubIndex>0</SubIndex>
							<Data>0200040000000f000000</Data>
						</InitCmd>
						<InitCmd>
							<Transition>PS</Transition>
							<Comment><![CDATA[Offline DOState]]></Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>16512</Index>
							<SubIndex>1</SubIndex>
							<Data>10</Data>
						</InitCmd>
						<InitCmd ModuleHandle="0">
							<Transition>PS</Transition>
							<Comment><![CDATA[MK2018 DO8 PNP ID]]></Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>32768</Index>
							<SubIndex>0</SubIndex>
							<Data>04</Data>
						</InitCmd>
						<InitCmd ModuleHandle="1">
							<Transition>PS</Transition>
							<Comment><![CDATA[Alarm Module ID]]></Comment>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>32769</Index>
							<SubIndex>0</SubIndex>
							<Data>0f</Data>
						</InitCmd>
					</InitCmds>
					<Profile>
						<ChannelInfo>
							<ProfileNo>5001</ProfileNo>
							<AddInfo>0</AddInfo>
						</ChannelInfo>
					</Profile>
				</CoE>
			</Mailbox>
			<InitCmds>
				<InitCmd>
					<Transition>PI</Transition>
					<Transition>BI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[set device state to INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>288</Ado>
					<Data>1100</Data>
					<Retries>3</Retries>
					<Timeout>10000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>PI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[check device state for INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>0100</Data>
						<DataMask>0f00</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>BI</Transition>
					<Comment><![CDATA[check device state for INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>0100</Data>
						<DataMask>0f00</DataMask>
						<Timeout>20000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set device state to INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>288</Ado>
					<Data>1100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>10000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[check device state for INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>0100</Data>
						<DataMask>0f00</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[assign EEPROM to ECAT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1280</Ado>
					<Data>00</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check vendor id]]></Comment>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1282</Ado>
					<Data>000108000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check vendor id]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>1288</Ado>
					<Data>00000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>18010000</Data>
						<Timeout>100</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check product code]]></Comment>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1282</Ado>
					<Data>00010a000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check product code]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>1288</Ado>
					<Data>00000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>00110000</Data>
						<Timeout>100</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set physical address]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>16</Ado>
					<Data>e903</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Transition>PI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear sm 0/1 (mailbox out/in)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2048</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>BI</Transition>
					<Comment><![CDATA[clear sm 0/1 (mailbox out/in)]]></Comment>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>2048</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set sm 0 (mailbox out)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2048</Ado>
					<Data>0010800026000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set sm 1 (mailbox in)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2056</Ado>
					<Data>0013800022000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>OP</Transition>
					<Comment><![CDATA[set device state to PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>1200</Data>
					<Retries>300</Retries>
					<Timeout>10000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>SP</Transition>
					<Transition>SI</Transition>
					<Transition>OP</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear sms]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2064</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set sm 2 (outputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2064</Ado>
					<Data>0016040064000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set sm 3 (inputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2072</Ado>
					<Data>001b020020000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set fmmu 0 (outputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1536</Ado>
					<Data>00000001040000070016000201000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set fmmu 1 (inputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1552</Ado>
					<Data>0000000102000007001b000101000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set fmmu 2 (mailbox state)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1568</Ado>
					<Data>00000009010000000d08000101000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>OS</Transition>
					<Comment><![CDATA[set device state to SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>0400</Data>
					<Retries>3</Retries>
					<Timeout>10000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>SI</Transition>
					<Transition>OP</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear fmmu 0]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1536</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>SI</Transition>
					<Transition>OP</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear fmmu 1]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1552</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PI</Transition>
					<Transition>BI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear fmmu 2]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1568</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>OP</Transition>
					<Comment><![CDATA[check device state for PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>020000000000</Data>
						<DataMask>0f0000000000</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[assign EEPROM to PDI]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1280</Ado>
					<Data>01</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>II</Transition>
					<Comment><![CDATA[assign EEPROM back to ECAT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1280</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[set device state to PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>1200</Data>
					<Cnt>1</Cnt>
					<Retries>300</Retries>
					<Timeout>10000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check device state for PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>020000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>BI</Transition>
					<Comment><![CDATA[assign EEPROM back to ECAT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1280</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IB</Transition>
					<Comment><![CDATA[set device state to BOOT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>1300</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>10000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IB</Transition>
					<Comment><![CDATA[check device state for BOOT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>030000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set device state to SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>0400</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>10000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[check device state for SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>040000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>OS</Transition>
					<Comment><![CDATA[check device state for SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>040000000000</Data>
						<DataMask>0f0000000000</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>SO</Transition>
					<Comment><![CDATA[set device state to OP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>0800</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>10000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>SO</Transition>
					<Comment><![CDATA[check device state for OP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>080000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
			</InitCmds>
		</Slave>
		<Cyclic>
			<Frame>
				<Cmd>
					<State>PREOP</State>
					<State>SAFEOP</State>
					<State>OP</State>
					<Comment><![CDATA[cyclic cmd]]></Comment>
					<Cmd>10</Cmd>
					<Addr>150994944</Addr>
					<DataLength>1</DataLength>
					<InputOffs>16</InputOffs>
					<OutputOffs>16</OutputOffs>
				</Cmd>
				<Cmd>
					<State>SAFEOP</State>
					<State>OP</State>
					<Comment><![CDATA[cyclic cmd]]></Comment>
					<Cmd>12</Cmd>
					<Addr>16777216</Addr>
					<DataLength>4</DataLength>
					<Cnt>3</Cnt>
					<InputOffs>29</InputOffs>
					<OutputOffs>29</OutputOffs>
				</Cmd>
				<Cmd>
					<State>PREOP</State>
					<State>SAFEOP</State>
					<State>OP</State>
					<Comment><![CDATA[cyclic cmd]]></Comment>
					<Cmd>7</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<DataLength>2</DataLength>
					<Cnt>1</Cnt>
					<InputOffs>45</InputOffs>
					<OutputOffs>45</OutputOffs>
				</Cmd>
			</Frame>
		</Cyclic>
		<ProcessImage>
			<Inputs>
				<ByteSize>1536</ByteSize>
				<Variable>
					<Name>Box 1 (MK1100).Module 2 (Alarm).Alarm Module TxPDO-Mapping.Alarm Stateword</Name>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>312</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.Frm0State</Name>
					<Comment><![CDATA[0x0001 = 1. EtherCAT command not sent (NOP requested)
0x0002 = 2. EtherCAT command not sent (NOP requested)
0x0004 = 3. EtherCAT command not sent (NOP requested)
...
0x4000 = 15. EtherCAT command not sent (NOP requested)
0x8000 = complete frame not sent
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12160</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.Frm0WcState</Name>
					<Comment><![CDATA[0x0001 = wrong working counter of 1. EtherCAT command received
0x0002 = wrong working counter of 2. EtherCAT command received
0x0004 = wrong working counter of 3. EtherCAT command received
...
0x4000 = wrong working counter of 15. EtherCAT command received
0x8000 = complete frame missing
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12176</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).WcState.WcState</Name>
					<Comment><![CDATA[0 = Data valid
1 = Data invalid
]]></Comment>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>12177</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.Frm0InputToggle</Name>
					<Comment><![CDATA[0x0001 = Toggle Bit: 1. EtherCAT command received new inputs
0x0002 = Toggle Bit: 2. EtherCAT command received new inputs
0x0004 = Toggle Bit: 3. EtherCAT command received new inputs
...
0x4000 = Toggle Bit: 15. EtherCAT command received new inputs
0x8000 = Old frame - not from the actual cycle - received
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12192</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).WcState.InputToggle</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>12193</BitOffs>
				</Variable>
				<Variable>
					<Name>SyncUnits.&lt;default&gt;.&lt;unreferenced&gt;.WcState.WcState</Name>
					<Comment><![CDATA[0 = Data valid
1 = Data invalid
]]></Comment>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>12208</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.SlaveCount</Name>
					<Comment><![CDATA[Actual count of EtherCAT slaves received]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12240</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.DevState</Name>
					<Comment><![CDATA[0x0001 = Link error
0x0002 = I/O locked after link error (I/O reset required)
0x0004 = Link error (redundancy adapter)
0x0008 = Missing one frame (redundancy mode)
0x0010 = Out of send resources (I/O reset required)
0x0020 = Watchdog triggered
0x0040 = Ethernet driver (miniport) not found
0x0080 = I/O reset active
0x0100 = At least one device in 'INIT' state
0x0200 = At least one device in 'PRE-OP' state
0x0400 = At least one device in 'SAFE-OP' state
0x0800 = At least one device indicates an error state
0x1000 = DC not in sync
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12272</BitOffs>
				</Variable>
				<Variable>
					<Name>InfoData.ChangeCount</Name>
					<Comment><![CDATA[Info data change counter]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12288</BitOffs>
				</Variable>
				<Variable>
					<Name>InfoData.DevId</Name>
					<Comment><![CDATA[DeviceId of EtherCAT device]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12304</BitOffs>
				</Variable>
				<Variable>
					<Name>InfoData.AmsNetId</Name>
					<Comment><![CDATA[AmsNetId of EtherCAT device]]></Comment>
					<DataType>AMSNETID</DataType>
					<BitSize>48</BitSize>
					<BitOffs>12320</BitOffs>
				</Variable>
				<Variable>
					<Name>InfoData.CfgSlaveCount</Name>
					<Comment><![CDATA[Count of configured EtherCAT slaves]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12368</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).InfoData.State</Name>
					<Comment><![CDATA[0x___1 = Slave in 'INIT' state
0x___2 = Slave in 'PREOP' state
0x___3 = Slave in 'BOOT' state
0x___4 = Slave in 'SAFEOP' state
0x___8 = Slave in 'OP' state
0x001_ = Slave signals error
0x002_ = Invalid vendorId, productCode... read
0x004_ = Initialization error occurred
0x008_ = Slave disabled
0x010_ = Slave not present
0x020_ = Slave signals link error
0x040_ = Slave signals missing link
0x080_ = Slave signals unexpected link
0x100_ = Communication port A
0x200_ = Communication port B
0x400_ = Communication port C
0x800_ = Communication port D
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12384</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).InfoData.AdsAddr</Name>
					<Comment><![CDATA[Complete ADS address to access the slave (mailbox)]]></Comment>
					<DataType>AMSADDR</DataType>
					<BitSize>64</BitSize>
					<BitOffs>12400</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).InfoData.DcOutputShift</Name>
					<DataType>DINT</DataType>
					<BitSize>32</BitSize>
					<BitOffs>12464</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).InfoData.DcInputShift</Name>
					<DataType>DINT</DataType>
					<BitSize>32</BitSize>
					<BitOffs>12496</BitOffs>
				</Variable>
				<Variable>
					<Name>SyncUnits.&lt;default&gt;.&lt;unreferenced&gt;.InfoData.ObjectId</Name>
					<DataType>OTCID</DataType>
					<BitSize>32</BitSize>
					<BitOffs>12528</BitOffs>
				</Variable>
				<Variable>
					<Name>SyncUnits.&lt;default&gt;.&lt;unreferenced&gt;.InfoData.State</Name>
					<Comment><![CDATA[0x___1 = At least one slave in 'INIT' state
0x___2 = At least one slave in 'PREOP' state
0x___3 = At least one slave in 'BOOT' state
0x___4 = At least one slave in 'SAFEOP' state
0x___8 = At least one slave in 'OP' state
0x001_ = At least one slave signals error
0x002_ = Invalid vendorId, productCode... read
0x004_ = Initialization error occurred
0x008_ = At least one slave disabled
0x010_ = At least one slave not present
0x020_ = At least one slave signals link error
0x040_ = At least one slave signals missing link
0x080_ = At least one slave signals unexpected link
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12560</BitOffs>
				</Variable>
				<Variable>
					<Name>SyncUnits.&lt;default&gt;.&lt;unreferenced&gt;.InfoData.SlaveCount</Name>
					<Comment><![CDATA[Info data slave counter]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12576</BitOffs>
				</Variable>
			</Inputs>
			<Outputs>
				<ByteSize>1536</ByteSize>
				<Variable>
					<Name>Box 1 (MK1100).Module 1 (MK2018).8 CH Digital Output PNP RxPDO-Mapping.DO1</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>312</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).Module 1 (MK2018).8 CH Digital Output PNP RxPDO-Mapping.DO2</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>313</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).Module 1 (MK2018).8 CH Digital Output PNP RxPDO-Mapping.DO3</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>314</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).Module 1 (MK2018).8 CH Digital Output PNP RxPDO-Mapping.DO4</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>315</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).Module 1 (MK2018).8 CH Digital Output PNP RxPDO-Mapping.DO5</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>316</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).Module 1 (MK2018).8 CH Digital Output PNP RxPDO-Mapping.DO6</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>317</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).Module 1 (MK2018).8 CH Digital Output PNP RxPDO-Mapping.DO7</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>318</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).Module 1 (MK2018).8 CH Digital Output PNP RxPDO-Mapping.DO8</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>319</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (MK1100).Module 2 (Alarm).Alarm Module RxPDO-Mapping.Alarm Ctrlword</Name>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>328</BitOffs>
				</Variable>
				<Variable>
					<Name>Outputs.Frm0Ctrl</Name>
					<Comment><![CDATA[0x0001 = prevent 1. EtherCAT command from sending (request NOP)
0x0002 = prevent 2. EtherCAT command from sending (request NOP)
0x0004 = prevent 3. EtherCAT command from sending (request NOP)
...
0x4000 = prevent 15. EtherCAT command from sending (request NOP)
0x8000 = prevent complete frame from sending
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12160</BitOffs>
				</Variable>
				<Variable>
					<Name>Outputs.Frm0WcCtrl</Name>
					<Comment><![CDATA[0x0001 = copy data with wrong working counter of 1. EtherCAT command
0x0002 = copy data with wrong working counter of 2. EtherCAT command
0x0004 = copy data with wrong working counter of 3. EtherCAT command
...
0x4000 = copy data with wrong working counter of 15. EtherCAT command
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12176</BitOffs>
				</Variable>
				<Variable>
					<Name>Outputs.DevCtrl</Name>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12272</BitOffs>
				</Variable>
			</Outputs>
		</ProcessImage>
	</Config>
</EtherCATConfig>
