#!/bin/bash

# 检查参数
if [ $# -ne 1 ]; then
    echo "用法: $0 <CPU核心编号>"
    echo "示例: $0 3  # 将所有网卡中断绑定到CPU3"
    exit 1
fi

# 验证输入的CPU编号是否有效
if ! [[ "$1" =~ ^[0-9]+$ ]]; then
    echo "错误: CPU核心编号必须是数字"
    exit 1
fi

# 获取所有网络设备（排除特定设备）
get_network_devices() {
    ip -br link | awk '$1 !~ /^(lo|virbr|docker|veth|br-|tun|tap|bond|team)/ && $1 !~ /^wl/ {print $1}'
}

# 将十六进制掩码转换为CPU列表
hex_to_cpulist() {
    local hex=$1
    # 确保输入是大写的十六进制
    hex=$(echo "$hex" | tr '[:lower:]' '[:upper:]')
    # 转换十六进制为二进制，并确保至少8位
    local bin=$(echo "ibase=16;obase=2;$hex" | bc | awk '{printf "%08d\n", $0}')
    local cpus=""
    
    # 从右到左遍历二进制位
    for (( i=0; i<${#bin}; i++ )); do
        if [ "${bin:${#bin}-i-1:1}" = "1" ]; then
            if [ -z "$cpus" ]; then
                cpus="$i"
            else
                cpus="$cpus,$i"
            fi
        fi
    done
    
    if [ -z "$cpus" ]; then
        echo "没有绑定CPU"
    else
        echo "CPU$cpus"
    fi
}

# 列出所有网卡相关的中断
list_network_irqs() {
    echo "发现以下网卡相关中断："
    echo "IRQ    当前绑定到    计数    类型"
    echo "----------------------------------------"
    
    # 获取所有网络设备
    ETHERNET_DEVS=($(get_network_devices))
    
    # 构建grep模式
    GREP_PATTERN=""
    for dev in "${ETHERNET_DEVS[@]}"; do
        if [ -z "$GREP_PATTERN" ]; then
            GREP_PATTERN="${dev}.*tx\|${dev}.*rx"
        else
            GREP_PATTERN="${GREP_PATTERN}\|${dev}.*tx\|${dev}.*rx"
        fi
    done
    
    # 使用两次grep，分别匹配网卡中断和gmac
    (grep -E "$GREP_PATTERN" /proc/interrupts; grep "gmac[0-9]" /proc/interrupts) | while read -r line; do
        irq=$(echo "$line" | awk '{print $1}' | tr -d ':')
        count=$(echo "$line" | awk '{print $2}')
        type=$(echo "$line" | grep -oE "[a-zA-Z0-9]+-[t|r]x-[0-9]+" || echo "$line" | grep -oE "gmac[0-9]+")
        if [ ! -z "$irq" ]; then
            affinity=$(cat /proc/irq/$irq/smp_affinity)
            cpu_list=$(hex_to_cpulist $affinity)
            echo "$irq    $cpu_list    $count    $type"
        fi
    done
}

# 列出当前中断分配情况
list_network_irqs

echo "开始将中断绑定到 CPU$1..."

# 计算smp_affinity值 (2的CPU编号次方)
AFFINITY_VALUE=$(echo "obase=16;$((1 << $1))" | bc)

# 获取所有网络设备
ETHERNET_DEVS=($(get_network_devices))

# 构建grep模式
GREP_PATTERN=""
for dev in "${ETHERNET_DEVS[@]}"; do
    if [ -z "$GREP_PATTERN" ]; then
        GREP_PATTERN="${dev}.*tx\|${dev}.*rx"
    else
        GREP_PATTERN="${GREP_PATTERN}\|${dev}.*tx\|${dev}.*rx"
    fi
done

# 使用两次grep，分别匹配网卡中断和gmac
(grep -E "$GREP_PATTERN" /proc/interrupts; grep "gmac[0-9]" /proc/interrupts) | while read -r line; do
    irq=$(echo "$line" | awk '{print $1}' | tr -d ':')
    type=$(echo "$line" | grep -oE "[a-zA-Z0-9]+-[t|r]x-[0-9]+" || echo "$line" | grep -oE "gmac[0-9]+")
    if [ ! -z "$irq" ]; then
        echo "正在将 $type (IRQ: $irq) 绑定到 CPU$1..."
        echo $AFFINITY_VALUE > /proc/irq/$irq/smp_affinity
    fi
done

echo "绑定完成" 