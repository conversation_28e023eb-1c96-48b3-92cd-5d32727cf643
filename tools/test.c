
#include <errno.h>
#include <signal.h>
#include <stdio.h>
#include <string.h>
#include <sys/resource.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <sched.h>
#include <sys/stat.h>
#include <sys/shm.h>
#include <time.h>
#include <pthread.h>  // 添加pthread头文件

#include "ecrt.h"

// Forward declarations
void cleanup_shm(void);
void signal_handler(int sig);
void check_slave_config_states(void);

// 定义PDO配置线程的参数结构
typedef struct {
    ec_slave_config_t *slave_config;
    ec_sync_info_t *sync_info;
    int *result;
} config_thread_param_t;

// PDO配置线程函数
void* config_slave_thread(void *arg) {
    config_thread_param_t *param = (config_thread_param_t *)arg;
    *(param->result) = ecrt_slave_config_pdos(param->slave_config, EC_END, param->sync_info);
    return NULL;
}

// 定义DC配置线程的参数结构
typedef struct {
    ec_slave_config_t *slave_config;
    uint16_t assign_activate;
    uint32_t sync0_cycle;
    uint32_t sync0_shift;
    uint32_t sync1_cycle;
    uint32_t sync1_shift;
    int *result;
} dc_config_thread_param_t;

// DC配置线程函数
void* dc_config_thread(void *arg) {
    dc_config_thread_param_t *param = (dc_config_thread_param_t *)arg;
    int ret = ecrt_slave_config_dc(
        param->slave_config,
        param->assign_activate,
        param->sync0_cycle,
        param->sync0_shift,
        param->sync1_cycle,
        param->sync1_shift
    );
    *(param->result) = ret;
    return NULL;
}

// Global control flags
static int run = 1;  // 控制主循环的标志
static int last_cycle = 0;  // 标记最后一个循环
static int all_slaves_op = 0;  // Add this flag to track ALL OP state


// Signal handler implementation
void signal_handler(int sig) {
    printf("\nSignal %d received, will exit after next cycle...\n", sig);
    last_cycle = 1;  // 设置最后一个循环标志
}

/* Time definitions */
#define NSEC_PER_SEC (1000000000L)
#define TIMESPEC2NS(T) ((uint64_t) (T).tv_sec * NSEC_PER_SEC + (T).tv_nsec)
#define CLOCK_TO_USE CLOCK_MONOTONIC

/* Shared memory configuration */
#define ETHERCAT_SHM_FILE "x7cbr8th1mbusdn_FlashLed_shm"
#define ETHERCAT_SHM_SIZE (sizeof(ethercat_shm_t))

/* Shared memory structure */
typedef struct {
    int shm_slave0_online_status; /* 从站0在线状态 */
    int shm_slave0_operational_status; /* 从站0运行状态 */
    int shm_slave0_al_state; /* 从站0AL状态 */

    int shm_slave0_rx_shm_slave0_rx_0x00003000_do1;
    int shm_slave0_rx_shm_slave0_rx_0x00003000_do2;
    int shm_slave0_rx_shm_slave0_rx_0x00003000_do3;
    int shm_slave0_rx_shm_slave0_rx_0x00003000_do4;
    int shm_slave0_rx_shm_slave0_rx_0x00003000_do5;
    int shm_slave0_rx_shm_slave0_rx_0x00003000_do6;
    int shm_slave0_rx_shm_slave0_rx_0x00003000_do7;
    int shm_slave0_rx_shm_slave0_rx_0x00003000_do8;
    int shm_slave0_rx_shm_slave0_rx_0x00000000_reserved;
    int shm_slave0_rx_shm_slave0_rx_0x00003001_alarm_ctrlword;
    int shm_slave0_tx_shm_slave0_tx_0x00002001_alarm_stateword;
} ethercat_shm_t;

static ethercat_shm_t *ethercat_shm = NULL;

/* Application Parameters */
#define TASK_FREQUENCY 4000 /*Hz*/
#define PERIOD_NS (NSEC_PER_SEC / TASK_FREQUENCY)

/* EtherCAT configurations */
#define MASTER_INDEX 0

static ec_master_t *master = NULL;
static ec_master_state_t master_state = {};
static ec_domain_t *domain1 = NULL;
static ec_domain_state_t domain1_state = {};
static ec_slave_config_t *sc_slave0 = NULL;
static ec_slave_config_state_t sc_slave0_state = {};
static uint8_t *domain1_pd = NULL;

#define slave0_POS 0,0
#define slave0_VID_PID 0x00000118,0x00001100

/* PDO mapping */

/* SDO data definitions */
// SDO data for slave 0
static uint8_t sdo_0_4080_1_data = 0x10;  // Module Config 1 (0x4080:1)
static uint8_t sdo_0_8000_0_data = 0x04;  // Module Config 0 (0x8000:0)
static uint8_t sdo_0_8001_0_data = 0x0f;  // Module Config 0 (0x8001:0)

static struct {
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do1_off;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do1_bit;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do2_off;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do2_bit;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do3_off;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do3_bit;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do4_off;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do4_bit;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do5_off;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do5_bit;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do6_off;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do6_bit;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do7_off;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do7_bit;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do8_off;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003000_do8_bit;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00000000_reserved;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x00003001_alarm_ctrlword;
    unsigned int pdo_slave0_tx_shm_slave0_tx_0x00002001_alarm_stateword;
} offset;

const static ec_pdo_entry_reg_t domain1_regs[] = {
    {slave0_POS, slave0_VID_PID, 0x00003000, 1, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do1_off, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do1_bit},
    {slave0_POS, slave0_VID_PID, 0x00003000, 2, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do2_off, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do2_bit},
    {slave0_POS, slave0_VID_PID, 0x00003000, 3, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do3_off, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do3_bit},
    {slave0_POS, slave0_VID_PID, 0x00003000, 4, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do4_off, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do4_bit},
    {slave0_POS, slave0_VID_PID, 0x00003000, 5, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do5_off, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do5_bit},
    {slave0_POS, slave0_VID_PID, 0x00003000, 6, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do6_off, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do6_bit},
    {slave0_POS, slave0_VID_PID, 0x00003000, 7, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do7_off, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do7_bit},
    {slave0_POS, slave0_VID_PID, 0x00003000, 8, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do8_off, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do8_bit},
    {slave0_POS, slave0_VID_PID, 0x00000000, 0, &offset.pdo_slave0_rx_shm_slave0_rx_0x00000000_reserved},
    {slave0_POS, slave0_VID_PID, 0x00003001, 0, &offset.pdo_slave0_rx_shm_slave0_rx_0x00003001_alarm_ctrlword},
    {slave0_POS, slave0_VID_PID, 0x00002001, 0, &offset.pdo_slave0_tx_shm_slave0_tx_0x00002001_alarm_stateword},
    {}
};


static ec_pdo_entry_info_t slave0_pdo_entries[] = {
    {0x00003000, 1, 1},  /* DO1 */
    {0x00003000, 2, 1},  /* DO2 */
    {0x00003000, 3, 1},  /* DO3 */
    {0x00003000, 4, 1},  /* DO4 */
    {0x00003000, 5, 1},  /* DO5 */
    {0x00003000, 6, 1},  /* DO6 */
    {0x00003000, 7, 1},  /* DO7 */
    {0x00003000, 8, 1},  /* DO8 */
    {0x00000000, 0, 8},  /* Reserved */
    {0x00003001, 0, 16},  /* Alarm Ctrlword */
    {0x00002001, 0, 16},  /* Alarm Stateword */
};

static ec_pdo_info_t slave0_pdos[] = {
    {0x00001600, 9, slave0_pdo_entries + 0},  /* RxPDO */
    {0x00001610, 1, slave0_pdo_entries + 9},  /* RxPDO */
    {0x00001a10, 1, slave0_pdo_entries + 10},  /* TxPDO */
};

static ec_sync_info_t slave0_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 2, slave0_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave0_pdos + 2, EC_WD_DISABLE},
    {0xff}
};

void create_shm() {
    // Create and open shared memory
    int fd = shm_open(ETHERCAT_SHM_FILE, O_CREAT | O_RDWR, 0666);
    if (fd < 0) {
        perror("shm_open failed");
        exit(EXIT_FAILURE);
    }

    // Set the size of shared memory
    if (ftruncate(fd, ETHERCAT_SHM_SIZE) < 0) {
        perror("ftruncate failed");
        exit(EXIT_FAILURE);
    }

    // Map shared memory
    ethercat_shm = (ethercat_shm_t *)mmap(NULL, ETHERCAT_SHM_SIZE, 
                                         PROT_READ | PROT_WRITE, 
                                         MAP_SHARED, fd, 0);
    if (ethercat_shm == MAP_FAILED) {
        perror("mmap failed");
        exit(EXIT_FAILURE);
    }

    // Initialize shared memory to zero
    memset(ethercat_shm, 0, ETHERCAT_SHM_SIZE);
    
    // Close file descriptor (mapping remains valid)
    close(fd);
}

void cleanup_shm() {
    if (ethercat_shm != NULL) {
        munmap(ethercat_shm, ETHERCAT_SHM_SIZE);
        shm_unlink(ETHERCAT_SHM_FILE);
    }
}

void check_slave_config_states(void)
{
    static ec_slave_config_state_t slaves_state[1];  // 从站数从配置获取
    static int first_check = 1;  // 第一次检查的标志
    int all_op = 1;  // 所有从站是否都在OP状态的标志
    
    // 检查所有从站状态
    for (int i = 0; i < 1; i++) {
        ec_slave_config_state_t s;
        ec_slave_config_t *sc = NULL;
        
        // 根据索引获取对应的从站配置
        switch(i) {
            case 0: sc = sc_slave0; break;
        }
        
        ecrt_slave_config_state(sc, &s);
        
        // 检查是否所有从站都在OP状态
        if (!s.operational) {
            all_op = 0;
        }
        
        // 只在状态发生变化时打印
        if (!first_check) {
            if (s.al_state != slaves_state[i].al_state)
                printf("slave %d: State 0x%02X.\n", i, s.al_state);
            if (s.online != slaves_state[i].online)
                printf("slave %d: %s.\n", i, s.online ? "online" : "offline");
            if (s.operational != slaves_state[i].operational)
                printf("slave %d: %soperational.\n", i, s.operational ? "" : "Not ");
        }
        
        slaves_state[i] = s;
    }
    
    // 修改 ALL OP 检测和文件操作部分
    static int op_printed = 0;
    char op_file[64];
    snprintf(op_file, sizeof(op_file), "/tmp/MASTER%d_ALL_OP", MASTER_INDEX);
    
    if (all_op && !op_printed) {
        printf("ALL OP\n");
        op_printed = 1;
        all_slaves_op = 1;  // Set the flag to indicate all slaves are in OP state
        // 创建 ALL OP 标记文件
        FILE *fp = fopen(op_file, "w");
        if (fp) {
            fclose(fp);
        } else {
            fprintf(stderr, "Failed to create ALL OP file: %s\n", op_file);
        }
    } else if (!all_op && op_printed) {
        op_printed = 0;
        // 删除 ALL OP 标记文件
        // if (remove(op_file) != 0) {
        //     fprintf(stderr, "Failed to remove ALL OP file: %s\n", op_file);
        // }
    }
    
    first_check = 0;
}

void cyclic_task(void)
{
    static int cycle_counter = 0;
    static struct timespec wakeupTime;
    static int initialized = 0;

    if (!initialized) {
        clock_gettime(CLOCK_TO_USE, &wakeupTime);
        initialized = 1;
    }

    wakeupTime.tv_nsec += PERIOD_NS;
    while (wakeupTime.tv_nsec >= NSEC_PER_SEC) {
        wakeupTime.tv_nsec -= NSEC_PER_SEC;
        wakeupTime.tv_sec++;
    }

    clock_nanosleep(CLOCK_TO_USE, TIMER_ABSTIME, &wakeupTime, NULL);

    ecrt_master_application_time(master, TIMESPEC2NS(wakeupTime));

    ecrt_master_receive(master);
    
    // 添加错误检查
    if (ecrt_master_state(master, &master_state)) {
        run = 0;  // 如果出现错误，触发程序退出
        fprintf(stderr, "Failed to get master state.\n");
        return;
    }
    
    ecrt_domain_process(domain1);

    if (cycle_counter) {
        cycle_counter--;
    } else {
          cycle_counter = TASK_FREQUENCY;
        // Only check states if not all slaves are in OP state
        if (!all_slaves_op) {
            check_slave_config_states();
        }
    }

    // 更新从站状态

    // 获取从站0状态
    ecrt_slave_config_state(sc_slave0, &sc_slave0_state);
    
    // 更新从站0状态到共享内存
    ethercat_shm->shm_slave0_online_status = sc_slave0_state.online;
    ethercat_shm->shm_slave0_operational_status = sc_slave0_state.operational;
    ethercat_shm->shm_slave0_al_state = sc_slave0_state.al_state;

    // Update shared memory with status
    ethercat_shm->shm_slave0_tx_shm_slave0_tx_0x00002001_alarm_stateword = EC_READ_U16(domain1_pd + offset.pdo_slave0_tx_shm_slave0_tx_0x00002001_alarm_stateword);

    // Write to EtherCAT
    EC_WRITE_BIT(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do1_off, offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do1_bit, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x00003000_do1);
    EC_WRITE_BIT(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do2_off, offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do2_bit, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x00003000_do2);
    EC_WRITE_BIT(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do3_off, offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do3_bit, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x00003000_do3);
    EC_WRITE_BIT(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do4_off, offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do4_bit, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x00003000_do4);
    EC_WRITE_BIT(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do5_off, offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do5_bit, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x00003000_do5);
    EC_WRITE_BIT(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do6_off, offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do6_bit, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x00003000_do6);
    EC_WRITE_BIT(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do7_off, offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do7_bit, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x00003000_do7);
    EC_WRITE_BIT(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do8_off, offset.pdo_slave0_rx_shm_slave0_rx_0x00003000_do8_bit, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x00003000_do8);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x00000000_reserved, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x00000000_reserved);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x00003001_alarm_ctrlword, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x00003001_alarm_ctrlword);

    // Send process data
    ecrt_domain_queue(domain1);
    ecrt_master_sync_slave_clocks(master);
    ecrt_master_sync_reference_clock(master);
    ecrt_master_send(master);

    if (last_cycle) {
        printf("Executing final cycle...\n");
        
        // 将从站切换到预运行状态
        printf("Switching slaves to PREOP state...\n");
        ecrt_master_deactivate(master);
        
        // 释放EtherCAT主站
        if (master) {
            printf("Releasing master...\n");
            ecrt_release_master(master);
        }
        
        // 清理共享内存
        cleanup_shm();
        
        printf("Shutdown complete\n");
        run = 0;  // 这将导致主循环退出
    }
}
int main(int argc, char **argv) {
    // 启动时先删除可能存在的 ALL OP 标记文件
    char op_file[64];
    snprintf(op_file, sizeof(op_file), "/tmp/MASTER%d_ALL_OP", MASTER_INDEX);
    remove(op_file);

    // Set up signal handler for cleanup
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // Lock memory to prevent paging
    if (mlockall(MCL_CURRENT | MCL_FUTURE) == -1) {
        perror("mlockall failed");
        return -1;
    }

    // Create shared memory
    create_shm();
    
    // Initialize EtherCAT master
    printf("Requesting master...\n");
    master = ecrt_request_master(MASTER_INDEX);
    if (!master) exit(EXIT_FAILURE);
    
    domain1 = ecrt_master_create_domain(master);
    if (!domain1) exit(EXIT_FAILURE);

    // Configure slaves
    printf("Configuring all slaves...\n");
    
    // 创建从站配置数组
    ec_slave_config_t *slave_configs[1];
    
    // 批量获取所有从站配置
    
    printf("Configuring slave 0...\n");
    slave_configs[0] = ecrt_master_slave_config(master, slave0_POS, slave0_VID_PID);
    if (!slave_configs[0]) {
        fprintf(stderr, "Failed to get slave0 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 0
    printf("Configuring SDOs for slave 0...\n");
    printf("  Configuring SDO 0x4080:1 (Module Config 1)...\n");
    if (ecrt_slave_config_sdo8(slave_configs[0], 0x4080, 1, sdo_0_4080_1_data)) {
        fprintf(stderr, "Failed to configure SDO 0x4080:1 for slave 0\n");
        exit(EXIT_FAILURE);
    }
    printf("  Configuring SDO 0x8000:0 (Module Config 0)...\n");
    if (ecrt_slave_config_sdo8(slave_configs[0], 0x8000, 0, sdo_0_8000_0_data)) {
        fprintf(stderr, "Failed to configure SDO 0x8000:0 for slave 0\n");
        exit(EXIT_FAILURE);
    }
    printf("  Configuring SDO 0x8001:0 (Module Config 0)...\n");
    if (ecrt_slave_config_sdo8(slave_configs[0], 0x8001, 0, sdo_0_8001_0_data)) {
        fprintf(stderr, "Failed to configure SDO 0x8001:0 for slave 0\n");
        exit(EXIT_FAILURE);
    }
    
    printf("SDO configuration completed for slave 0\n");

    
    // 保存配置引用
    
    sc_slave0 = slave_configs[0];
    
    // 创建PDO配置线程
    pthread_t threads[1];
    int config_results[1] = {0};
    config_thread_param_t thread_params[1];
    
    printf("Starting parallel PDO configuration...\n");
    
    // 初始化PDO配置线程参数
    
    thread_params[0].slave_config = slave_configs[0];
    thread_params[0].sync_info = slave0_syncs;
    thread_params[0].result = &config_results[0];
    
    // 启动PDO配置线程
    for(int i = 0; i < 1; i++) {
        if (pthread_create(&threads[i], NULL, config_slave_thread, &thread_params[i])) {
            fprintf(stderr, "Failed to create PDO configuration thread %d\n", i);
            exit(EXIT_FAILURE);
        }
    }
    
    // 等待所有PDO配置线程完成
    for(int i = 0; i < 1; i++) {
        pthread_join(threads[i], NULL);
        if (config_results[i]) {
            fprintf(stderr, "Failed to configure PDOs for slave %d\n", i);
            exit(EXIT_FAILURE);
        }
    }
    
    printf("Parallel PDO configuration completed\n");

    // 配置 DC
    printf("Starting parallel DC configuration...\n");
    
    // 创建DC配置线程数组和参数
    pthread_t dc_threads[1];
    int dc_results[1] = {0};
    dc_config_thread_param_t dc_params[1];
    
    // 初始化DC配置参数
    
    // No DC configuration needed for this slave
    
    // 等待所有DC配置线程完成
    // Skip waiting for non-DC slave
    
    printf("Parallel DC configuration completed\n");

    if (ecrt_domain_reg_pdo_entry_list(domain1, domain1_regs)) {
        fprintf(stderr, "PDO entry registration failed!\n");
        exit(EXIT_FAILURE);
    }

    printf("Activating master...\n");
    if (ecrt_master_activate(master)) {
        exit(EXIT_FAILURE);
    }

    if (!(domain1_pd = ecrt_domain_data(domain1))) {
        exit(EXIT_FAILURE);
    }

    // Set real-time priority
    struct sched_param param = {};
    param.sched_priority = sched_get_priority_max(SCHED_FIFO);
    printf("Using priority %i.\n", param.sched_priority);
    if (sched_setscheduler(0, SCHED_FIFO, &param) == -1) {
        perror("sched_setscheduler failed");
    }

    printf("Started.\n");
    printf("Shared memory interface created at %s\n", ETHERCAT_SHM_FILE);
    
    // 修改后的主循环
    while (run) {
        cyclic_task();
    }

    return EXIT_SUCCESS;
}