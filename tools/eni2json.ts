import { parseString } from 'xml2js';
import * as fs from 'fs';
import * as path from 'path';

interface SDO {
  name: string;
  index: string;
  subindex: string;
  type: string;
  value: string;
}

interface PDO {
  name: string;
  index: string;
  subindex: string;
  type: string;
}

interface PDOEntry {
  index: string;
  subindex: string;
  name: string;
  type: string;
}

interface PDOGroup {
  index: string;
  entries: PDOEntry[];
  entryOffset: number;
}

interface PDOMapping {
  rx_pdos: PDOGroup[];
  tx_pdos: PDOGroup[];
}

interface Sync {
  index: number;
  direction: string;
  pdos: string[];
  watchdog: string;
}

interface Slave {
  index: string;
  name: string;
  vid: string;
  pid: string;
  rx_pdo: string;
  tx_pdo: string;
  sdos: SDO[];
  rx_pdos: PDO[];  // 保持原有结构
  tx_pdos: PDO[];  // 保持原有结构
  pdo_mapping: PDOMapping;  // 修改为新结构
  syncs: Sync[];
}

interface SlaveConfig {
  slaves: Slave[];
}

function convertType(dataType: string): string {
  const typeMap: { [key: string]: string } = {
    'UINT': 'uint16',
    'UDINT': 'uint32',
    'USINT': 'uint8',
    'BOOL': 'bool',
    'BIT': 'bool'
  };
  return typeMap[dataType.toUpperCase()] || 'bool';
}

function formatHexValue(value: string | number | any): string {
  // 处理对象格式的值
  if (typeof value === 'object') {
    if (value._ !== undefined) {
      value = value._;
    } else {
      console.warn('无法解析的值格式:', value);
      return '0x00000000';
    }
  }

  // 如果值是undefined或null
  if (value === undefined || value === null) {
    return '0x00000000';
  }

  // 处理十六进制字符串格式
  if (typeof value === 'string') {
    if (value.startsWith('#x') || value.startsWith('0x')) {
      const hexValue = value.replace(/^#x|^0x/i, '').toLowerCase();
      return '0x' + hexValue.padStart(8, '0');
    }
    // 将字符串解析为数字
    value = parseInt(value, 10);
  }

  // 处理数字类型
  if (typeof value === 'number') {
    return '0x' + value.toString(16).padStart(8, '0').toLowerCase();
  }

  return '0x00000000';
}

function convertSdoValue(value: string, type: string): string {
  // 移除0x或#x前缀
  let hexValue = value.replace(/^(0x|#x)/i, '');
  
  // 确保是8位
  hexValue = hexValue.padStart(8, '0');
  
  // 根据类型处理值
  switch(type) {
    case 'uint8':
      // 对于uint8,只取最后一个字节
      return '0x' + hexValue.slice(-2);
    case 'uint16':
      // 对于uint16,取后两个字节并反转
      const bytes16 = hexValue.slice(-4).match(/.{2}/g) || [];
      return '0x' + bytes16.reverse().join('');
    case 'uint32':
      // 对于uint32,转换所有字节序
      const bytes32 = hexValue.match(/.{2}/g) || [];
      return '0x' + bytes32.reverse().join('');
    default:
      return '0x' + hexValue;
  }
}

function extractPDOEntries(pdo: any): PDO[] {
  const entries: PDO[] = [];
  if (!pdo || !pdo.Entry) return entries;

  // 将单个条目转换为数组
  const entryArray = Array.isArray(pdo.Entry) ? pdo.Entry : [pdo.Entry];
  
  entryArray.forEach((entry: any) => {
    // 添加空值检查
    if (!entry || !entry.Name || !entry.Index) {
      console.warn('跳过无效的PDO条目:', entry);
      return;
    }

    try {
      entries.push({
        name: entry.Name[0] || 'Unknown',
        index: formatHexValue(entry.Index[0] || 0),
        subindex: entry.SubIndex ? entry.SubIndex[0] : '0',
        type: entry.DataType ? convertType(entry.DataType[0]) : 'bool'
      });
    } catch (error) {
      console.warn('处理PDO条目时出错:', error);
    }
  });

  return entries;
}

function extractSDOs(mailbox: any): SDO[] {
  const sdos: SDO[] = [];
  if (!mailbox || !mailbox.CoE || !mailbox.CoE[0].InitCmds) return sdos;

  const initCmds = mailbox.CoE[0].InitCmds[0].InitCmd;
  initCmds.forEach((cmd: any) => {
    const index = parseInt(cmd.Index[0], 10);
    const subIndex = parseInt(cmd.SubIndex[0], 10);
    const data = cmd.Data[0];
    
    // 确定SDO类型和长度
    let type = 'uint32';
    if (data.length <= 2) type = 'uint8';
    else if (data.length <= 4) type = 'uint16';
    
    // 使用ModuleHandle作为模块标识
    const moduleHandle = cmd.ModuleHandle ? cmd.ModuleHandle[0] : '';
    const moduleName = moduleHandle === '0' ? 'Output' : 
                      moduleHandle === '1' ? 'Input' : 
                      `Module${moduleHandle}`;

    sdos.push({
      name: `${moduleName} Config ${subIndex}`,
      index: '0x' + index.toString(16).padStart(4, '0'),
      subindex: subIndex.toString(),
      type: type,
      value: convertSdoValue(data, type)  // 传入类型参数
    });
  });

  return sdos;
}

function extractPDOGroup(pdo: any): PDOGroup {
  try {
    let indexValue = pdo.Index?.[0];
    if (typeof indexValue === 'object' && indexValue._) {
      indexValue = indexValue._;
    }

    const entries = pdo.Entry ? pdo.Entry.map((entry: any) => {
      let entryIndex = entry.Index?.[0];
      if (typeof entryIndex === 'object' && entryIndex._) {
        entryIndex = entryIndex._;
      }

      return {
        index: formatHexValue(entryIndex || 0),
        subindex: entry.SubIndex?.[0] || '0',
        name: entry.Name?.[0] || 'Reserved',
        type: entry.DataType?.[0] ? convertType(entry.DataType[0]) : 
              entry.BitLen?.[0] === '1' ? 'bool' : 'uint8'
      };
    }) : [];

    return {
      index: formatHexValue(indexValue || 0),
      entries,
      entryOffset: 0
    };
  } catch (error) {
    console.error('处理PDO组时出错:', error);
    return {
      index: '0x0000',
      entries: [],
      entryOffset: 0
    };
  }
}

function extractPDOMappings(processData: any): PDOMapping {
  const mapping: PDOMapping = {
    rx_pdos: [],
    tx_pdos: []
  };
  
  let currentOffset = 0;
  
  try {
    if (processData.RxPdo) {
      const rxPdos = Array.isArray(processData.RxPdo) ? 
        processData.RxPdo : [processData.RxPdo];

      mapping.rx_pdos = rxPdos.map((pdo: any) => {
        const group = extractPDOGroup(pdo);
        group.entryOffset = currentOffset;
        currentOffset += group.entries.length;
        return group;
      });
    }
    
    if (processData.TxPdo) {
      const txPdos = Array.isArray(processData.TxPdo) ? 
        processData.TxPdo : [processData.TxPdo];

      mapping.tx_pdos = txPdos.map((pdo: any) => {
        const group = extractPDOGroup(pdo);
        group.entryOffset = currentOffset;
        currentOffset += group.entries.length;
        return group;
      });
    }
  } catch (error) {
    console.error('处理PDO映射时出错:', error);
  }
  
  return mapping;
}

function extractPDOs(processData: any): { 
  rxPdos: PDO[], 
  txPdos: PDO[], 
  rxPdoIndices: string[], 
  txPdoIndices: string[],
  pdoMapping: PDOMapping
} {
  console.log('ProcessData:', JSON.stringify(processData, null, 2));
  const rxPdos: PDO[] = [];
  const txPdos: PDO[] = [];
  const rxPdoIndices: string[] = [];
  const txPdoIndices: string[] = [];

  try {
    if (processData.RxPdo) {
      // 确保RxPdo是数组
      const rxPdoArray = Array.isArray(processData.RxPdo) ? 
        processData.RxPdo : [processData.RxPdo];

      rxPdoArray.forEach((pdo: any) => {
        if (pdo && pdo.Index) {
          rxPdoIndices.push(formatHexValue(pdo.Index[0]));
          rxPdos.push(...extractPDOEntries(pdo));
        }
      });
    }

    if (processData.TxPdo) {
      // 确保TxPdo是数组
      const txPdoArray = Array.isArray(processData.TxPdo) ? 
        processData.TxPdo : [processData.TxPdo];

      txPdoArray.forEach((pdo: any) => {
        if (pdo && pdo.Index) {
          txPdoIndices.push(formatHexValue(pdo.Index[0]));
          txPdos.push(...extractPDOEntries(pdo));
        }
      });
    }
  } catch (error) {
    console.error('处理PDO映射时出错:', error);
  }

  const pdoMapping = extractPDOMappings(processData);

  return { rxPdos, txPdos, rxPdoIndices, txPdoIndices, pdoMapping };
}

function extractSyncs(processData: any): Sync[] {
  const syncs: Sync[] = [];
  
  // 添加默认的SM0和SM1
  syncs.push(
    {
      index: 0,
      direction: "OUTPUT",
      pdos: [],
      watchdog: "DISABLE"
    },
    {
      index: 1,
      direction: "INPUT",
      pdos: [],
      watchdog: "DISABLE"
    }
  );

  // 处理RxPDO (SM2)
  if (processData.RxPdo) {
    const rxPdoIndices = processData.RxPdo.map((pdo: any) => formatHexValue(pdo.Index[0]));
    syncs.push({
      index: 2,
      direction: "OUTPUT",
      pdos: rxPdoIndices,
      watchdog: "ENABLE"
    });
  }

  // 处理TxPDO (SM3)
  if (processData.TxPdo) {
    const txPdoIndices = processData.TxPdo.map((pdo: any) => formatHexValue(pdo.Index[0]));
    syncs.push({
      index: 3,
      direction: "INPUT",
      pdos: txPdoIndices,
      watchdog: "DISABLE"
    });
  }

  return syncs;
}

function parseENI(xmlContent: string): Promise<SlaveConfig> {
  return new Promise((resolve, reject) => {
    parseString(xmlContent, { explicitArray: true }, (err, result) => {
      if (err) {
        reject(err);
        return;
      }

      const slaves: Slave[] = [];
      const config = result.EtherCATConfig.Config[0];

      if (config.Slave) {
        config.Slave.forEach((slave: any, index: number) => {
          const info = slave.Info[0];
          const processData = slave.ProcessData[0];
          const { rxPdos, txPdos, rxPdoIndices, txPdoIndices, pdoMapping } = extractPDOs(processData);
          const sdos = extractSDOs(slave.Mailbox && slave.Mailbox[0]);
          const syncs = extractSyncs(processData);

          // 移除 CDATA 包装
          const name = info.Name[0].replace(/\[\[CDATA\[(.*?)\]\]\]/, '$1');

          slaves.push({
            index: index.toString(),
            name: name,
            vid: formatHexValue(info.VendorId[0]),
            pid: formatHexValue(info.ProductCode[0]),
            rx_pdo: rxPdoIndices.join(', '),
            tx_pdo: txPdoIndices.join(', '),
            sdos: sdos,
            rx_pdos: rxPdos,
            tx_pdos: txPdos,
            pdo_mapping: pdoMapping,
            syncs: syncs
          });
        });
      }

      resolve({ slaves });
    });
  });
}

export async function getSlaveConfig(xmlContent: string): Promise<SlaveConfig> {
  return parseENI(xmlContent);
}

async function main() {
  try {
    const xmlPath = path.join(__dirname, 'MK1100-New.xml');
    // const xmlPath = path.join(__dirname, 'CTL_ECT_ENI-4.xml');
    const outputPath = path.join(__dirname, 'generated_slave_config.json');
    
    const xmlContent = fs.readFileSync(xmlPath, 'utf-8');
    const slaveConfig = await parseENI(xmlContent);
    
    fs.writeFileSync(
      outputPath,
      JSON.stringify(slaveConfig, null, 2),
      'utf-8'
    );
    
    console.log('转换完成！输出文件：', outputPath);
  } catch (error) {
    console.error('错误：', error);
  }
}

// 仅在直接运行时执行main函数
if (require.main === module) {
  main();
}

// 导出所有需要的接口和函数
export {
  SlaveConfig,
  Slave,
  SDO,
  PDO,
  PDOEntry,
  PDOGroup,
  PDOMapping,
  Sync
};