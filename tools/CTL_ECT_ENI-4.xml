<?xml version="1.0"?>
<EtherCATConfig xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="EtherCATConfig.xsd" Version="1.3">
	<Config>
		<Master>
			<Info>
				<Name><![CDATA[Device 1 (EtherCAT)]]></Name>
				<Destination>010105010000</Destination>
				<Source>047c167b18a7</Source>
				<EtherType>a488</EtherType>
			</Info>
			<MailboxStates>
				<StartAddr>150994944</StartAddr>
				<Count>1</Count>
			</MailboxStates>
			<InitCmds>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[read slave count]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>7</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>0</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[read slave count]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>7</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>0</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[enable ECAT IRQ]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>512</Ado>
					<Data>0400</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear configured addresses]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>16</Ado>
					<Data>0000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear crc register]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>768</Ado>
					<Data>0000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>PI</Transition>
					<Transition>BI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear fmmu]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>1536</Ado>
					<DataLength>256</DataLength>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear sm]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2048</Ado>
					<DataLength>256</DataLength>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear dc system time]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2320</Ado>
					<DataLength>32</DataLength>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[clear dc cycle cfg]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2433</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[reset dc speed]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2352</Ado>
					<Data>0010</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[configure dc filter]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>2356</Ado>
					<Data>000c</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<BeforeSlave>true</BeforeSlave>
					<Comment><![CDATA[en/disable second physical address]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>8</Cmd>
					<Adp>0</Adp>
					<Ado>259</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
			</InitCmds>
		</Master>
		<Slave>
			<Info>
				<Name><![CDATA[Box 1 (CTL_ECT)]]></Name>
				<PhysAddr>1001</PhysAddr>
				<AutoIncAddr>0</AutoIncAddr>
				<Physics>YY</Physics>
				<VendorId>153</VendorId>
				<ProductCode>131856</ProductCode>
				<RevisionNo>2</RevisionNo>
				<SerialNo>0</SerialNo>
			</Info>
			<ProcessData>
				<Send>
					<BitStart>312</BitStart>
					<BitLength>32</BitLength>
				</Send>
				<Recv>
					<BitStart>312</BitStart>
					<BitLength>224</BitLength>
				</Recv>
				<Sm2>
					<Type>Outputs</Type>
					<StartAddress>4352</StartAddress>
					<ControlByte>100</ControlByte>
					<Enable>1</Enable>
					<Pdo>5632</Pdo>
					<Pdo>5634</Pdo>
				</Sm2>
				<Sm3>
					<Type>Inputs</Type>
					<StartAddress>5120</StartAddress>
					<ControlByte>32</ControlByte>
					<Enable>1</Enable>
					<Pdo>6656</Pdo>
					<Pdo>6657</Pdo>
					<Pdo>6658</Pdo>
					<Pdo>6659</Pdo>
				</Sm3>
				<TxPdo Fixed="true" Sm="3">
					<Index DependOnSlot="true">#x1a00</Index>
					<Name>ModuleStatus</Name>
					<Entry>
						<Index DependOnSlot="true">#x8002</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Module State</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x8003</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Module Err Num</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</TxPdo>
				<RxPdo Fixed="true" Sm="2">
					<Index DependOnSlot="true">#x1600</Index>
					<Name>DQ-16 Outputs</Name>
					<Entry>
						<Index DependOnSlot="true">#x7000</Index>
						<SubIndex>1</SubIndex>
						<BitLen>8</BitLen>
						<Name>OutByte0</Name>
						<DataType>USINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x7000</Index>
						<SubIndex>2</SubIndex>
						<BitLen>8</BitLen>
						<Name>OutByte1</Name>
						<DataType>USINT</DataType>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="true" Sm="3">
					<Index DependOnSlot="true">#x1a01</Index>
					<Name>DI-16 Input</Name>
					<Entry>
						<Index DependOnSlot="true">#x6100</Index>
						<SubIndex>1</SubIndex>
						<BitLen>8</BitLen>
						<Name>InByte0</Name>
						<DataType>USINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x6100</Index>
						<SubIndex>2</SubIndex>
						<BitLen>8</BitLen>
						<Name>InByte1</Name>
						<DataType>USINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x8102</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Module State</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x8103</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Module Err Num</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="true" Sm="3">
					<Index DependOnSlot="true">#x1a02</Index>
					<Name>ModuleStatus</Name>
					<Entry>
						<Index DependOnSlot="true">#x8202</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Module State</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x8203</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Module Err Num</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</TxPdo>
				<RxPdo Fixed="true" Sm="2">
					<Index DependOnSlot="true">#x1602</Index>
					<Name>DQ-16 Outputs</Name>
					<Entry>
						<Index DependOnSlot="true">#x7200</Index>
						<SubIndex>1</SubIndex>
						<BitLen>8</BitLen>
						<Name>OutByte0</Name>
						<DataType>USINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x7200</Index>
						<SubIndex>2</SubIndex>
						<BitLen>8</BitLen>
						<Name>OutByte1</Name>
						<DataType>USINT</DataType>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="true" Sm="3">
					<Index DependOnSlot="true">#x1a03</Index>
					<Name>DI-16 Input</Name>
					<Entry>
						<Index DependOnSlot="true">#x6300</Index>
						<SubIndex>1</SubIndex>
						<BitLen>8</BitLen>
						<Name>InByte0</Name>
						<DataType>USINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x6300</Index>
						<SubIndex>2</SubIndex>
						<BitLen>8</BitLen>
						<Name>InByte1</Name>
						<DataType>USINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x8302</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Module State</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x8303</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Module Err Num</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</TxPdo>
			</ProcessData>
			<Mailbox DataLinkLayer="true">
				<Send>
					<Start>4096</Start>
					<Length>128</Length>
				</Send>
				<Recv>
					<Start>4224</Start>
					<Length>128</Length>
					<StatusBitAddr>0</StatusBitAddr>
				</Recv>
				<Protocol>CoE</Protocol>
				<CoE>
					<InitCmds>
						<InitCmd ModuleHandle="0">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>32769</Index>
							<SubIndex>1</SubIndex>
							<Data>72030000</Data>
						</InitCmd>
						<InitCmd ModuleHandle="0">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>28674</Index>
							<SubIndex>1</SubIndex>
							<Data>ff</Data>
						</InitCmd>
						<InitCmd ModuleHandle="0">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>28674</Index>
							<SubIndex>2</SubIndex>
							<Data>ff</Data>
						</InitCmd>
						<InitCmd ModuleHandle="1">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>33025</Index>
							<SubIndex>1</SubIndex>
							<Data>81010000</Data>
						</InitCmd>
						<InitCmd ModuleHandle="1">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24833</Index>
							<SubIndex>1</SubIndex>
							<Data>06</Data>
						</InitCmd>
						<InitCmd ModuleHandle="1">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24833</Index>
							<SubIndex>2</SubIndex>
							<Data>06</Data>
						</InitCmd>
						<InitCmd ModuleHandle="1">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24833</Index>
							<SubIndex>3</SubIndex>
							<Data>06</Data>
						</InitCmd>
						<InitCmd ModuleHandle="1">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>24833</Index>
							<SubIndex>4</SubIndex>
							<Data>06</Data>
						</InitCmd>
						<InitCmd ModuleHandle="2">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>33281</Index>
							<SubIndex>1</SubIndex>
							<Data>72030000</Data>
						</InitCmd>
						<InitCmd ModuleHandle="2">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>29186</Index>
							<SubIndex>1</SubIndex>
							<Data>ff</Data>
						</InitCmd>
						<InitCmd ModuleHandle="2">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>29186</Index>
							<SubIndex>2</SubIndex>
							<Data>ff</Data>
						</InitCmd>
						<InitCmd ModuleHandle="3">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>33537</Index>
							<SubIndex>1</SubIndex>
							<Data>81010000</Data>
						</InitCmd>
						<InitCmd ModuleHandle="3">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>25345</Index>
							<SubIndex>1</SubIndex>
							<Data>06</Data>
						</InitCmd>
						<InitCmd ModuleHandle="3">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>25345</Index>
							<SubIndex>2</SubIndex>
							<Data>06</Data>
						</InitCmd>
						<InitCmd ModuleHandle="3">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>25345</Index>
							<SubIndex>3</SubIndex>
							<Data>06</Data>
						</InitCmd>
						<InitCmd ModuleHandle="3">
							<Transition>PS</Transition>
							<Timeout>0</Timeout>
							<Ccs>1</Ccs>
							<Index>25345</Index>
							<SubIndex>4</SubIndex>
							<Data>06</Data>
						</InitCmd>
					</InitCmds>
					<Profile>
						<ChannelInfo>
							<ProfileNo>5001</ProfileNo>
							<AddInfo>0</AddInfo>
						</ChannelInfo>
					</Profile>
				</CoE>
			</Mailbox>
			<InitCmds>
				<InitCmd>
					<Transition>PI</Transition>
					<Transition>BI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[set device state to INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>288</Ado>
					<Data>1100</Data>
					<Retries>3</Retries>
					<Timeout>5000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>PI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[check device state for INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>0100</Data>
						<DataMask>0f00</DataMask>
						<Timeout>5000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>BI</Transition>
					<Comment><![CDATA[check device state for INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>0100</Data>
						<DataMask>0f00</DataMask>
						<Timeout>10000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set device state to INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>288</Ado>
					<Data>1100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>2000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[check device state for INIT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<Data>0000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>0100</Data>
						<DataMask>0f00</DataMask>
						<Timeout>2000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[assign EEPROM to ECAT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1280</Ado>
					<Data>00</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check vendor id]]></Comment>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1282</Ado>
					<Data>000108000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check vendor id]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>1288</Ado>
					<Data>00000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>99000000</Data>
						<Timeout>100</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check product code]]></Comment>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1282</Ado>
					<Data>00010a000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check product code]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>1</Cmd>
					<Adp>0</Adp>
					<Ado>1288</Ado>
					<Data>00000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>10030200</Data>
						<Timeout>100</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set physical address]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>16</Ado>
					<Data>e903</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Transition>PI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear sm 0/1 (mailbox out/in)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2048</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>BI</Transition>
					<Comment><![CDATA[clear sm 0/1 (mailbox out/in)]]></Comment>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>2048</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set sm 0 (mailbox out)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2048</Ado>
					<Data>0010800026000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set sm 1 (mailbox in)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2056</Ado>
					<Data>8010800022000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>OP</Transition>
					<Comment><![CDATA[set device state to PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>1200</Data>
					<Retries>300</Retries>
					<Timeout>200</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>SP</Transition>
					<Transition>SI</Transition>
					<Transition>OP</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear sms]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2064</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set sm 2 (outputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2064</Ado>
					<Data>0011040064000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set sm 3 (inputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>2072</Ado>
					<Data>00141c0020000100</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set fmmu 0 (outputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1536</Ado>
					<Data>00000001040000070011000201000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set fmmu 1 (inputs)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1552</Ado>
					<Data>000000011c0000070014000101000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[set fmmu 2 (mailbox state)]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1568</Ado>
					<Data>00000009010000000d08000101000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>OS</Transition>
					<Comment><![CDATA[set device state to SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>0400</Data>
					<Retries>3</Retries>
					<Timeout>200</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>SI</Transition>
					<Transition>OP</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear fmmu 0]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1536</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>SI</Transition>
					<Transition>OP</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear fmmu 1]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1552</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>PI</Transition>
					<Transition>BI</Transition>
					<Transition>SI</Transition>
					<Transition>OI</Transition>
					<Comment><![CDATA[clear fmmu 2]]></Comment>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1568</Ado>
					<Data>00000000000000000000000000000000</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>SP</Transition>
					<Transition>OP</Transition>
					<Comment><![CDATA[check device state for PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>020000000000</Data>
						<DataMask>0f0000000000</DataMask>
						<Timeout>5000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>IB</Transition>
					<Comment><![CDATA[assign EEPROM to PDI]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>1280</Ado>
					<Data>01</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>II</Transition>
					<Comment><![CDATA[assign EEPROM back to ECAT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1280</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[set device state to PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>1200</Data>
					<Cnt>1</Cnt>
					<Retries>300</Retries>
					<Timeout>2000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Comment><![CDATA[check device state for PREOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>020000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>2000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>IP</Transition>
					<Transition>BI</Transition>
					<Comment><![CDATA[assign EEPROM back to ECAT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>2</Cmd>
					<Adp>0</Adp>
					<Ado>1280</Ado>
					<Data>00</Data>
					<Retries>3</Retries>
				</InitCmd>
				<InitCmd>
					<Transition>IB</Transition>
					<Comment><![CDATA[set device state to BOOT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>1300</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>2000</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>IB</Transition>
					<Comment><![CDATA[check device state for BOOT]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>030000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>2000</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[set device state to SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>0400</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>65535</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>PS</Transition>
					<Comment><![CDATA[check device state for SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>040000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>65535</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>OS</Transition>
					<Comment><![CDATA[check device state for SAFEOP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Retries>3</Retries>
					<Validate>
						<Data>040000000000</Data>
						<DataMask>0f0000000000</DataMask>
						<Timeout>200</Timeout>
					</Validate>
				</InitCmd>
				<InitCmd>
					<Transition>SO</Transition>
					<Comment><![CDATA[set device state to OP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>5</Cmd>
					<Adp>1001</Adp>
					<Ado>288</Ado>
					<Data>0800</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Timeout>65535</Timeout>
				</InitCmd>
				<InitCmd>
					<Transition>SO</Transition>
					<Comment><![CDATA[check device state for OP]]></Comment>
					<Requires>cycle</Requires>
					<Cmd>4</Cmd>
					<Adp>1001</Adp>
					<Ado>304</Ado>
					<Data>000000000000</Data>
					<Cnt>1</Cnt>
					<Retries>3</Retries>
					<Validate>
						<Data>080000000000</Data>
						<DataMask>1f0000000000</DataMask>
						<Timeout>65535</Timeout>
					</Validate>
				</InitCmd>
			</InitCmds>
		</Slave>
		<Cyclic>
			<Frame>
				<Cmd>
					<State>PREOP</State>
					<State>SAFEOP</State>
					<State>OP</State>
					<Comment><![CDATA[cyclic cmd]]></Comment>
					<Cmd>10</Cmd>
					<Addr>150994944</Addr>
					<DataLength>1</DataLength>
					<InputOffs>16</InputOffs>
					<OutputOffs>16</OutputOffs>
				</Cmd>
				<Cmd>
					<State>SAFEOP</State>
					<State>OP</State>
					<Comment><![CDATA[cyclic cmd]]></Comment>
					<Cmd>12</Cmd>
					<Addr>16777216</Addr>
					<DataLength>28</DataLength>
					<Cnt>3</Cnt>
					<InputOffs>29</InputOffs>
					<OutputOffs>29</OutputOffs>
				</Cmd>
				<Cmd>
					<State>PREOP</State>
					<State>SAFEOP</State>
					<State>OP</State>
					<Comment><![CDATA[cyclic cmd]]></Comment>
					<Cmd>7</Cmd>
					<Adp>0</Adp>
					<Ado>304</Ado>
					<DataLength>2</DataLength>
					<Cnt>1</Cnt>
					<InputOffs>69</InputOffs>
					<OutputOffs>69</OutputOffs>
				</Cmd>
			</Frame>
		</Cyclic>
		<ProcessImage>
			<Inputs>
				<ByteSize>1536</ByteSize>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 1 (DQT16).ModuleStatus.Module State</Name>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>312</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 1 (DQT16).ModuleStatus.Module Err Num</Name>
					<DataType>UDINT</DataType>
					<BitSize>32</BitSize>
					<BitOffs>328</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 2 (DIT16).DI-16 Input.InByte0</Name>
					<DataType>USINT</DataType>
					<BitSize>8</BitSize>
					<BitOffs>360</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 2 (DIT16).DI-16 Input.InByte1</Name>
					<DataType>USINT</DataType>
					<BitSize>8</BitSize>
					<BitOffs>368</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 2 (DIT16).DI-16 Input.Module State</Name>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>376</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 2 (DIT16).DI-16 Input.Module Err Num</Name>
					<DataType>UDINT</DataType>
					<BitSize>32</BitSize>
					<BitOffs>392</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 3 (DQT16).ModuleStatus.Module State</Name>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>424</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 3 (DQT16).ModuleStatus.Module Err Num</Name>
					<DataType>UDINT</DataType>
					<BitSize>32</BitSize>
					<BitOffs>440</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 4 (DIT16).DI-16 Input.InByte0</Name>
					<DataType>USINT</DataType>
					<BitSize>8</BitSize>
					<BitOffs>472</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 4 (DIT16).DI-16 Input.InByte1</Name>
					<DataType>USINT</DataType>
					<BitSize>8</BitSize>
					<BitOffs>480</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 4 (DIT16).DI-16 Input.Module State</Name>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>488</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 4 (DIT16).DI-16 Input.Module Err Num</Name>
					<DataType>UDINT</DataType>
					<BitSize>32</BitSize>
					<BitOffs>504</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.Frm0State</Name>
					<Comment><![CDATA[0x0001 = 1. EtherCAT command not sent (NOP requested)
0x0002 = 2. EtherCAT command not sent (NOP requested)
0x0004 = 3. EtherCAT command not sent (NOP requested)
...
0x4000 = 15. EtherCAT command not sent (NOP requested)
0x8000 = complete frame not sent
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12160</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.Frm0WcState</Name>
					<Comment><![CDATA[0x0001 = wrong working counter of 1. EtherCAT command received
0x0002 = wrong working counter of 2. EtherCAT command received
0x0004 = wrong working counter of 3. EtherCAT command received
...
0x4000 = wrong working counter of 15. EtherCAT command received
0x8000 = complete frame missing
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12176</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).WcState.WcState</Name>
					<Comment><![CDATA[0 = Data valid
1 = Data invalid
]]></Comment>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>12177</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.Frm0InputToggle</Name>
					<Comment><![CDATA[0x0001 = Toggle Bit: 1. EtherCAT command received new inputs
0x0002 = Toggle Bit: 2. EtherCAT command received new inputs
0x0004 = Toggle Bit: 3. EtherCAT command received new inputs
...
0x4000 = Toggle Bit: 15. EtherCAT command received new inputs
0x8000 = Old frame - not from the actual cycle - received
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12192</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).WcState.InputToggle</Name>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>12193</BitOffs>
				</Variable>
				<Variable>
					<Name>SyncUnits.&lt;default&gt;.&lt;unreferenced&gt;.WcState.WcState</Name>
					<Comment><![CDATA[0 = Data valid
1 = Data invalid
]]></Comment>
					<DataType>BIT</DataType>
					<BitSize>1</BitSize>
					<BitOffs>12208</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.SlaveCount</Name>
					<Comment><![CDATA[Actual count of EtherCAT slaves received]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12240</BitOffs>
				</Variable>
				<Variable>
					<Name>Inputs.DevState</Name>
					<Comment><![CDATA[0x0001 = Link error
0x0002 = I/O locked after link error (I/O reset required)
0x0004 = Link error (redundancy adapter)
0x0008 = Missing one frame (redundancy mode)
0x0010 = Out of send resources (I/O reset required)
0x0020 = Watchdog triggered
0x0040 = Ethernet driver (miniport) not found
0x0080 = I/O reset active
0x0100 = At least one device in 'INIT' state
0x0200 = At least one device in 'PRE-OP' state
0x0400 = At least one device in 'SAFE-OP' state
0x0800 = At least one device indicates an error state
0x1000 = DC not in sync
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12272</BitOffs>
				</Variable>
				<Variable>
					<Name>InfoData.ChangeCount</Name>
					<Comment><![CDATA[Info data change counter]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12288</BitOffs>
				</Variable>
				<Variable>
					<Name>InfoData.DevId</Name>
					<Comment><![CDATA[DeviceId of EtherCAT device]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12304</BitOffs>
				</Variable>
				<Variable>
					<Name>InfoData.AmsNetId</Name>
					<Comment><![CDATA[AmsNetId of EtherCAT device]]></Comment>
					<DataType>AMSNETID</DataType>
					<BitSize>48</BitSize>
					<BitOffs>12320</BitOffs>
				</Variable>
				<Variable>
					<Name>InfoData.CfgSlaveCount</Name>
					<Comment><![CDATA[Count of configured EtherCAT slaves]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12368</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).InfoData.State</Name>
					<Comment><![CDATA[0x___1 = Slave in 'INIT' state
0x___2 = Slave in 'PREOP' state
0x___3 = Slave in 'BOOT' state
0x___4 = Slave in 'SAFEOP' state
0x___8 = Slave in 'OP' state
0x001_ = Slave signals error
0x002_ = Invalid vendorId, productCode... read
0x004_ = Initialization error occurred
0x008_ = Slave disabled
0x010_ = Slave not present
0x020_ = Slave signals link error
0x040_ = Slave signals missing link
0x080_ = Slave signals unexpected link
0x100_ = Communication port A
0x200_ = Communication port B
0x400_ = Communication port C
0x800_ = Communication port D
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12384</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).InfoData.AdsAddr</Name>
					<Comment><![CDATA[Complete ADS address to access the slave (mailbox)]]></Comment>
					<DataType>AMSADDR</DataType>
					<BitSize>64</BitSize>
					<BitOffs>12400</BitOffs>
				</Variable>
				<Variable>
					<Name>SyncUnits.&lt;default&gt;.&lt;unreferenced&gt;.InfoData.ObjectId</Name>
					<DataType>OTCID</DataType>
					<BitSize>32</BitSize>
					<BitOffs>12464</BitOffs>
				</Variable>
				<Variable>
					<Name>SyncUnits.&lt;default&gt;.&lt;unreferenced&gt;.InfoData.State</Name>
					<Comment><![CDATA[0x___1 = At least one slave in 'INIT' state
0x___2 = At least one slave in 'PREOP' state
0x___3 = At least one slave in 'BOOT' state
0x___4 = At least one slave in 'SAFEOP' state
0x___8 = At least one slave in 'OP' state
0x001_ = At least one slave signals error
0x002_ = Invalid vendorId, productCode... read
0x004_ = Initialization error occurred
0x008_ = At least one slave disabled
0x010_ = At least one slave not present
0x020_ = At least one slave signals link error
0x040_ = At least one slave signals missing link
0x080_ = At least one slave signals unexpected link
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12496</BitOffs>
				</Variable>
				<Variable>
					<Name>SyncUnits.&lt;default&gt;.&lt;unreferenced&gt;.InfoData.SlaveCount</Name>
					<Comment><![CDATA[Info data slave counter]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12512</BitOffs>
				</Variable>
			</Inputs>
			<Outputs>
				<ByteSize>1536</ByteSize>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 1 (DQT16).DQ-16 Outputs.OutByte0</Name>
					<DataType>USINT</DataType>
					<BitSize>8</BitSize>
					<BitOffs>312</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 1 (DQT16).DQ-16 Outputs.OutByte1</Name>
					<DataType>USINT</DataType>
					<BitSize>8</BitSize>
					<BitOffs>320</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 3 (DQT16).DQ-16 Outputs.OutByte0</Name>
					<DataType>USINT</DataType>
					<BitSize>8</BitSize>
					<BitOffs>328</BitOffs>
				</Variable>
				<Variable>
					<Name>Box 1 (CTL_ECT).Module 3 (DQT16).DQ-16 Outputs.OutByte1</Name>
					<DataType>USINT</DataType>
					<BitSize>8</BitSize>
					<BitOffs>336</BitOffs>
				</Variable>
				<Variable>
					<Name>Outputs.Frm0Ctrl</Name>
					<Comment><![CDATA[0x0001 = prevent 1. EtherCAT command from sending (request NOP)
0x0002 = prevent 2. EtherCAT command from sending (request NOP)
0x0004 = prevent 3. EtherCAT command from sending (request NOP)
...
0x4000 = prevent 15. EtherCAT command from sending (request NOP)
0x8000 = prevent complete frame from sending
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12160</BitOffs>
				</Variable>
				<Variable>
					<Name>Outputs.Frm0WcCtrl</Name>
					<Comment><![CDATA[0x0001 = copy data with wrong working counter of 1. EtherCAT command
0x0002 = copy data with wrong working counter of 2. EtherCAT command
0x0004 = copy data with wrong working counter of 3. EtherCAT command
...
0x4000 = copy data with wrong working counter of 15. EtherCAT command
]]></Comment>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12176</BitOffs>
				</Variable>
				<Variable>
					<Name>Outputs.DevCtrl</Name>
					<DataType>UINT</DataType>
					<BitSize>16</BitSize>
					<BitOffs>12272</BitOffs>
				</Variable>
			</Outputs>
		</ProcessImage>
	</Config>
</EtherCATConfig>
