{"slaves": [{"index": "0", "name": "Box 1 (MK1100)", "vid": "0x00000118", "pid": "0x00001100", "rx_pdo": "0x00001600, 0x00001610", "tx_pdo": "0x00001a10", "sdos": [{"name": "Module Config 1", "index": "0x4080", "subindex": "1", "type": "uint8", "value": "0x10"}, {"name": "Module Config 0", "index": "0x8000", "subindex": "0", "type": "uint8", "value": "0x04"}, {"name": "Module Config 0", "index": "0x8001", "subindex": "0", "type": "uint8", "value": "0x0f"}], "rx_pdos": [{"name": "DO1", "index": "0x00003000", "subindex": "1", "type": "bool"}, {"name": "DO2", "index": "0x00003000", "subindex": "2", "type": "bool"}, {"name": "DO3", "index": "0x00003000", "subindex": "3", "type": "bool"}, {"name": "DO4", "index": "0x00003000", "subindex": "4", "type": "bool"}, {"name": "DO5", "index": "0x00003000", "subindex": "5", "type": "bool"}, {"name": "DO6", "index": "0x00003000", "subindex": "6", "type": "bool"}, {"name": "DO7", "index": "0x00003000", "subindex": "7", "type": "bool"}, {"name": "DO8", "index": "0x00003000", "subindex": "8", "type": "bool"}, {"name": "Reserved", "index": "0x00000000", "subindex": "0", "type": "uint8"}, {"name": "Alarm Ctrlword", "index": "0x00003001", "subindex": "0", "type": "uint16"}], "tx_pdos": [{"name": "Alarm Stateword", "index": "0x00002001", "subindex": "0", "type": "uint16"}], "pdo_mapping": {"rx_pdos": [{"index": "0x00001600", "entries": [{"index": "0x00003000", "subindex": "1", "name": "DO1", "type": "bool"}, {"index": "0x00003000", "subindex": "2", "name": "DO2", "type": "bool"}, {"index": "0x00003000", "subindex": "3", "name": "DO3", "type": "bool"}, {"index": "0x00003000", "subindex": "4", "name": "DO4", "type": "bool"}, {"index": "0x00003000", "subindex": "5", "name": "DO5", "type": "bool"}, {"index": "0x00003000", "subindex": "6", "name": "DO6", "type": "bool"}, {"index": "0x00003000", "subindex": "7", "name": "DO7", "type": "bool"}, {"index": "0x00003000", "subindex": "8", "name": "DO8", "type": "bool"}, {"index": "0x00000000", "subindex": "0", "name": "Reserved", "type": "uint8"}], "entryOffset": 0}, {"index": "0x00001610", "entries": [{"index": "0x00003001", "subindex": "0", "name": "Alarm Ctrlword", "type": "uint16"}], "entryOffset": 9}], "tx_pdos": [{"index": "0x00001a10", "entries": [{"index": "0x00002001", "subindex": "0", "name": "Alarm Stateword", "type": "uint16"}], "entryOffset": 10}]}, "syncs": [{"index": 0, "direction": "OUTPUT", "pdos": [], "watchdog": "DISABLE"}, {"index": 1, "direction": "INPUT", "pdos": [], "watchdog": "DISABLE"}, {"index": 2, "direction": "OUTPUT", "pdos": ["0x00001600", "0x00001601", "0x00001602", "0x00001610"], "watchdog": "ENABLE"}, {"index": 3, "direction": "INPUT", "pdos": ["0x00001a10"], "watchdog": "DISABLE"}], "exclude": {"0x00001600": ["0x00001601", "0x00001602"], "0x00001601": ["0x00001600", "0x00001602"], "0x00001602": ["0x00001600", "0x00001601"]}}]}