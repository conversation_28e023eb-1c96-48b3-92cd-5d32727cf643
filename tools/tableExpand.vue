// 添加相应的属性和方法，这边自定义数据  我以官网为例  :expandedRowKeys="expandedRowKeys"   目的是使expandedRowKeys只有最新点开子表单的key
<template>
<a-table :columns="columns" :data-source="data" :expandedRowKeys="expandedRowKeys" @expand="expand" class="components-table-demo-nested">
  <template #operation>
    <a>Publish</a>
  </template>
  <template #expandedRowRender>
    <a-table :columns="innerColumns" :data-source="innerData" :pagination="false">
      <template #status>
        <span>
          <a-badge status="success" />
          Finished
        </span>
      </template>
      <template #operation>
        <span class="table-operation">
          <a>Pause</a>
          <a>Stop</a>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item>Action 1</a-menu-item>
                <a-menu-item>Action 2</a-menu-item>
              </a-menu>
            </template>
            <a>
              More
              <down-outlined />
            </a>
          </a-dropdown>
        </span>
      </template>
    </a-table>
  </template>
</a-table>
</template>
<script lang="ts">
import { DownOutlined } from '@ant-design/icons-vue';
import { defineComponent } from 'vue';

const columns = [
{ title: 'Name', dataIndex: 'name', key: 'name' },
{ title: 'Platform', dataIndex: 'platform', key: 'platform' },
{ title: 'Version', dataIndex: 'version', key: 'version' },
{ title: 'Upgraded', dataIndex: 'upgradeNum', key: 'upgradeNum' },
{ title: 'Creator', dataIndex: 'creator', key: 'creator' },
{ title: 'Date', dataIndex: 'createdAt', key: 'createdAt' },
{ title: 'Action', key: 'operation', slots: { customRender: 'operation' } },
];

interface DataItem {
key: number;
name: string;
platform: string;
version: string;
upgradeNum: number;
creator: string;
createdAt: string;
}

const data: DataItem[] = [];
for (let i = 0; i < 3; ++i) {
data.push({
  key: i,
  name: 'Screem',
  platform: 'iOS',
  version: '10.3.4.5654',
  upgradeNum: 500,
  creator: 'Jack',
  createdAt: '2014-12-24 23:12:00',
});
}

const innerColumns = [
{ title: 'Date', dataIndex: 'date', key: 'date' },
{ title: 'Name', dataIndex: 'name', key: 'name' },
{ title: 'Status', key: 'state', slots: { customRender: 'status' } },
{ title: 'Upgrade Status', dataIndex: 'upgradeNum', key: 'upgradeNum' },
{
  title: 'Action',
  dataIndex: 'operation',
  key: 'operation',
  slots: { customRender: 'operation' },
},
];

interface innerDataItem {
key: number;
date: string;
name: string;
upgradeNum: string;
}

const innerData: innerDataItem[] = [];
for (let i = 0; i < 3; ++i) {
innerData.push({
  key: i,
  date: '2014-12-24 23:12:00',
  name: 'This is production name',
  upgradeNum: 'Upgraded: 56',
});
}
const expandedRowKeys = ref([])
const expand = (expanded,record) => {
  expandedRowKeys.value = []
// 只展开一行
if (expanded) {
  //进这个判断说明当前已经有展开的了
  //返回某个指定的字符串值在字符串中首次出现的位置，下标为0
  let index = expandedRowKeys.value.indexOf(record.id)
  if (index > -1) {
    //如果出现则截取这个id,1d到1相当于0，针对重复点击一个
    expandedRowKeys.value.splice(index, 1)
  } else {
    //如果没出现则截取所有id,添加点击id，0到1，针对已经有一个展开，点另一个会进入判断
    expandedRowKeys.value.splice(0, expandedRowKeys.value.length)
    expandedRowKeys.value.push(record.id)
  }
} else {
  //数组长度小于0，说明都没展开，第一次点击，id添加到数组，数组有谁的id谁就展开
  expandedRowKeys.value.push(record.id)
}
  
}
export default defineComponent({
components: {
  DownOutlined,
},
setup() {
  return {
    data,
    columns,
    innerColumns,
    innerData,
    expandedRowKeys,
      
  };
},
});
</script>

