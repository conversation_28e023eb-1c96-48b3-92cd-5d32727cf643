using System;
using System.IO;
using System.IO.MemoryMappedFiles;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace EtherCATControl
{
    // 共享内存结构体
    [StructLayout(LayoutKind.Sequential)]
    public struct EtherCATSharedMemory
    {
        public int shm_slave0_online_status; // 从站0在线状态
        public int shm_slave0_operational_status; // 从站0运行状态
        public int shm_slave0_al_state; // 从站0AL状态

        public int shm_slave0_rx_0x7000_outbyte0;
        public int shm_slave0_rx_0x7000_outbyte1;
        public int shm_slave0_tx_0x8002_module_state;
        public int shm_slave0_tx_0x8003_module_err_num;
        public int shm_slave0_tx_0x6100_inbyte0;
        public int shm_slave0_tx_0x6100_inbyte1;
        public int shm_slave0_tx_0x8102_module_state;
        public int shm_slave0_tx_0x8103_module_err_num;
    }

    public class EtherCATController : IDisposable
    {
        private readonly MemoryMappedFile _memoryMappedFile;
        private readonly MemoryMappedViewAccessor _viewAccessor;
        private EtherCATSharedMemory _sharedMemory;

        // Linux RT 相关定义
        private enum SchedPolicy
        {
            SCHED_FIFO = 1
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct SchedParam
        {
            public int sched_priority;
        }

        [DllImport("libc", EntryPoint = "sched_setscheduler")]
        private static extern int sched_setscheduler(int pid, int policy, ref SchedParam param);

        [DllImport("libc", EntryPoint = "sched_get_priority_max")]
        private static extern int sched_get_priority_max(int policy);

        public EtherCATController(string EtherCATSharedMemoryFilePath)
        {
            _memoryMappedFile = MemoryMappedFile.CreateFromFile(EtherCATSharedMemoryFilePath, FileMode.Open);
            _viewAccessor = _memoryMappedFile.CreateViewAccessor();
            _sharedMemory = new EtherCATSharedMemory();
        }

        private void UpdateSharedMemory()
        {
            _viewAccessor.Read(0, out _sharedMemory);
        }

        private void WriteSharedMemory()
        {
            _viewAccessor.Write(0, ref _sharedMemory);
        }

        public bool WaitForIOReady(CancellationToken cancellationToken = default)
        {
            Console.WriteLine("等待远程IO就绪...");
            int timeoutMs = 5000; // 5秒超时
            int elapsed = 0;
            
            while (!cancellationToken.IsCancellationRequested && elapsed < timeoutMs)
            {
                UpdateSharedMemory();
                
                // 检查在线状态和运行状态
                if (_sharedMemory.shm_slave0_online_status == 1 && 
                    _sharedMemory.shm_slave0_operational_status == 1)
                {
                    Console.WriteLine("远程IO已就绪");
                    return true;
                }
                
                Thread.Sleep(100);
                elapsed += 100;
            }
            
            if (elapsed >= timeoutMs)
            {
                Console.WriteLine("等待远程IO就绪超时");
                Console.WriteLine($"在线状态: {_sharedMemory.shm_slave0_online_status}");
                Console.WriteLine($"运行状态: {_sharedMemory.shm_slave0_operational_status}");
                Console.WriteLine($"AL状态: {_sharedMemory.shm_slave0_al_state}");
            }
            
            return false;
        }

        public void SetDigitalOutputs(byte byte0, byte byte1)
        {
            _sharedMemory.shm_slave0_rx_0x7000_outbyte0 = byte0;
            _sharedMemory.shm_slave0_rx_0x7000_outbyte1 = byte1;
            WriteSharedMemory();
            Console.WriteLine($"设置数字输出: Byte0=0x{byte0:X2}, Byte1=0x{byte1:X2}");
        }

        public (byte byte0, byte byte1) GetDigitalInputs()
        {
            UpdateSharedMemory();
            byte inByte0 = (byte)_sharedMemory.shm_slave0_tx_0x6100_inbyte0;
            byte inByte1 = (byte)_sharedMemory.shm_slave0_tx_0x6100_inbyte1;
            Console.WriteLine($"读取数字输入: Byte0=0x{inByte0:X2}, Byte1=0x{inByte1:X2}");
            return (inByte0, inByte1);
        }

        public void EnterRealtimeMode()
        {
            var param = new SchedParam { sched_priority = sched_get_priority_max((int)SchedPolicy.SCHED_FIFO) };
            if (sched_setscheduler(0, (int)SchedPolicy.SCHED_FIFO, ref param) != 0)
            {
                Console.WriteLine("Warning: Failed to set SCHED_FIFO scheduler");
            }
        }

        public async Task RunControlSequence(CancellationToken cancellationToken)
        {
            try
            {
                // 等待IO就绪
                if (!WaitForIOReady(cancellationToken))
                {
                    Console.WriteLine("IO未就绪，退出控制序列");
                    return;
                }

                // 循环点亮模式：从左到右依次点亮
                byte outputValue = 0x01;
                bool direction = true; // true表示向左移位，false表示向右移位
                
                while (!cancellationToken.IsCancellationRequested)
                {
                    // 设置输出
                    SetDigitalOutputs(outputValue, outputValue);  // 只使用第一个字节
                    
                    // 读取输入状态
                    var (inByte0, inByte1) = GetDigitalInputs();
                    
                    // 检查模块状态
                    UpdateSharedMemory();
                    if (_sharedMemory.shm_slave0_tx_0x8002_module_state != 0)
                    {
                        Console.WriteLine($"模块错误: State=0x{_sharedMemory.shm_slave0_tx_0x8002_module_state:X4}");
                    }

                    // 计算下一个输出值
                    if (direction)
                    {
                        outputValue = (byte)(outputValue << 1);
                        if (outputValue == 0x80) direction = false;  // 到达最左边，改变方向
                    }
                    else
                    {
                        outputValue = (byte)(outputValue >> 1);
                        if (outputValue == 0x01) direction = true;   // 到达最右边，改变方向
                    }
                    
                    await Task.Delay(200, cancellationToken); // 200ms延时，使流水灯效果更流畅
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("控制序列被取消");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"控制序列错误: {ex.Message}");
            }
            finally
            {
                // 清除所有输出
                SetDigitalOutputs(0, 0);
                Console.WriteLine("控制序列结束");
            }
        }

        public void Dispose()
        {
            _viewAccessor?.Dispose();
            _memoryMappedFile?.Dispose();
        }
    }

    class Program
    {
        static async Task Main(string[] args)
        {
            try 
            {
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json")
                );

                var requestData = new { programName = $"{Path.GetFileName(Process.GetCurrentProcess().MainModule.FileName)}" };
                var jsonContent = JsonSerializer.Serialize(requestData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(
                    "http://192.168.1.12:3000/api/programs/start-middleware", 
                    content
                );

                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Failed to get shared memory path: {response.StatusCode}");
                    return;
                }

                var sharedMemoryFilePath = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Starting EtherCAT Control Program with shared memory file: {sharedMemoryFilePath}...");

                using var controller = new EtherCATController(sharedMemoryFilePath);
                controller.EnterRealtimeMode();

                var cts = new CancellationTokenSource();

                // 添加SIGTERM信号处理
                AppDomain.CurrentDomain.ProcessExit += (s, e) => {
                    Console.WriteLine("收到SIGTERM信号，正在优雅退出...");
                    cts.Cancel();
                };

                Console.CancelKeyPress += (s, e) => {
                    e.Cancel = true;
                    cts.Cancel();
                };

                try
                {
                    await controller.RunControlSequence(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    Console.WriteLine("程序正在停止...");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error: {ex.Message}");
                }
                finally
                {
                    // 确保在所有情况下都执行清理操作
                    Console.WriteLine($"即将停止中间层");
                    // 发送停止通知到API
                    try 
                    {
                        var stopContent = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                        Console.WriteLine($"发送停止通知");
                        var _ = client.PostAsync("http://192.168.1.12:3000/api/programs/stop-middleware", stopContent).GetAwaiter().GetResult();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"停止通知发送失败: {ex.Message}");
                    }
                    Console.WriteLine("程序已完全停止。");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Startup error: {ex.Message}");
            }
        }
    }
}