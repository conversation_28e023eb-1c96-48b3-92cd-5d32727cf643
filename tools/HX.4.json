{"slaves": [{"index": "0", "name": "Box 1 (CTL_ECT)", "pdo_mapping": {"rx_pdos": [{"entries": [{"index": "0x00007000", "name": "OutByte0", "subindex": "1", "type": "uint8"}, {"index": "0x00007000", "name": "OutByte1", "subindex": "2", "type": "uint8"}], "entryOffset": 0, "index": "0x00001600"}, {"entries": [{"index": "0x00007200", "name": "OutByte0", "subindex": "1", "type": "uint8"}, {"index": "0x00007200", "name": "OutByte1", "subindex": "2", "type": "uint8"}], "entryOffset": 2, "index": "0x00001602"}], "tx_pdos": [{"entries": [{"index": "0x00008002", "name": "Module State", "subindex": "0", "type": "uint16"}, {"index": "0x00008003", "name": "<PERSON><PERSON><PERSON>", "subindex": "0", "type": "uint32"}], "entryOffset": 4, "index": "0x00001a00"}, {"entries": [{"index": "0x00006100", "name": "InByte0", "subindex": "1", "type": "uint8"}, {"index": "0x00006100", "name": "InByte1", "subindex": "2", "type": "uint8"}, {"index": "0x00008102", "name": "Module State", "subindex": "0", "type": "uint16"}, {"index": "0x00008103", "name": "<PERSON><PERSON><PERSON>", "subindex": "0", "type": "uint32"}], "entryOffset": 6, "index": "0x00001a01"}, {"entries": [{"index": "0x00008202", "name": "Module State", "subindex": "0", "type": "uint16"}, {"index": "0x00008203", "name": "<PERSON><PERSON><PERSON>", "subindex": "0", "type": "uint32"}], "entryOffset": 10, "index": "0x00001a02"}, {"entries": [{"index": "0x00006300", "name": "InByte0", "subindex": "1", "type": "uint8"}, {"index": "0x00006300", "name": "InByte1", "subindex": "2", "type": "uint8"}, {"index": "0x00008302", "name": "Module State", "subindex": "0", "type": "uint16"}, {"index": "0x00008303", "name": "<PERSON><PERSON><PERSON>", "subindex": "0", "type": "uint32"}], "entryOffset": 12, "index": "0x00001a03"}]}, "pid": "0x00020310", "rx_pdo": "0x00001600, 0x00001602", "rx_pdos": [{"index": "0x00007000", "name": "OutByte0", "subindex": "1", "type": "uint8"}, {"index": "0x00007000", "name": "OutByte1", "subindex": "2", "type": "uint8"}, {"index": "0x00007200", "name": "OutByte0", "subindex": "1", "type": "uint8"}, {"index": "0x00007200", "name": "OutByte1", "subindex": "2", "type": "uint8"}], "sdos": [{"index": "0x8001", "name": "Module Config 1", "subindex": "1", "type": "uint32", "value": "0x00000372"}, {"index": "0x7002", "name": "Module Config 1", "subindex": "1", "type": "uint8", "value": "0xff"}, {"index": "0x7002", "name": "Module Config 2", "subindex": "2", "type": "uint8", "value": "0xff"}, {"index": "0x8101", "name": "Module Config 1", "subindex": "1", "type": "uint32", "value": "0x00000181"}, {"index": "0x6101", "name": "Module Config 1", "subindex": "1", "type": "uint8", "value": "0x06"}, {"index": "0x6101", "name": "Module Config 2", "subindex": "2", "type": "uint8", "value": "0x06"}, {"index": "0x6101", "name": "Module Config 3", "subindex": "3", "type": "uint8", "value": "0x06"}, {"index": "0x6101", "name": "Module Config 4", "subindex": "4", "type": "uint8", "value": "0x06"}, {"index": "0x8201", "name": "Module Config 1", "subindex": "1", "type": "uint32", "value": "0x00000372"}, {"index": "0x7202", "name": "Module Config 1", "subindex": "1", "type": "uint8", "value": "0xff"}, {"index": "0x7202", "name": "Module Config 2", "subindex": "2", "type": "uint8", "value": "0xff"}, {"index": "0x8301", "name": "Module Config 1", "subindex": "1", "type": "uint32", "value": "0x00000181"}, {"index": "0x6301", "name": "Module Config 1", "subindex": "1", "type": "uint8", "value": "0x06"}, {"index": "0x6301", "name": "Module Config 2", "subindex": "2", "type": "uint8", "value": "0x06"}, {"index": "0x6301", "name": "Module Config 3", "subindex": "3", "type": "uint8", "value": "0x06"}, {"index": "0x6301", "name": "Module Config 4", "subindex": "4", "type": "uint8", "value": "0x06"}], "syncs": [{"direction": "OUTPUT", "index": 0, "pdos": [], "watchdog": "DISABLE"}, {"direction": "INPUT", "index": 1, "pdos": [], "watchdog": "DISABLE"}, {"direction": "OUTPUT", "index": 2, "pdos": ["0x00001600", "0x00001602"], "watchdog": "ENABLE"}, {"direction": "INPUT", "index": 3, "pdos": ["0x00001a00", "0x00001a01", "0x00001a02", "0x00001a03"], "watchdog": "DISABLE"}], "tx_pdo": "0x00001a00, 0x00001a01, 0x00001a02, 0x00001a03", "tx_pdos": [{"index": "0x00008002", "name": "Module State", "subindex": "0", "type": "uint16"}, {"index": "0x00008003", "name": "<PERSON><PERSON><PERSON>", "subindex": "0", "type": "uint32"}, {"index": "0x00006100", "name": "InByte0", "subindex": "1", "type": "uint8"}, {"index": "0x00006100", "name": "InByte1", "subindex": "2", "type": "uint8"}, {"index": "0x00008102", "name": "Module State", "subindex": "0", "type": "uint16"}, {"index": "0x00008103", "name": "<PERSON><PERSON><PERSON>", "subindex": "0", "type": "uint32"}, {"index": "0x00008202", "name": "Module State", "subindex": "0", "type": "uint16"}, {"index": "0x00008203", "name": "<PERSON><PERSON><PERSON>", "subindex": "0", "type": "uint32"}, {"index": "0x00006300", "name": "InByte0", "subindex": "1", "type": "uint8"}, {"index": "0x00006300", "name": "InByte1", "subindex": "2", "type": "uint8"}, {"index": "0x00008302", "name": "Module State", "subindex": "0", "type": "uint16"}, {"index": "0x00008303", "name": "<PERSON><PERSON><PERSON>", "subindex": "0", "type": "uint32"}], "vid": "0x00000099"}]}