variables:
  GIT_STRATEGY: clone
  TIMESTAMP: ${CI_PIPELINE_CREATED_AT}
  NODE_VERSION: "20"  # 明确指定 Node.js 版本
  BUILDDIR: "/tmp/build-${CI_PIPELINE_ID}"  # 使用 pipeline ID 确保唯一性

build:
  tags:
    - lxc
  rules:
    - if: $CI_COMMIT_TAG
      when: always
    - when: never
  
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
      - backend/node_modules/
  
  before_script:
    # 创建临时构建目录并挂载 tmpfs
    - |
      sudo mkdir -p "$BUILDDIR"
      sudo mount -t tmpfs -o size=16G tmpfs "$BUILDDIR"
      sudo chown -R $(id -u):$(id -g) "$BUILDDIR"
      
    # 复制项目文件到临时构建目录
    - cp -r . "$BUILDDIR/"
    - cd "$BUILDDIR"
    
    # 设置 npm 相关镜像
    - npm config set registry https://registry.npmmirror.com
    
  script:
    # 创建环境变量文件
    - |
      cat > .env << EOF
      VUE_APP_VERSION="${CI_COMMIT_TAG:-dev}"
      VUE_APP_GIT_SHA="${CI_COMMIT_SHORT_SHA:-unknown}"
      EOF
    
    # 构建前端
    - echo "开始构建前端..."
    - npm install --verbose
    - npm run build
    
    # 构建后端
    - echo "开始构建后端..."
    - cd backend
    - npm install --verbose
    - npm run esbuild
    - cd ..
    
    # 创建临时目录进行文件整理
    - echo "开始文件整理..."
    - mkdir -p temp/backend
    
    # 复制后端文件
    - echo "检查后端构建文件..."
    - ls -la backend/dist/
    - cp backend/dist/server.mjs temp/backend/
    #- cp backend/dist/server.mjs.map temp/backend/
    
    # 复制前端文件
    - echo "复制前端文件..."
    - cp -r dist temp/dist
    
    # 生成MD5列表 (在 temp 目录下)
    - echo "生成MD5校验文件..."
    - cd temp/dist
    - find . -type f -exec md5sum {} \; | sort > frontend_md5.txt
    - cd ../backend
    - find . -type f -exec md5sum {} \; | sort > ../frontend_md5.txt
    - cd ../.. # 返回 BUILDDIR 根目录
    
    # 创建完整安装包
    - echo "创建完整安装包..."
    - |
      FULL_PACKAGE_NAME="ethercat-webui-${CI_COMMIT_TAG}-${CI_COMMIT_SHORT_SHA}-${TIMESTAMP}"
      FULL_TEMP_DIR="${BUILDDIR}/full_temp" # 完整包临时目录
      mkdir -p "$FULL_TEMP_DIR"
      
      # 复制 temp 目录内容到完整包临时目录
      cp -r temp/* "$FULL_TEMP_DIR/"
      
      # 复制完整包基础文件 (base 目录) 到完整包临时目录
      cp -r base/* "$FULL_TEMP_DIR/"
      chmod +x "$FULL_TEMP_DIR"/install.sh
      
      cd "$FULL_TEMP_DIR"
      # 创建完整包 (在 full_temp 目录下打包)
      tar -czf "${CI_PROJECT_DIR}/${FULL_PACKAGE_NAME}.tar.gz" .
      cd .. # 返回 BUILDDIR 根目录
      
    # 创建增量更新包
    - echo "创建增量更新包..."
    - |
      UPDATE_PACKAGE_NAME="ethercat-webui-update-${CI_COMMIT_TAG}-${CI_COMMIT_SHORT_SHA}-${TIMESTAMP}"
      UPDATE_TEMP_DIR="${BUILDDIR}/update_temp" # 更新包临时目录
      mkdir -p "$UPDATE_TEMP_DIR"
      
      # 复制 temp/dist 和 temp/backend 到更新包临时目录
      cp -r temp/dist "$UPDATE_TEMP_DIR/"
      cp -r temp/backend "$UPDATE_TEMP_DIR/"
      
      # 复制更新包专用文件 (update 目录) 到更新包临时目录
      cp -r update/* "$UPDATE_TEMP_DIR/"
      chmod +x "$UPDATE_TEMP_DIR"/install.sh
      
      cd "$UPDATE_TEMP_DIR"
      # 创建更新包 (在 update_temp 目录下打包)
      tar -czf "${CI_PROJECT_DIR}/${UPDATE_PACKAGE_NAME}.tar.gz" .
      cd .. # 返回 BUILDDIR 根目录
      
    # 清理临时文件
    - echo "清理临时文件..."
    - rm -rf temp # 删除顶层 temp 目录
    
  after_script:
    - echo "构建完成状态：$?"
    # 清理临时构建目录
    - |
      if [ -d "$BUILDDIR" ]; then
        cd /
        sudo umount "$BUILDDIR" || true
        sudo rm -rf "$BUILDDIR"
      fi
    
  artifacts:
    name: "ethercat-webui-${CI_COMMIT_TAG}-${CI_COMMIT_SHORT_SHA}"
    paths:
      - "*.tar.gz"
    expire_in: never 