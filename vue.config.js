const { defineConfig } = require('@vue/cli-service')
const path = require('path');
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');

module.exports = defineConfig({
  parallel: true,  // 启用多线程
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          javascriptEnabled: true,
          modifyVars: {
            'primary-color': '#007AFF',
            'link-color': '#007AFF',
            'border-radius-base': '12px',
          },
          additionalData: `
            @import "@/styles/variables.less";
          `
        },
      },
    },
  },
  devServer: {
    proxy: {
      '/api': {
        target: 'http://************:3000',
        changeOrigin: true
       }
      // '/ws': {
      //   target: 'ws://************:3000',
      //   ws: true
      // }
    }
  },
  transpileDependencies: true,
  configureWebpack: {
    plugins: [
      new MonacoWebpackPlugin({
        languages: ['json', 'javascript', 'typescript', 'html', 'css', 'less']
      })
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      },
      fallback: {
        "timers": require.resolve("timers-browserify"),
        "stream": require.resolve("stream-browserify"),
        "buffer": require.resolve("buffer/"),
        "util": require.resolve("util/"),
        "events": require.resolve("events/"),
      }
    }
  },
  chainWebpack: config => {
    config.plugin('terser').use(TerserPlugin, [{
      parallel: true // terser启用多线程
    }])
    config.optimization.splitChunks({
      chunks: 'all',
      maxInitialRequests: Infinity,
      minSize: 20000
    })
    config.module
      .rule('js')
      .use('esbuild-loader')
      .loader('esbuild-loader')
      .options({
        target: 'es2015'
      })
  },
  productionSourceMap: false,  // 禁用源码映射
}) 