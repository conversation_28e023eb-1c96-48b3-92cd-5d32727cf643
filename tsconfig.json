{"compilerOptions": {"target": "es2018", "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/**/*", "src/types/*.d.ts"], "exclude": ["node_modules"]}