#include "sdk.h"
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h> // For sleep(), pause()
#include <signal.h> // For signal handling

// Global flag to indicate if shutdown is in progress
static volatile sig_atomic_t g_shutdown_request = 0;

// Signal handler function
void signal_handler(int sig) {
    fprintf(stdout, "\nMiddleware Host: Signal %d received. Initiating shutdown...\n", sig);
    g_shutdown_request = 1;
}

int main(int argc, char *argv[]) {
    int core_to_pin = -1; // Default: no specific core pinning for the SDK's cyclic task via host

    // Basic argument parsing for core pinning
    if (argc > 1) {
        core_to_pin = atoi(argv[1]);
        fprintf(stdout, "Middleware Host: Requested core pinning for SDK cyclic task: %d\n", core_to_pin);
    }

    // Setup signal handling
    struct sigaction sa;
    sa.sa_handler = signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0; // Or SA_RESTART to restart syscalls if interrupted by this signal

    if (sigaction(SIGINT, &sa, NULL) == -1) {
        perror("Middleware Host: Error setting SIGINT handler");
        return EXIT_FAILURE;
    }
    if (sigaction(SIGTERM, &sa, NULL) == -1) {
        perror("Middleware Host: Error setting SIGTERM handler");
        return EXIT_FAILURE;
    }

    fprintf(stdout, "Middleware Host: Initializing SDK\n");
    if (sdk_init() != 0) {
        fprintf(stderr, "Middleware Host: SDK initialization failed. Exiting.\n");
        return EXIT_FAILURE;
    }
    fprintf(stdout, "Middleware Host: SDK initialized successfully.\n");

    // Optional: Set core affinity for the SDK's cyclic task
    // (Uncomment the function in sdk.h and implement in sdk.c if you want to use this)
    /*
    if (core_to_pin >= 0) {
        if (sdk_set_core_affinity(core_to_pin) == 0) {
            fprintf(stdout, "Middleware Host: Requested SDK cyclic task affinity to core %d.\n", core_to_pin);
        } else {
            fprintf(stderr, "Middleware Host: Failed to set SDK cyclic task affinity to core %d.\n", core_to_pin);
            // Continue without it, or treat as fatal error depending on requirements
        }
    }
    */

    fprintf(stdout, "Middleware Host: Starting SDK cyclic task...\n");
    if (sdk_start_cyclic_task() != 0) {
        fprintf(stderr, "Middleware Host: Failed to start SDK cyclic task. Cleaning up and exiting.\n");
        sdk_cleanup();
        return EXIT_FAILURE;
    }
    fprintf(stdout, "Middleware Host: SDK cyclic task started. Running until shutdown signal...\n");

    // Keep the host running until a shutdown signal is received
    while (!g_shutdown_request) {
        pause(); // pause() waits for any signal
    }

    fprintf(stdout, "Middleware Host: Shutdown request received. Stopping SDK task...\n");
    sdk_stop_cyclic_task();
    fprintf(stdout, "Middleware Host: SDK task stopped.\n");

    fprintf(stdout, "Middleware Host: Cleaning up SDK resources...\n");
    sdk_cleanup();
    fprintf(stdout, "Middleware Host: SDK cleanup complete. Exiting.\n");

    return EXIT_SUCCESS;
} 