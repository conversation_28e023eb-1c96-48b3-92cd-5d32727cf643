{"template": [{"name": "DS402常用控制位", "rx_pdos": [{"name": "control_word", "comment": "控制字", "index": "0x6040", "subindex": "0", "type": "uint16", "bitlen": 16}, {"name": "target_speed", "index": "0x60FF", "subindex": "0", "type": "int32", "bitlen": 32, "comment": "目标速度"}, {"name": "quick_stop_option_code", "comment": "快速停机方式选择", "index": "0x605A", "subindex": "0", "type": "uint8", "bitlen": 8}, {"name": "shutdown_option_code", "comment": "电机减速停机方式选择", "index": "0x605B", "subindex": "0", "type": "uint8", "bitlen": 8}, {"name": "disable_option_code", "comment": "断使能停机方式选择", "index": "0x605C", "subindex": "0", "type": "uint8", "bitlen": 8}, {"name": "halt_option_code", "comment": "暂停停机方式选择", "index": "0x605D", "subindex": "0", "type": "uint8", "bitlen": 8}, {"name": "stop_code", "comment": "报警停止代码", "index": "0x605E", "subindex": "0", "type": "uint8", "bitlen": 8}, {"name": "operation_mode", "comment": "操作模式设置", "index": "0x6060", "subindex": "0", "type": "uint8", "bitlen": 8}, {"name": "target_position", "comment": "目标位置", "index": "0x607A", "subindex": "0", "type": "int32", "bitlen": 32}, {"name": "home_offset", "comment": "原点偏置", "index": "0x607C", "subindex": "0", "type": "int32", "bitlen": 32}, {"name": "soft_limit_min", "comment": "软限位最小值", "index": "0x607D", "subindex": "1", "type": "int32", "bitlen": 32}, {"name": "soft_limit_max", "comment": "软限位最大值", "index": "0x607D", "subindex": "2", "type": "int32", "bitlen": 32}, {"name": "motor_direction", "comment": "电机运行方向", "index": "0x607E", "subindex": "0", "type": "uint8", "bitlen": 8}, {"name": "protocol_speed", "comment": "协议速度", "index": "0x6081", "subindex": "0", "type": "int32", "bitlen": 32}, {"name": "protocol_acceleration", "comment": "协议加速度", "index": "0x6083", "subindex": "0", "type": "uint32", "bitlen": 32}, {"name": "protocol_deceleration", "comment": "协议减速度", "index": "0x6084", "subindex": "0", "type": "uint32", "bitlen": 32}, {"name": "quick_stop_deceleration", "comment": "急停减速度", "index": "0x6085", "subindex": "0", "type": "uint32", "bitlen": 32}, {"name": "torque_slope", "comment": "转矩斜率", "index": "0x6087", "subindex": "0", "type": "uint32", "bitlen": 32}, {"name": "home_method", "comment": "回零方法", "index": "0x6098", "subindex": "0", "type": "int8", "bitlen": 8}, {"name": "home_high_speed", "comment": "回零高速", "index": "0x6099", "subindex": "1", "type": "int32", "bitlen": 32}, {"name": "home_low_speed", "comment": "回零低速", "index": "0x6099", "subindex": "2", "type": "int32", "bitlen": 32}, {"name": "home_acceleration", "comment": "回零加减速度", "index": "0x609A", "subindex": "0", "type": "uint32", "bitlen": 32}], "tx_pdos": [{"name": "status_word", "comment": "状态字", "index": "0x6041", "subindex": "0", "type": "uint16", "bitlen": 16}, {"name": "error_code", "comment": "错误代码", "index": "0x603F", "subindex": "0", "type": "uint16", "bitlen": 16}, {"name": "operation_mode_display", "comment": "操作模式显示", "index": "0x6061", "subindex": "0", "type": "uint8", "bitlen": 8}, {"name": "actual_position", "comment": "实际反馈位置", "index": "0x6064", "subindex": "0", "type": "int32", "bitlen": 32}, {"name": "position_error_window", "comment": "位置偏差窗口", "index": "0x6065", "subindex": "0", "type": "int32", "bitlen": 32}, {"name": "actual_velocity", "comment": "实际反馈速度", "index": "0x606C", "subindex": "0", "type": "int32", "bitlen": 32}, {"name": "velocity_window", "comment": "速度窗口", "index": "0x606D", "subindex": "0", "type": "uint16", "bitlen": 16}, {"name": "zero_speed_threshold", "comment": "零速门限", "index": "0x606F", "subindex": "0", "type": "uint16", "bitlen": 16}, {"name": "actual_torque", "comment": "实际转矩", "index": "0x6077", "subindex": "0", "type": "int16", "bitlen": 16}, {"name": "dc_link_voltage", "comment": "直流母线电压", "index": "0x6079", "subindex": "0", "type": "uint32", "bitlen": 32}, {"name": "protocol_max_acceleration", "comment": "协议最大加速度", "index": "0x60C5", "subindex": "0", "type": "uint32", "bitlen": 32}, {"name": "protocol_max_deceleration", "comment": "协议最大减速度", "index": "0x60C6", "subindex": "0", "type": "uint32", "bitlen": 32}]}, {"name": "EV1616DN", "rx_pdos": [{"name": "Output_1", "index": "0x7010", "subindex": "1", "type": "bool", "bitlen": 1, "comment": "IO输出1"}, {"name": "Output_2", "index": "0x7010", "subindex": "2", "type": "bool", "bitlen": 1, "comment": "IO输出2"}, {"name": "Output_3", "index": "0x7010", "subindex": "3", "type": "bool", "bitlen": 1, "comment": "IO输出3"}, {"name": "Output_4", "index": "0x7010", "subindex": "4", "type": "bool", "bitlen": 1, "comment": "IO输出4"}, {"name": "Output_5", "index": "0x7010", "subindex": "5", "type": "bool", "bitlen": 1, "comment": "IO输出5"}, {"name": "Output_6", "index": "0x7010", "subindex": "6", "type": "bool", "bitlen": 1, "comment": "IO输出6"}, {"name": "Output_7", "index": "0x7010", "subindex": "7", "type": "bool", "bitlen": 1, "comment": "IO输出7"}, {"name": "Output_8", "index": "0x7010", "subindex": "8", "type": "bool", "bitlen": 1, "comment": "IO输出8"}, {"name": "Output_9", "index": "0x7010", "subindex": "9", "type": "bool", "bitlen": 1, "comment": "IO输出9"}, {"name": "Output_10", "index": "0x7010", "subindex": "10", "type": "bool", "bitlen": 1, "comment": "IO输出10"}, {"name": "Output_11", "index": "0x7010", "subindex": "11", "type": "bool", "bitlen": 1, "comment": "IO输出11"}, {"name": "Output_12", "index": "0x7010", "subindex": "12", "type": "bool", "bitlen": 1, "comment": "IO输出12"}, {"name": "Output_13", "index": "0x7010", "subindex": "13", "type": "bool", "bitlen": 1, "comment": "IO输出13"}, {"name": "Output_14", "index": "0x7010", "subindex": "14", "type": "bool", "bitlen": 1, "comment": "IO输出14"}, {"name": "Output_15", "index": "0x7010", "subindex": "15", "type": "bool", "bitlen": 1, "comment": "IO输出15"}, {"name": "Output_16", "index": "0x7010", "subindex": "16", "type": "bool", "bitlen": 1, "comment": "IO输出16"}], "tx_pdos": [{"name": "Input_1", "index": "0x6000", "subindex": "1", "type": "bool", "bitlen": 1, "comment": "IO输入1"}, {"name": "Input_2", "index": "0x6000", "subindex": "2", "type": "bool", "bitlen": 1, "comment": "IO输入2"}, {"name": "Input_3", "index": "0x6000", "subindex": "3", "type": "bool", "bitlen": 1, "comment": "IO输入3"}, {"name": "Input_4", "index": "0x6000", "subindex": "4", "type": "bool", "bitlen": 1, "comment": "IO输入4"}, {"name": "Input_5", "index": "0x6000", "subindex": "5", "type": "bool", "bitlen": 1, "comment": "IO输入5"}, {"name": "Input_6", "index": "0x6000", "subindex": "6", "type": "bool", "bitlen": 1, "comment": "IO输入6"}, {"name": "Input_7", "index": "0x6000", "subindex": "7", "type": "bool", "bitlen": 1, "comment": "IO输入7"}, {"name": "Input_8", "index": "0x6000", "subindex": "8", "type": "bool", "bitlen": 1, "comment": "IO输入8"}, {"name": "Input_9", "index": "0x6000", "subindex": "9", "type": "bool", "bitlen": 1, "comment": "IO输入9"}, {"name": "Input_10", "index": "0x6000", "subindex": "16", "type": "bool", "bitlen": 1, "comment": "IO输入16"}, {"name": "Input_11", "index": "0x6000", "subindex": "17", "type": "bool", "bitlen": 1, "comment": "IO输入17"}, {"name": "Input_12", "index": "0x6000", "subindex": "18", "type": "bool", "bitlen": 1, "comment": "IO输入12"}, {"name": "Input_13", "index": "0x6000", "subindex": "19", "type": "bool", "bitlen": 1, "comment": "IO输入13"}, {"name": "Input_14", "index": "0x6000", "subindex": "20", "type": "bool", "bitlen": 1, "comment": "IO输入14"}, {"name": "Input_15", "index": "0x6000", "subindex": "21", "type": "bool", "bitlen": 1, "comment": "IO输入15"}, {"name": "Input_16", "index": "0x6000", "subindex": "22", "type": "bool", "bitlen": 1, "comment": "IO输入16"}]}]}