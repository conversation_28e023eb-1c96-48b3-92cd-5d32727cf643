#!/bin/bash

# 定义目标目录
TARGET_DIR="/app/ethercat-web-ui"

# 检查是否有权限创建目录
if [ "$EUID" -ne 0 ]; then
    echo "请以 root 权限运行此脚本."
    exit 1
fi

# 检测系统架构并选择对应的 pocketbase 二进制
ARCH=$(uname -m)
if [ "$ARCH" = "x86_64" ]; then
    POCKETBASE_BIN="pocketbase_amd64"
elif [ "$ARCH" = "aarch64" ]; then
    POCKETBASE_BIN="pocketbase_arm64"
else
    echo "不支持的系统架构: $ARCH"
    exit 1
fi

# 创建 /app/ethercat-ui 目录
echo "创建目标目录 $TARGET_DIR..."
mkdir -p "$TARGET_DIR"

# 复制 backend 和 dist 到目标目录
echo "复制 ./backend 到 $TARGET_DIR..."
cp -r ./backend "$TARGET_DIR/"
echo "复制 ./dist 到 $TARGET_DIR..."
cp -r ./dist "$TARGET_DIR/"

# 创建 systemd 服务文件
SERVICE_FILE="/etc/systemd/system/ethercat-web-ui.service"
echo "创建 systemd 服务文件 $SERVICE_FILE..."

cat <<EOL > "$SERVICE_FILE"
[Unit]
Description=Ethercat Web UI Server with PocketBase
After=network.target

[Service]
Type=simple
WorkingDirectory=$TARGET_DIR/backend
ExecStart=/bin/bash -c "./${POCKETBASE_BIN} serve & node server.mjs"
Restart=always

[Install]
WantedBy=multi-user.target
EOL

# 设置正确的权限
echo "设置服务文件权限..."
chmod 644 "$SERVICE_FILE"

# 设置 pocketbase 可执行权限
echo "设置 pocketbase 可执行权限..."
chmod +x "$TARGET_DIR/backend/${POCKETBASE_BIN}"

# 重新加载 systemd 并启用服务
echo "重新加载 systemd 配置..."
systemctl daemon-reload

echo "启用 ethercat-web-ui 服务..."
systemctl enable ethercat-web-ui.service

echo "启动 ethercat-web-ui 服务..."
systemctl start ethercat-web-ui.service

# 显示服务状态
echo "检查服务状态..."
systemctl status ethercat-web-ui.service

echo "安装完成！"
