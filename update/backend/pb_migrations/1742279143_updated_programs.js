/// <reference path="../pb_data/types.d.ts" />
migrate((db) => {
  const dao = new Dao(db)
  const collection = dao.findCollectionByNameOrId("um078ovy9kbc6v1")

  // add
  collection.schema.addField(new SchemaField({
    "system": false,
    "id": "5rwqki4a",
    "name": "programCpu",
    "type": "text",
    "required": false,
    "presentable": false,
    "unique": false,
    "options": {
      "min": null,
      "max": null,
      "pattern": ""
    }
  }))

  // add
  collection.schema.addField(new SchemaField({
    "system": false,
    "id": "brjf2ac9",
    "name": "middlewareCpu",
    "type": "text",
    "required": false,
    "presentable": false,
    "unique": false,
    "options": {
      "min": null,
      "max": null,
      "pattern": ""
    }
  }))

  return dao.saveCollection(collection)
}, (db) => {
  const dao = new Dao(db)
  const collection = dao.findCollectionByNameOrId("um078ovy9kbc6v1")

  // remove
  collection.schema.removeField("5rwqki4a")

  // remove
  collection.schema.removeField("brjf2ac9")

  return dao.saveCollection(collection)
})
