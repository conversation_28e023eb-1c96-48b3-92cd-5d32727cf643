import requests

ip = '************'
url = f'http://{ip}:3000/api/programs/external-upload'

print(url)

programName = 'WC'

data = {
  'name': programName,
  'masterIndex': 0,
  'force': '1'
}

files = {
  'program': ('publish.zip', open('publish.zip', 'rb'), 'application/zip'),
  'config': ('slave.json', open('slave.json', 'rb'), 'application/json')
}
# data为表单数据
response = requests.post(url, data=data, files=files)
print(response.status_code)
# print(response.text)
print(response.json())