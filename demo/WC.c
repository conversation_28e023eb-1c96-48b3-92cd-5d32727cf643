
#include <errno.h>
#include <signal.h>
#include <stdio.h>
#include <string.h>
#include <sys/resource.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <sched.h>
#include <sys/stat.h>
#include <sys/shm.h>
#include <time.h>
#include <pthread.h>  // 添加pthread头文件

#include "ecrt.h"

// Forward declarations
void cleanup_shm(void);
void signal_handler(int sig);
void check_slave_config_states(void);

// 定义PDO配置线程的参数结构
typedef struct {
    ec_slave_config_t *slave_config;
    ec_sync_info_t *sync_info;
    int *result;
} config_thread_param_t;

// PDO配置线程函数
void* config_slave_thread(void *arg) {
    config_thread_param_t *param = (config_thread_param_t *)arg;
    *(param->result) = ecrt_slave_config_pdos(param->slave_config, EC_END, param->sync_info);
    return NULL;
}

// 定义DC配置线程的参数结构
typedef struct {
    ec_slave_config_t *slave_config;
    uint16_t assign_activate;
    uint32_t sync0_cycle;
    uint32_t sync0_shift;
    uint32_t sync1_cycle;
    uint32_t sync1_shift;
    int *result;
} dc_config_thread_param_t;

// DC配置线程函数
void* dc_config_thread(void *arg) {
    dc_config_thread_param_t *param = (dc_config_thread_param_t *)arg;
    int ret = ecrt_slave_config_dc(
        param->slave_config,
        param->assign_activate,
        param->sync0_cycle,
        param->sync0_shift,
        param->sync1_cycle,
        param->sync1_shift
    );
    *(param->result) = ret;
    return NULL;
}

// Global control flags
static int run = 1;  // 控制主循环的标志
static int last_cycle = 0;  // 标记最后一个循环
static int all_slaves_op = 0;  // Add this flag to track ALL OP state


// Signal handler implementation
void signal_handler(int sig) {
    printf("\nSignal %d received, will exit after next cycle...\n", sig);
    last_cycle = 1;  // 设置最后一个循环标志
}

/* Time definitions */
#define NSEC_PER_SEC (1000000000L)
#define TIMESPEC2NS(T) ((uint64_t) (T).tv_sec * NSEC_PER_SEC + (T).tv_nsec)
#define CLOCK_TO_USE CLOCK_MONOTONIC

/* Shared memory configuration */
#define ETHERCAT_SHM_FILE "24tm1c9ubwr0uyp_WC_shm"
#define ETHERCAT_SHM_SIZE (sizeof(ethercat_shm_t))

/* Shared memory structure */
typedef struct {
    int shm_slave0_online_status; /* 从站0在线状态 */
    int shm_slave0_operational_status; /* 从站0运行状态 */
    int shm_slave0_al_state; /* 从站0AL状态 */

    int shm_slave0_rx_shm_slave0_rx_0x6040_control_word; /* 控制字 */
    int shm_slave0_rx_shm_slave0_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave0_rx_shm_slave0_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave0_tx_shm_slave0_tx_0x6041_status_word; /* 状态字 */
    int shm_slave1_online_status; /* 从站1在线状态 */
    int shm_slave1_operational_status; /* 从站1运行状态 */
    int shm_slave1_al_state; /* 从站1AL状态 */

    int shm_slave1_rx_shm_slave1_rx_0x6040_control_word; /* 控制字 */
    int shm_slave1_rx_shm_slave1_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave1_rx_shm_slave1_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave1_tx_shm_slave1_tx_0x6041_status_word; /* 状态字 */
    int shm_slave2_online_status; /* 从站2在线状态 */
    int shm_slave2_operational_status; /* 从站2运行状态 */
    int shm_slave2_al_state; /* 从站2AL状态 */

    int shm_slave2_rx_shm_slave2_rx_0x6040_control_word; /* 控制字 */
    int shm_slave2_rx_shm_slave2_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave2_rx_shm_slave2_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave2_tx_shm_slave2_tx_0x6041_status_word; /* 状态字 */
    int shm_slave3_online_status; /* 从站3在线状态 */
    int shm_slave3_operational_status; /* 从站3运行状态 */
    int shm_slave3_al_state; /* 从站3AL状态 */

    int shm_slave3_rx_shm_slave3_rx_0x6040_control_word; /* 控制字 */
    int shm_slave3_rx_shm_slave3_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave3_rx_shm_slave3_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave3_tx_shm_slave3_tx_0x6041_status_word; /* 状态字 */
    int shm_slave4_online_status; /* 从站4在线状态 */
    int shm_slave4_operational_status; /* 从站4运行状态 */
    int shm_slave4_al_state; /* 从站4AL状态 */

    int shm_slave4_rx_shm_slave4_rx_0x6040_control_word; /* 控制字 */
    int shm_slave4_rx_shm_slave4_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave4_rx_shm_slave4_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave4_tx_shm_slave4_tx_0x6041_status_word; /* 状态字 */
    int shm_slave5_online_status; /* 从站5在线状态 */
    int shm_slave5_operational_status; /* 从站5运行状态 */
    int shm_slave5_al_state; /* 从站5AL状态 */

    int shm_slave5_rx_shm_slave5_rx_0x6040_control_word; /* 控制字 */
    int shm_slave5_rx_shm_slave5_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave5_rx_shm_slave5_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave5_tx_shm_slave5_tx_0x6041_status_word; /* 状态字 */
    int shm_slave6_online_status; /* 从站6在线状态 */
    int shm_slave6_operational_status; /* 从站6运行状态 */
    int shm_slave6_al_state; /* 从站6AL状态 */

    int shm_slave6_rx_shm_slave6_rx_0x6040_control_word; /* 控制字 */
    int shm_slave6_rx_shm_slave6_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave6_rx_shm_slave6_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave6_tx_shm_slave6_tx_0x6041_status_word; /* 状态字 */
    int shm_slave7_online_status; /* 从站7在线状态 */
    int shm_slave7_operational_status; /* 从站7运行状态 */
    int shm_slave7_al_state; /* 从站7AL状态 */

    int shm_slave7_rx_shm_slave7_rx_0x6040_control_word; /* 控制字 */
    int shm_slave7_rx_shm_slave7_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave7_rx_shm_slave7_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave7_tx_shm_slave7_tx_0x6041_status_word; /* 状态字 */
    int shm_slave8_online_status; /* 从站8在线状态 */
    int shm_slave8_operational_status; /* 从站8运行状态 */
    int shm_slave8_al_state; /* 从站8AL状态 */

    int shm_slave8_rx_shm_slave8_rx_0x6040_control_word; /* 控制字 */
    int shm_slave8_rx_shm_slave8_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave8_rx_shm_slave8_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave8_tx_shm_slave8_tx_0x6041_status_word; /* 状态字 */
    int shm_slave9_online_status; /* 从站9在线状态 */
    int shm_slave9_operational_status; /* 从站9运行状态 */
    int shm_slave9_al_state; /* 从站9AL状态 */

    int shm_slave9_rx_shm_slave9_rx_0x6040_control_word; /* 控制字 */
    int shm_slave9_rx_shm_slave9_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave9_rx_shm_slave9_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave9_tx_shm_slave9_tx_0x6041_status_word; /* 状态字 */
    int shm_slave10_online_status; /* 从站10在线状态 */
    int shm_slave10_operational_status; /* 从站10运行状态 */
    int shm_slave10_al_state; /* 从站10AL状态 */

    int shm_slave10_rx_shm_slave10_rx_0x6040_control_word; /* 控制字 */
    int shm_slave10_rx_shm_slave10_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave10_rx_shm_slave10_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave10_tx_shm_slave10_tx_0x6041_status_word; /* 状态字 */
    int shm_slave11_online_status; /* 从站11在线状态 */
    int shm_slave11_operational_status; /* 从站11运行状态 */
    int shm_slave11_al_state; /* 从站11AL状态 */

    int shm_slave11_rx_shm_slave11_rx_0x6040_control_word; /* 控制字 */
    int shm_slave11_rx_shm_slave11_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave11_rx_shm_slave11_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave11_tx_shm_slave11_tx_0x6041_status_word; /* 状态字 */
    int shm_slave12_online_status; /* 从站12在线状态 */
    int shm_slave12_operational_status; /* 从站12运行状态 */
    int shm_slave12_al_state; /* 从站12AL状态 */

    int shm_slave12_rx_shm_slave12_rx_0x6040_control_word; /* 控制字 */
    int shm_slave12_rx_shm_slave12_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave12_rx_shm_slave12_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave12_tx_shm_slave12_tx_0x6041_status_word; /* 状态字 */
    int shm_slave13_online_status; /* 从站13在线状态 */
    int shm_slave13_operational_status; /* 从站13运行状态 */
    int shm_slave13_al_state; /* 从站13AL状态 */

    int shm_slave13_rx_shm_slave13_rx_0x6040_control_word; /* 控制字 */
    int shm_slave13_rx_shm_slave13_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave13_rx_shm_slave13_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave13_tx_shm_slave13_tx_0x6041_status_word; /* 状态字 */
    int shm_slave14_online_status; /* 从站14在线状态 */
    int shm_slave14_operational_status; /* 从站14运行状态 */
    int shm_slave14_al_state; /* 从站14AL状态 */

    int shm_slave14_rx_shm_slave14_rx_0x6040_control_word; /* 控制字 */
    int shm_slave14_rx_shm_slave14_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave14_rx_shm_slave14_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave14_tx_shm_slave14_tx_0x6041_status_word; /* 状态字 */
    int shm_slave15_online_status; /* 从站15在线状态 */
    int shm_slave15_operational_status; /* 从站15运行状态 */
    int shm_slave15_al_state; /* 从站15AL状态 */

    int shm_slave15_rx_shm_slave15_rx_0x6040_control_word; /* 控制字 */
    int shm_slave15_rx_shm_slave15_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave15_rx_shm_slave15_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave15_tx_shm_slave15_tx_0x6041_status_word; /* 状态字 */
    int shm_slave16_online_status; /* 从站16在线状态 */
    int shm_slave16_operational_status; /* 从站16运行状态 */
    int shm_slave16_al_state; /* 从站16AL状态 */

    int shm_slave16_rx_shm_slave16_rx_0x6040_control_word; /* 控制字 */
    int shm_slave16_rx_shm_slave16_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave16_rx_shm_slave16_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave16_tx_shm_slave16_tx_0x6041_status_word; /* 状态字 */
    int shm_slave17_online_status; /* 从站17在线状态 */
    int shm_slave17_operational_status; /* 从站17运行状态 */
    int shm_slave17_al_state; /* 从站17AL状态 */

    int shm_slave17_rx_shm_slave17_rx_0x6040_control_word; /* 控制字 */
    int shm_slave17_rx_shm_slave17_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave17_rx_shm_slave17_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave17_tx_shm_slave17_tx_0x6041_status_word; /* 状态字 */
    int shm_slave18_online_status; /* 从站18在线状态 */
    int shm_slave18_operational_status; /* 从站18运行状态 */
    int shm_slave18_al_state; /* 从站18AL状态 */

    int shm_slave18_rx_shm_slave18_rx_0x6040_control_word; /* 控制字 */
    int shm_slave18_rx_shm_slave18_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave18_rx_shm_slave18_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave18_tx_shm_slave18_tx_0x6041_status_word; /* 状态字 */
    int shm_slave19_online_status; /* 从站19在线状态 */
    int shm_slave19_operational_status; /* 从站19运行状态 */
    int shm_slave19_al_state; /* 从站19AL状态 */

    int shm_slave19_rx_shm_slave19_rx_0x6040_control_word; /* 控制字 */
    int shm_slave19_rx_shm_slave19_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave19_rx_shm_slave19_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave19_tx_shm_slave19_tx_0x6041_status_word; /* 状态字 */
    int shm_slave20_online_status; /* 从站20在线状态 */
    int shm_slave20_operational_status; /* 从站20运行状态 */
    int shm_slave20_al_state; /* 从站20AL状态 */

    int shm_slave20_rx_shm_slave20_rx_0x6040_control_word; /* 控制字 */
    int shm_slave20_rx_shm_slave20_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave20_rx_shm_slave20_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave20_tx_shm_slave20_tx_0x6041_status_word; /* 状态字 */
    int shm_slave21_online_status; /* 从站21在线状态 */
    int shm_slave21_operational_status; /* 从站21运行状态 */
    int shm_slave21_al_state; /* 从站21AL状态 */

    int shm_slave21_rx_shm_slave21_rx_0x6040_control_word; /* 控制字 */
    int shm_slave21_rx_shm_slave21_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave21_rx_shm_slave21_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave21_tx_shm_slave21_tx_0x6041_status_word; /* 状态字 */
    int shm_slave22_online_status; /* 从站22在线状态 */
    int shm_slave22_operational_status; /* 从站22运行状态 */
    int shm_slave22_al_state; /* 从站22AL状态 */

    int shm_slave22_rx_shm_slave22_rx_0x6040_control_word; /* 控制字 */
    int shm_slave22_rx_shm_slave22_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave22_rx_shm_slave22_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave22_tx_shm_slave22_tx_0x6041_status_word; /* 状态字 */
    int shm_slave23_online_status; /* 从站23在线状态 */
    int shm_slave23_operational_status; /* 从站23运行状态 */
    int shm_slave23_al_state; /* 从站23AL状态 */

    int shm_slave23_rx_shm_slave23_rx_0x6040_control_word; /* 控制字 */
    int shm_slave23_rx_shm_slave23_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave23_rx_shm_slave23_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave23_tx_shm_slave23_tx_0x6041_status_word; /* 状态字 */
    int shm_slave24_online_status; /* 从站24在线状态 */
    int shm_slave24_operational_status; /* 从站24运行状态 */
    int shm_slave24_al_state; /* 从站24AL状态 */

    int shm_slave24_rx_shm_slave24_rx_0x6040_control_word; /* 控制字 */
    int shm_slave24_rx_shm_slave24_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave24_rx_shm_slave24_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave24_tx_shm_slave24_tx_0x6041_status_word; /* 状态字 */
    int shm_slave25_online_status; /* 从站25在线状态 */
    int shm_slave25_operational_status; /* 从站25运行状态 */
    int shm_slave25_al_state; /* 从站25AL状态 */

    int shm_slave25_rx_shm_slave25_rx_0x6040_control_word; /* 控制字 */
    int shm_slave25_rx_shm_slave25_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave25_rx_shm_slave25_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave25_tx_shm_slave25_tx_0x6041_status_word; /* 状态字 */
    int shm_slave26_online_status; /* 从站26在线状态 */
    int shm_slave26_operational_status; /* 从站26运行状态 */
    int shm_slave26_al_state; /* 从站26AL状态 */

    int shm_slave26_rx_shm_slave26_rx_0x6040_control_word; /* 控制字 */
    int shm_slave26_rx_shm_slave26_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave26_rx_shm_slave26_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave26_tx_shm_slave26_tx_0x6041_status_word; /* 状态字 */
    int shm_slave27_online_status; /* 从站27在线状态 */
    int shm_slave27_operational_status; /* 从站27运行状态 */
    int shm_slave27_al_state; /* 从站27AL状态 */

    int shm_slave27_rx_shm_slave27_rx_0x6040_control_word; /* 控制字 */
    int shm_slave27_rx_shm_slave27_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave27_rx_shm_slave27_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave27_tx_shm_slave27_tx_0x6041_status_word; /* 状态字 */
    int shm_slave28_online_status; /* 从站28在线状态 */
    int shm_slave28_operational_status; /* 从站28运行状态 */
    int shm_slave28_al_state; /* 从站28AL状态 */

    int shm_slave28_rx_shm_slave28_rx_0x6040_control_word; /* 控制字 */
    int shm_slave28_rx_shm_slave28_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave28_rx_shm_slave28_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave28_tx_shm_slave28_tx_0x6041_status_word; /* 状态字 */
    int shm_slave29_online_status; /* 从站29在线状态 */
    int shm_slave29_operational_status; /* 从站29运行状态 */
    int shm_slave29_al_state; /* 从站29AL状态 */

    int shm_slave29_rx_shm_slave29_rx_0x6040_control_word; /* 控制字 */
    int shm_slave29_rx_shm_slave29_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave29_rx_shm_slave29_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave29_tx_shm_slave29_tx_0x6041_status_word; /* 状态字 */
    int shm_slave30_online_status; /* 从站30在线状态 */
    int shm_slave30_operational_status; /* 从站30运行状态 */
    int shm_slave30_al_state; /* 从站30AL状态 */

    int shm_slave30_rx_shm_slave30_rx_0x6040_control_word; /* 控制字 */
    int shm_slave30_rx_shm_slave30_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave30_rx_shm_slave30_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave30_tx_shm_slave30_tx_0x6041_status_word; /* 状态字 */
    int shm_slave31_online_status; /* 从站31在线状态 */
    int shm_slave31_operational_status; /* 从站31运行状态 */
    int shm_slave31_al_state; /* 从站31AL状态 */

    int shm_slave31_rx_shm_slave31_rx_0x6040_control_word; /* 控制字 */
    int shm_slave31_rx_shm_slave31_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave31_rx_shm_slave31_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave31_tx_shm_slave31_tx_0x6041_status_word; /* 状态字 */
    int shm_slave32_online_status; /* 从站32在线状态 */
    int shm_slave32_operational_status; /* 从站32运行状态 */
    int shm_slave32_al_state; /* 从站32AL状态 */

    int shm_slave32_rx_shm_slave32_rx_0x6040_control_word; /* 控制字 */
    int shm_slave32_rx_shm_slave32_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave32_rx_shm_slave32_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave32_tx_shm_slave32_tx_0x6041_status_word; /* 状态字 */
    int shm_slave33_online_status; /* 从站33在线状态 */
    int shm_slave33_operational_status; /* 从站33运行状态 */
    int shm_slave33_al_state; /* 从站33AL状态 */

    int shm_slave33_rx_shm_slave33_rx_0x6040_control_word; /* 控制字 */
    int shm_slave33_rx_shm_slave33_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave33_rx_shm_slave33_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave33_tx_shm_slave33_tx_0x6041_status_word; /* 状态字 */
    int shm_slave34_online_status; /* 从站34在线状态 */
    int shm_slave34_operational_status; /* 从站34运行状态 */
    int shm_slave34_al_state; /* 从站34AL状态 */

    int shm_slave34_rx_shm_slave34_rx_0x6040_control_word; /* 控制字 */
    int shm_slave34_rx_shm_slave34_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave34_rx_shm_slave34_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave34_tx_shm_slave34_tx_0x6041_status_word; /* 状态字 */
    int shm_slave35_online_status; /* 从站35在线状态 */
    int shm_slave35_operational_status; /* 从站35运行状态 */
    int shm_slave35_al_state; /* 从站35AL状态 */

    int shm_slave35_rx_shm_slave35_rx_0x6040_control_word; /* 控制字 */
    int shm_slave35_rx_shm_slave35_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave35_rx_shm_slave35_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave35_tx_shm_slave35_tx_0x6041_status_word; /* 状态字 */
    int shm_slave36_online_status; /* 从站36在线状态 */
    int shm_slave36_operational_status; /* 从站36运行状态 */
    int shm_slave36_al_state; /* 从站36AL状态 */

    int shm_slave36_rx_shm_slave36_rx_0x6040_control_word; /* 控制字 */
    int shm_slave36_rx_shm_slave36_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave36_rx_shm_slave36_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave36_tx_shm_slave36_tx_0x6041_status_word; /* 状态字 */
    int shm_slave37_online_status; /* 从站37在线状态 */
    int shm_slave37_operational_status; /* 从站37运行状态 */
    int shm_slave37_al_state; /* 从站37AL状态 */

    int shm_slave37_rx_shm_slave37_rx_0x6040_control_word; /* 控制字 */
    int shm_slave37_rx_shm_slave37_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave37_rx_shm_slave37_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave37_tx_shm_slave37_tx_0x6041_status_word; /* 状态字 */
    int shm_slave38_online_status; /* 从站38在线状态 */
    int shm_slave38_operational_status; /* 从站38运行状态 */
    int shm_slave38_al_state; /* 从站38AL状态 */

    int shm_slave38_rx_shm_slave38_rx_0x6040_control_word; /* 控制字 */
    int shm_slave38_rx_shm_slave38_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave38_rx_shm_slave38_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave38_tx_shm_slave38_tx_0x6041_status_word; /* 状态字 */
    int shm_slave39_online_status; /* 从站39在线状态 */
    int shm_slave39_operational_status; /* 从站39运行状态 */
    int shm_slave39_al_state; /* 从站39AL状态 */

    int shm_slave39_rx_shm_slave39_rx_0x6040_control_word; /* 控制字 */
    int shm_slave39_rx_shm_slave39_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave39_rx_shm_slave39_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave39_tx_shm_slave39_tx_0x6041_status_word; /* 状态字 */
    int shm_slave40_online_status; /* 从站40在线状态 */
    int shm_slave40_operational_status; /* 从站40运行状态 */
    int shm_slave40_al_state; /* 从站40AL状态 */

    int shm_slave40_rx_shm_slave40_rx_0x6040_control_word; /* 控制字 */
    int shm_slave40_rx_shm_slave40_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave40_rx_shm_slave40_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave40_tx_shm_slave40_tx_0x6041_status_word; /* 状态字 */
    int shm_slave41_online_status; /* 从站41在线状态 */
    int shm_slave41_operational_status; /* 从站41运行状态 */
    int shm_slave41_al_state; /* 从站41AL状态 */

    int shm_slave41_rx_shm_slave41_rx_0x6040_control_word; /* 控制字 */
    int shm_slave41_rx_shm_slave41_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave41_rx_shm_slave41_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave41_tx_shm_slave41_tx_0x6041_status_word; /* 状态字 */
    int shm_slave42_online_status; /* 从站42在线状态 */
    int shm_slave42_operational_status; /* 从站42运行状态 */
    int shm_slave42_al_state; /* 从站42AL状态 */

    int shm_slave42_rx_shm_slave42_rx_0x6040_control_word; /* 控制字 */
    int shm_slave42_rx_shm_slave42_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave42_rx_shm_slave42_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave42_tx_shm_slave42_tx_0x6041_status_word; /* 状态字 */
    int shm_slave43_online_status; /* 从站43在线状态 */
    int shm_slave43_operational_status; /* 从站43运行状态 */
    int shm_slave43_al_state; /* 从站43AL状态 */

    int shm_slave43_rx_shm_slave43_rx_0x6040_control_word; /* 控制字 */
    int shm_slave43_rx_shm_slave43_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave43_rx_shm_slave43_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave43_tx_shm_slave43_tx_0x6041_status_word; /* 状态字 */
    int shm_slave44_online_status; /* 从站44在线状态 */
    int shm_slave44_operational_status; /* 从站44运行状态 */
    int shm_slave44_al_state; /* 从站44AL状态 */

    int shm_slave44_rx_shm_slave44_rx_0x6040_control_word; /* 控制字 */
    int shm_slave44_rx_shm_slave44_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave44_rx_shm_slave44_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave44_tx_shm_slave44_tx_0x6041_status_word; /* 状态字 */
    int shm_slave45_online_status; /* 从站45在线状态 */
    int shm_slave45_operational_status; /* 从站45运行状态 */
    int shm_slave45_al_state; /* 从站45AL状态 */

    int shm_slave45_rx_shm_slave45_rx_0x6040_control_word; /* 控制字 */
    int shm_slave45_rx_shm_slave45_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave45_rx_shm_slave45_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave45_tx_shm_slave45_tx_0x6041_status_word; /* 状态字 */
    int shm_slave46_online_status; /* 从站46在线状态 */
    int shm_slave46_operational_status; /* 从站46运行状态 */
    int shm_slave46_al_state; /* 从站46AL状态 */

    int shm_slave46_rx_shm_slave46_rx_0x6040_control_word; /* 控制字 */
    int shm_slave46_rx_shm_slave46_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave46_rx_shm_slave46_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave46_tx_shm_slave46_tx_0x6041_status_word; /* 状态字 */
    int shm_slave47_online_status; /* 从站47在线状态 */
    int shm_slave47_operational_status; /* 从站47运行状态 */
    int shm_slave47_al_state; /* 从站47AL状态 */

    int shm_slave47_rx_shm_slave47_rx_0x6040_control_word; /* 控制字 */
    int shm_slave47_rx_shm_slave47_rx_0x60ff_target_speed; /* 目标速度 */
    int shm_slave47_rx_shm_slave47_rx_0x6060_operation_mode; /* 操作模式设置 */
    int shm_slave47_tx_shm_slave47_tx_0x6041_status_word; /* 状态字 */
} ethercat_shm_t;

static ethercat_shm_t *ethercat_shm = NULL;

/* Application Parameters */
#define TASK_FREQUENCY 1000 /*Hz*/
#define PERIOD_NS (NSEC_PER_SEC / TASK_FREQUENCY)

/* EtherCAT configurations */
#define MASTER_INDEX 0

static ec_master_t *master = NULL;
static ec_master_state_t master_state = {};
static ec_domain_t *domain1 = NULL;
static ec_domain_state_t domain1_state = {};
static ec_slave_config_t *sc_slave0 = NULL;
static ec_slave_config_state_t sc_slave0_state = {};
static ec_slave_config_t *sc_slave1 = NULL;
static ec_slave_config_state_t sc_slave1_state = {};
static ec_slave_config_t *sc_slave2 = NULL;
static ec_slave_config_state_t sc_slave2_state = {};
static ec_slave_config_t *sc_slave3 = NULL;
static ec_slave_config_state_t sc_slave3_state = {};
static ec_slave_config_t *sc_slave4 = NULL;
static ec_slave_config_state_t sc_slave4_state = {};
static ec_slave_config_t *sc_slave5 = NULL;
static ec_slave_config_state_t sc_slave5_state = {};
static ec_slave_config_t *sc_slave6 = NULL;
static ec_slave_config_state_t sc_slave6_state = {};
static ec_slave_config_t *sc_slave7 = NULL;
static ec_slave_config_state_t sc_slave7_state = {};
static ec_slave_config_t *sc_slave8 = NULL;
static ec_slave_config_state_t sc_slave8_state = {};
static ec_slave_config_t *sc_slave9 = NULL;
static ec_slave_config_state_t sc_slave9_state = {};
static ec_slave_config_t *sc_slave10 = NULL;
static ec_slave_config_state_t sc_slave10_state = {};
static ec_slave_config_t *sc_slave11 = NULL;
static ec_slave_config_state_t sc_slave11_state = {};
static ec_slave_config_t *sc_slave12 = NULL;
static ec_slave_config_state_t sc_slave12_state = {};
static ec_slave_config_t *sc_slave13 = NULL;
static ec_slave_config_state_t sc_slave13_state = {};
static ec_slave_config_t *sc_slave14 = NULL;
static ec_slave_config_state_t sc_slave14_state = {};
static ec_slave_config_t *sc_slave15 = NULL;
static ec_slave_config_state_t sc_slave15_state = {};
static ec_slave_config_t *sc_slave16 = NULL;
static ec_slave_config_state_t sc_slave16_state = {};
static ec_slave_config_t *sc_slave17 = NULL;
static ec_slave_config_state_t sc_slave17_state = {};
static ec_slave_config_t *sc_slave18 = NULL;
static ec_slave_config_state_t sc_slave18_state = {};
static ec_slave_config_t *sc_slave19 = NULL;
static ec_slave_config_state_t sc_slave19_state = {};
static ec_slave_config_t *sc_slave20 = NULL;
static ec_slave_config_state_t sc_slave20_state = {};
static ec_slave_config_t *sc_slave21 = NULL;
static ec_slave_config_state_t sc_slave21_state = {};
static ec_slave_config_t *sc_slave22 = NULL;
static ec_slave_config_state_t sc_slave22_state = {};
static ec_slave_config_t *sc_slave23 = NULL;
static ec_slave_config_state_t sc_slave23_state = {};
static ec_slave_config_t *sc_slave24 = NULL;
static ec_slave_config_state_t sc_slave24_state = {};
static ec_slave_config_t *sc_slave25 = NULL;
static ec_slave_config_state_t sc_slave25_state = {};
static ec_slave_config_t *sc_slave26 = NULL;
static ec_slave_config_state_t sc_slave26_state = {};
static ec_slave_config_t *sc_slave27 = NULL;
static ec_slave_config_state_t sc_slave27_state = {};
static ec_slave_config_t *sc_slave28 = NULL;
static ec_slave_config_state_t sc_slave28_state = {};
static ec_slave_config_t *sc_slave29 = NULL;
static ec_slave_config_state_t sc_slave29_state = {};
static ec_slave_config_t *sc_slave30 = NULL;
static ec_slave_config_state_t sc_slave30_state = {};
static ec_slave_config_t *sc_slave31 = NULL;
static ec_slave_config_state_t sc_slave31_state = {};
static ec_slave_config_t *sc_slave32 = NULL;
static ec_slave_config_state_t sc_slave32_state = {};
static ec_slave_config_t *sc_slave33 = NULL;
static ec_slave_config_state_t sc_slave33_state = {};
static ec_slave_config_t *sc_slave34 = NULL;
static ec_slave_config_state_t sc_slave34_state = {};
static ec_slave_config_t *sc_slave35 = NULL;
static ec_slave_config_state_t sc_slave35_state = {};
static ec_slave_config_t *sc_slave36 = NULL;
static ec_slave_config_state_t sc_slave36_state = {};
static ec_slave_config_t *sc_slave37 = NULL;
static ec_slave_config_state_t sc_slave37_state = {};
static ec_slave_config_t *sc_slave38 = NULL;
static ec_slave_config_state_t sc_slave38_state = {};
static ec_slave_config_t *sc_slave39 = NULL;
static ec_slave_config_state_t sc_slave39_state = {};
static ec_slave_config_t *sc_slave40 = NULL;
static ec_slave_config_state_t sc_slave40_state = {};
static ec_slave_config_t *sc_slave41 = NULL;
static ec_slave_config_state_t sc_slave41_state = {};
static ec_slave_config_t *sc_slave42 = NULL;
static ec_slave_config_state_t sc_slave42_state = {};
static ec_slave_config_t *sc_slave43 = NULL;
static ec_slave_config_state_t sc_slave43_state = {};
static ec_slave_config_t *sc_slave44 = NULL;
static ec_slave_config_state_t sc_slave44_state = {};
static ec_slave_config_t *sc_slave45 = NULL;
static ec_slave_config_state_t sc_slave45_state = {};
static ec_slave_config_t *sc_slave46 = NULL;
static ec_slave_config_state_t sc_slave46_state = {};
static ec_slave_config_t *sc_slave47 = NULL;
static ec_slave_config_state_t sc_slave47_state = {};
static uint8_t *domain1_pd = NULL;

#define slave0_POS 0,0
#define slave0_VID_PID 0x00850104,0x01030507
#define slave1_POS 0,1
#define slave1_VID_PID 0x00850104,0x01030507
#define slave2_POS 0,2
#define slave2_VID_PID 0x00850104,0x01030507
#define slave3_POS 0,3
#define slave3_VID_PID 0x00850104,0x01030507
#define slave4_POS 0,4
#define slave4_VID_PID 0x00850104,0x01030507
#define slave5_POS 0,5
#define slave5_VID_PID 0x00850104,0x01030507
#define slave6_POS 0,6
#define slave6_VID_PID 0x00850104,0x01030507
#define slave7_POS 0,7
#define slave7_VID_PID 0x00850104,0x01030507
#define slave8_POS 0,8
#define slave8_VID_PID 0x00850104,0x01030507
#define slave9_POS 0,9
#define slave9_VID_PID 0x00850104,0x01030507
#define slave10_POS 0,10
#define slave10_VID_PID 0x00850104,0x01030507
#define slave11_POS 0,11
#define slave11_VID_PID 0x00850104,0x01030507
#define slave12_POS 0,12
#define slave12_VID_PID 0x00850104,0x01030507
#define slave13_POS 0,13
#define slave13_VID_PID 0x00850104,0x01030507
#define slave14_POS 0,14
#define slave14_VID_PID 0x00850104,0x01030507
#define slave15_POS 0,15
#define slave15_VID_PID 0x00850104,0x01030507
#define slave16_POS 0,16
#define slave16_VID_PID 0x00850104,0x01030507
#define slave17_POS 0,17
#define slave17_VID_PID 0x00850104,0x01030507
#define slave18_POS 0,18
#define slave18_VID_PID 0x00850104,0x01030507
#define slave19_POS 0,19
#define slave19_VID_PID 0x00850104,0x01030507
#define slave20_POS 0,20
#define slave20_VID_PID 0x00850104,0x01030507
#define slave21_POS 0,21
#define slave21_VID_PID 0x00850104,0x01030507
#define slave22_POS 0,22
#define slave22_VID_PID 0x00850104,0x01030507
#define slave23_POS 0,23
#define slave23_VID_PID 0x00850104,0x01030507
#define slave24_POS 0,24
#define slave24_VID_PID 0x00850104,0x01030507
#define slave25_POS 0,25
#define slave25_VID_PID 0x00850104,0x01030507
#define slave26_POS 0,26
#define slave26_VID_PID 0x00850104,0x01030507
#define slave27_POS 0,27
#define slave27_VID_PID 0x00850104,0x01030507
#define slave28_POS 0,28
#define slave28_VID_PID 0x00850104,0x01030507
#define slave29_POS 0,29
#define slave29_VID_PID 0x00850104,0x01030507
#define slave30_POS 0,30
#define slave30_VID_PID 0x00850104,0x01030507
#define slave31_POS 0,31
#define slave31_VID_PID 0x00850104,0x01030507
#define slave32_POS 0,32
#define slave32_VID_PID 0x00850104,0x01030507
#define slave33_POS 0,33
#define slave33_VID_PID 0x00850104,0x01030507
#define slave34_POS 0,34
#define slave34_VID_PID 0x00850104,0x01030507
#define slave35_POS 0,35
#define slave35_VID_PID 0x00850104,0x01030507
#define slave36_POS 0,36
#define slave36_VID_PID 0x00850104,0x01030507
#define slave37_POS 0,37
#define slave37_VID_PID 0x00850104,0x01030507
#define slave38_POS 0,38
#define slave38_VID_PID 0x00850104,0x01030507
#define slave39_POS 0,39
#define slave39_VID_PID 0x00850104,0x01030507
#define slave40_POS 0,40
#define slave40_VID_PID 0x00850104,0x01030507
#define slave41_POS 0,41
#define slave41_VID_PID 0x00850104,0x01030507
#define slave42_POS 0,42
#define slave42_VID_PID 0x00850104,0x01030507
#define slave43_POS 0,43
#define slave43_VID_PID 0x00850104,0x01030507
#define slave44_POS 0,44
#define slave44_VID_PID 0x00850104,0x01030507
#define slave45_POS 0,45
#define slave45_VID_PID 0x00850104,0x01030507
#define slave46_POS 0,46
#define slave46_VID_PID 0x00850104,0x01030507
#define slave47_POS 0,47
#define slave47_VID_PID 0x00850104,0x01030507

/* PDO mapping */

/* SDO data definitions */
static struct {
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x6040_control_word;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x60ff_target_speed;
    unsigned int pdo_slave0_rx_shm_slave0_rx_0x6060_operation_mode;
    unsigned int pdo_slave0_tx_shm_slave0_tx_0x6041_status_word;
    unsigned int pdo_slave1_rx_shm_slave1_rx_0x6040_control_word;
    unsigned int pdo_slave1_rx_shm_slave1_rx_0x60ff_target_speed;
    unsigned int pdo_slave1_rx_shm_slave1_rx_0x6060_operation_mode;
    unsigned int pdo_slave1_tx_shm_slave1_tx_0x6041_status_word;
    unsigned int pdo_slave2_rx_shm_slave2_rx_0x6040_control_word;
    unsigned int pdo_slave2_rx_shm_slave2_rx_0x60ff_target_speed;
    unsigned int pdo_slave2_rx_shm_slave2_rx_0x6060_operation_mode;
    unsigned int pdo_slave2_tx_shm_slave2_tx_0x6041_status_word;
    unsigned int pdo_slave3_rx_shm_slave3_rx_0x6040_control_word;
    unsigned int pdo_slave3_rx_shm_slave3_rx_0x60ff_target_speed;
    unsigned int pdo_slave3_rx_shm_slave3_rx_0x6060_operation_mode;
    unsigned int pdo_slave3_tx_shm_slave3_tx_0x6041_status_word;
    unsigned int pdo_slave4_rx_shm_slave4_rx_0x6040_control_word;
    unsigned int pdo_slave4_rx_shm_slave4_rx_0x60ff_target_speed;
    unsigned int pdo_slave4_rx_shm_slave4_rx_0x6060_operation_mode;
    unsigned int pdo_slave4_tx_shm_slave4_tx_0x6041_status_word;
    unsigned int pdo_slave5_rx_shm_slave5_rx_0x6040_control_word;
    unsigned int pdo_slave5_rx_shm_slave5_rx_0x60ff_target_speed;
    unsigned int pdo_slave5_rx_shm_slave5_rx_0x6060_operation_mode;
    unsigned int pdo_slave5_tx_shm_slave5_tx_0x6041_status_word;
    unsigned int pdo_slave6_rx_shm_slave6_rx_0x6040_control_word;
    unsigned int pdo_slave6_rx_shm_slave6_rx_0x60ff_target_speed;
    unsigned int pdo_slave6_rx_shm_slave6_rx_0x6060_operation_mode;
    unsigned int pdo_slave6_tx_shm_slave6_tx_0x6041_status_word;
    unsigned int pdo_slave7_rx_shm_slave7_rx_0x6040_control_word;
    unsigned int pdo_slave7_rx_shm_slave7_rx_0x60ff_target_speed;
    unsigned int pdo_slave7_rx_shm_slave7_rx_0x6060_operation_mode;
    unsigned int pdo_slave7_tx_shm_slave7_tx_0x6041_status_word;
    unsigned int pdo_slave8_rx_shm_slave8_rx_0x6040_control_word;
    unsigned int pdo_slave8_rx_shm_slave8_rx_0x60ff_target_speed;
    unsigned int pdo_slave8_rx_shm_slave8_rx_0x6060_operation_mode;
    unsigned int pdo_slave8_tx_shm_slave8_tx_0x6041_status_word;
    unsigned int pdo_slave9_rx_shm_slave9_rx_0x6040_control_word;
    unsigned int pdo_slave9_rx_shm_slave9_rx_0x60ff_target_speed;
    unsigned int pdo_slave9_rx_shm_slave9_rx_0x6060_operation_mode;
    unsigned int pdo_slave9_tx_shm_slave9_tx_0x6041_status_word;
    unsigned int pdo_slave10_rx_shm_slave10_rx_0x6040_control_word;
    unsigned int pdo_slave10_rx_shm_slave10_rx_0x60ff_target_speed;
    unsigned int pdo_slave10_rx_shm_slave10_rx_0x6060_operation_mode;
    unsigned int pdo_slave10_tx_shm_slave10_tx_0x6041_status_word;
    unsigned int pdo_slave11_rx_shm_slave11_rx_0x6040_control_word;
    unsigned int pdo_slave11_rx_shm_slave11_rx_0x60ff_target_speed;
    unsigned int pdo_slave11_rx_shm_slave11_rx_0x6060_operation_mode;
    unsigned int pdo_slave11_tx_shm_slave11_tx_0x6041_status_word;
    unsigned int pdo_slave12_rx_shm_slave12_rx_0x6040_control_word;
    unsigned int pdo_slave12_rx_shm_slave12_rx_0x60ff_target_speed;
    unsigned int pdo_slave12_rx_shm_slave12_rx_0x6060_operation_mode;
    unsigned int pdo_slave12_tx_shm_slave12_tx_0x6041_status_word;
    unsigned int pdo_slave13_rx_shm_slave13_rx_0x6040_control_word;
    unsigned int pdo_slave13_rx_shm_slave13_rx_0x60ff_target_speed;
    unsigned int pdo_slave13_rx_shm_slave13_rx_0x6060_operation_mode;
    unsigned int pdo_slave13_tx_shm_slave13_tx_0x6041_status_word;
    unsigned int pdo_slave14_rx_shm_slave14_rx_0x6040_control_word;
    unsigned int pdo_slave14_rx_shm_slave14_rx_0x60ff_target_speed;
    unsigned int pdo_slave14_rx_shm_slave14_rx_0x6060_operation_mode;
    unsigned int pdo_slave14_tx_shm_slave14_tx_0x6041_status_word;
    unsigned int pdo_slave15_rx_shm_slave15_rx_0x6040_control_word;
    unsigned int pdo_slave15_rx_shm_slave15_rx_0x60ff_target_speed;
    unsigned int pdo_slave15_rx_shm_slave15_rx_0x6060_operation_mode;
    unsigned int pdo_slave15_tx_shm_slave15_tx_0x6041_status_word;
    unsigned int pdo_slave16_rx_shm_slave16_rx_0x6040_control_word;
    unsigned int pdo_slave16_rx_shm_slave16_rx_0x60ff_target_speed;
    unsigned int pdo_slave16_rx_shm_slave16_rx_0x6060_operation_mode;
    unsigned int pdo_slave16_tx_shm_slave16_tx_0x6041_status_word;
    unsigned int pdo_slave17_rx_shm_slave17_rx_0x6040_control_word;
    unsigned int pdo_slave17_rx_shm_slave17_rx_0x60ff_target_speed;
    unsigned int pdo_slave17_rx_shm_slave17_rx_0x6060_operation_mode;
    unsigned int pdo_slave17_tx_shm_slave17_tx_0x6041_status_word;
    unsigned int pdo_slave18_rx_shm_slave18_rx_0x6040_control_word;
    unsigned int pdo_slave18_rx_shm_slave18_rx_0x60ff_target_speed;
    unsigned int pdo_slave18_rx_shm_slave18_rx_0x6060_operation_mode;
    unsigned int pdo_slave18_tx_shm_slave18_tx_0x6041_status_word;
    unsigned int pdo_slave19_rx_shm_slave19_rx_0x6040_control_word;
    unsigned int pdo_slave19_rx_shm_slave19_rx_0x60ff_target_speed;
    unsigned int pdo_slave19_rx_shm_slave19_rx_0x6060_operation_mode;
    unsigned int pdo_slave19_tx_shm_slave19_tx_0x6041_status_word;
    unsigned int pdo_slave20_rx_shm_slave20_rx_0x6040_control_word;
    unsigned int pdo_slave20_rx_shm_slave20_rx_0x60ff_target_speed;
    unsigned int pdo_slave20_rx_shm_slave20_rx_0x6060_operation_mode;
    unsigned int pdo_slave20_tx_shm_slave20_tx_0x6041_status_word;
    unsigned int pdo_slave21_rx_shm_slave21_rx_0x6040_control_word;
    unsigned int pdo_slave21_rx_shm_slave21_rx_0x60ff_target_speed;
    unsigned int pdo_slave21_rx_shm_slave21_rx_0x6060_operation_mode;
    unsigned int pdo_slave21_tx_shm_slave21_tx_0x6041_status_word;
    unsigned int pdo_slave22_rx_shm_slave22_rx_0x6040_control_word;
    unsigned int pdo_slave22_rx_shm_slave22_rx_0x60ff_target_speed;
    unsigned int pdo_slave22_rx_shm_slave22_rx_0x6060_operation_mode;
    unsigned int pdo_slave22_tx_shm_slave22_tx_0x6041_status_word;
    unsigned int pdo_slave23_rx_shm_slave23_rx_0x6040_control_word;
    unsigned int pdo_slave23_rx_shm_slave23_rx_0x60ff_target_speed;
    unsigned int pdo_slave23_rx_shm_slave23_rx_0x6060_operation_mode;
    unsigned int pdo_slave23_tx_shm_slave23_tx_0x6041_status_word;
    unsigned int pdo_slave24_rx_shm_slave24_rx_0x6040_control_word;
    unsigned int pdo_slave24_rx_shm_slave24_rx_0x60ff_target_speed;
    unsigned int pdo_slave24_rx_shm_slave24_rx_0x6060_operation_mode;
    unsigned int pdo_slave24_tx_shm_slave24_tx_0x6041_status_word;
    unsigned int pdo_slave25_rx_shm_slave25_rx_0x6040_control_word;
    unsigned int pdo_slave25_rx_shm_slave25_rx_0x60ff_target_speed;
    unsigned int pdo_slave25_rx_shm_slave25_rx_0x6060_operation_mode;
    unsigned int pdo_slave25_tx_shm_slave25_tx_0x6041_status_word;
    unsigned int pdo_slave26_rx_shm_slave26_rx_0x6040_control_word;
    unsigned int pdo_slave26_rx_shm_slave26_rx_0x60ff_target_speed;
    unsigned int pdo_slave26_rx_shm_slave26_rx_0x6060_operation_mode;
    unsigned int pdo_slave26_tx_shm_slave26_tx_0x6041_status_word;
    unsigned int pdo_slave27_rx_shm_slave27_rx_0x6040_control_word;
    unsigned int pdo_slave27_rx_shm_slave27_rx_0x60ff_target_speed;
    unsigned int pdo_slave27_rx_shm_slave27_rx_0x6060_operation_mode;
    unsigned int pdo_slave27_tx_shm_slave27_tx_0x6041_status_word;
    unsigned int pdo_slave28_rx_shm_slave28_rx_0x6040_control_word;
    unsigned int pdo_slave28_rx_shm_slave28_rx_0x60ff_target_speed;
    unsigned int pdo_slave28_rx_shm_slave28_rx_0x6060_operation_mode;
    unsigned int pdo_slave28_tx_shm_slave28_tx_0x6041_status_word;
    unsigned int pdo_slave29_rx_shm_slave29_rx_0x6040_control_word;
    unsigned int pdo_slave29_rx_shm_slave29_rx_0x60ff_target_speed;
    unsigned int pdo_slave29_rx_shm_slave29_rx_0x6060_operation_mode;
    unsigned int pdo_slave29_tx_shm_slave29_tx_0x6041_status_word;
    unsigned int pdo_slave30_rx_shm_slave30_rx_0x6040_control_word;
    unsigned int pdo_slave30_rx_shm_slave30_rx_0x60ff_target_speed;
    unsigned int pdo_slave30_rx_shm_slave30_rx_0x6060_operation_mode;
    unsigned int pdo_slave30_tx_shm_slave30_tx_0x6041_status_word;
    unsigned int pdo_slave31_rx_shm_slave31_rx_0x6040_control_word;
    unsigned int pdo_slave31_rx_shm_slave31_rx_0x60ff_target_speed;
    unsigned int pdo_slave31_rx_shm_slave31_rx_0x6060_operation_mode;
    unsigned int pdo_slave31_tx_shm_slave31_tx_0x6041_status_word;
    unsigned int pdo_slave32_rx_shm_slave32_rx_0x6040_control_word;
    unsigned int pdo_slave32_rx_shm_slave32_rx_0x60ff_target_speed;
    unsigned int pdo_slave32_rx_shm_slave32_rx_0x6060_operation_mode;
    unsigned int pdo_slave32_tx_shm_slave32_tx_0x6041_status_word;
    unsigned int pdo_slave33_rx_shm_slave33_rx_0x6040_control_word;
    unsigned int pdo_slave33_rx_shm_slave33_rx_0x60ff_target_speed;
    unsigned int pdo_slave33_rx_shm_slave33_rx_0x6060_operation_mode;
    unsigned int pdo_slave33_tx_shm_slave33_tx_0x6041_status_word;
    unsigned int pdo_slave34_rx_shm_slave34_rx_0x6040_control_word;
    unsigned int pdo_slave34_rx_shm_slave34_rx_0x60ff_target_speed;
    unsigned int pdo_slave34_rx_shm_slave34_rx_0x6060_operation_mode;
    unsigned int pdo_slave34_tx_shm_slave34_tx_0x6041_status_word;
    unsigned int pdo_slave35_rx_shm_slave35_rx_0x6040_control_word;
    unsigned int pdo_slave35_rx_shm_slave35_rx_0x60ff_target_speed;
    unsigned int pdo_slave35_rx_shm_slave35_rx_0x6060_operation_mode;
    unsigned int pdo_slave35_tx_shm_slave35_tx_0x6041_status_word;
    unsigned int pdo_slave36_rx_shm_slave36_rx_0x6040_control_word;
    unsigned int pdo_slave36_rx_shm_slave36_rx_0x60ff_target_speed;
    unsigned int pdo_slave36_rx_shm_slave36_rx_0x6060_operation_mode;
    unsigned int pdo_slave36_tx_shm_slave36_tx_0x6041_status_word;
    unsigned int pdo_slave37_rx_shm_slave37_rx_0x6040_control_word;
    unsigned int pdo_slave37_rx_shm_slave37_rx_0x60ff_target_speed;
    unsigned int pdo_slave37_rx_shm_slave37_rx_0x6060_operation_mode;
    unsigned int pdo_slave37_tx_shm_slave37_tx_0x6041_status_word;
    unsigned int pdo_slave38_rx_shm_slave38_rx_0x6040_control_word;
    unsigned int pdo_slave38_rx_shm_slave38_rx_0x60ff_target_speed;
    unsigned int pdo_slave38_rx_shm_slave38_rx_0x6060_operation_mode;
    unsigned int pdo_slave38_tx_shm_slave38_tx_0x6041_status_word;
    unsigned int pdo_slave39_rx_shm_slave39_rx_0x6040_control_word;
    unsigned int pdo_slave39_rx_shm_slave39_rx_0x60ff_target_speed;
    unsigned int pdo_slave39_rx_shm_slave39_rx_0x6060_operation_mode;
    unsigned int pdo_slave39_tx_shm_slave39_tx_0x6041_status_word;
    unsigned int pdo_slave40_rx_shm_slave40_rx_0x6040_control_word;
    unsigned int pdo_slave40_rx_shm_slave40_rx_0x60ff_target_speed;
    unsigned int pdo_slave40_rx_shm_slave40_rx_0x6060_operation_mode;
    unsigned int pdo_slave40_tx_shm_slave40_tx_0x6041_status_word;
    unsigned int pdo_slave41_rx_shm_slave41_rx_0x6040_control_word;
    unsigned int pdo_slave41_rx_shm_slave41_rx_0x60ff_target_speed;
    unsigned int pdo_slave41_rx_shm_slave41_rx_0x6060_operation_mode;
    unsigned int pdo_slave41_tx_shm_slave41_tx_0x6041_status_word;
    unsigned int pdo_slave42_rx_shm_slave42_rx_0x6040_control_word;
    unsigned int pdo_slave42_rx_shm_slave42_rx_0x60ff_target_speed;
    unsigned int pdo_slave42_rx_shm_slave42_rx_0x6060_operation_mode;
    unsigned int pdo_slave42_tx_shm_slave42_tx_0x6041_status_word;
    unsigned int pdo_slave43_rx_shm_slave43_rx_0x6040_control_word;
    unsigned int pdo_slave43_rx_shm_slave43_rx_0x60ff_target_speed;
    unsigned int pdo_slave43_rx_shm_slave43_rx_0x6060_operation_mode;
    unsigned int pdo_slave43_tx_shm_slave43_tx_0x6041_status_word;
    unsigned int pdo_slave44_rx_shm_slave44_rx_0x6040_control_word;
    unsigned int pdo_slave44_rx_shm_slave44_rx_0x60ff_target_speed;
    unsigned int pdo_slave44_rx_shm_slave44_rx_0x6060_operation_mode;
    unsigned int pdo_slave44_tx_shm_slave44_tx_0x6041_status_word;
    unsigned int pdo_slave45_rx_shm_slave45_rx_0x6040_control_word;
    unsigned int pdo_slave45_rx_shm_slave45_rx_0x60ff_target_speed;
    unsigned int pdo_slave45_rx_shm_slave45_rx_0x6060_operation_mode;
    unsigned int pdo_slave45_tx_shm_slave45_tx_0x6041_status_word;
    unsigned int pdo_slave46_rx_shm_slave46_rx_0x6040_control_word;
    unsigned int pdo_slave46_rx_shm_slave46_rx_0x60ff_target_speed;
    unsigned int pdo_slave46_rx_shm_slave46_rx_0x6060_operation_mode;
    unsigned int pdo_slave46_tx_shm_slave46_tx_0x6041_status_word;
    unsigned int pdo_slave47_rx_shm_slave47_rx_0x6040_control_word;
    unsigned int pdo_slave47_rx_shm_slave47_rx_0x60ff_target_speed;
    unsigned int pdo_slave47_rx_shm_slave47_rx_0x6060_operation_mode;
    unsigned int pdo_slave47_tx_shm_slave47_tx_0x6041_status_word;
} offset;

const static ec_pdo_entry_reg_t domain1_regs[] = {
    {slave0_POS, slave0_VID_PID, 0x6040, 0, &offset.pdo_slave0_rx_shm_slave0_rx_0x6040_control_word},
    {slave0_POS, slave0_VID_PID, 0x60ff, 0, &offset.pdo_slave0_rx_shm_slave0_rx_0x60ff_target_speed},
    {slave0_POS, slave0_VID_PID, 0x6060, 0, &offset.pdo_slave0_rx_shm_slave0_rx_0x6060_operation_mode},
    {slave0_POS, slave0_VID_PID, 0x6041, 0, &offset.pdo_slave0_tx_shm_slave0_tx_0x6041_status_word},
    {slave1_POS, slave1_VID_PID, 0x6040, 0, &offset.pdo_slave1_rx_shm_slave1_rx_0x6040_control_word},
    {slave1_POS, slave1_VID_PID, 0x60ff, 0, &offset.pdo_slave1_rx_shm_slave1_rx_0x60ff_target_speed},
    {slave1_POS, slave1_VID_PID, 0x6060, 0, &offset.pdo_slave1_rx_shm_slave1_rx_0x6060_operation_mode},
    {slave1_POS, slave1_VID_PID, 0x6041, 0, &offset.pdo_slave1_tx_shm_slave1_tx_0x6041_status_word},
    {slave2_POS, slave2_VID_PID, 0x6040, 0, &offset.pdo_slave2_rx_shm_slave2_rx_0x6040_control_word},
    {slave2_POS, slave2_VID_PID, 0x60ff, 0, &offset.pdo_slave2_rx_shm_slave2_rx_0x60ff_target_speed},
    {slave2_POS, slave2_VID_PID, 0x6060, 0, &offset.pdo_slave2_rx_shm_slave2_rx_0x6060_operation_mode},
    {slave2_POS, slave2_VID_PID, 0x6041, 0, &offset.pdo_slave2_tx_shm_slave2_tx_0x6041_status_word},
    {slave3_POS, slave3_VID_PID, 0x6040, 0, &offset.pdo_slave3_rx_shm_slave3_rx_0x6040_control_word},
    {slave3_POS, slave3_VID_PID, 0x60ff, 0, &offset.pdo_slave3_rx_shm_slave3_rx_0x60ff_target_speed},
    {slave3_POS, slave3_VID_PID, 0x6060, 0, &offset.pdo_slave3_rx_shm_slave3_rx_0x6060_operation_mode},
    {slave3_POS, slave3_VID_PID, 0x6041, 0, &offset.pdo_slave3_tx_shm_slave3_tx_0x6041_status_word},
    {slave4_POS, slave4_VID_PID, 0x6040, 0, &offset.pdo_slave4_rx_shm_slave4_rx_0x6040_control_word},
    {slave4_POS, slave4_VID_PID, 0x60ff, 0, &offset.pdo_slave4_rx_shm_slave4_rx_0x60ff_target_speed},
    {slave4_POS, slave4_VID_PID, 0x6060, 0, &offset.pdo_slave4_rx_shm_slave4_rx_0x6060_operation_mode},
    {slave4_POS, slave4_VID_PID, 0x6041, 0, &offset.pdo_slave4_tx_shm_slave4_tx_0x6041_status_word},
    {slave5_POS, slave5_VID_PID, 0x6040, 0, &offset.pdo_slave5_rx_shm_slave5_rx_0x6040_control_word},
    {slave5_POS, slave5_VID_PID, 0x60ff, 0, &offset.pdo_slave5_rx_shm_slave5_rx_0x60ff_target_speed},
    {slave5_POS, slave5_VID_PID, 0x6060, 0, &offset.pdo_slave5_rx_shm_slave5_rx_0x6060_operation_mode},
    {slave5_POS, slave5_VID_PID, 0x6041, 0, &offset.pdo_slave5_tx_shm_slave5_tx_0x6041_status_word},
    {slave6_POS, slave6_VID_PID, 0x6040, 0, &offset.pdo_slave6_rx_shm_slave6_rx_0x6040_control_word},
    {slave6_POS, slave6_VID_PID, 0x60ff, 0, &offset.pdo_slave6_rx_shm_slave6_rx_0x60ff_target_speed},
    {slave6_POS, slave6_VID_PID, 0x6060, 0, &offset.pdo_slave6_rx_shm_slave6_rx_0x6060_operation_mode},
    {slave6_POS, slave6_VID_PID, 0x6041, 0, &offset.pdo_slave6_tx_shm_slave6_tx_0x6041_status_word},
    {slave7_POS, slave7_VID_PID, 0x6040, 0, &offset.pdo_slave7_rx_shm_slave7_rx_0x6040_control_word},
    {slave7_POS, slave7_VID_PID, 0x60ff, 0, &offset.pdo_slave7_rx_shm_slave7_rx_0x60ff_target_speed},
    {slave7_POS, slave7_VID_PID, 0x6060, 0, &offset.pdo_slave7_rx_shm_slave7_rx_0x6060_operation_mode},
    {slave7_POS, slave7_VID_PID, 0x6041, 0, &offset.pdo_slave7_tx_shm_slave7_tx_0x6041_status_word},
    {slave8_POS, slave8_VID_PID, 0x6040, 0, &offset.pdo_slave8_rx_shm_slave8_rx_0x6040_control_word},
    {slave8_POS, slave8_VID_PID, 0x60ff, 0, &offset.pdo_slave8_rx_shm_slave8_rx_0x60ff_target_speed},
    {slave8_POS, slave8_VID_PID, 0x6060, 0, &offset.pdo_slave8_rx_shm_slave8_rx_0x6060_operation_mode},
    {slave8_POS, slave8_VID_PID, 0x6041, 0, &offset.pdo_slave8_tx_shm_slave8_tx_0x6041_status_word},
    {slave9_POS, slave9_VID_PID, 0x6040, 0, &offset.pdo_slave9_rx_shm_slave9_rx_0x6040_control_word},
    {slave9_POS, slave9_VID_PID, 0x60ff, 0, &offset.pdo_slave9_rx_shm_slave9_rx_0x60ff_target_speed},
    {slave9_POS, slave9_VID_PID, 0x6060, 0, &offset.pdo_slave9_rx_shm_slave9_rx_0x6060_operation_mode},
    {slave9_POS, slave9_VID_PID, 0x6041, 0, &offset.pdo_slave9_tx_shm_slave9_tx_0x6041_status_word},
    {slave10_POS, slave10_VID_PID, 0x6040, 0, &offset.pdo_slave10_rx_shm_slave10_rx_0x6040_control_word},
    {slave10_POS, slave10_VID_PID, 0x60ff, 0, &offset.pdo_slave10_rx_shm_slave10_rx_0x60ff_target_speed},
    {slave10_POS, slave10_VID_PID, 0x6060, 0, &offset.pdo_slave10_rx_shm_slave10_rx_0x6060_operation_mode},
    {slave10_POS, slave10_VID_PID, 0x6041, 0, &offset.pdo_slave10_tx_shm_slave10_tx_0x6041_status_word},
    {slave11_POS, slave11_VID_PID, 0x6040, 0, &offset.pdo_slave11_rx_shm_slave11_rx_0x6040_control_word},
    {slave11_POS, slave11_VID_PID, 0x60ff, 0, &offset.pdo_slave11_rx_shm_slave11_rx_0x60ff_target_speed},
    {slave11_POS, slave11_VID_PID, 0x6060, 0, &offset.pdo_slave11_rx_shm_slave11_rx_0x6060_operation_mode},
    {slave11_POS, slave11_VID_PID, 0x6041, 0, &offset.pdo_slave11_tx_shm_slave11_tx_0x6041_status_word},
    {slave12_POS, slave12_VID_PID, 0x6040, 0, &offset.pdo_slave12_rx_shm_slave12_rx_0x6040_control_word},
    {slave12_POS, slave12_VID_PID, 0x60ff, 0, &offset.pdo_slave12_rx_shm_slave12_rx_0x60ff_target_speed},
    {slave12_POS, slave12_VID_PID, 0x6060, 0, &offset.pdo_slave12_rx_shm_slave12_rx_0x6060_operation_mode},
    {slave12_POS, slave12_VID_PID, 0x6041, 0, &offset.pdo_slave12_tx_shm_slave12_tx_0x6041_status_word},
    {slave13_POS, slave13_VID_PID, 0x6040, 0, &offset.pdo_slave13_rx_shm_slave13_rx_0x6040_control_word},
    {slave13_POS, slave13_VID_PID, 0x60ff, 0, &offset.pdo_slave13_rx_shm_slave13_rx_0x60ff_target_speed},
    {slave13_POS, slave13_VID_PID, 0x6060, 0, &offset.pdo_slave13_rx_shm_slave13_rx_0x6060_operation_mode},
    {slave13_POS, slave13_VID_PID, 0x6041, 0, &offset.pdo_slave13_tx_shm_slave13_tx_0x6041_status_word},
    {slave14_POS, slave14_VID_PID, 0x6040, 0, &offset.pdo_slave14_rx_shm_slave14_rx_0x6040_control_word},
    {slave14_POS, slave14_VID_PID, 0x60ff, 0, &offset.pdo_slave14_rx_shm_slave14_rx_0x60ff_target_speed},
    {slave14_POS, slave14_VID_PID, 0x6060, 0, &offset.pdo_slave14_rx_shm_slave14_rx_0x6060_operation_mode},
    {slave14_POS, slave14_VID_PID, 0x6041, 0, &offset.pdo_slave14_tx_shm_slave14_tx_0x6041_status_word},
    {slave15_POS, slave15_VID_PID, 0x6040, 0, &offset.pdo_slave15_rx_shm_slave15_rx_0x6040_control_word},
    {slave15_POS, slave15_VID_PID, 0x60ff, 0, &offset.pdo_slave15_rx_shm_slave15_rx_0x60ff_target_speed},
    {slave15_POS, slave15_VID_PID, 0x6060, 0, &offset.pdo_slave15_rx_shm_slave15_rx_0x6060_operation_mode},
    {slave15_POS, slave15_VID_PID, 0x6041, 0, &offset.pdo_slave15_tx_shm_slave15_tx_0x6041_status_word},
    {slave16_POS, slave16_VID_PID, 0x6040, 0, &offset.pdo_slave16_rx_shm_slave16_rx_0x6040_control_word},
    {slave16_POS, slave16_VID_PID, 0x60ff, 0, &offset.pdo_slave16_rx_shm_slave16_rx_0x60ff_target_speed},
    {slave16_POS, slave16_VID_PID, 0x6060, 0, &offset.pdo_slave16_rx_shm_slave16_rx_0x6060_operation_mode},
    {slave16_POS, slave16_VID_PID, 0x6041, 0, &offset.pdo_slave16_tx_shm_slave16_tx_0x6041_status_word},
    {slave17_POS, slave17_VID_PID, 0x6040, 0, &offset.pdo_slave17_rx_shm_slave17_rx_0x6040_control_word},
    {slave17_POS, slave17_VID_PID, 0x60ff, 0, &offset.pdo_slave17_rx_shm_slave17_rx_0x60ff_target_speed},
    {slave17_POS, slave17_VID_PID, 0x6060, 0, &offset.pdo_slave17_rx_shm_slave17_rx_0x6060_operation_mode},
    {slave17_POS, slave17_VID_PID, 0x6041, 0, &offset.pdo_slave17_tx_shm_slave17_tx_0x6041_status_word},
    {slave18_POS, slave18_VID_PID, 0x6040, 0, &offset.pdo_slave18_rx_shm_slave18_rx_0x6040_control_word},
    {slave18_POS, slave18_VID_PID, 0x60ff, 0, &offset.pdo_slave18_rx_shm_slave18_rx_0x60ff_target_speed},
    {slave18_POS, slave18_VID_PID, 0x6060, 0, &offset.pdo_slave18_rx_shm_slave18_rx_0x6060_operation_mode},
    {slave18_POS, slave18_VID_PID, 0x6041, 0, &offset.pdo_slave18_tx_shm_slave18_tx_0x6041_status_word},
    {slave19_POS, slave19_VID_PID, 0x6040, 0, &offset.pdo_slave19_rx_shm_slave19_rx_0x6040_control_word},
    {slave19_POS, slave19_VID_PID, 0x60ff, 0, &offset.pdo_slave19_rx_shm_slave19_rx_0x60ff_target_speed},
    {slave19_POS, slave19_VID_PID, 0x6060, 0, &offset.pdo_slave19_rx_shm_slave19_rx_0x6060_operation_mode},
    {slave19_POS, slave19_VID_PID, 0x6041, 0, &offset.pdo_slave19_tx_shm_slave19_tx_0x6041_status_word},
    {slave20_POS, slave20_VID_PID, 0x6040, 0, &offset.pdo_slave20_rx_shm_slave20_rx_0x6040_control_word},
    {slave20_POS, slave20_VID_PID, 0x60ff, 0, &offset.pdo_slave20_rx_shm_slave20_rx_0x60ff_target_speed},
    {slave20_POS, slave20_VID_PID, 0x6060, 0, &offset.pdo_slave20_rx_shm_slave20_rx_0x6060_operation_mode},
    {slave20_POS, slave20_VID_PID, 0x6041, 0, &offset.pdo_slave20_tx_shm_slave20_tx_0x6041_status_word},
    {slave21_POS, slave21_VID_PID, 0x6040, 0, &offset.pdo_slave21_rx_shm_slave21_rx_0x6040_control_word},
    {slave21_POS, slave21_VID_PID, 0x60ff, 0, &offset.pdo_slave21_rx_shm_slave21_rx_0x60ff_target_speed},
    {slave21_POS, slave21_VID_PID, 0x6060, 0, &offset.pdo_slave21_rx_shm_slave21_rx_0x6060_operation_mode},
    {slave21_POS, slave21_VID_PID, 0x6041, 0, &offset.pdo_slave21_tx_shm_slave21_tx_0x6041_status_word},
    {slave22_POS, slave22_VID_PID, 0x6040, 0, &offset.pdo_slave22_rx_shm_slave22_rx_0x6040_control_word},
    {slave22_POS, slave22_VID_PID, 0x60ff, 0, &offset.pdo_slave22_rx_shm_slave22_rx_0x60ff_target_speed},
    {slave22_POS, slave22_VID_PID, 0x6060, 0, &offset.pdo_slave22_rx_shm_slave22_rx_0x6060_operation_mode},
    {slave22_POS, slave22_VID_PID, 0x6041, 0, &offset.pdo_slave22_tx_shm_slave22_tx_0x6041_status_word},
    {slave23_POS, slave23_VID_PID, 0x6040, 0, &offset.pdo_slave23_rx_shm_slave23_rx_0x6040_control_word},
    {slave23_POS, slave23_VID_PID, 0x60ff, 0, &offset.pdo_slave23_rx_shm_slave23_rx_0x60ff_target_speed},
    {slave23_POS, slave23_VID_PID, 0x6060, 0, &offset.pdo_slave23_rx_shm_slave23_rx_0x6060_operation_mode},
    {slave23_POS, slave23_VID_PID, 0x6041, 0, &offset.pdo_slave23_tx_shm_slave23_tx_0x6041_status_word},
    {slave24_POS, slave24_VID_PID, 0x6040, 0, &offset.pdo_slave24_rx_shm_slave24_rx_0x6040_control_word},
    {slave24_POS, slave24_VID_PID, 0x60ff, 0, &offset.pdo_slave24_rx_shm_slave24_rx_0x60ff_target_speed},
    {slave24_POS, slave24_VID_PID, 0x6060, 0, &offset.pdo_slave24_rx_shm_slave24_rx_0x6060_operation_mode},
    {slave24_POS, slave24_VID_PID, 0x6041, 0, &offset.pdo_slave24_tx_shm_slave24_tx_0x6041_status_word},
    {slave25_POS, slave25_VID_PID, 0x6040, 0, &offset.pdo_slave25_rx_shm_slave25_rx_0x6040_control_word},
    {slave25_POS, slave25_VID_PID, 0x60ff, 0, &offset.pdo_slave25_rx_shm_slave25_rx_0x60ff_target_speed},
    {slave25_POS, slave25_VID_PID, 0x6060, 0, &offset.pdo_slave25_rx_shm_slave25_rx_0x6060_operation_mode},
    {slave25_POS, slave25_VID_PID, 0x6041, 0, &offset.pdo_slave25_tx_shm_slave25_tx_0x6041_status_word},
    {slave26_POS, slave26_VID_PID, 0x6040, 0, &offset.pdo_slave26_rx_shm_slave26_rx_0x6040_control_word},
    {slave26_POS, slave26_VID_PID, 0x60ff, 0, &offset.pdo_slave26_rx_shm_slave26_rx_0x60ff_target_speed},
    {slave26_POS, slave26_VID_PID, 0x6060, 0, &offset.pdo_slave26_rx_shm_slave26_rx_0x6060_operation_mode},
    {slave26_POS, slave26_VID_PID, 0x6041, 0, &offset.pdo_slave26_tx_shm_slave26_tx_0x6041_status_word},
    {slave27_POS, slave27_VID_PID, 0x6040, 0, &offset.pdo_slave27_rx_shm_slave27_rx_0x6040_control_word},
    {slave27_POS, slave27_VID_PID, 0x60ff, 0, &offset.pdo_slave27_rx_shm_slave27_rx_0x60ff_target_speed},
    {slave27_POS, slave27_VID_PID, 0x6060, 0, &offset.pdo_slave27_rx_shm_slave27_rx_0x6060_operation_mode},
    {slave27_POS, slave27_VID_PID, 0x6041, 0, &offset.pdo_slave27_tx_shm_slave27_tx_0x6041_status_word},
    {slave28_POS, slave28_VID_PID, 0x6040, 0, &offset.pdo_slave28_rx_shm_slave28_rx_0x6040_control_word},
    {slave28_POS, slave28_VID_PID, 0x60ff, 0, &offset.pdo_slave28_rx_shm_slave28_rx_0x60ff_target_speed},
    {slave28_POS, slave28_VID_PID, 0x6060, 0, &offset.pdo_slave28_rx_shm_slave28_rx_0x6060_operation_mode},
    {slave28_POS, slave28_VID_PID, 0x6041, 0, &offset.pdo_slave28_tx_shm_slave28_tx_0x6041_status_word},
    {slave29_POS, slave29_VID_PID, 0x6040, 0, &offset.pdo_slave29_rx_shm_slave29_rx_0x6040_control_word},
    {slave29_POS, slave29_VID_PID, 0x60ff, 0, &offset.pdo_slave29_rx_shm_slave29_rx_0x60ff_target_speed},
    {slave29_POS, slave29_VID_PID, 0x6060, 0, &offset.pdo_slave29_rx_shm_slave29_rx_0x6060_operation_mode},
    {slave29_POS, slave29_VID_PID, 0x6041, 0, &offset.pdo_slave29_tx_shm_slave29_tx_0x6041_status_word},
    {slave30_POS, slave30_VID_PID, 0x6040, 0, &offset.pdo_slave30_rx_shm_slave30_rx_0x6040_control_word},
    {slave30_POS, slave30_VID_PID, 0x60ff, 0, &offset.pdo_slave30_rx_shm_slave30_rx_0x60ff_target_speed},
    {slave30_POS, slave30_VID_PID, 0x6060, 0, &offset.pdo_slave30_rx_shm_slave30_rx_0x6060_operation_mode},
    {slave30_POS, slave30_VID_PID, 0x6041, 0, &offset.pdo_slave30_tx_shm_slave30_tx_0x6041_status_word},
    {slave31_POS, slave31_VID_PID, 0x6040, 0, &offset.pdo_slave31_rx_shm_slave31_rx_0x6040_control_word},
    {slave31_POS, slave31_VID_PID, 0x60ff, 0, &offset.pdo_slave31_rx_shm_slave31_rx_0x60ff_target_speed},
    {slave31_POS, slave31_VID_PID, 0x6060, 0, &offset.pdo_slave31_rx_shm_slave31_rx_0x6060_operation_mode},
    {slave31_POS, slave31_VID_PID, 0x6041, 0, &offset.pdo_slave31_tx_shm_slave31_tx_0x6041_status_word},
    {slave32_POS, slave32_VID_PID, 0x6040, 0, &offset.pdo_slave32_rx_shm_slave32_rx_0x6040_control_word},
    {slave32_POS, slave32_VID_PID, 0x60ff, 0, &offset.pdo_slave32_rx_shm_slave32_rx_0x60ff_target_speed},
    {slave32_POS, slave32_VID_PID, 0x6060, 0, &offset.pdo_slave32_rx_shm_slave32_rx_0x6060_operation_mode},
    {slave32_POS, slave32_VID_PID, 0x6041, 0, &offset.pdo_slave32_tx_shm_slave32_tx_0x6041_status_word},
    {slave33_POS, slave33_VID_PID, 0x6040, 0, &offset.pdo_slave33_rx_shm_slave33_rx_0x6040_control_word},
    {slave33_POS, slave33_VID_PID, 0x60ff, 0, &offset.pdo_slave33_rx_shm_slave33_rx_0x60ff_target_speed},
    {slave33_POS, slave33_VID_PID, 0x6060, 0, &offset.pdo_slave33_rx_shm_slave33_rx_0x6060_operation_mode},
    {slave33_POS, slave33_VID_PID, 0x6041, 0, &offset.pdo_slave33_tx_shm_slave33_tx_0x6041_status_word},
    {slave34_POS, slave34_VID_PID, 0x6040, 0, &offset.pdo_slave34_rx_shm_slave34_rx_0x6040_control_word},
    {slave34_POS, slave34_VID_PID, 0x60ff, 0, &offset.pdo_slave34_rx_shm_slave34_rx_0x60ff_target_speed},
    {slave34_POS, slave34_VID_PID, 0x6060, 0, &offset.pdo_slave34_rx_shm_slave34_rx_0x6060_operation_mode},
    {slave34_POS, slave34_VID_PID, 0x6041, 0, &offset.pdo_slave34_tx_shm_slave34_tx_0x6041_status_word},
    {slave35_POS, slave35_VID_PID, 0x6040, 0, &offset.pdo_slave35_rx_shm_slave35_rx_0x6040_control_word},
    {slave35_POS, slave35_VID_PID, 0x60ff, 0, &offset.pdo_slave35_rx_shm_slave35_rx_0x60ff_target_speed},
    {slave35_POS, slave35_VID_PID, 0x6060, 0, &offset.pdo_slave35_rx_shm_slave35_rx_0x6060_operation_mode},
    {slave35_POS, slave35_VID_PID, 0x6041, 0, &offset.pdo_slave35_tx_shm_slave35_tx_0x6041_status_word},
    {slave36_POS, slave36_VID_PID, 0x6040, 0, &offset.pdo_slave36_rx_shm_slave36_rx_0x6040_control_word},
    {slave36_POS, slave36_VID_PID, 0x60ff, 0, &offset.pdo_slave36_rx_shm_slave36_rx_0x60ff_target_speed},
    {slave36_POS, slave36_VID_PID, 0x6060, 0, &offset.pdo_slave36_rx_shm_slave36_rx_0x6060_operation_mode},
    {slave36_POS, slave36_VID_PID, 0x6041, 0, &offset.pdo_slave36_tx_shm_slave36_tx_0x6041_status_word},
    {slave37_POS, slave37_VID_PID, 0x6040, 0, &offset.pdo_slave37_rx_shm_slave37_rx_0x6040_control_word},
    {slave37_POS, slave37_VID_PID, 0x60ff, 0, &offset.pdo_slave37_rx_shm_slave37_rx_0x60ff_target_speed},
    {slave37_POS, slave37_VID_PID, 0x6060, 0, &offset.pdo_slave37_rx_shm_slave37_rx_0x6060_operation_mode},
    {slave37_POS, slave37_VID_PID, 0x6041, 0, &offset.pdo_slave37_tx_shm_slave37_tx_0x6041_status_word},
    {slave38_POS, slave38_VID_PID, 0x6040, 0, &offset.pdo_slave38_rx_shm_slave38_rx_0x6040_control_word},
    {slave38_POS, slave38_VID_PID, 0x60ff, 0, &offset.pdo_slave38_rx_shm_slave38_rx_0x60ff_target_speed},
    {slave38_POS, slave38_VID_PID, 0x6060, 0, &offset.pdo_slave38_rx_shm_slave38_rx_0x6060_operation_mode},
    {slave38_POS, slave38_VID_PID, 0x6041, 0, &offset.pdo_slave38_tx_shm_slave38_tx_0x6041_status_word},
    {slave39_POS, slave39_VID_PID, 0x6040, 0, &offset.pdo_slave39_rx_shm_slave39_rx_0x6040_control_word},
    {slave39_POS, slave39_VID_PID, 0x60ff, 0, &offset.pdo_slave39_rx_shm_slave39_rx_0x60ff_target_speed},
    {slave39_POS, slave39_VID_PID, 0x6060, 0, &offset.pdo_slave39_rx_shm_slave39_rx_0x6060_operation_mode},
    {slave39_POS, slave39_VID_PID, 0x6041, 0, &offset.pdo_slave39_tx_shm_slave39_tx_0x6041_status_word},
    {slave40_POS, slave40_VID_PID, 0x6040, 0, &offset.pdo_slave40_rx_shm_slave40_rx_0x6040_control_word},
    {slave40_POS, slave40_VID_PID, 0x60ff, 0, &offset.pdo_slave40_rx_shm_slave40_rx_0x60ff_target_speed},
    {slave40_POS, slave40_VID_PID, 0x6060, 0, &offset.pdo_slave40_rx_shm_slave40_rx_0x6060_operation_mode},
    {slave40_POS, slave40_VID_PID, 0x6041, 0, &offset.pdo_slave40_tx_shm_slave40_tx_0x6041_status_word},
    {slave41_POS, slave41_VID_PID, 0x6040, 0, &offset.pdo_slave41_rx_shm_slave41_rx_0x6040_control_word},
    {slave41_POS, slave41_VID_PID, 0x60ff, 0, &offset.pdo_slave41_rx_shm_slave41_rx_0x60ff_target_speed},
    {slave41_POS, slave41_VID_PID, 0x6060, 0, &offset.pdo_slave41_rx_shm_slave41_rx_0x6060_operation_mode},
    {slave41_POS, slave41_VID_PID, 0x6041, 0, &offset.pdo_slave41_tx_shm_slave41_tx_0x6041_status_word},
    {slave42_POS, slave42_VID_PID, 0x6040, 0, &offset.pdo_slave42_rx_shm_slave42_rx_0x6040_control_word},
    {slave42_POS, slave42_VID_PID, 0x60ff, 0, &offset.pdo_slave42_rx_shm_slave42_rx_0x60ff_target_speed},
    {slave42_POS, slave42_VID_PID, 0x6060, 0, &offset.pdo_slave42_rx_shm_slave42_rx_0x6060_operation_mode},
    {slave42_POS, slave42_VID_PID, 0x6041, 0, &offset.pdo_slave42_tx_shm_slave42_tx_0x6041_status_word},
    {slave43_POS, slave43_VID_PID, 0x6040, 0, &offset.pdo_slave43_rx_shm_slave43_rx_0x6040_control_word},
    {slave43_POS, slave43_VID_PID, 0x60ff, 0, &offset.pdo_slave43_rx_shm_slave43_rx_0x60ff_target_speed},
    {slave43_POS, slave43_VID_PID, 0x6060, 0, &offset.pdo_slave43_rx_shm_slave43_rx_0x6060_operation_mode},
    {slave43_POS, slave43_VID_PID, 0x6041, 0, &offset.pdo_slave43_tx_shm_slave43_tx_0x6041_status_word},
    {slave44_POS, slave44_VID_PID, 0x6040, 0, &offset.pdo_slave44_rx_shm_slave44_rx_0x6040_control_word},
    {slave44_POS, slave44_VID_PID, 0x60ff, 0, &offset.pdo_slave44_rx_shm_slave44_rx_0x60ff_target_speed},
    {slave44_POS, slave44_VID_PID, 0x6060, 0, &offset.pdo_slave44_rx_shm_slave44_rx_0x6060_operation_mode},
    {slave44_POS, slave44_VID_PID, 0x6041, 0, &offset.pdo_slave44_tx_shm_slave44_tx_0x6041_status_word},
    {slave45_POS, slave45_VID_PID, 0x6040, 0, &offset.pdo_slave45_rx_shm_slave45_rx_0x6040_control_word},
    {slave45_POS, slave45_VID_PID, 0x60ff, 0, &offset.pdo_slave45_rx_shm_slave45_rx_0x60ff_target_speed},
    {slave45_POS, slave45_VID_PID, 0x6060, 0, &offset.pdo_slave45_rx_shm_slave45_rx_0x6060_operation_mode},
    {slave45_POS, slave45_VID_PID, 0x6041, 0, &offset.pdo_slave45_tx_shm_slave45_tx_0x6041_status_word},
    {slave46_POS, slave46_VID_PID, 0x6040, 0, &offset.pdo_slave46_rx_shm_slave46_rx_0x6040_control_word},
    {slave46_POS, slave46_VID_PID, 0x60ff, 0, &offset.pdo_slave46_rx_shm_slave46_rx_0x60ff_target_speed},
    {slave46_POS, slave46_VID_PID, 0x6060, 0, &offset.pdo_slave46_rx_shm_slave46_rx_0x6060_operation_mode},
    {slave46_POS, slave46_VID_PID, 0x6041, 0, &offset.pdo_slave46_tx_shm_slave46_tx_0x6041_status_word},
    {slave47_POS, slave47_VID_PID, 0x6040, 0, &offset.pdo_slave47_rx_shm_slave47_rx_0x6040_control_word},
    {slave47_POS, slave47_VID_PID, 0x60ff, 0, &offset.pdo_slave47_rx_shm_slave47_rx_0x60ff_target_speed},
    {slave47_POS, slave47_VID_PID, 0x6060, 0, &offset.pdo_slave47_rx_shm_slave47_rx_0x6060_operation_mode},
    {slave47_POS, slave47_VID_PID, 0x6041, 0, &offset.pdo_slave47_tx_shm_slave47_tx_0x6041_status_word},
    {}
};


static ec_pdo_entry_info_t slave0_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave0_pdos[] = {
    {0x1601, 3, slave0_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave0_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave0_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave0_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave0_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave1_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave1_pdos[] = {
    {0x1601, 3, slave1_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave1_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave1_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave1_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave1_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave2_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave2_pdos[] = {
    {0x1601, 3, slave2_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave2_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave2_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave2_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave2_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave3_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave3_pdos[] = {
    {0x1601, 3, slave3_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave3_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave3_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave3_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave3_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave4_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave4_pdos[] = {
    {0x1601, 3, slave4_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave4_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave4_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave4_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave4_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave5_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave5_pdos[] = {
    {0x1601, 3, slave5_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave5_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave5_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave5_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave5_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave6_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave6_pdos[] = {
    {0x1601, 3, slave6_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave6_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave6_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave6_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave6_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave7_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave7_pdos[] = {
    {0x1601, 3, slave7_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave7_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave7_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave7_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave7_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave8_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave8_pdos[] = {
    {0x1601, 3, slave8_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave8_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave8_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave8_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave8_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave9_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave9_pdos[] = {
    {0x1601, 3, slave9_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave9_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave9_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave9_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave9_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave10_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave10_pdos[] = {
    {0x1601, 3, slave10_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave10_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave10_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave10_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave10_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave11_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave11_pdos[] = {
    {0x1601, 3, slave11_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave11_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave11_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave11_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave11_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave12_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave12_pdos[] = {
    {0x1601, 3, slave12_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave12_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave12_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave12_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave12_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave13_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave13_pdos[] = {
    {0x1601, 3, slave13_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave13_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave13_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave13_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave13_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave14_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave14_pdos[] = {
    {0x1601, 3, slave14_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave14_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave14_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave14_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave14_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave15_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave15_pdos[] = {
    {0x1601, 3, slave15_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave15_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave15_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave15_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave15_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave16_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave16_pdos[] = {
    {0x1601, 3, slave16_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave16_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave16_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave16_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave16_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave17_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave17_pdos[] = {
    {0x1601, 3, slave17_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave17_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave17_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave17_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave17_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave18_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave18_pdos[] = {
    {0x1601, 3, slave18_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave18_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave18_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave18_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave18_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave19_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave19_pdos[] = {
    {0x1601, 3, slave19_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave19_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave19_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave19_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave19_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave20_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave20_pdos[] = {
    {0x1601, 3, slave20_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave20_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave20_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave20_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave20_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave21_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave21_pdos[] = {
    {0x1601, 3, slave21_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave21_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave21_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave21_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave21_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave22_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave22_pdos[] = {
    {0x1601, 3, slave22_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave22_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave22_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave22_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave22_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave23_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave23_pdos[] = {
    {0x1601, 3, slave23_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave23_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave23_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave23_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave23_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave24_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave24_pdos[] = {
    {0x1601, 3, slave24_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave24_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave24_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave24_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave24_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave25_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave25_pdos[] = {
    {0x1601, 3, slave25_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave25_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave25_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave25_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave25_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave26_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave26_pdos[] = {
    {0x1601, 3, slave26_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave26_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave26_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave26_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave26_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave27_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave27_pdos[] = {
    {0x1601, 3, slave27_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave27_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave27_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave27_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave27_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave28_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave28_pdos[] = {
    {0x1601, 3, slave28_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave28_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave28_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave28_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave28_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave29_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave29_pdos[] = {
    {0x1601, 3, slave29_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave29_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave29_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave29_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave29_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave30_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave30_pdos[] = {
    {0x1601, 3, slave30_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave30_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave30_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave30_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave30_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave31_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave31_pdos[] = {
    {0x1601, 3, slave31_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave31_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave31_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave31_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave31_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave32_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave32_pdos[] = {
    {0x1601, 3, slave32_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave32_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave32_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave32_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave32_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave33_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave33_pdos[] = {
    {0x1601, 3, slave33_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave33_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave33_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave33_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave33_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave34_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave34_pdos[] = {
    {0x1601, 3, slave34_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave34_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave34_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave34_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave34_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave35_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave35_pdos[] = {
    {0x1601, 3, slave35_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave35_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave35_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave35_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave35_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave36_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave36_pdos[] = {
    {0x1601, 3, slave36_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave36_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave36_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave36_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave36_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave37_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave37_pdos[] = {
    {0x1601, 3, slave37_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave37_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave37_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave37_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave37_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave38_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave38_pdos[] = {
    {0x1601, 3, slave38_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave38_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave38_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave38_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave38_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave39_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave39_pdos[] = {
    {0x1601, 3, slave39_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave39_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave39_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave39_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave39_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave40_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave40_pdos[] = {
    {0x1601, 3, slave40_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave40_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave40_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave40_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave40_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave41_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave41_pdos[] = {
    {0x1601, 3, slave41_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave41_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave41_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave41_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave41_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave42_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave42_pdos[] = {
    {0x1601, 3, slave42_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave42_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave42_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave42_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave42_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave43_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave43_pdos[] = {
    {0x1601, 3, slave43_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave43_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave43_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave43_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave43_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave44_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave44_pdos[] = {
    {0x1601, 3, slave44_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave44_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave44_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave44_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave44_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave45_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave45_pdos[] = {
    {0x1601, 3, slave45_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave45_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave45_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave45_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave45_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave46_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave46_pdos[] = {
    {0x1601, 3, slave46_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave46_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave46_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave46_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave46_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

static ec_pdo_entry_info_t slave47_pdo_entries[] = {
    {0x6040, 0, 16},  /* control_word */
    {0x60ff, 0, 32},  /* target_speed */
    {0x6060, 0, 8},  /* operation_mode */
    {0x6041, 0, 16},  /* status_word */
};

static ec_pdo_info_t slave47_pdos[] = {
    {0x1601, 3, slave47_pdo_entries + 0},  /* RxPDO */
    {0x1a00, 1, slave47_pdo_entries + 3},  /* TxPDO */
};

static ec_sync_info_t slave47_syncs[] = {
    {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
    {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
    {2, EC_DIR_OUTPUT, 1, slave47_pdos + 0, EC_WD_ENABLE},
    {3, EC_DIR_INPUT, 1, slave47_pdos + 1, EC_WD_DISABLE},
    {0xff}
};

void create_shm() {
    // Create and open shared memory
    int fd = shm_open(ETHERCAT_SHM_FILE, O_CREAT | O_RDWR, 0666);
    if (fd < 0) {
        perror("shm_open failed");
        exit(EXIT_FAILURE);
    }

    // Set the size of shared memory
    if (ftruncate(fd, ETHERCAT_SHM_SIZE) < 0) {
        perror("ftruncate failed");
        exit(EXIT_FAILURE);
    }

    // Map shared memory
    ethercat_shm = (ethercat_shm_t *)mmap(NULL, ETHERCAT_SHM_SIZE, 
                                         PROT_READ | PROT_WRITE, 
                                         MAP_SHARED, fd, 0);
    if (ethercat_shm == MAP_FAILED) {
        perror("mmap failed");
        exit(EXIT_FAILURE);
    }

    // Initialize shared memory to zero
    memset(ethercat_shm, 0, ETHERCAT_SHM_SIZE);
    
    // Close file descriptor (mapping remains valid)
    close(fd);
}

void cleanup_shm() {
    if (ethercat_shm != NULL) {
        munmap(ethercat_shm, ETHERCAT_SHM_SIZE);
        shm_unlink(ETHERCAT_SHM_FILE);
    }
}

void check_slave_config_states(void)
{
    static ec_slave_config_state_t slaves_state[48];  // 从站数从配置获取
    static int first_check = 1;  // 第一次检查的标志
    int all_op = 1;  // 所有从站是否都在OP状态的标志
    
    // 检查所有从站状态
    for (int i = 0; i < 48; i++) {
        ec_slave_config_state_t s;
        ec_slave_config_t *sc = NULL;
        
        // 根据索引获取对应的从站配置
        switch(i) {
            case 0: sc = sc_slave0; break;
            case 1: sc = sc_slave1; break;
            case 2: sc = sc_slave2; break;
            case 3: sc = sc_slave3; break;
            case 4: sc = sc_slave4; break;
            case 5: sc = sc_slave5; break;
            case 6: sc = sc_slave6; break;
            case 7: sc = sc_slave7; break;
            case 8: sc = sc_slave8; break;
            case 9: sc = sc_slave9; break;
            case 10: sc = sc_slave10; break;
            case 11: sc = sc_slave11; break;
            case 12: sc = sc_slave12; break;
            case 13: sc = sc_slave13; break;
            case 14: sc = sc_slave14; break;
            case 15: sc = sc_slave15; break;
            case 16: sc = sc_slave16; break;
            case 17: sc = sc_slave17; break;
            case 18: sc = sc_slave18; break;
            case 19: sc = sc_slave19; break;
            case 20: sc = sc_slave20; break;
            case 21: sc = sc_slave21; break;
            case 22: sc = sc_slave22; break;
            case 23: sc = sc_slave23; break;
            case 24: sc = sc_slave24; break;
            case 25: sc = sc_slave25; break;
            case 26: sc = sc_slave26; break;
            case 27: sc = sc_slave27; break;
            case 28: sc = sc_slave28; break;
            case 29: sc = sc_slave29; break;
            case 30: sc = sc_slave30; break;
            case 31: sc = sc_slave31; break;
            case 32: sc = sc_slave32; break;
            case 33: sc = sc_slave33; break;
            case 34: sc = sc_slave34; break;
            case 35: sc = sc_slave35; break;
            case 36: sc = sc_slave36; break;
            case 37: sc = sc_slave37; break;
            case 38: sc = sc_slave38; break;
            case 39: sc = sc_slave39; break;
            case 40: sc = sc_slave40; break;
            case 41: sc = sc_slave41; break;
            case 42: sc = sc_slave42; break;
            case 43: sc = sc_slave43; break;
            case 44: sc = sc_slave44; break;
            case 45: sc = sc_slave45; break;
            case 46: sc = sc_slave46; break;
            case 47: sc = sc_slave47; break;
        }
        
        ecrt_slave_config_state(sc, &s);
        
        // 检查是否所有从站都在OP状态
        if (!s.operational) {
            all_op = 0;
        }
        
        // 只在状态发生变化时打印
        if (!first_check) {
            if (s.al_state != slaves_state[i].al_state)
                printf("slave %d: State 0x%02X.\n", i, s.al_state);
            if (s.online != slaves_state[i].online)
                printf("slave %d: %s.\n", i, s.online ? "online" : "offline");
            if (s.operational != slaves_state[i].operational)
                printf("slave %d: %soperational.\n", i, s.operational ? "" : "Not ");
        }
        
        slaves_state[i] = s;
    }
    
    // 修改 ALL OP 检测和文件操作部分
    static int op_printed = 0;
    char op_file[64];
    snprintf(op_file, sizeof(op_file), "/tmp/MASTER%d_ALL_OP", MASTER_INDEX);
    
    if (all_op && !op_printed) {
        printf("ALL OP\n");
        op_printed = 1;
        all_slaves_op = 1;  // Set the flag to indicate all slaves are in OP state
        // 创建 ALL OP 标记文件
        FILE *fp = fopen(op_file, "w");
        if (fp) {
            fclose(fp);
        } else {
            fprintf(stderr, "Failed to create ALL OP file: %s\n", op_file);
        }
    } else if (!all_op && op_printed) {
        op_printed = 0;
        // 删除 ALL OP 标记文件
        // if (remove(op_file) != 0) {
        //     fprintf(stderr, "Failed to remove ALL OP file: %s\n", op_file);
        // }
    }
    
    first_check = 0;
}

void cyclic_task(void)
{
    static int cycle_counter = 0;
    static struct timespec wakeupTime;
    static int initialized = 0;

    if (!initialized) {
        clock_gettime(CLOCK_TO_USE, &wakeupTime);
        initialized = 1;
    }

    wakeupTime.tv_nsec += PERIOD_NS;
    while (wakeupTime.tv_nsec >= NSEC_PER_SEC) {
        wakeupTime.tv_nsec -= NSEC_PER_SEC;
        wakeupTime.tv_sec++;
    }

    clock_nanosleep(CLOCK_TO_USE, TIMER_ABSTIME, &wakeupTime, NULL);

    ecrt_master_application_time(master, TIMESPEC2NS(wakeupTime));

    ecrt_master_receive(master);
    
    // 添加错误检查
    if (ecrt_master_state(master, &master_state)) {
        run = 0;  // 如果出现错误，触发程序退出
        fprintf(stderr, "Failed to get master state.\n");
        return;
    }
    
    ecrt_domain_process(domain1);

    if (cycle_counter) {
        cycle_counter--;
    } else {
          cycle_counter = TASK_FREQUENCY;
        // Only check states if not all slaves are in OP state
        if (!all_slaves_op) {
            check_slave_config_states();
        }
    }

    // 更新从站状态

    // 获取从站0状态
    ecrt_slave_config_state(sc_slave0, &sc_slave0_state);
    
    // 更新从站0状态到共享内存
    ethercat_shm->shm_slave0_online_status = sc_slave0_state.online;
    ethercat_shm->shm_slave0_operational_status = sc_slave0_state.operational;
    ethercat_shm->shm_slave0_al_state = sc_slave0_state.al_state;

    // 获取从站1状态
    ecrt_slave_config_state(sc_slave1, &sc_slave1_state);
    
    // 更新从站1状态到共享内存
    ethercat_shm->shm_slave1_online_status = sc_slave1_state.online;
    ethercat_shm->shm_slave1_operational_status = sc_slave1_state.operational;
    ethercat_shm->shm_slave1_al_state = sc_slave1_state.al_state;

    // 获取从站2状态
    ecrt_slave_config_state(sc_slave2, &sc_slave2_state);
    
    // 更新从站2状态到共享内存
    ethercat_shm->shm_slave2_online_status = sc_slave2_state.online;
    ethercat_shm->shm_slave2_operational_status = sc_slave2_state.operational;
    ethercat_shm->shm_slave2_al_state = sc_slave2_state.al_state;

    // 获取从站3状态
    ecrt_slave_config_state(sc_slave3, &sc_slave3_state);
    
    // 更新从站3状态到共享内存
    ethercat_shm->shm_slave3_online_status = sc_slave3_state.online;
    ethercat_shm->shm_slave3_operational_status = sc_slave3_state.operational;
    ethercat_shm->shm_slave3_al_state = sc_slave3_state.al_state;

    // 获取从站4状态
    ecrt_slave_config_state(sc_slave4, &sc_slave4_state);
    
    // 更新从站4状态到共享内存
    ethercat_shm->shm_slave4_online_status = sc_slave4_state.online;
    ethercat_shm->shm_slave4_operational_status = sc_slave4_state.operational;
    ethercat_shm->shm_slave4_al_state = sc_slave4_state.al_state;

    // 获取从站5状态
    ecrt_slave_config_state(sc_slave5, &sc_slave5_state);
    
    // 更新从站5状态到共享内存
    ethercat_shm->shm_slave5_online_status = sc_slave5_state.online;
    ethercat_shm->shm_slave5_operational_status = sc_slave5_state.operational;
    ethercat_shm->shm_slave5_al_state = sc_slave5_state.al_state;

    // 获取从站6状态
    ecrt_slave_config_state(sc_slave6, &sc_slave6_state);
    
    // 更新从站6状态到共享内存
    ethercat_shm->shm_slave6_online_status = sc_slave6_state.online;
    ethercat_shm->shm_slave6_operational_status = sc_slave6_state.operational;
    ethercat_shm->shm_slave6_al_state = sc_slave6_state.al_state;

    // 获取从站7状态
    ecrt_slave_config_state(sc_slave7, &sc_slave7_state);
    
    // 更新从站7状态到共享内存
    ethercat_shm->shm_slave7_online_status = sc_slave7_state.online;
    ethercat_shm->shm_slave7_operational_status = sc_slave7_state.operational;
    ethercat_shm->shm_slave7_al_state = sc_slave7_state.al_state;

    // 获取从站8状态
    ecrt_slave_config_state(sc_slave8, &sc_slave8_state);
    
    // 更新从站8状态到共享内存
    ethercat_shm->shm_slave8_online_status = sc_slave8_state.online;
    ethercat_shm->shm_slave8_operational_status = sc_slave8_state.operational;
    ethercat_shm->shm_slave8_al_state = sc_slave8_state.al_state;

    // 获取从站9状态
    ecrt_slave_config_state(sc_slave9, &sc_slave9_state);
    
    // 更新从站9状态到共享内存
    ethercat_shm->shm_slave9_online_status = sc_slave9_state.online;
    ethercat_shm->shm_slave9_operational_status = sc_slave9_state.operational;
    ethercat_shm->shm_slave9_al_state = sc_slave9_state.al_state;

    // 获取从站10状态
    ecrt_slave_config_state(sc_slave10, &sc_slave10_state);
    
    // 更新从站10状态到共享内存
    ethercat_shm->shm_slave10_online_status = sc_slave10_state.online;
    ethercat_shm->shm_slave10_operational_status = sc_slave10_state.operational;
    ethercat_shm->shm_slave10_al_state = sc_slave10_state.al_state;

    // 获取从站11状态
    ecrt_slave_config_state(sc_slave11, &sc_slave11_state);
    
    // 更新从站11状态到共享内存
    ethercat_shm->shm_slave11_online_status = sc_slave11_state.online;
    ethercat_shm->shm_slave11_operational_status = sc_slave11_state.operational;
    ethercat_shm->shm_slave11_al_state = sc_slave11_state.al_state;

    // 获取从站12状态
    ecrt_slave_config_state(sc_slave12, &sc_slave12_state);
    
    // 更新从站12状态到共享内存
    ethercat_shm->shm_slave12_online_status = sc_slave12_state.online;
    ethercat_shm->shm_slave12_operational_status = sc_slave12_state.operational;
    ethercat_shm->shm_slave12_al_state = sc_slave12_state.al_state;

    // 获取从站13状态
    ecrt_slave_config_state(sc_slave13, &sc_slave13_state);
    
    // 更新从站13状态到共享内存
    ethercat_shm->shm_slave13_online_status = sc_slave13_state.online;
    ethercat_shm->shm_slave13_operational_status = sc_slave13_state.operational;
    ethercat_shm->shm_slave13_al_state = sc_slave13_state.al_state;

    // 获取从站14状态
    ecrt_slave_config_state(sc_slave14, &sc_slave14_state);
    
    // 更新从站14状态到共享内存
    ethercat_shm->shm_slave14_online_status = sc_slave14_state.online;
    ethercat_shm->shm_slave14_operational_status = sc_slave14_state.operational;
    ethercat_shm->shm_slave14_al_state = sc_slave14_state.al_state;

    // 获取从站15状态
    ecrt_slave_config_state(sc_slave15, &sc_slave15_state);
    
    // 更新从站15状态到共享内存
    ethercat_shm->shm_slave15_online_status = sc_slave15_state.online;
    ethercat_shm->shm_slave15_operational_status = sc_slave15_state.operational;
    ethercat_shm->shm_slave15_al_state = sc_slave15_state.al_state;

    // 获取从站16状态
    ecrt_slave_config_state(sc_slave16, &sc_slave16_state);
    
    // 更新从站16状态到共享内存
    ethercat_shm->shm_slave16_online_status = sc_slave16_state.online;
    ethercat_shm->shm_slave16_operational_status = sc_slave16_state.operational;
    ethercat_shm->shm_slave16_al_state = sc_slave16_state.al_state;

    // 获取从站17状态
    ecrt_slave_config_state(sc_slave17, &sc_slave17_state);
    
    // 更新从站17状态到共享内存
    ethercat_shm->shm_slave17_online_status = sc_slave17_state.online;
    ethercat_shm->shm_slave17_operational_status = sc_slave17_state.operational;
    ethercat_shm->shm_slave17_al_state = sc_slave17_state.al_state;

    // 获取从站18状态
    ecrt_slave_config_state(sc_slave18, &sc_slave18_state);
    
    // 更新从站18状态到共享内存
    ethercat_shm->shm_slave18_online_status = sc_slave18_state.online;
    ethercat_shm->shm_slave18_operational_status = sc_slave18_state.operational;
    ethercat_shm->shm_slave18_al_state = sc_slave18_state.al_state;

    // 获取从站19状态
    ecrt_slave_config_state(sc_slave19, &sc_slave19_state);
    
    // 更新从站19状态到共享内存
    ethercat_shm->shm_slave19_online_status = sc_slave19_state.online;
    ethercat_shm->shm_slave19_operational_status = sc_slave19_state.operational;
    ethercat_shm->shm_slave19_al_state = sc_slave19_state.al_state;

    // 获取从站20状态
    ecrt_slave_config_state(sc_slave20, &sc_slave20_state);
    
    // 更新从站20状态到共享内存
    ethercat_shm->shm_slave20_online_status = sc_slave20_state.online;
    ethercat_shm->shm_slave20_operational_status = sc_slave20_state.operational;
    ethercat_shm->shm_slave20_al_state = sc_slave20_state.al_state;

    // 获取从站21状态
    ecrt_slave_config_state(sc_slave21, &sc_slave21_state);
    
    // 更新从站21状态到共享内存
    ethercat_shm->shm_slave21_online_status = sc_slave21_state.online;
    ethercat_shm->shm_slave21_operational_status = sc_slave21_state.operational;
    ethercat_shm->shm_slave21_al_state = sc_slave21_state.al_state;

    // 获取从站22状态
    ecrt_slave_config_state(sc_slave22, &sc_slave22_state);
    
    // 更新从站22状态到共享内存
    ethercat_shm->shm_slave22_online_status = sc_slave22_state.online;
    ethercat_shm->shm_slave22_operational_status = sc_slave22_state.operational;
    ethercat_shm->shm_slave22_al_state = sc_slave22_state.al_state;

    // 获取从站23状态
    ecrt_slave_config_state(sc_slave23, &sc_slave23_state);
    
    // 更新从站23状态到共享内存
    ethercat_shm->shm_slave23_online_status = sc_slave23_state.online;
    ethercat_shm->shm_slave23_operational_status = sc_slave23_state.operational;
    ethercat_shm->shm_slave23_al_state = sc_slave23_state.al_state;

    // 获取从站24状态
    ecrt_slave_config_state(sc_slave24, &sc_slave24_state);
    
    // 更新从站24状态到共享内存
    ethercat_shm->shm_slave24_online_status = sc_slave24_state.online;
    ethercat_shm->shm_slave24_operational_status = sc_slave24_state.operational;
    ethercat_shm->shm_slave24_al_state = sc_slave24_state.al_state;

    // 获取从站25状态
    ecrt_slave_config_state(sc_slave25, &sc_slave25_state);
    
    // 更新从站25状态到共享内存
    ethercat_shm->shm_slave25_online_status = sc_slave25_state.online;
    ethercat_shm->shm_slave25_operational_status = sc_slave25_state.operational;
    ethercat_shm->shm_slave25_al_state = sc_slave25_state.al_state;

    // 获取从站26状态
    ecrt_slave_config_state(sc_slave26, &sc_slave26_state);
    
    // 更新从站26状态到共享内存
    ethercat_shm->shm_slave26_online_status = sc_slave26_state.online;
    ethercat_shm->shm_slave26_operational_status = sc_slave26_state.operational;
    ethercat_shm->shm_slave26_al_state = sc_slave26_state.al_state;

    // 获取从站27状态
    ecrt_slave_config_state(sc_slave27, &sc_slave27_state);
    
    // 更新从站27状态到共享内存
    ethercat_shm->shm_slave27_online_status = sc_slave27_state.online;
    ethercat_shm->shm_slave27_operational_status = sc_slave27_state.operational;
    ethercat_shm->shm_slave27_al_state = sc_slave27_state.al_state;

    // 获取从站28状态
    ecrt_slave_config_state(sc_slave28, &sc_slave28_state);
    
    // 更新从站28状态到共享内存
    ethercat_shm->shm_slave28_online_status = sc_slave28_state.online;
    ethercat_shm->shm_slave28_operational_status = sc_slave28_state.operational;
    ethercat_shm->shm_slave28_al_state = sc_slave28_state.al_state;

    // 获取从站29状态
    ecrt_slave_config_state(sc_slave29, &sc_slave29_state);
    
    // 更新从站29状态到共享内存
    ethercat_shm->shm_slave29_online_status = sc_slave29_state.online;
    ethercat_shm->shm_slave29_operational_status = sc_slave29_state.operational;
    ethercat_shm->shm_slave29_al_state = sc_slave29_state.al_state;

    // 获取从站30状态
    ecrt_slave_config_state(sc_slave30, &sc_slave30_state);
    
    // 更新从站30状态到共享内存
    ethercat_shm->shm_slave30_online_status = sc_slave30_state.online;
    ethercat_shm->shm_slave30_operational_status = sc_slave30_state.operational;
    ethercat_shm->shm_slave30_al_state = sc_slave30_state.al_state;

    // 获取从站31状态
    ecrt_slave_config_state(sc_slave31, &sc_slave31_state);
    
    // 更新从站31状态到共享内存
    ethercat_shm->shm_slave31_online_status = sc_slave31_state.online;
    ethercat_shm->shm_slave31_operational_status = sc_slave31_state.operational;
    ethercat_shm->shm_slave31_al_state = sc_slave31_state.al_state;

    // 获取从站32状态
    ecrt_slave_config_state(sc_slave32, &sc_slave32_state);
    
    // 更新从站32状态到共享内存
    ethercat_shm->shm_slave32_online_status = sc_slave32_state.online;
    ethercat_shm->shm_slave32_operational_status = sc_slave32_state.operational;
    ethercat_shm->shm_slave32_al_state = sc_slave32_state.al_state;

    // 获取从站33状态
    ecrt_slave_config_state(sc_slave33, &sc_slave33_state);
    
    // 更新从站33状态到共享内存
    ethercat_shm->shm_slave33_online_status = sc_slave33_state.online;
    ethercat_shm->shm_slave33_operational_status = sc_slave33_state.operational;
    ethercat_shm->shm_slave33_al_state = sc_slave33_state.al_state;

    // 获取从站34状态
    ecrt_slave_config_state(sc_slave34, &sc_slave34_state);
    
    // 更新从站34状态到共享内存
    ethercat_shm->shm_slave34_online_status = sc_slave34_state.online;
    ethercat_shm->shm_slave34_operational_status = sc_slave34_state.operational;
    ethercat_shm->shm_slave34_al_state = sc_slave34_state.al_state;

    // 获取从站35状态
    ecrt_slave_config_state(sc_slave35, &sc_slave35_state);
    
    // 更新从站35状态到共享内存
    ethercat_shm->shm_slave35_online_status = sc_slave35_state.online;
    ethercat_shm->shm_slave35_operational_status = sc_slave35_state.operational;
    ethercat_shm->shm_slave35_al_state = sc_slave35_state.al_state;

    // 获取从站36状态
    ecrt_slave_config_state(sc_slave36, &sc_slave36_state);
    
    // 更新从站36状态到共享内存
    ethercat_shm->shm_slave36_online_status = sc_slave36_state.online;
    ethercat_shm->shm_slave36_operational_status = sc_slave36_state.operational;
    ethercat_shm->shm_slave36_al_state = sc_slave36_state.al_state;

    // 获取从站37状态
    ecrt_slave_config_state(sc_slave37, &sc_slave37_state);
    
    // 更新从站37状态到共享内存
    ethercat_shm->shm_slave37_online_status = sc_slave37_state.online;
    ethercat_shm->shm_slave37_operational_status = sc_slave37_state.operational;
    ethercat_shm->shm_slave37_al_state = sc_slave37_state.al_state;

    // 获取从站38状态
    ecrt_slave_config_state(sc_slave38, &sc_slave38_state);
    
    // 更新从站38状态到共享内存
    ethercat_shm->shm_slave38_online_status = sc_slave38_state.online;
    ethercat_shm->shm_slave38_operational_status = sc_slave38_state.operational;
    ethercat_shm->shm_slave38_al_state = sc_slave38_state.al_state;

    // 获取从站39状态
    ecrt_slave_config_state(sc_slave39, &sc_slave39_state);
    
    // 更新从站39状态到共享内存
    ethercat_shm->shm_slave39_online_status = sc_slave39_state.online;
    ethercat_shm->shm_slave39_operational_status = sc_slave39_state.operational;
    ethercat_shm->shm_slave39_al_state = sc_slave39_state.al_state;

    // 获取从站40状态
    ecrt_slave_config_state(sc_slave40, &sc_slave40_state);
    
    // 更新从站40状态到共享内存
    ethercat_shm->shm_slave40_online_status = sc_slave40_state.online;
    ethercat_shm->shm_slave40_operational_status = sc_slave40_state.operational;
    ethercat_shm->shm_slave40_al_state = sc_slave40_state.al_state;

    // 获取从站41状态
    ecrt_slave_config_state(sc_slave41, &sc_slave41_state);
    
    // 更新从站41状态到共享内存
    ethercat_shm->shm_slave41_online_status = sc_slave41_state.online;
    ethercat_shm->shm_slave41_operational_status = sc_slave41_state.operational;
    ethercat_shm->shm_slave41_al_state = sc_slave41_state.al_state;

    // 获取从站42状态
    ecrt_slave_config_state(sc_slave42, &sc_slave42_state);
    
    // 更新从站42状态到共享内存
    ethercat_shm->shm_slave42_online_status = sc_slave42_state.online;
    ethercat_shm->shm_slave42_operational_status = sc_slave42_state.operational;
    ethercat_shm->shm_slave42_al_state = sc_slave42_state.al_state;

    // 获取从站43状态
    ecrt_slave_config_state(sc_slave43, &sc_slave43_state);
    
    // 更新从站43状态到共享内存
    ethercat_shm->shm_slave43_online_status = sc_slave43_state.online;
    ethercat_shm->shm_slave43_operational_status = sc_slave43_state.operational;
    ethercat_shm->shm_slave43_al_state = sc_slave43_state.al_state;

    // 获取从站44状态
    ecrt_slave_config_state(sc_slave44, &sc_slave44_state);
    
    // 更新从站44状态到共享内存
    ethercat_shm->shm_slave44_online_status = sc_slave44_state.online;
    ethercat_shm->shm_slave44_operational_status = sc_slave44_state.operational;
    ethercat_shm->shm_slave44_al_state = sc_slave44_state.al_state;

    // 获取从站45状态
    ecrt_slave_config_state(sc_slave45, &sc_slave45_state);
    
    // 更新从站45状态到共享内存
    ethercat_shm->shm_slave45_online_status = sc_slave45_state.online;
    ethercat_shm->shm_slave45_operational_status = sc_slave45_state.operational;
    ethercat_shm->shm_slave45_al_state = sc_slave45_state.al_state;

    // 获取从站46状态
    ecrt_slave_config_state(sc_slave46, &sc_slave46_state);
    
    // 更新从站46状态到共享内存
    ethercat_shm->shm_slave46_online_status = sc_slave46_state.online;
    ethercat_shm->shm_slave46_operational_status = sc_slave46_state.operational;
    ethercat_shm->shm_slave46_al_state = sc_slave46_state.al_state;

    // 获取从站47状态
    ecrt_slave_config_state(sc_slave47, &sc_slave47_state);
    
    // 更新从站47状态到共享内存
    ethercat_shm->shm_slave47_online_status = sc_slave47_state.online;
    ethercat_shm->shm_slave47_operational_status = sc_slave47_state.operational;
    ethercat_shm->shm_slave47_al_state = sc_slave47_state.al_state;

    // Update shared memory with status
    ethercat_shm->shm_slave0_tx_shm_slave0_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave0_tx_shm_slave0_tx_0x6041_status_word);
    ethercat_shm->shm_slave1_tx_shm_slave1_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave1_tx_shm_slave1_tx_0x6041_status_word);
    ethercat_shm->shm_slave2_tx_shm_slave2_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave2_tx_shm_slave2_tx_0x6041_status_word);
    ethercat_shm->shm_slave3_tx_shm_slave3_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave3_tx_shm_slave3_tx_0x6041_status_word);
    ethercat_shm->shm_slave4_tx_shm_slave4_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave4_tx_shm_slave4_tx_0x6041_status_word);
    ethercat_shm->shm_slave5_tx_shm_slave5_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave5_tx_shm_slave5_tx_0x6041_status_word);
    ethercat_shm->shm_slave6_tx_shm_slave6_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave6_tx_shm_slave6_tx_0x6041_status_word);
    ethercat_shm->shm_slave7_tx_shm_slave7_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave7_tx_shm_slave7_tx_0x6041_status_word);
    ethercat_shm->shm_slave8_tx_shm_slave8_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave8_tx_shm_slave8_tx_0x6041_status_word);
    ethercat_shm->shm_slave9_tx_shm_slave9_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave9_tx_shm_slave9_tx_0x6041_status_word);
    ethercat_shm->shm_slave10_tx_shm_slave10_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave10_tx_shm_slave10_tx_0x6041_status_word);
    ethercat_shm->shm_slave11_tx_shm_slave11_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave11_tx_shm_slave11_tx_0x6041_status_word);
    ethercat_shm->shm_slave12_tx_shm_slave12_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave12_tx_shm_slave12_tx_0x6041_status_word);
    ethercat_shm->shm_slave13_tx_shm_slave13_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave13_tx_shm_slave13_tx_0x6041_status_word);
    ethercat_shm->shm_slave14_tx_shm_slave14_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave14_tx_shm_slave14_tx_0x6041_status_word);
    ethercat_shm->shm_slave15_tx_shm_slave15_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave15_tx_shm_slave15_tx_0x6041_status_word);
    ethercat_shm->shm_slave16_tx_shm_slave16_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave16_tx_shm_slave16_tx_0x6041_status_word);
    ethercat_shm->shm_slave17_tx_shm_slave17_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave17_tx_shm_slave17_tx_0x6041_status_word);
    ethercat_shm->shm_slave18_tx_shm_slave18_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave18_tx_shm_slave18_tx_0x6041_status_word);
    ethercat_shm->shm_slave19_tx_shm_slave19_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave19_tx_shm_slave19_tx_0x6041_status_word);
    ethercat_shm->shm_slave20_tx_shm_slave20_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave20_tx_shm_slave20_tx_0x6041_status_word);
    ethercat_shm->shm_slave21_tx_shm_slave21_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave21_tx_shm_slave21_tx_0x6041_status_word);
    ethercat_shm->shm_slave22_tx_shm_slave22_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave22_tx_shm_slave22_tx_0x6041_status_word);
    ethercat_shm->shm_slave23_tx_shm_slave23_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave23_tx_shm_slave23_tx_0x6041_status_word);
    ethercat_shm->shm_slave24_tx_shm_slave24_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave24_tx_shm_slave24_tx_0x6041_status_word);
    ethercat_shm->shm_slave25_tx_shm_slave25_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave25_tx_shm_slave25_tx_0x6041_status_word);
    ethercat_shm->shm_slave26_tx_shm_slave26_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave26_tx_shm_slave26_tx_0x6041_status_word);
    ethercat_shm->shm_slave27_tx_shm_slave27_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave27_tx_shm_slave27_tx_0x6041_status_word);
    ethercat_shm->shm_slave28_tx_shm_slave28_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave28_tx_shm_slave28_tx_0x6041_status_word);
    ethercat_shm->shm_slave29_tx_shm_slave29_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave29_tx_shm_slave29_tx_0x6041_status_word);
    ethercat_shm->shm_slave30_tx_shm_slave30_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave30_tx_shm_slave30_tx_0x6041_status_word);
    ethercat_shm->shm_slave31_tx_shm_slave31_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave31_tx_shm_slave31_tx_0x6041_status_word);
    ethercat_shm->shm_slave32_tx_shm_slave32_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave32_tx_shm_slave32_tx_0x6041_status_word);
    ethercat_shm->shm_slave33_tx_shm_slave33_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave33_tx_shm_slave33_tx_0x6041_status_word);
    ethercat_shm->shm_slave34_tx_shm_slave34_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave34_tx_shm_slave34_tx_0x6041_status_word);
    ethercat_shm->shm_slave35_tx_shm_slave35_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave35_tx_shm_slave35_tx_0x6041_status_word);
    ethercat_shm->shm_slave36_tx_shm_slave36_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave36_tx_shm_slave36_tx_0x6041_status_word);
    ethercat_shm->shm_slave37_tx_shm_slave37_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave37_tx_shm_slave37_tx_0x6041_status_word);
    ethercat_shm->shm_slave38_tx_shm_slave38_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave38_tx_shm_slave38_tx_0x6041_status_word);
    ethercat_shm->shm_slave39_tx_shm_slave39_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave39_tx_shm_slave39_tx_0x6041_status_word);
    ethercat_shm->shm_slave40_tx_shm_slave40_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave40_tx_shm_slave40_tx_0x6041_status_word);
    ethercat_shm->shm_slave41_tx_shm_slave41_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave41_tx_shm_slave41_tx_0x6041_status_word);
    ethercat_shm->shm_slave42_tx_shm_slave42_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave42_tx_shm_slave42_tx_0x6041_status_word);
    ethercat_shm->shm_slave43_tx_shm_slave43_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave43_tx_shm_slave43_tx_0x6041_status_word);
    ethercat_shm->shm_slave44_tx_shm_slave44_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave44_tx_shm_slave44_tx_0x6041_status_word);
    ethercat_shm->shm_slave45_tx_shm_slave45_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave45_tx_shm_slave45_tx_0x6041_status_word);
    ethercat_shm->shm_slave46_tx_shm_slave46_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave46_tx_shm_slave46_tx_0x6041_status_word);
    ethercat_shm->shm_slave47_tx_shm_slave47_tx_0x6041_status_word = EC_READ_U16(domain1_pd + offset.pdo_slave47_tx_shm_slave47_tx_0x6041_status_word);

    // Write to EtherCAT
    EC_WRITE_U16(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x6040_control_word, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x60ff_target_speed, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave0_rx_shm_slave0_rx_0x6060_operation_mode, ethercat_shm->shm_slave0_rx_shm_slave0_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave1_rx_shm_slave1_rx_0x6040_control_word, ethercat_shm->shm_slave1_rx_shm_slave1_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave1_rx_shm_slave1_rx_0x60ff_target_speed, ethercat_shm->shm_slave1_rx_shm_slave1_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave1_rx_shm_slave1_rx_0x6060_operation_mode, ethercat_shm->shm_slave1_rx_shm_slave1_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave2_rx_shm_slave2_rx_0x6040_control_word, ethercat_shm->shm_slave2_rx_shm_slave2_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave2_rx_shm_slave2_rx_0x60ff_target_speed, ethercat_shm->shm_slave2_rx_shm_slave2_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave2_rx_shm_slave2_rx_0x6060_operation_mode, ethercat_shm->shm_slave2_rx_shm_slave2_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave3_rx_shm_slave3_rx_0x6040_control_word, ethercat_shm->shm_slave3_rx_shm_slave3_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave3_rx_shm_slave3_rx_0x60ff_target_speed, ethercat_shm->shm_slave3_rx_shm_slave3_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave3_rx_shm_slave3_rx_0x6060_operation_mode, ethercat_shm->shm_slave3_rx_shm_slave3_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave4_rx_shm_slave4_rx_0x6040_control_word, ethercat_shm->shm_slave4_rx_shm_slave4_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave4_rx_shm_slave4_rx_0x60ff_target_speed, ethercat_shm->shm_slave4_rx_shm_slave4_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave4_rx_shm_slave4_rx_0x6060_operation_mode, ethercat_shm->shm_slave4_rx_shm_slave4_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave5_rx_shm_slave5_rx_0x6040_control_word, ethercat_shm->shm_slave5_rx_shm_slave5_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave5_rx_shm_slave5_rx_0x60ff_target_speed, ethercat_shm->shm_slave5_rx_shm_slave5_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave5_rx_shm_slave5_rx_0x6060_operation_mode, ethercat_shm->shm_slave5_rx_shm_slave5_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave6_rx_shm_slave6_rx_0x6040_control_word, ethercat_shm->shm_slave6_rx_shm_slave6_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave6_rx_shm_slave6_rx_0x60ff_target_speed, ethercat_shm->shm_slave6_rx_shm_slave6_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave6_rx_shm_slave6_rx_0x6060_operation_mode, ethercat_shm->shm_slave6_rx_shm_slave6_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave7_rx_shm_slave7_rx_0x6040_control_word, ethercat_shm->shm_slave7_rx_shm_slave7_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave7_rx_shm_slave7_rx_0x60ff_target_speed, ethercat_shm->shm_slave7_rx_shm_slave7_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave7_rx_shm_slave7_rx_0x6060_operation_mode, ethercat_shm->shm_slave7_rx_shm_slave7_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave8_rx_shm_slave8_rx_0x6040_control_word, ethercat_shm->shm_slave8_rx_shm_slave8_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave8_rx_shm_slave8_rx_0x60ff_target_speed, ethercat_shm->shm_slave8_rx_shm_slave8_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave8_rx_shm_slave8_rx_0x6060_operation_mode, ethercat_shm->shm_slave8_rx_shm_slave8_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave9_rx_shm_slave9_rx_0x6040_control_word, ethercat_shm->shm_slave9_rx_shm_slave9_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave9_rx_shm_slave9_rx_0x60ff_target_speed, ethercat_shm->shm_slave9_rx_shm_slave9_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave9_rx_shm_slave9_rx_0x6060_operation_mode, ethercat_shm->shm_slave9_rx_shm_slave9_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave10_rx_shm_slave10_rx_0x6040_control_word, ethercat_shm->shm_slave10_rx_shm_slave10_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave10_rx_shm_slave10_rx_0x60ff_target_speed, ethercat_shm->shm_slave10_rx_shm_slave10_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave10_rx_shm_slave10_rx_0x6060_operation_mode, ethercat_shm->shm_slave10_rx_shm_slave10_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave11_rx_shm_slave11_rx_0x6040_control_word, ethercat_shm->shm_slave11_rx_shm_slave11_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave11_rx_shm_slave11_rx_0x60ff_target_speed, ethercat_shm->shm_slave11_rx_shm_slave11_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave11_rx_shm_slave11_rx_0x6060_operation_mode, ethercat_shm->shm_slave11_rx_shm_slave11_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave12_rx_shm_slave12_rx_0x6040_control_word, ethercat_shm->shm_slave12_rx_shm_slave12_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave12_rx_shm_slave12_rx_0x60ff_target_speed, ethercat_shm->shm_slave12_rx_shm_slave12_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave12_rx_shm_slave12_rx_0x6060_operation_mode, ethercat_shm->shm_slave12_rx_shm_slave12_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave13_rx_shm_slave13_rx_0x6040_control_word, ethercat_shm->shm_slave13_rx_shm_slave13_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave13_rx_shm_slave13_rx_0x60ff_target_speed, ethercat_shm->shm_slave13_rx_shm_slave13_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave13_rx_shm_slave13_rx_0x6060_operation_mode, ethercat_shm->shm_slave13_rx_shm_slave13_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave14_rx_shm_slave14_rx_0x6040_control_word, ethercat_shm->shm_slave14_rx_shm_slave14_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave14_rx_shm_slave14_rx_0x60ff_target_speed, ethercat_shm->shm_slave14_rx_shm_slave14_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave14_rx_shm_slave14_rx_0x6060_operation_mode, ethercat_shm->shm_slave14_rx_shm_slave14_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave15_rx_shm_slave15_rx_0x6040_control_word, ethercat_shm->shm_slave15_rx_shm_slave15_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave15_rx_shm_slave15_rx_0x60ff_target_speed, ethercat_shm->shm_slave15_rx_shm_slave15_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave15_rx_shm_slave15_rx_0x6060_operation_mode, ethercat_shm->shm_slave15_rx_shm_slave15_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave16_rx_shm_slave16_rx_0x6040_control_word, ethercat_shm->shm_slave16_rx_shm_slave16_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave16_rx_shm_slave16_rx_0x60ff_target_speed, ethercat_shm->shm_slave16_rx_shm_slave16_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave16_rx_shm_slave16_rx_0x6060_operation_mode, ethercat_shm->shm_slave16_rx_shm_slave16_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave17_rx_shm_slave17_rx_0x6040_control_word, ethercat_shm->shm_slave17_rx_shm_slave17_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave17_rx_shm_slave17_rx_0x60ff_target_speed, ethercat_shm->shm_slave17_rx_shm_slave17_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave17_rx_shm_slave17_rx_0x6060_operation_mode, ethercat_shm->shm_slave17_rx_shm_slave17_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave18_rx_shm_slave18_rx_0x6040_control_word, ethercat_shm->shm_slave18_rx_shm_slave18_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave18_rx_shm_slave18_rx_0x60ff_target_speed, ethercat_shm->shm_slave18_rx_shm_slave18_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave18_rx_shm_slave18_rx_0x6060_operation_mode, ethercat_shm->shm_slave18_rx_shm_slave18_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave19_rx_shm_slave19_rx_0x6040_control_word, ethercat_shm->shm_slave19_rx_shm_slave19_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave19_rx_shm_slave19_rx_0x60ff_target_speed, ethercat_shm->shm_slave19_rx_shm_slave19_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave19_rx_shm_slave19_rx_0x6060_operation_mode, ethercat_shm->shm_slave19_rx_shm_slave19_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave20_rx_shm_slave20_rx_0x6040_control_word, ethercat_shm->shm_slave20_rx_shm_slave20_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave20_rx_shm_slave20_rx_0x60ff_target_speed, ethercat_shm->shm_slave20_rx_shm_slave20_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave20_rx_shm_slave20_rx_0x6060_operation_mode, ethercat_shm->shm_slave20_rx_shm_slave20_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave21_rx_shm_slave21_rx_0x6040_control_word, ethercat_shm->shm_slave21_rx_shm_slave21_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave21_rx_shm_slave21_rx_0x60ff_target_speed, ethercat_shm->shm_slave21_rx_shm_slave21_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave21_rx_shm_slave21_rx_0x6060_operation_mode, ethercat_shm->shm_slave21_rx_shm_slave21_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave22_rx_shm_slave22_rx_0x6040_control_word, ethercat_shm->shm_slave22_rx_shm_slave22_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave22_rx_shm_slave22_rx_0x60ff_target_speed, ethercat_shm->shm_slave22_rx_shm_slave22_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave22_rx_shm_slave22_rx_0x6060_operation_mode, ethercat_shm->shm_slave22_rx_shm_slave22_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave23_rx_shm_slave23_rx_0x6040_control_word, ethercat_shm->shm_slave23_rx_shm_slave23_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave23_rx_shm_slave23_rx_0x60ff_target_speed, ethercat_shm->shm_slave23_rx_shm_slave23_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave23_rx_shm_slave23_rx_0x6060_operation_mode, ethercat_shm->shm_slave23_rx_shm_slave23_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave24_rx_shm_slave24_rx_0x6040_control_word, ethercat_shm->shm_slave24_rx_shm_slave24_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave24_rx_shm_slave24_rx_0x60ff_target_speed, ethercat_shm->shm_slave24_rx_shm_slave24_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave24_rx_shm_slave24_rx_0x6060_operation_mode, ethercat_shm->shm_slave24_rx_shm_slave24_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave25_rx_shm_slave25_rx_0x6040_control_word, ethercat_shm->shm_slave25_rx_shm_slave25_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave25_rx_shm_slave25_rx_0x60ff_target_speed, ethercat_shm->shm_slave25_rx_shm_slave25_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave25_rx_shm_slave25_rx_0x6060_operation_mode, ethercat_shm->shm_slave25_rx_shm_slave25_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave26_rx_shm_slave26_rx_0x6040_control_word, ethercat_shm->shm_slave26_rx_shm_slave26_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave26_rx_shm_slave26_rx_0x60ff_target_speed, ethercat_shm->shm_slave26_rx_shm_slave26_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave26_rx_shm_slave26_rx_0x6060_operation_mode, ethercat_shm->shm_slave26_rx_shm_slave26_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave27_rx_shm_slave27_rx_0x6040_control_word, ethercat_shm->shm_slave27_rx_shm_slave27_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave27_rx_shm_slave27_rx_0x60ff_target_speed, ethercat_shm->shm_slave27_rx_shm_slave27_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave27_rx_shm_slave27_rx_0x6060_operation_mode, ethercat_shm->shm_slave27_rx_shm_slave27_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave28_rx_shm_slave28_rx_0x6040_control_word, ethercat_shm->shm_slave28_rx_shm_slave28_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave28_rx_shm_slave28_rx_0x60ff_target_speed, ethercat_shm->shm_slave28_rx_shm_slave28_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave28_rx_shm_slave28_rx_0x6060_operation_mode, ethercat_shm->shm_slave28_rx_shm_slave28_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave29_rx_shm_slave29_rx_0x6040_control_word, ethercat_shm->shm_slave29_rx_shm_slave29_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave29_rx_shm_slave29_rx_0x60ff_target_speed, ethercat_shm->shm_slave29_rx_shm_slave29_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave29_rx_shm_slave29_rx_0x6060_operation_mode, ethercat_shm->shm_slave29_rx_shm_slave29_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave30_rx_shm_slave30_rx_0x6040_control_word, ethercat_shm->shm_slave30_rx_shm_slave30_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave30_rx_shm_slave30_rx_0x60ff_target_speed, ethercat_shm->shm_slave30_rx_shm_slave30_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave30_rx_shm_slave30_rx_0x6060_operation_mode, ethercat_shm->shm_slave30_rx_shm_slave30_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave31_rx_shm_slave31_rx_0x6040_control_word, ethercat_shm->shm_slave31_rx_shm_slave31_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave31_rx_shm_slave31_rx_0x60ff_target_speed, ethercat_shm->shm_slave31_rx_shm_slave31_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave31_rx_shm_slave31_rx_0x6060_operation_mode, ethercat_shm->shm_slave31_rx_shm_slave31_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave32_rx_shm_slave32_rx_0x6040_control_word, ethercat_shm->shm_slave32_rx_shm_slave32_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave32_rx_shm_slave32_rx_0x60ff_target_speed, ethercat_shm->shm_slave32_rx_shm_slave32_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave32_rx_shm_slave32_rx_0x6060_operation_mode, ethercat_shm->shm_slave32_rx_shm_slave32_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave33_rx_shm_slave33_rx_0x6040_control_word, ethercat_shm->shm_slave33_rx_shm_slave33_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave33_rx_shm_slave33_rx_0x60ff_target_speed, ethercat_shm->shm_slave33_rx_shm_slave33_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave33_rx_shm_slave33_rx_0x6060_operation_mode, ethercat_shm->shm_slave33_rx_shm_slave33_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave34_rx_shm_slave34_rx_0x6040_control_word, ethercat_shm->shm_slave34_rx_shm_slave34_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave34_rx_shm_slave34_rx_0x60ff_target_speed, ethercat_shm->shm_slave34_rx_shm_slave34_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave34_rx_shm_slave34_rx_0x6060_operation_mode, ethercat_shm->shm_slave34_rx_shm_slave34_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave35_rx_shm_slave35_rx_0x6040_control_word, ethercat_shm->shm_slave35_rx_shm_slave35_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave35_rx_shm_slave35_rx_0x60ff_target_speed, ethercat_shm->shm_slave35_rx_shm_slave35_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave35_rx_shm_slave35_rx_0x6060_operation_mode, ethercat_shm->shm_slave35_rx_shm_slave35_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave36_rx_shm_slave36_rx_0x6040_control_word, ethercat_shm->shm_slave36_rx_shm_slave36_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave36_rx_shm_slave36_rx_0x60ff_target_speed, ethercat_shm->shm_slave36_rx_shm_slave36_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave36_rx_shm_slave36_rx_0x6060_operation_mode, ethercat_shm->shm_slave36_rx_shm_slave36_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave37_rx_shm_slave37_rx_0x6040_control_word, ethercat_shm->shm_slave37_rx_shm_slave37_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave37_rx_shm_slave37_rx_0x60ff_target_speed, ethercat_shm->shm_slave37_rx_shm_slave37_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave37_rx_shm_slave37_rx_0x6060_operation_mode, ethercat_shm->shm_slave37_rx_shm_slave37_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave38_rx_shm_slave38_rx_0x6040_control_word, ethercat_shm->shm_slave38_rx_shm_slave38_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave38_rx_shm_slave38_rx_0x60ff_target_speed, ethercat_shm->shm_slave38_rx_shm_slave38_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave38_rx_shm_slave38_rx_0x6060_operation_mode, ethercat_shm->shm_slave38_rx_shm_slave38_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave39_rx_shm_slave39_rx_0x6040_control_word, ethercat_shm->shm_slave39_rx_shm_slave39_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave39_rx_shm_slave39_rx_0x60ff_target_speed, ethercat_shm->shm_slave39_rx_shm_slave39_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave39_rx_shm_slave39_rx_0x6060_operation_mode, ethercat_shm->shm_slave39_rx_shm_slave39_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave40_rx_shm_slave40_rx_0x6040_control_word, ethercat_shm->shm_slave40_rx_shm_slave40_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave40_rx_shm_slave40_rx_0x60ff_target_speed, ethercat_shm->shm_slave40_rx_shm_slave40_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave40_rx_shm_slave40_rx_0x6060_operation_mode, ethercat_shm->shm_slave40_rx_shm_slave40_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave41_rx_shm_slave41_rx_0x6040_control_word, ethercat_shm->shm_slave41_rx_shm_slave41_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave41_rx_shm_slave41_rx_0x60ff_target_speed, ethercat_shm->shm_slave41_rx_shm_slave41_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave41_rx_shm_slave41_rx_0x6060_operation_mode, ethercat_shm->shm_slave41_rx_shm_slave41_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave42_rx_shm_slave42_rx_0x6040_control_word, ethercat_shm->shm_slave42_rx_shm_slave42_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave42_rx_shm_slave42_rx_0x60ff_target_speed, ethercat_shm->shm_slave42_rx_shm_slave42_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave42_rx_shm_slave42_rx_0x6060_operation_mode, ethercat_shm->shm_slave42_rx_shm_slave42_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave43_rx_shm_slave43_rx_0x6040_control_word, ethercat_shm->shm_slave43_rx_shm_slave43_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave43_rx_shm_slave43_rx_0x60ff_target_speed, ethercat_shm->shm_slave43_rx_shm_slave43_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave43_rx_shm_slave43_rx_0x6060_operation_mode, ethercat_shm->shm_slave43_rx_shm_slave43_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave44_rx_shm_slave44_rx_0x6040_control_word, ethercat_shm->shm_slave44_rx_shm_slave44_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave44_rx_shm_slave44_rx_0x60ff_target_speed, ethercat_shm->shm_slave44_rx_shm_slave44_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave44_rx_shm_slave44_rx_0x6060_operation_mode, ethercat_shm->shm_slave44_rx_shm_slave44_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave45_rx_shm_slave45_rx_0x6040_control_word, ethercat_shm->shm_slave45_rx_shm_slave45_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave45_rx_shm_slave45_rx_0x60ff_target_speed, ethercat_shm->shm_slave45_rx_shm_slave45_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave45_rx_shm_slave45_rx_0x6060_operation_mode, ethercat_shm->shm_slave45_rx_shm_slave45_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave46_rx_shm_slave46_rx_0x6040_control_word, ethercat_shm->shm_slave46_rx_shm_slave46_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave46_rx_shm_slave46_rx_0x60ff_target_speed, ethercat_shm->shm_slave46_rx_shm_slave46_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave46_rx_shm_slave46_rx_0x6060_operation_mode, ethercat_shm->shm_slave46_rx_shm_slave46_rx_0x6060_operation_mode);
    EC_WRITE_U16(domain1_pd + offset.pdo_slave47_rx_shm_slave47_rx_0x6040_control_word, ethercat_shm->shm_slave47_rx_shm_slave47_rx_0x6040_control_word);
    EC_WRITE_U32(domain1_pd + offset.pdo_slave47_rx_shm_slave47_rx_0x60ff_target_speed, ethercat_shm->shm_slave47_rx_shm_slave47_rx_0x60ff_target_speed);
    EC_WRITE_U8(domain1_pd + offset.pdo_slave47_rx_shm_slave47_rx_0x6060_operation_mode, ethercat_shm->shm_slave47_rx_shm_slave47_rx_0x6060_operation_mode);

    // Send process data
    ecrt_domain_queue(domain1);
    ecrt_master_sync_slave_clocks(master);
    ecrt_master_sync_reference_clock(master);
    ecrt_master_send(master);

    if (last_cycle) {
        printf("Executing final cycle...\n");
        
        // 将从站切换到预运行状态
        printf("Switching slaves to PREOP state...\n");
        ecrt_master_deactivate(master);
        
        // 释放EtherCAT主站
        if (master) {
            printf("Releasing master...\n");
            ecrt_release_master(master);
        }
        
        // 清理共享内存
        cleanup_shm();
        
        printf("Shutdown complete\n");
        run = 0;  // 这将导致主循环退出
    }
}
int main(int argc, char **argv) {
    // 启动时先删除可能存在的 ALL OP 标记文件
    char op_file[64];
    snprintf(op_file, sizeof(op_file), "/tmp/MASTER%d_ALL_OP", MASTER_INDEX);
    remove(op_file);

    // Set up signal handler for cleanup
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // Lock memory to prevent paging
    if (mlockall(MCL_CURRENT | MCL_FUTURE) == -1) {
        perror("mlockall failed");
        return -1;
    }

    // Create shared memory
    create_shm();
    
    // Initialize EtherCAT master
    printf("Requesting master...\n");
    master = ecrt_request_master(MASTER_INDEX);
    if (!master) exit(EXIT_FAILURE);
    
    domain1 = ecrt_master_create_domain(master);
    if (!domain1) exit(EXIT_FAILURE);

    // Configure slaves
    printf("Configuring all slaves...\n");
    
    // 创建从站配置数组
    ec_slave_config_t *slave_configs[48];
    
    // 批量获取所有从站配置
    
    printf("Configuring slave 0...\n");
    slave_configs[0] = ecrt_master_slave_config(master, slave0_POS, slave0_VID_PID);
    if (!slave_configs[0]) {
        fprintf(stderr, "Failed to get slave0 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 0
    printf("Configuring SDOs for slave 0...\n");
    
    
    printf("SDO configuration completed for slave 0\n");


    printf("Configuring slave 1...\n");
    slave_configs[1] = ecrt_master_slave_config(master, slave1_POS, slave1_VID_PID);
    if (!slave_configs[1]) {
        fprintf(stderr, "Failed to get slave1 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 1
    printf("Configuring SDOs for slave 1...\n");
    
    
    printf("SDO configuration completed for slave 1\n");


    printf("Configuring slave 2...\n");
    slave_configs[2] = ecrt_master_slave_config(master, slave2_POS, slave2_VID_PID);
    if (!slave_configs[2]) {
        fprintf(stderr, "Failed to get slave2 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 2
    printf("Configuring SDOs for slave 2...\n");
    
    
    printf("SDO configuration completed for slave 2\n");


    printf("Configuring slave 3...\n");
    slave_configs[3] = ecrt_master_slave_config(master, slave3_POS, slave3_VID_PID);
    if (!slave_configs[3]) {
        fprintf(stderr, "Failed to get slave3 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 3
    printf("Configuring SDOs for slave 3...\n");
    
    
    printf("SDO configuration completed for slave 3\n");


    printf("Configuring slave 4...\n");
    slave_configs[4] = ecrt_master_slave_config(master, slave4_POS, slave4_VID_PID);
    if (!slave_configs[4]) {
        fprintf(stderr, "Failed to get slave4 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 4
    printf("Configuring SDOs for slave 4...\n");
    
    
    printf("SDO configuration completed for slave 4\n");


    printf("Configuring slave 5...\n");
    slave_configs[5] = ecrt_master_slave_config(master, slave5_POS, slave5_VID_PID);
    if (!slave_configs[5]) {
        fprintf(stderr, "Failed to get slave5 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 5
    printf("Configuring SDOs for slave 5...\n");
    
    
    printf("SDO configuration completed for slave 5\n");


    printf("Configuring slave 6...\n");
    slave_configs[6] = ecrt_master_slave_config(master, slave6_POS, slave6_VID_PID);
    if (!slave_configs[6]) {
        fprintf(stderr, "Failed to get slave6 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 6
    printf("Configuring SDOs for slave 6...\n");
    
    
    printf("SDO configuration completed for slave 6\n");


    printf("Configuring slave 7...\n");
    slave_configs[7] = ecrt_master_slave_config(master, slave7_POS, slave7_VID_PID);
    if (!slave_configs[7]) {
        fprintf(stderr, "Failed to get slave7 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 7
    printf("Configuring SDOs for slave 7...\n");
    
    
    printf("SDO configuration completed for slave 7\n");


    printf("Configuring slave 8...\n");
    slave_configs[8] = ecrt_master_slave_config(master, slave8_POS, slave8_VID_PID);
    if (!slave_configs[8]) {
        fprintf(stderr, "Failed to get slave8 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 8
    printf("Configuring SDOs for slave 8...\n");
    
    
    printf("SDO configuration completed for slave 8\n");


    printf("Configuring slave 9...\n");
    slave_configs[9] = ecrt_master_slave_config(master, slave9_POS, slave9_VID_PID);
    if (!slave_configs[9]) {
        fprintf(stderr, "Failed to get slave9 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 9
    printf("Configuring SDOs for slave 9...\n");
    
    
    printf("SDO configuration completed for slave 9\n");


    printf("Configuring slave 10...\n");
    slave_configs[10] = ecrt_master_slave_config(master, slave10_POS, slave10_VID_PID);
    if (!slave_configs[10]) {
        fprintf(stderr, "Failed to get slave10 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 10
    printf("Configuring SDOs for slave 10...\n");
    
    
    printf("SDO configuration completed for slave 10\n");


    printf("Configuring slave 11...\n");
    slave_configs[11] = ecrt_master_slave_config(master, slave11_POS, slave11_VID_PID);
    if (!slave_configs[11]) {
        fprintf(stderr, "Failed to get slave11 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 11
    printf("Configuring SDOs for slave 11...\n");
    
    
    printf("SDO configuration completed for slave 11\n");


    printf("Configuring slave 12...\n");
    slave_configs[12] = ecrt_master_slave_config(master, slave12_POS, slave12_VID_PID);
    if (!slave_configs[12]) {
        fprintf(stderr, "Failed to get slave12 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 12
    printf("Configuring SDOs for slave 12...\n");
    
    
    printf("SDO configuration completed for slave 12\n");


    printf("Configuring slave 13...\n");
    slave_configs[13] = ecrt_master_slave_config(master, slave13_POS, slave13_VID_PID);
    if (!slave_configs[13]) {
        fprintf(stderr, "Failed to get slave13 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 13
    printf("Configuring SDOs for slave 13...\n");
    
    
    printf("SDO configuration completed for slave 13\n");


    printf("Configuring slave 14...\n");
    slave_configs[14] = ecrt_master_slave_config(master, slave14_POS, slave14_VID_PID);
    if (!slave_configs[14]) {
        fprintf(stderr, "Failed to get slave14 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 14
    printf("Configuring SDOs for slave 14...\n");
    
    
    printf("SDO configuration completed for slave 14\n");


    printf("Configuring slave 15...\n");
    slave_configs[15] = ecrt_master_slave_config(master, slave15_POS, slave15_VID_PID);
    if (!slave_configs[15]) {
        fprintf(stderr, "Failed to get slave15 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 15
    printf("Configuring SDOs for slave 15...\n");
    
    
    printf("SDO configuration completed for slave 15\n");


    printf("Configuring slave 16...\n");
    slave_configs[16] = ecrt_master_slave_config(master, slave16_POS, slave16_VID_PID);
    if (!slave_configs[16]) {
        fprintf(stderr, "Failed to get slave16 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 16
    printf("Configuring SDOs for slave 16...\n");
    
    
    printf("SDO configuration completed for slave 16\n");


    printf("Configuring slave 17...\n");
    slave_configs[17] = ecrt_master_slave_config(master, slave17_POS, slave17_VID_PID);
    if (!slave_configs[17]) {
        fprintf(stderr, "Failed to get slave17 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 17
    printf("Configuring SDOs for slave 17...\n");
    
    
    printf("SDO configuration completed for slave 17\n");


    printf("Configuring slave 18...\n");
    slave_configs[18] = ecrt_master_slave_config(master, slave18_POS, slave18_VID_PID);
    if (!slave_configs[18]) {
        fprintf(stderr, "Failed to get slave18 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 18
    printf("Configuring SDOs for slave 18...\n");
    
    
    printf("SDO configuration completed for slave 18\n");


    printf("Configuring slave 19...\n");
    slave_configs[19] = ecrt_master_slave_config(master, slave19_POS, slave19_VID_PID);
    if (!slave_configs[19]) {
        fprintf(stderr, "Failed to get slave19 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 19
    printf("Configuring SDOs for slave 19...\n");
    
    
    printf("SDO configuration completed for slave 19\n");


    printf("Configuring slave 20...\n");
    slave_configs[20] = ecrt_master_slave_config(master, slave20_POS, slave20_VID_PID);
    if (!slave_configs[20]) {
        fprintf(stderr, "Failed to get slave20 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 20
    printf("Configuring SDOs for slave 20...\n");
    
    
    printf("SDO configuration completed for slave 20\n");


    printf("Configuring slave 21...\n");
    slave_configs[21] = ecrt_master_slave_config(master, slave21_POS, slave21_VID_PID);
    if (!slave_configs[21]) {
        fprintf(stderr, "Failed to get slave21 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 21
    printf("Configuring SDOs for slave 21...\n");
    
    
    printf("SDO configuration completed for slave 21\n");


    printf("Configuring slave 22...\n");
    slave_configs[22] = ecrt_master_slave_config(master, slave22_POS, slave22_VID_PID);
    if (!slave_configs[22]) {
        fprintf(stderr, "Failed to get slave22 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 22
    printf("Configuring SDOs for slave 22...\n");
    
    
    printf("SDO configuration completed for slave 22\n");


    printf("Configuring slave 23...\n");
    slave_configs[23] = ecrt_master_slave_config(master, slave23_POS, slave23_VID_PID);
    if (!slave_configs[23]) {
        fprintf(stderr, "Failed to get slave23 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 23
    printf("Configuring SDOs for slave 23...\n");
    
    
    printf("SDO configuration completed for slave 23\n");


    printf("Configuring slave 24...\n");
    slave_configs[24] = ecrt_master_slave_config(master, slave24_POS, slave24_VID_PID);
    if (!slave_configs[24]) {
        fprintf(stderr, "Failed to get slave24 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 24
    printf("Configuring SDOs for slave 24...\n");
    
    
    printf("SDO configuration completed for slave 24\n");


    printf("Configuring slave 25...\n");
    slave_configs[25] = ecrt_master_slave_config(master, slave25_POS, slave25_VID_PID);
    if (!slave_configs[25]) {
        fprintf(stderr, "Failed to get slave25 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 25
    printf("Configuring SDOs for slave 25...\n");
    
    
    printf("SDO configuration completed for slave 25\n");


    printf("Configuring slave 26...\n");
    slave_configs[26] = ecrt_master_slave_config(master, slave26_POS, slave26_VID_PID);
    if (!slave_configs[26]) {
        fprintf(stderr, "Failed to get slave26 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 26
    printf("Configuring SDOs for slave 26...\n");
    
    
    printf("SDO configuration completed for slave 26\n");


    printf("Configuring slave 27...\n");
    slave_configs[27] = ecrt_master_slave_config(master, slave27_POS, slave27_VID_PID);
    if (!slave_configs[27]) {
        fprintf(stderr, "Failed to get slave27 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 27
    printf("Configuring SDOs for slave 27...\n");
    
    
    printf("SDO configuration completed for slave 27\n");


    printf("Configuring slave 28...\n");
    slave_configs[28] = ecrt_master_slave_config(master, slave28_POS, slave28_VID_PID);
    if (!slave_configs[28]) {
        fprintf(stderr, "Failed to get slave28 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 28
    printf("Configuring SDOs for slave 28...\n");
    
    
    printf("SDO configuration completed for slave 28\n");


    printf("Configuring slave 29...\n");
    slave_configs[29] = ecrt_master_slave_config(master, slave29_POS, slave29_VID_PID);
    if (!slave_configs[29]) {
        fprintf(stderr, "Failed to get slave29 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 29
    printf("Configuring SDOs for slave 29...\n");
    
    
    printf("SDO configuration completed for slave 29\n");


    printf("Configuring slave 30...\n");
    slave_configs[30] = ecrt_master_slave_config(master, slave30_POS, slave30_VID_PID);
    if (!slave_configs[30]) {
        fprintf(stderr, "Failed to get slave30 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 30
    printf("Configuring SDOs for slave 30...\n");
    
    
    printf("SDO configuration completed for slave 30\n");


    printf("Configuring slave 31...\n");
    slave_configs[31] = ecrt_master_slave_config(master, slave31_POS, slave31_VID_PID);
    if (!slave_configs[31]) {
        fprintf(stderr, "Failed to get slave31 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 31
    printf("Configuring SDOs for slave 31...\n");
    
    
    printf("SDO configuration completed for slave 31\n");


    printf("Configuring slave 32...\n");
    slave_configs[32] = ecrt_master_slave_config(master, slave32_POS, slave32_VID_PID);
    if (!slave_configs[32]) {
        fprintf(stderr, "Failed to get slave32 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 32
    printf("Configuring SDOs for slave 32...\n");
    
    
    printf("SDO configuration completed for slave 32\n");


    printf("Configuring slave 33...\n");
    slave_configs[33] = ecrt_master_slave_config(master, slave33_POS, slave33_VID_PID);
    if (!slave_configs[33]) {
        fprintf(stderr, "Failed to get slave33 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 33
    printf("Configuring SDOs for slave 33...\n");
    
    
    printf("SDO configuration completed for slave 33\n");


    printf("Configuring slave 34...\n");
    slave_configs[34] = ecrt_master_slave_config(master, slave34_POS, slave34_VID_PID);
    if (!slave_configs[34]) {
        fprintf(stderr, "Failed to get slave34 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 34
    printf("Configuring SDOs for slave 34...\n");
    
    
    printf("SDO configuration completed for slave 34\n");


    printf("Configuring slave 35...\n");
    slave_configs[35] = ecrt_master_slave_config(master, slave35_POS, slave35_VID_PID);
    if (!slave_configs[35]) {
        fprintf(stderr, "Failed to get slave35 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 35
    printf("Configuring SDOs for slave 35...\n");
    
    
    printf("SDO configuration completed for slave 35\n");


    printf("Configuring slave 36...\n");
    slave_configs[36] = ecrt_master_slave_config(master, slave36_POS, slave36_VID_PID);
    if (!slave_configs[36]) {
        fprintf(stderr, "Failed to get slave36 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 36
    printf("Configuring SDOs for slave 36...\n");
    
    
    printf("SDO configuration completed for slave 36\n");


    printf("Configuring slave 37...\n");
    slave_configs[37] = ecrt_master_slave_config(master, slave37_POS, slave37_VID_PID);
    if (!slave_configs[37]) {
        fprintf(stderr, "Failed to get slave37 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 37
    printf("Configuring SDOs for slave 37...\n");
    
    
    printf("SDO configuration completed for slave 37\n");


    printf("Configuring slave 38...\n");
    slave_configs[38] = ecrt_master_slave_config(master, slave38_POS, slave38_VID_PID);
    if (!slave_configs[38]) {
        fprintf(stderr, "Failed to get slave38 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 38
    printf("Configuring SDOs for slave 38...\n");
    
    
    printf("SDO configuration completed for slave 38\n");


    printf("Configuring slave 39...\n");
    slave_configs[39] = ecrt_master_slave_config(master, slave39_POS, slave39_VID_PID);
    if (!slave_configs[39]) {
        fprintf(stderr, "Failed to get slave39 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 39
    printf("Configuring SDOs for slave 39...\n");
    
    
    printf("SDO configuration completed for slave 39\n");


    printf("Configuring slave 40...\n");
    slave_configs[40] = ecrt_master_slave_config(master, slave40_POS, slave40_VID_PID);
    if (!slave_configs[40]) {
        fprintf(stderr, "Failed to get slave40 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 40
    printf("Configuring SDOs for slave 40...\n");
    
    
    printf("SDO configuration completed for slave 40\n");


    printf("Configuring slave 41...\n");
    slave_configs[41] = ecrt_master_slave_config(master, slave41_POS, slave41_VID_PID);
    if (!slave_configs[41]) {
        fprintf(stderr, "Failed to get slave41 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 41
    printf("Configuring SDOs for slave 41...\n");
    
    
    printf("SDO configuration completed for slave 41\n");


    printf("Configuring slave 42...\n");
    slave_configs[42] = ecrt_master_slave_config(master, slave42_POS, slave42_VID_PID);
    if (!slave_configs[42]) {
        fprintf(stderr, "Failed to get slave42 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 42
    printf("Configuring SDOs for slave 42...\n");
    
    
    printf("SDO configuration completed for slave 42\n");


    printf("Configuring slave 43...\n");
    slave_configs[43] = ecrt_master_slave_config(master, slave43_POS, slave43_VID_PID);
    if (!slave_configs[43]) {
        fprintf(stderr, "Failed to get slave43 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 43
    printf("Configuring SDOs for slave 43...\n");
    
    
    printf("SDO configuration completed for slave 43\n");


    printf("Configuring slave 44...\n");
    slave_configs[44] = ecrt_master_slave_config(master, slave44_POS, slave44_VID_PID);
    if (!slave_configs[44]) {
        fprintf(stderr, "Failed to get slave44 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 44
    printf("Configuring SDOs for slave 44...\n");
    
    
    printf("SDO configuration completed for slave 44\n");


    printf("Configuring slave 45...\n");
    slave_configs[45] = ecrt_master_slave_config(master, slave45_POS, slave45_VID_PID);
    if (!slave_configs[45]) {
        fprintf(stderr, "Failed to get slave45 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 45
    printf("Configuring SDOs for slave 45...\n");
    
    
    printf("SDO configuration completed for slave 45\n");


    printf("Configuring slave 46...\n");
    slave_configs[46] = ecrt_master_slave_config(master, slave46_POS, slave46_VID_PID);
    if (!slave_configs[46]) {
        fprintf(stderr, "Failed to get slave46 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 46
    printf("Configuring SDOs for slave 46...\n");
    
    
    printf("SDO configuration completed for slave 46\n");


    printf("Configuring slave 47...\n");
    slave_configs[47] = ecrt_master_slave_config(master, slave47_POS, slave47_VID_PID);
    if (!slave_configs[47]) {
        fprintf(stderr, "Failed to get slave47 configuration!\n");
        exit(EXIT_FAILURE);
    }
    
    // Configure SDOs for slave 47
    printf("Configuring SDOs for slave 47...\n");
    
    
    printf("SDO configuration completed for slave 47\n");

    
    // 保存配置引用
    
    sc_slave0 = slave_configs[0];

    sc_slave1 = slave_configs[1];

    sc_slave2 = slave_configs[2];

    sc_slave3 = slave_configs[3];

    sc_slave4 = slave_configs[4];

    sc_slave5 = slave_configs[5];

    sc_slave6 = slave_configs[6];

    sc_slave7 = slave_configs[7];

    sc_slave8 = slave_configs[8];

    sc_slave9 = slave_configs[9];

    sc_slave10 = slave_configs[10];

    sc_slave11 = slave_configs[11];

    sc_slave12 = slave_configs[12];

    sc_slave13 = slave_configs[13];

    sc_slave14 = slave_configs[14];

    sc_slave15 = slave_configs[15];

    sc_slave16 = slave_configs[16];

    sc_slave17 = slave_configs[17];

    sc_slave18 = slave_configs[18];

    sc_slave19 = slave_configs[19];

    sc_slave20 = slave_configs[20];

    sc_slave21 = slave_configs[21];

    sc_slave22 = slave_configs[22];

    sc_slave23 = slave_configs[23];

    sc_slave24 = slave_configs[24];

    sc_slave25 = slave_configs[25];

    sc_slave26 = slave_configs[26];

    sc_slave27 = slave_configs[27];

    sc_slave28 = slave_configs[28];

    sc_slave29 = slave_configs[29];

    sc_slave30 = slave_configs[30];

    sc_slave31 = slave_configs[31];

    sc_slave32 = slave_configs[32];

    sc_slave33 = slave_configs[33];

    sc_slave34 = slave_configs[34];

    sc_slave35 = slave_configs[35];

    sc_slave36 = slave_configs[36];

    sc_slave37 = slave_configs[37];

    sc_slave38 = slave_configs[38];

    sc_slave39 = slave_configs[39];

    sc_slave40 = slave_configs[40];

    sc_slave41 = slave_configs[41];

    sc_slave42 = slave_configs[42];

    sc_slave43 = slave_configs[43];

    sc_slave44 = slave_configs[44];

    sc_slave45 = slave_configs[45];

    sc_slave46 = slave_configs[46];

    sc_slave47 = slave_configs[47];
    
    // 创建PDO配置线程
    pthread_t threads[48];
    int config_results[48] = {0};
    config_thread_param_t thread_params[48];
    
    printf("Starting parallel PDO configuration...\n");
    
    // 初始化PDO配置线程参数
    
    thread_params[0].slave_config = slave_configs[0];
    thread_params[0].sync_info = slave0_syncs;
    thread_params[0].result = &config_results[0];

    thread_params[1].slave_config = slave_configs[1];
    thread_params[1].sync_info = slave1_syncs;
    thread_params[1].result = &config_results[1];

    thread_params[2].slave_config = slave_configs[2];
    thread_params[2].sync_info = slave2_syncs;
    thread_params[2].result = &config_results[2];

    thread_params[3].slave_config = slave_configs[3];
    thread_params[3].sync_info = slave3_syncs;
    thread_params[3].result = &config_results[3];

    thread_params[4].slave_config = slave_configs[4];
    thread_params[4].sync_info = slave4_syncs;
    thread_params[4].result = &config_results[4];

    thread_params[5].slave_config = slave_configs[5];
    thread_params[5].sync_info = slave5_syncs;
    thread_params[5].result = &config_results[5];

    thread_params[6].slave_config = slave_configs[6];
    thread_params[6].sync_info = slave6_syncs;
    thread_params[6].result = &config_results[6];

    thread_params[7].slave_config = slave_configs[7];
    thread_params[7].sync_info = slave7_syncs;
    thread_params[7].result = &config_results[7];

    thread_params[8].slave_config = slave_configs[8];
    thread_params[8].sync_info = slave8_syncs;
    thread_params[8].result = &config_results[8];

    thread_params[9].slave_config = slave_configs[9];
    thread_params[9].sync_info = slave9_syncs;
    thread_params[9].result = &config_results[9];

    thread_params[10].slave_config = slave_configs[10];
    thread_params[10].sync_info = slave10_syncs;
    thread_params[10].result = &config_results[10];

    thread_params[11].slave_config = slave_configs[11];
    thread_params[11].sync_info = slave11_syncs;
    thread_params[11].result = &config_results[11];

    thread_params[12].slave_config = slave_configs[12];
    thread_params[12].sync_info = slave12_syncs;
    thread_params[12].result = &config_results[12];

    thread_params[13].slave_config = slave_configs[13];
    thread_params[13].sync_info = slave13_syncs;
    thread_params[13].result = &config_results[13];

    thread_params[14].slave_config = slave_configs[14];
    thread_params[14].sync_info = slave14_syncs;
    thread_params[14].result = &config_results[14];

    thread_params[15].slave_config = slave_configs[15];
    thread_params[15].sync_info = slave15_syncs;
    thread_params[15].result = &config_results[15];

    thread_params[16].slave_config = slave_configs[16];
    thread_params[16].sync_info = slave16_syncs;
    thread_params[16].result = &config_results[16];

    thread_params[17].slave_config = slave_configs[17];
    thread_params[17].sync_info = slave17_syncs;
    thread_params[17].result = &config_results[17];

    thread_params[18].slave_config = slave_configs[18];
    thread_params[18].sync_info = slave18_syncs;
    thread_params[18].result = &config_results[18];

    thread_params[19].slave_config = slave_configs[19];
    thread_params[19].sync_info = slave19_syncs;
    thread_params[19].result = &config_results[19];

    thread_params[20].slave_config = slave_configs[20];
    thread_params[20].sync_info = slave20_syncs;
    thread_params[20].result = &config_results[20];

    thread_params[21].slave_config = slave_configs[21];
    thread_params[21].sync_info = slave21_syncs;
    thread_params[21].result = &config_results[21];

    thread_params[22].slave_config = slave_configs[22];
    thread_params[22].sync_info = slave22_syncs;
    thread_params[22].result = &config_results[22];

    thread_params[23].slave_config = slave_configs[23];
    thread_params[23].sync_info = slave23_syncs;
    thread_params[23].result = &config_results[23];

    thread_params[24].slave_config = slave_configs[24];
    thread_params[24].sync_info = slave24_syncs;
    thread_params[24].result = &config_results[24];

    thread_params[25].slave_config = slave_configs[25];
    thread_params[25].sync_info = slave25_syncs;
    thread_params[25].result = &config_results[25];

    thread_params[26].slave_config = slave_configs[26];
    thread_params[26].sync_info = slave26_syncs;
    thread_params[26].result = &config_results[26];

    thread_params[27].slave_config = slave_configs[27];
    thread_params[27].sync_info = slave27_syncs;
    thread_params[27].result = &config_results[27];

    thread_params[28].slave_config = slave_configs[28];
    thread_params[28].sync_info = slave28_syncs;
    thread_params[28].result = &config_results[28];

    thread_params[29].slave_config = slave_configs[29];
    thread_params[29].sync_info = slave29_syncs;
    thread_params[29].result = &config_results[29];

    thread_params[30].slave_config = slave_configs[30];
    thread_params[30].sync_info = slave30_syncs;
    thread_params[30].result = &config_results[30];

    thread_params[31].slave_config = slave_configs[31];
    thread_params[31].sync_info = slave31_syncs;
    thread_params[31].result = &config_results[31];

    thread_params[32].slave_config = slave_configs[32];
    thread_params[32].sync_info = slave32_syncs;
    thread_params[32].result = &config_results[32];

    thread_params[33].slave_config = slave_configs[33];
    thread_params[33].sync_info = slave33_syncs;
    thread_params[33].result = &config_results[33];

    thread_params[34].slave_config = slave_configs[34];
    thread_params[34].sync_info = slave34_syncs;
    thread_params[34].result = &config_results[34];

    thread_params[35].slave_config = slave_configs[35];
    thread_params[35].sync_info = slave35_syncs;
    thread_params[35].result = &config_results[35];

    thread_params[36].slave_config = slave_configs[36];
    thread_params[36].sync_info = slave36_syncs;
    thread_params[36].result = &config_results[36];

    thread_params[37].slave_config = slave_configs[37];
    thread_params[37].sync_info = slave37_syncs;
    thread_params[37].result = &config_results[37];

    thread_params[38].slave_config = slave_configs[38];
    thread_params[38].sync_info = slave38_syncs;
    thread_params[38].result = &config_results[38];

    thread_params[39].slave_config = slave_configs[39];
    thread_params[39].sync_info = slave39_syncs;
    thread_params[39].result = &config_results[39];

    thread_params[40].slave_config = slave_configs[40];
    thread_params[40].sync_info = slave40_syncs;
    thread_params[40].result = &config_results[40];

    thread_params[41].slave_config = slave_configs[41];
    thread_params[41].sync_info = slave41_syncs;
    thread_params[41].result = &config_results[41];

    thread_params[42].slave_config = slave_configs[42];
    thread_params[42].sync_info = slave42_syncs;
    thread_params[42].result = &config_results[42];

    thread_params[43].slave_config = slave_configs[43];
    thread_params[43].sync_info = slave43_syncs;
    thread_params[43].result = &config_results[43];

    thread_params[44].slave_config = slave_configs[44];
    thread_params[44].sync_info = slave44_syncs;
    thread_params[44].result = &config_results[44];

    thread_params[45].slave_config = slave_configs[45];
    thread_params[45].sync_info = slave45_syncs;
    thread_params[45].result = &config_results[45];

    thread_params[46].slave_config = slave_configs[46];
    thread_params[46].sync_info = slave46_syncs;
    thread_params[46].result = &config_results[46];

    thread_params[47].slave_config = slave_configs[47];
    thread_params[47].sync_info = slave47_syncs;
    thread_params[47].result = &config_results[47];
    
    // 启动PDO配置线程
    for(int i = 0; i < 48; i++) {
        if (pthread_create(&threads[i], NULL, config_slave_thread, &thread_params[i])) {
            fprintf(stderr, "Failed to create PDO configuration thread %d\n", i);
            exit(EXIT_FAILURE);
        }
    }
    
    // 等待所有PDO配置线程完成
    for(int i = 0; i < 48; i++) {
        pthread_join(threads[i], NULL);
        if (config_results[i]) {
            fprintf(stderr, "Failed to configure PDOs for slave %d\n", i);
            exit(EXIT_FAILURE);
        }
    }
    
    printf("Parallel PDO configuration completed\n");

    // 配置 DC
    printf("Starting parallel DC configuration...\n");
    
    // 创建DC配置线程数组和参数
    pthread_t dc_threads[48];
    int dc_results[48] = {0};
    dc_config_thread_param_t dc_params[48];
    
    // 初始化DC配置参数
    
    
    dc_params[0].slave_config = slave_configs[0];
    dc_params[0].assign_activate = 0x0300;
    dc_params[0].sync0_cycle = PERIOD_NS;
    dc_params[0].sync0_shift = 0;
    dc_params[0].sync1_cycle = 0;
    dc_params[0].sync1_shift = 0;
    dc_params[0].result = &dc_results[0];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[0], NULL, dc_config_thread, &dc_params[0])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 0);
        exit(EXIT_FAILURE);
    }

    
    dc_params[1].slave_config = slave_configs[1];
    dc_params[1].assign_activate = 0x0300;
    dc_params[1].sync0_cycle = PERIOD_NS;
    dc_params[1].sync0_shift = 0;
    dc_params[1].sync1_cycle = 0;
    dc_params[1].sync1_shift = 0;
    dc_params[1].result = &dc_results[1];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[1], NULL, dc_config_thread, &dc_params[1])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 1);
        exit(EXIT_FAILURE);
    }

    
    dc_params[2].slave_config = slave_configs[2];
    dc_params[2].assign_activate = 0x0300;
    dc_params[2].sync0_cycle = PERIOD_NS;
    dc_params[2].sync0_shift = 0;
    dc_params[2].sync1_cycle = 0;
    dc_params[2].sync1_shift = 0;
    dc_params[2].result = &dc_results[2];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[2], NULL, dc_config_thread, &dc_params[2])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 2);
        exit(EXIT_FAILURE);
    }

    
    dc_params[3].slave_config = slave_configs[3];
    dc_params[3].assign_activate = 0x0300;
    dc_params[3].sync0_cycle = PERIOD_NS;
    dc_params[3].sync0_shift = 0;
    dc_params[3].sync1_cycle = 0;
    dc_params[3].sync1_shift = 0;
    dc_params[3].result = &dc_results[3];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[3], NULL, dc_config_thread, &dc_params[3])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 3);
        exit(EXIT_FAILURE);
    }

    
    dc_params[4].slave_config = slave_configs[4];
    dc_params[4].assign_activate = 0x0300;
    dc_params[4].sync0_cycle = PERIOD_NS;
    dc_params[4].sync0_shift = 0;
    dc_params[4].sync1_cycle = 0;
    dc_params[4].sync1_shift = 0;
    dc_params[4].result = &dc_results[4];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[4], NULL, dc_config_thread, &dc_params[4])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 4);
        exit(EXIT_FAILURE);
    }

    
    dc_params[5].slave_config = slave_configs[5];
    dc_params[5].assign_activate = 0x0300;
    dc_params[5].sync0_cycle = PERIOD_NS;
    dc_params[5].sync0_shift = 0;
    dc_params[5].sync1_cycle = 0;
    dc_params[5].sync1_shift = 0;
    dc_params[5].result = &dc_results[5];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[5], NULL, dc_config_thread, &dc_params[5])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 5);
        exit(EXIT_FAILURE);
    }

    
    dc_params[6].slave_config = slave_configs[6];
    dc_params[6].assign_activate = 0x0300;
    dc_params[6].sync0_cycle = PERIOD_NS;
    dc_params[6].sync0_shift = 0;
    dc_params[6].sync1_cycle = 0;
    dc_params[6].sync1_shift = 0;
    dc_params[6].result = &dc_results[6];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[6], NULL, dc_config_thread, &dc_params[6])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 6);
        exit(EXIT_FAILURE);
    }

    
    dc_params[7].slave_config = slave_configs[7];
    dc_params[7].assign_activate = 0x0300;
    dc_params[7].sync0_cycle = PERIOD_NS;
    dc_params[7].sync0_shift = 0;
    dc_params[7].sync1_cycle = 0;
    dc_params[7].sync1_shift = 0;
    dc_params[7].result = &dc_results[7];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[7], NULL, dc_config_thread, &dc_params[7])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 7);
        exit(EXIT_FAILURE);
    }

    
    dc_params[8].slave_config = slave_configs[8];
    dc_params[8].assign_activate = 0x0300;
    dc_params[8].sync0_cycle = PERIOD_NS;
    dc_params[8].sync0_shift = 0;
    dc_params[8].sync1_cycle = 0;
    dc_params[8].sync1_shift = 0;
    dc_params[8].result = &dc_results[8];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[8], NULL, dc_config_thread, &dc_params[8])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 8);
        exit(EXIT_FAILURE);
    }

    
    dc_params[9].slave_config = slave_configs[9];
    dc_params[9].assign_activate = 0x0300;
    dc_params[9].sync0_cycle = PERIOD_NS;
    dc_params[9].sync0_shift = 0;
    dc_params[9].sync1_cycle = 0;
    dc_params[9].sync1_shift = 0;
    dc_params[9].result = &dc_results[9];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[9], NULL, dc_config_thread, &dc_params[9])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 9);
        exit(EXIT_FAILURE);
    }

    
    dc_params[10].slave_config = slave_configs[10];
    dc_params[10].assign_activate = 0x0300;
    dc_params[10].sync0_cycle = PERIOD_NS;
    dc_params[10].sync0_shift = 0;
    dc_params[10].sync1_cycle = 0;
    dc_params[10].sync1_shift = 0;
    dc_params[10].result = &dc_results[10];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[10], NULL, dc_config_thread, &dc_params[10])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 10);
        exit(EXIT_FAILURE);
    }

    
    dc_params[11].slave_config = slave_configs[11];
    dc_params[11].assign_activate = 0x0300;
    dc_params[11].sync0_cycle = PERIOD_NS;
    dc_params[11].sync0_shift = 0;
    dc_params[11].sync1_cycle = 0;
    dc_params[11].sync1_shift = 0;
    dc_params[11].result = &dc_results[11];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[11], NULL, dc_config_thread, &dc_params[11])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 11);
        exit(EXIT_FAILURE);
    }

    
    dc_params[12].slave_config = slave_configs[12];
    dc_params[12].assign_activate = 0x0300;
    dc_params[12].sync0_cycle = PERIOD_NS;
    dc_params[12].sync0_shift = 0;
    dc_params[12].sync1_cycle = 0;
    dc_params[12].sync1_shift = 0;
    dc_params[12].result = &dc_results[12];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[12], NULL, dc_config_thread, &dc_params[12])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 12);
        exit(EXIT_FAILURE);
    }

    
    dc_params[13].slave_config = slave_configs[13];
    dc_params[13].assign_activate = 0x0300;
    dc_params[13].sync0_cycle = PERIOD_NS;
    dc_params[13].sync0_shift = 0;
    dc_params[13].sync1_cycle = 0;
    dc_params[13].sync1_shift = 0;
    dc_params[13].result = &dc_results[13];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[13], NULL, dc_config_thread, &dc_params[13])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 13);
        exit(EXIT_FAILURE);
    }

    
    dc_params[14].slave_config = slave_configs[14];
    dc_params[14].assign_activate = 0x0300;
    dc_params[14].sync0_cycle = PERIOD_NS;
    dc_params[14].sync0_shift = 0;
    dc_params[14].sync1_cycle = 0;
    dc_params[14].sync1_shift = 0;
    dc_params[14].result = &dc_results[14];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[14], NULL, dc_config_thread, &dc_params[14])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 14);
        exit(EXIT_FAILURE);
    }

    
    dc_params[15].slave_config = slave_configs[15];
    dc_params[15].assign_activate = 0x0300;
    dc_params[15].sync0_cycle = PERIOD_NS;
    dc_params[15].sync0_shift = 0;
    dc_params[15].sync1_cycle = 0;
    dc_params[15].sync1_shift = 0;
    dc_params[15].result = &dc_results[15];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[15], NULL, dc_config_thread, &dc_params[15])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 15);
        exit(EXIT_FAILURE);
    }

    
    dc_params[16].slave_config = slave_configs[16];
    dc_params[16].assign_activate = 0x0300;
    dc_params[16].sync0_cycle = PERIOD_NS;
    dc_params[16].sync0_shift = 0;
    dc_params[16].sync1_cycle = 0;
    dc_params[16].sync1_shift = 0;
    dc_params[16].result = &dc_results[16];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[16], NULL, dc_config_thread, &dc_params[16])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 16);
        exit(EXIT_FAILURE);
    }

    
    dc_params[17].slave_config = slave_configs[17];
    dc_params[17].assign_activate = 0x0300;
    dc_params[17].sync0_cycle = PERIOD_NS;
    dc_params[17].sync0_shift = 0;
    dc_params[17].sync1_cycle = 0;
    dc_params[17].sync1_shift = 0;
    dc_params[17].result = &dc_results[17];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[17], NULL, dc_config_thread, &dc_params[17])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 17);
        exit(EXIT_FAILURE);
    }

    
    dc_params[18].slave_config = slave_configs[18];
    dc_params[18].assign_activate = 0x0300;
    dc_params[18].sync0_cycle = PERIOD_NS;
    dc_params[18].sync0_shift = 0;
    dc_params[18].sync1_cycle = 0;
    dc_params[18].sync1_shift = 0;
    dc_params[18].result = &dc_results[18];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[18], NULL, dc_config_thread, &dc_params[18])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 18);
        exit(EXIT_FAILURE);
    }

    
    dc_params[19].slave_config = slave_configs[19];
    dc_params[19].assign_activate = 0x0300;
    dc_params[19].sync0_cycle = PERIOD_NS;
    dc_params[19].sync0_shift = 0;
    dc_params[19].sync1_cycle = 0;
    dc_params[19].sync1_shift = 0;
    dc_params[19].result = &dc_results[19];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[19], NULL, dc_config_thread, &dc_params[19])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 19);
        exit(EXIT_FAILURE);
    }

    
    dc_params[20].slave_config = slave_configs[20];
    dc_params[20].assign_activate = 0x0300;
    dc_params[20].sync0_cycle = PERIOD_NS;
    dc_params[20].sync0_shift = 0;
    dc_params[20].sync1_cycle = 0;
    dc_params[20].sync1_shift = 0;
    dc_params[20].result = &dc_results[20];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[20], NULL, dc_config_thread, &dc_params[20])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 20);
        exit(EXIT_FAILURE);
    }

    
    dc_params[21].slave_config = slave_configs[21];
    dc_params[21].assign_activate = 0x0300;
    dc_params[21].sync0_cycle = PERIOD_NS;
    dc_params[21].sync0_shift = 0;
    dc_params[21].sync1_cycle = 0;
    dc_params[21].sync1_shift = 0;
    dc_params[21].result = &dc_results[21];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[21], NULL, dc_config_thread, &dc_params[21])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 21);
        exit(EXIT_FAILURE);
    }

    
    dc_params[22].slave_config = slave_configs[22];
    dc_params[22].assign_activate = 0x0300;
    dc_params[22].sync0_cycle = PERIOD_NS;
    dc_params[22].sync0_shift = 0;
    dc_params[22].sync1_cycle = 0;
    dc_params[22].sync1_shift = 0;
    dc_params[22].result = &dc_results[22];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[22], NULL, dc_config_thread, &dc_params[22])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 22);
        exit(EXIT_FAILURE);
    }

    
    dc_params[23].slave_config = slave_configs[23];
    dc_params[23].assign_activate = 0x0300;
    dc_params[23].sync0_cycle = PERIOD_NS;
    dc_params[23].sync0_shift = 0;
    dc_params[23].sync1_cycle = 0;
    dc_params[23].sync1_shift = 0;
    dc_params[23].result = &dc_results[23];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[23], NULL, dc_config_thread, &dc_params[23])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 23);
        exit(EXIT_FAILURE);
    }

    
    dc_params[24].slave_config = slave_configs[24];
    dc_params[24].assign_activate = 0x0300;
    dc_params[24].sync0_cycle = PERIOD_NS;
    dc_params[24].sync0_shift = 0;
    dc_params[24].sync1_cycle = 0;
    dc_params[24].sync1_shift = 0;
    dc_params[24].result = &dc_results[24];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[24], NULL, dc_config_thread, &dc_params[24])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 24);
        exit(EXIT_FAILURE);
    }

    
    dc_params[25].slave_config = slave_configs[25];
    dc_params[25].assign_activate = 0x0300;
    dc_params[25].sync0_cycle = PERIOD_NS;
    dc_params[25].sync0_shift = 0;
    dc_params[25].sync1_cycle = 0;
    dc_params[25].sync1_shift = 0;
    dc_params[25].result = &dc_results[25];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[25], NULL, dc_config_thread, &dc_params[25])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 25);
        exit(EXIT_FAILURE);
    }

    
    dc_params[26].slave_config = slave_configs[26];
    dc_params[26].assign_activate = 0x0300;
    dc_params[26].sync0_cycle = PERIOD_NS;
    dc_params[26].sync0_shift = 0;
    dc_params[26].sync1_cycle = 0;
    dc_params[26].sync1_shift = 0;
    dc_params[26].result = &dc_results[26];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[26], NULL, dc_config_thread, &dc_params[26])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 26);
        exit(EXIT_FAILURE);
    }

    
    dc_params[27].slave_config = slave_configs[27];
    dc_params[27].assign_activate = 0x0300;
    dc_params[27].sync0_cycle = PERIOD_NS;
    dc_params[27].sync0_shift = 0;
    dc_params[27].sync1_cycle = 0;
    dc_params[27].sync1_shift = 0;
    dc_params[27].result = &dc_results[27];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[27], NULL, dc_config_thread, &dc_params[27])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 27);
        exit(EXIT_FAILURE);
    }

    
    dc_params[28].slave_config = slave_configs[28];
    dc_params[28].assign_activate = 0x0300;
    dc_params[28].sync0_cycle = PERIOD_NS;
    dc_params[28].sync0_shift = 0;
    dc_params[28].sync1_cycle = 0;
    dc_params[28].sync1_shift = 0;
    dc_params[28].result = &dc_results[28];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[28], NULL, dc_config_thread, &dc_params[28])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 28);
        exit(EXIT_FAILURE);
    }

    
    dc_params[29].slave_config = slave_configs[29];
    dc_params[29].assign_activate = 0x0300;
    dc_params[29].sync0_cycle = PERIOD_NS;
    dc_params[29].sync0_shift = 0;
    dc_params[29].sync1_cycle = 0;
    dc_params[29].sync1_shift = 0;
    dc_params[29].result = &dc_results[29];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[29], NULL, dc_config_thread, &dc_params[29])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 29);
        exit(EXIT_FAILURE);
    }

    
    dc_params[30].slave_config = slave_configs[30];
    dc_params[30].assign_activate = 0x0300;
    dc_params[30].sync0_cycle = PERIOD_NS;
    dc_params[30].sync0_shift = 0;
    dc_params[30].sync1_cycle = 0;
    dc_params[30].sync1_shift = 0;
    dc_params[30].result = &dc_results[30];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[30], NULL, dc_config_thread, &dc_params[30])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 30);
        exit(EXIT_FAILURE);
    }

    
    dc_params[31].slave_config = slave_configs[31];
    dc_params[31].assign_activate = 0x0300;
    dc_params[31].sync0_cycle = PERIOD_NS;
    dc_params[31].sync0_shift = 0;
    dc_params[31].sync1_cycle = 0;
    dc_params[31].sync1_shift = 0;
    dc_params[31].result = &dc_results[31];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[31], NULL, dc_config_thread, &dc_params[31])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 31);
        exit(EXIT_FAILURE);
    }

    
    dc_params[32].slave_config = slave_configs[32];
    dc_params[32].assign_activate = 0x0300;
    dc_params[32].sync0_cycle = PERIOD_NS;
    dc_params[32].sync0_shift = 0;
    dc_params[32].sync1_cycle = 0;
    dc_params[32].sync1_shift = 0;
    dc_params[32].result = &dc_results[32];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[32], NULL, dc_config_thread, &dc_params[32])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 32);
        exit(EXIT_FAILURE);
    }

    
    dc_params[33].slave_config = slave_configs[33];
    dc_params[33].assign_activate = 0x0300;
    dc_params[33].sync0_cycle = PERIOD_NS;
    dc_params[33].sync0_shift = 0;
    dc_params[33].sync1_cycle = 0;
    dc_params[33].sync1_shift = 0;
    dc_params[33].result = &dc_results[33];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[33], NULL, dc_config_thread, &dc_params[33])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 33);
        exit(EXIT_FAILURE);
    }

    
    dc_params[34].slave_config = slave_configs[34];
    dc_params[34].assign_activate = 0x0300;
    dc_params[34].sync0_cycle = PERIOD_NS;
    dc_params[34].sync0_shift = 0;
    dc_params[34].sync1_cycle = 0;
    dc_params[34].sync1_shift = 0;
    dc_params[34].result = &dc_results[34];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[34], NULL, dc_config_thread, &dc_params[34])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 34);
        exit(EXIT_FAILURE);
    }

    
    dc_params[35].slave_config = slave_configs[35];
    dc_params[35].assign_activate = 0x0300;
    dc_params[35].sync0_cycle = PERIOD_NS;
    dc_params[35].sync0_shift = 0;
    dc_params[35].sync1_cycle = 0;
    dc_params[35].sync1_shift = 0;
    dc_params[35].result = &dc_results[35];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[35], NULL, dc_config_thread, &dc_params[35])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 35);
        exit(EXIT_FAILURE);
    }

    
    dc_params[36].slave_config = slave_configs[36];
    dc_params[36].assign_activate = 0x0300;
    dc_params[36].sync0_cycle = PERIOD_NS;
    dc_params[36].sync0_shift = 0;
    dc_params[36].sync1_cycle = 0;
    dc_params[36].sync1_shift = 0;
    dc_params[36].result = &dc_results[36];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[36], NULL, dc_config_thread, &dc_params[36])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 36);
        exit(EXIT_FAILURE);
    }

    
    dc_params[37].slave_config = slave_configs[37];
    dc_params[37].assign_activate = 0x0300;
    dc_params[37].sync0_cycle = PERIOD_NS;
    dc_params[37].sync0_shift = 0;
    dc_params[37].sync1_cycle = 0;
    dc_params[37].sync1_shift = 0;
    dc_params[37].result = &dc_results[37];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[37], NULL, dc_config_thread, &dc_params[37])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 37);
        exit(EXIT_FAILURE);
    }

    
    dc_params[38].slave_config = slave_configs[38];
    dc_params[38].assign_activate = 0x0300;
    dc_params[38].sync0_cycle = PERIOD_NS;
    dc_params[38].sync0_shift = 0;
    dc_params[38].sync1_cycle = 0;
    dc_params[38].sync1_shift = 0;
    dc_params[38].result = &dc_results[38];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[38], NULL, dc_config_thread, &dc_params[38])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 38);
        exit(EXIT_FAILURE);
    }

    
    dc_params[39].slave_config = slave_configs[39];
    dc_params[39].assign_activate = 0x0300;
    dc_params[39].sync0_cycle = PERIOD_NS;
    dc_params[39].sync0_shift = 0;
    dc_params[39].sync1_cycle = 0;
    dc_params[39].sync1_shift = 0;
    dc_params[39].result = &dc_results[39];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[39], NULL, dc_config_thread, &dc_params[39])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 39);
        exit(EXIT_FAILURE);
    }

    
    dc_params[40].slave_config = slave_configs[40];
    dc_params[40].assign_activate = 0x0300;
    dc_params[40].sync0_cycle = PERIOD_NS;
    dc_params[40].sync0_shift = 0;
    dc_params[40].sync1_cycle = 0;
    dc_params[40].sync1_shift = 0;
    dc_params[40].result = &dc_results[40];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[40], NULL, dc_config_thread, &dc_params[40])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 40);
        exit(EXIT_FAILURE);
    }

    
    dc_params[41].slave_config = slave_configs[41];
    dc_params[41].assign_activate = 0x0300;
    dc_params[41].sync0_cycle = PERIOD_NS;
    dc_params[41].sync0_shift = 0;
    dc_params[41].sync1_cycle = 0;
    dc_params[41].sync1_shift = 0;
    dc_params[41].result = &dc_results[41];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[41], NULL, dc_config_thread, &dc_params[41])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 41);
        exit(EXIT_FAILURE);
    }

    
    dc_params[42].slave_config = slave_configs[42];
    dc_params[42].assign_activate = 0x0300;
    dc_params[42].sync0_cycle = PERIOD_NS;
    dc_params[42].sync0_shift = 0;
    dc_params[42].sync1_cycle = 0;
    dc_params[42].sync1_shift = 0;
    dc_params[42].result = &dc_results[42];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[42], NULL, dc_config_thread, &dc_params[42])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 42);
        exit(EXIT_FAILURE);
    }

    
    dc_params[43].slave_config = slave_configs[43];
    dc_params[43].assign_activate = 0x0300;
    dc_params[43].sync0_cycle = PERIOD_NS;
    dc_params[43].sync0_shift = 0;
    dc_params[43].sync1_cycle = 0;
    dc_params[43].sync1_shift = 0;
    dc_params[43].result = &dc_results[43];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[43], NULL, dc_config_thread, &dc_params[43])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 43);
        exit(EXIT_FAILURE);
    }

    
    dc_params[44].slave_config = slave_configs[44];
    dc_params[44].assign_activate = 0x0300;
    dc_params[44].sync0_cycle = PERIOD_NS;
    dc_params[44].sync0_shift = 0;
    dc_params[44].sync1_cycle = 0;
    dc_params[44].sync1_shift = 0;
    dc_params[44].result = &dc_results[44];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[44], NULL, dc_config_thread, &dc_params[44])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 44);
        exit(EXIT_FAILURE);
    }

    
    dc_params[45].slave_config = slave_configs[45];
    dc_params[45].assign_activate = 0x0300;
    dc_params[45].sync0_cycle = PERIOD_NS;
    dc_params[45].sync0_shift = 0;
    dc_params[45].sync1_cycle = 0;
    dc_params[45].sync1_shift = 0;
    dc_params[45].result = &dc_results[45];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[45], NULL, dc_config_thread, &dc_params[45])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 45);
        exit(EXIT_FAILURE);
    }

    
    dc_params[46].slave_config = slave_configs[46];
    dc_params[46].assign_activate = 0x0300;
    dc_params[46].sync0_cycle = PERIOD_NS;
    dc_params[46].sync0_shift = 0;
    dc_params[46].sync1_cycle = 0;
    dc_params[46].sync1_shift = 0;
    dc_params[46].result = &dc_results[46];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[46], NULL, dc_config_thread, &dc_params[46])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 46);
        exit(EXIT_FAILURE);
    }

    
    dc_params[47].slave_config = slave_configs[47];
    dc_params[47].assign_activate = 0x0300;
    dc_params[47].sync0_cycle = PERIOD_NS;
    dc_params[47].sync0_shift = 0;
    dc_params[47].sync1_cycle = 0;
    dc_params[47].sync1_shift = 0;
    dc_params[47].result = &dc_results[47];
    
    // 创建DC配置线程
    if (pthread_create(&dc_threads[47], NULL, dc_config_thread, &dc_params[47])) {
        fprintf(stderr, "Failed to create DC configuration thread for slave %d\n", 47);
        exit(EXIT_FAILURE);
    }
    
    // 等待所有DC配置线程完成
    pthread_join(dc_threads[0], NULL);
    if (dc_results[0]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 0);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[1], NULL);
    if (dc_results[1]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 1);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[2], NULL);
    if (dc_results[2]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 2);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[3], NULL);
    if (dc_results[3]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 3);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[4], NULL);
    if (dc_results[4]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 4);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[5], NULL);
    if (dc_results[5]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 5);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[6], NULL);
    if (dc_results[6]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 6);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[7], NULL);
    if (dc_results[7]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 7);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[8], NULL);
    if (dc_results[8]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 8);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[9], NULL);
    if (dc_results[9]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 9);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[10], NULL);
    if (dc_results[10]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 10);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[11], NULL);
    if (dc_results[11]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 11);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[12], NULL);
    if (dc_results[12]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 12);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[13], NULL);
    if (dc_results[13]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 13);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[14], NULL);
    if (dc_results[14]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 14);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[15], NULL);
    if (dc_results[15]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 15);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[16], NULL);
    if (dc_results[16]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 16);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[17], NULL);
    if (dc_results[17]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 17);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[18], NULL);
    if (dc_results[18]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 18);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[19], NULL);
    if (dc_results[19]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 19);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[20], NULL);
    if (dc_results[20]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 20);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[21], NULL);
    if (dc_results[21]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 21);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[22], NULL);
    if (dc_results[22]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 22);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[23], NULL);
    if (dc_results[23]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 23);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[24], NULL);
    if (dc_results[24]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 24);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[25], NULL);
    if (dc_results[25]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 25);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[26], NULL);
    if (dc_results[26]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 26);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[27], NULL);
    if (dc_results[27]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 27);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[28], NULL);
    if (dc_results[28]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 28);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[29], NULL);
    if (dc_results[29]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 29);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[30], NULL);
    if (dc_results[30]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 30);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[31], NULL);
    if (dc_results[31]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 31);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[32], NULL);
    if (dc_results[32]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 32);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[33], NULL);
    if (dc_results[33]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 33);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[34], NULL);
    if (dc_results[34]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 34);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[35], NULL);
    if (dc_results[35]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 35);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[36], NULL);
    if (dc_results[36]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 36);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[37], NULL);
    if (dc_results[37]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 37);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[38], NULL);
    if (dc_results[38]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 38);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[39], NULL);
    if (dc_results[39]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 39);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[40], NULL);
    if (dc_results[40]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 40);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[41], NULL);
    if (dc_results[41]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 41);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[42], NULL);
    if (dc_results[42]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 42);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[43], NULL);
    if (dc_results[43]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 43);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[44], NULL);
    if (dc_results[44]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 44);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[45], NULL);
    if (dc_results[45]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 45);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[46], NULL);
    if (dc_results[46]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 46);
        exit(EXIT_FAILURE);
    }
pthread_join(dc_threads[47], NULL);
    if (dc_results[47]) {
        fprintf(stderr, "Failed to configure DC for slave %d\n", 47);
        exit(EXIT_FAILURE);
    }
    
    printf("Parallel DC configuration completed\n");

    if (ecrt_domain_reg_pdo_entry_list(domain1, domain1_regs)) {
        fprintf(stderr, "PDO entry registration failed!\n");
        exit(EXIT_FAILURE);
    }

    printf("Activating master...\n");
    if (ecrt_master_activate(master)) {
        exit(EXIT_FAILURE);
    }

    if (!(domain1_pd = ecrt_domain_data(domain1))) {
        exit(EXIT_FAILURE);
    }

    // Set real-time priority
    struct sched_param param = {};
    param.sched_priority = sched_get_priority_max(SCHED_FIFO);
    printf("Using priority %i.\n", param.sched_priority);
    if (sched_setscheduler(0, SCHED_FIFO, &param) == -1) {
        perror("sched_setscheduler failed");
    }

    printf("Started.\n");
    printf("Shared memory interface created at %s\n", ETHERCAT_SHM_FILE);
    
    // 修改后的主循环
    while (run) {
        cyclic_task();
    }

    return EXIT_SUCCESS;
}