using System;
using System.IO;
using System.IO.MemoryMappedFiles;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Linq;

namespace EtherCATControl
{
    // 共享内存结构体
    [StructLayout(LayoutKind.Sequential)]
    public struct EtherCATSharedMemory
    {
        public int shm_slave0_online_status; // 从站0在线状态
        public int shm_slave0_operational_status; // 从站0运行状态
        public int shm_slave0_al_state; // 从站0AL状态

        public int shm_slave0_rx_0x6040_control_word; // 控制字
        public int shm_slave0_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave0_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave0_tx_0x6041_status_word; // 状态字
    }

    // Linux RT 相关定义
    [StructLayout(LayoutKind.Sequential)]
    public struct sched_param
    {
        public int sched_priority;
    }

    public class EtherCATController : IDisposable
    {
        private readonly MemoryMappedFile _memoryMappedFile;
        private readonly MemoryMappedViewAccessor _viewAccessor;
        private EtherCATSharedMemory _sharedMemory;

        // P/Invoke 定义
        [DllImport("libc", SetLastError = true)]
        private static extern int sched_setscheduler(int pid, int policy, ref sched_param param);
        
        [DllImport("libc", SetLastError = true)]
        private static extern int sched_get_priority_max(int policy);

        [DllImport("libc")]
        private static extern int getpid();

        public EtherCATController(string EtherCATSharedMemoryFilePath)
        {
            _memoryMappedFile = MemoryMappedFile.CreateFromFile(EtherCATSharedMemoryFilePath, FileMode.Open);
            _viewAccessor = _memoryMappedFile.CreateViewAccessor();
            _sharedMemory = new EtherCATSharedMemory();
        }

        private void UpdateSharedMemory()
        {
            _viewAccessor.Read(0, out _sharedMemory);
        }

        private void WriteSharedMemory()
        {
            _viewAccessor.Write(0, ref _sharedMemory);
        }

        private bool CheckStatusWord(ushort expectedStatus)
        {
            UpdateSharedMemory();
            return (_sharedMemory.shm_slave0_tx_0x6041_status_word & 0x0FFF) == expectedStatus;
        }

        private bool WaitForStatus(int expectedStatus, int timeoutMs = 1000)
        {
            int elapsed = 0;
            while (elapsed < timeoutMs)
            {
                UpdateSharedMemory();
                int currentStatus = _sharedMemory.shm_slave0_tx_0x6041_status_word & 0x0FFF;
                
                // 添加状态变化日志
                Console.WriteLine($"Current status: 0x{currentStatus:X4}, Expected: 0x{expectedStatus:X4}");
                
                if (currentStatus == expectedStatus)
                {
                    return true;
                }
                Thread.Sleep(10);
                elapsed += 10;
            }
            
            // 超时时输出最后的状态
            UpdateSharedMemory();
            int finalStatus = _sharedMemory.shm_slave0_tx_0x6041_status_word & 0x0FFF;
            Console.WriteLine($"Status wait timeout. Final status: 0x{finalStatus:X4}, Expected: 0x{expectedStatus:X4}");
            return false;
        }

        public bool WaitForServoReady(CancellationToken cancellationToken = default)
        {
            Console.WriteLine("等待伺服就绪...");
            int timeoutMs = 5000; // 5秒超时
            int elapsed = 0;
            
            while (!cancellationToken.IsCancellationRequested && elapsed < timeoutMs)
            {
                UpdateSharedMemory();
                int statusWord = _sharedMemory.shm_slave0_tx_0x6041_status_word;
                
                // 详细打印状态字的每个位
                Console.WriteLine($"当前状态字: 0x{statusWord:X4}");
                Console.WriteLine($"Ready to switch on: {(statusWord & 0x0001) != 0}");
                Console.WriteLine($"Switched on: {(statusWord & 0x0002) != 0}");
                Console.WriteLine($"Operation enabled: {(statusWord & 0x0004) != 0}");
                Console.WriteLine($"Fault: {(statusWord & 0x0008) != 0}");
                Console.WriteLine($"Voltage enabled: {(statusWord & 0x0010) != 0}");
                Console.WriteLine($"Quick stop: {(statusWord & 0x0020) != 0}");
                Console.WriteLine($"Switch on disabled: {(statusWord & 0x0040) != 0}");
                Console.WriteLine($"Warning: {(statusWord & 0x0080) != 0}");

                // 修改判断逻辑：检查状态字是否为0x1637或0x0637
                // 0x1637 = 0001 0110 0011 0111
                // 0x0637 = 0000 0110 0011 0111
                // 主要关注低12位，高4位可能会有变化
                bool isOperationEnabled = (statusWord & 0x0FFF) == 0x0637 || (statusWord & 0x0FFF) == 0x1637;
                
                if (isOperationEnabled)
                {
                    Console.WriteLine("伺服已就绪");
                    return true;
                }
                
                Thread.Sleep(100);
                elapsed += 100;
            }
            
            if (elapsed >= timeoutMs)
            {
                Console.WriteLine($"等待伺服就绪超时，最后状态字: 0x{_sharedMemory.shm_slave0_tx_0x6041_status_word:X4}");
            }
            
            return false;
        }

        public void EnableServo()
        {
            Console.WriteLine("正在使能伺服...");
            
            try
            {
                // 初始化命令字
                int command = 0x004F;
                bool enabled = false;
                int timeoutMs = 10000;
                int elapsed = 0;

                UpdateSharedMemory();
                _sharedMemory.shm_slave0_rx_0x6040_control_word = 0x0080;
                _sharedMemory.shm_slave0_rx_0x6060_operation_mode = 0x09;
                WriteSharedMemory();
                Thread.Sleep(10);
                
                while (!enabled && elapsed < timeoutMs)
                {
                    UpdateSharedMemory();
                    int statusWord = _sharedMemory.shm_slave0_tx_0x6041_status_word;
                    
                    // 打印当前状态
                    Console.WriteLine($"当前状态字: 0x{statusWord:X4}");
                    
                    if ((statusWord & command) == 0x0040)
                    {
                        // 设置速度模式并发送第一个控制字
                        //_sharedMemory.shm_slave0_rx_0x6060_operation_mode = 0x09; // 速度模式
                        _sharedMemory.shm_slave0_rx_0x6040_control_word = 0x0006;
                        command = 0x006F;
                    }
                    else if ((statusWord & command) == 0x0021)
                    {
                        _sharedMemory.shm_slave0_rx_0x6040_control_word = 0x0007;
                        command = 0x006F;
                    }
                    else if ((statusWord & command) == 0x0023)
                    {
                        _sharedMemory.shm_slave0_rx_0x6040_control_word = 0x000F;
                        command = 0x006F;
                    }
                    else if ((statusWord & command) == 0x0027)
                    {
                        // 伺服已经使能成功
                        Console.WriteLine("伺服使能成功");
                        enabled = true;
                        break;
                    }
                    
                    WriteSharedMemory();
                    Thread.Sleep(100);
                    elapsed += 100;
                }
                
                if (!enabled)
                {
                    Console.WriteLine("伺服使能失败：等待就绪超时");
                    // 诊断信息
                    Console.WriteLine($"诊断信息:");
                    Console.WriteLine($"- 最终状态字: 0x{_sharedMemory.shm_slave0_tx_0x6041_status_word:X4}");
                    Console.WriteLine($"- 操作模式: 0x{_sharedMemory.shm_slave0_rx_0x6060_operation_mode:X2}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"伺服使能过程出错: {ex.Message}");
            }
        }

        public void DisableServo()
        {
            Console.WriteLine("Disabling servo...");
             _sharedMemory.shm_slave0_rx_0x6040_control_word = 0x0080;
              WriteSharedMemory();
             //Thread.Sleep(100);
             _sharedMemory.shm_slave0_rx_0x6040_control_word = 0x02;
             //Console.WriteLine("quick stop!");
              WriteSharedMemory();
            //Thread.Sleep(100);
            _sharedMemory.shm_slave0_rx_0x6040_control_word = 0x00;
            WriteSharedMemory();
            //Thread.Sleep(100);
            Console.WriteLine("Servo disabled.");
        }

        public void SetVelocity(int velocity)
        {
            _sharedMemory.shm_slave0_rx_0x60ff_target_speed = velocity;
            WriteSharedMemory();
            Console.WriteLine($"Set velocity to: {velocity}");
            
        }

        public void UpdateStatus()
        {
            UpdateSharedMemory();
        }

        public void EnterRealtimeMode()
        {
            try 
            {
                // 设置.NET线程优先级为最高
                Thread.CurrentThread.Priority = ThreadPriority.Highest;

                // 在Linux系统上设置实时调度策略
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    const int SCHED_FIFO = 1;
                    
                    // 获取当前进程ID
                    int pid = getpid();
                    Console.WriteLine($"当前进程ID: {pid}");

                    // 获取SCHED_FIFO的最高优先级
                    int maxPriority = sched_get_priority_max(SCHED_FIFO);
                    Console.WriteLine($"SCHED_FIFO最高优先级: {maxPriority}");
                    
                    var schedParam = new sched_param { sched_priority = maxPriority };
                    
                    // 设置当前进程为SCHED_FIFO策略
                    int result = sched_setscheduler(pid, SCHED_FIFO, ref schedParam);
                    if (result != 0)
                    {
                        int error = Marshal.GetLastWin32Error();
                        Console.WriteLine($"警告: 设置SCHED_FIFO调度策略失败。错误码: {error}");
                        Console.WriteLine("请尝试使用以下命令运行程序：");
                        Console.WriteLine($"sudo chrt -f {maxPriority} dotnet run");
                        Console.WriteLine("或者使用以下命令：");
                        Console.WriteLine($"sudo nice -n -{maxPriority} dotnet run");
                    }
                    else 
                    {
                        Console.WriteLine($"成功设置实时调度策略SCHED_FIFO，优先级: {maxPriority}");
                    }
                }
                // 在Windows系统上设置进程优先级
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    using var process = Process.GetCurrentProcess();
                    process.PriorityClass = ProcessPriorityClass.RealTime;
                    Console.WriteLine("成功设置Windows进程优先级为实时");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置实时模式时出错: {ex.Message}");
            }
        }
        public async Task RunControlSequence(CancellationToken cancellationToken)
        {
            try
            {
                // 设置当前线程优先级为最高
                Thread.CurrentThread.Priority = ThreadPriority.Highest;
                Console.WriteLine($"当前线程优先级已设置为: {Thread.CurrentThread.Priority}");

                bool servoEnabled = false;
                bool wasOnline = false;

                // 创建一个高优先级的任务来处理速度更新
                var velocityUpdateTask = Task.Run(async () =>
                {
                    Thread.CurrentThread.Priority = ThreadPriority.Highest;
                    Console.WriteLine($"速度更新线程优先级已设置为: {Thread.CurrentThread.Priority}");
                    
                    while (!cancellationToken.IsCancellationRequested)
                    {
                        if (servoEnabled)
                        {
                            SetVelocity(1000000);
                        }
                        // await Task.Delay(1, cancellationToken);
                    }
                }, cancellationToken);

                while (!cancellationToken.IsCancellationRequested)
                {
                    UpdateSharedMemory();
                    bool isOnline = _sharedMemory.shm_slave0_online_status == 1;
                    bool isOperational = _sharedMemory.shm_slave0_operational_status == 1;

                    // 检测从站状态变化
                    if (isOnline != wasOnline)
                    {
                        if (isOnline)
                        {
                            Console.WriteLine("从站已上线，正在检测运行状态...");
                            
                            // 等待从站入OP状态
                            while (!isOperational && !cancellationToken.IsCancellationRequested)
                            {
                                Console.WriteLine("等待从站进入OP状态...");
                                await Task.Delay(100, cancellationToken);
                                UpdateSharedMemory();
                                isOperational = _sharedMemory.shm_slave0_operational_status == 1;
                            }

                            if (isOperational)
                            {
                                Console.WriteLine("从站已进入运行状态，正在使能伺服...");
                                EnableServo();
                                if (WaitForServoReady(cancellationToken))
                                {
                                    servoEnabled = true;
                                    Console.WriteLine("伺服使能成功");
                                }
                                else
                                {
                                    Console.WriteLine("伺服使能失败");
                                    servoEnabled = false;
                                }
                            }
                        }
                        else
                        {
                            Console.WriteLine("从站离线，正在禁用伺服...");
                            DisableServo();
                            servoEnabled = false;
                        }
                        wasOnline = isOnline;
                    }
                }

                await velocityUpdateTask; // 等待速度更新任务完成
            }
            catch (Exception ex)
            {
                Console.WriteLine($"控制序列出错: {ex.Message}");
            }
            finally
            {
                DisableServo();
                Console.WriteLine("进程已完成。");
            }
        }

        public void Dispose()
        {
            _viewAccessor?.Dispose();
            _memoryMappedFile?.Dispose();
        }
    }

    class Program
    {
        // 从站状态响应模型
        private class SlaveStatus
        {
            public int index { get; set; }
            public int master { get; set; }
            public string name { get; set; }
            public string state { get; set; }
            public bool online { get; set; }
            public int operationalStatus { get; set; }
            public int alState { get; set; }
            public string vendorId { get; set; }
            public string productCode { get; set; }
            public string position { get; set; }
            public int? errorCode { get; set; }
            public int? speed { get; set; }
        }

        private class SlaveStatusResponse
        {
            public int totalCount { get; set; }
            public int opCount { get; set; }
            public int preopCount { get; set; }
            public int otherCount { get; set; }
            public List<SlaveStatus> slaves { get; set; }
        }

        private class SlaveStatusApiResponse
        {
            public int code { get; set; }
            public string msg { get; set; }
            public SlaveStatusResponse data { get; set; }
        }
        
        private class ShmFile 
        {
            public string shmFile { get; set; }
        }

        private class ApiResponse
        {
            public int code { get; set; }
            public string msg { get; set; }
            public ShmFile data { get; set; }
        }

        private static bool IsValidHexValue(string hex)
        {
            if (string.IsNullOrEmpty(hex)) return false;
            // 移除0x前缀后检查是否全为0
            string value = hex.StartsWith("0x") ? hex.Substring(2) : hex;
            return !value.All(c => c == '0');
        }

        private static async Task<bool> CheckSlavesStatus(HttpClient client)
        {
            const int MAX_RETRIES = 10;
            const int RETRY_DELAY_MS = 1000;

            for (int i = 0; i < MAX_RETRIES; i++)
            {
                try
                {
                    Console.WriteLine($"正在检查从站状态，第{i + 1}次尝试...");
                    
                    var response = await client.GetAsync(
                        "http://127.0.0.1:3000/api/ethercat/all-slaves-status?detail=1"
                    );

                    if (!response.IsSuccessStatusCode)
                    {
                        Console.WriteLine($"获取从站状态失败: {response.StatusCode}");
                        continue;
                    }

                    var responseContent = await response.Content.ReadAsStringAsync();
                    var statusResponse = JsonSerializer.Deserialize<SlaveStatusApiResponse>(responseContent);

                    if (statusResponse.code != 0)
                    {
                        Console.WriteLine($"API错误: {statusResponse.msg}");
                        continue;
                    }

                    var data = statusResponse.data;
                    
                    // 检查条件1：preopCount和totalCount是否相等
                    if (data.preopCount != data.totalCount)
                    {
                        Console.WriteLine($"从站状态不匹配: preopCount({data.preopCount}) != totalCount({data.totalCount})");
                        await Task.Delay(RETRY_DELAY_MS);
                        continue;
                    }

                    // 检查条件2：检查每个从站的vendorId和productCode
                    bool allSlavesValid = true;
                    foreach (var slave in data.slaves)
                    {
                        if (!IsValidHexValue(slave.vendorId) || !IsValidHexValue(slave.productCode))
                        {
                            Console.WriteLine($"从站 {slave.index} 的vendorId({slave.vendorId})或productCode({slave.productCode})无效");
                            allSlavesValid = false;
                            break;
                        }
                    }

                    if (allSlavesValid)
                    {
                        Console.WriteLine("从站状态检查通过");
                        return true;
                    }

                    await Task.Delay(RETRY_DELAY_MS);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"检查从站状态时发生错误: {ex.Message}");
                    await Task.Delay(RETRY_DELAY_MS);
                }
            }

            Console.WriteLine($"从站状态检查失败，已达到最大重试次数({MAX_RETRIES})");
            return false;
        }

        static async Task Main(string[] args)
        {
            try 
            {
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json")
                );

                // 首先检查从站状态
                if (!await CheckSlavesStatus(client))
                {
                    Console.WriteLine("从站状态检查失败，程序退出");
                    return;
                }

                var requestData = new { programName = $"{Path.GetFileName(Process.GetCurrentProcess().MainModule.FileName)}"};
                var jsonContent = JsonSerializer.Serialize(requestData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(
                    "http://127.0.0.1:3000/api/programs/start-middleware", 
                    content
                );

                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Failed to get shared memory path: {response.StatusCode}");
                    return;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse>(responseContent);

                if (apiResponse.code != 0) // 假设0表示成功
                {
                    Console.WriteLine($"API error: {apiResponse.msg}");
                    return;
                }

                var sharedMemoryFilePath = apiResponse.data.shmFile;
                Console.WriteLine($"Starting EtherCAT Control Program with shared memory file: {sharedMemoryFilePath}...");

                using var controller = new EtherCATController(sharedMemoryFilePath);
                controller.EnterRealtimeMode();

                var cts = new CancellationTokenSource();
                Console.CancelKeyPress += (s, e) => {
                    e.Cancel = true;
                    cts.Cancel();
                };

                try
                {
                    await controller.RunControlSequence(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    Console.WriteLine("Operation cancelled by user.");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Startup error: {ex.Message}");
            }
        }
    }
}