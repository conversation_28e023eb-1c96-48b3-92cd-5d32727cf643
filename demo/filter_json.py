import json
import sys
import os

def filter_pdos(slave):
    # 需要保留的PDO索引列表
    allowed_indices = ['0x6060', '0x60ff', '0x6040', '0x6041']
    
    # 过滤rx_pdos
    if 'rx_pdos' in slave:
        # 只保留指定index的PDO
        slave['rx_pdos'] = [
            pdo for pdo in slave['rx_pdos']
            if pdo.get('index') in allowed_indices
        ]
    
    # 过滤tx_pdos
    if 'tx_pdos' in slave:
        # 只保留指定index的PDO
        slave['tx_pdos'] = [
            pdo for pdo in slave['tx_pdos']
            if pdo.get('index') in allowed_indices
        ]
    
    return slave

def process_slave_config(slave_count):
    # 读取原始slave.json文件
    try:
        with open('slave.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print("错误: 当前目录下未找到slave.json文件")
        return
    except json.JSONDecodeError:
        print("错误: slave.json文件格式不正确")
        return
    
    # 获取原始从站配置
    original_slaves = data.get('slaves', [])
    original_count = len(original_slaves)
    
    # 检查从站数量
    if slave_count <= 0:
        print("错误: 从站个数必须大于0")
        return
    
    if slave_count > original_count:
        print(f"警告: 请求的从站数量({slave_count})大于配置文件中的数量({original_count})，将保留所有从站配置")
        slave_count = original_count
    
    # 截取所需数量的从站配置
    processed_slaves = original_slaves[:slave_count]
    
    # 对每个从站进行PDO过滤
    filtered_slaves = [filter_pdos(slave) for slave in processed_slaves]
    
    # 创建新的配置对象
    new_config = {
        "slaves": filtered_slaves
    }
    
    # 生成新的文件名
    output_filename = f'slave{slave_count}_filtered.json'
    
    # 写入新文件
    try:
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(new_config, f, indent=2, ensure_ascii=False)
        print(f"成功: 已生成新的配置文件 {output_filename}")
        
        # 打印过滤结果统计
        print("\n过滤结果统计:")
        for i, slave in enumerate(filtered_slaves):
            rx_pdos_count = len(slave.get('rx_pdos', []))
            tx_pdos_count = len(slave.get('tx_pdos', []))
            print(f"从站 {i}:")
            print(f"  - 保留了 {rx_pdos_count} 个rx_pdos")
            print(f"  - 保留了 {tx_pdos_count} 个tx_pdos")
            
    except IOError as e:
        print(f"错误: 无法写入文件 {output_filename}: {e}")

if __name__ == "__main__":
    # 检查参数
    if len(sys.argv) != 2:
        print("用法: python filter_json.py <从站个数>")
        sys.exit(1)
    
    try:
        slave_count = int(sys.argv[1])
    except ValueError:
        print("错误: 从站个数必须是整数")
        sys.exit(1)
    
    process_slave_config(slave_count)