﻿using System;
using System.IO;
using System.IO.MemoryMappedFiles;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Linq;
using System.Reflection;

namespace EtherCATControl
{
    // 共享内存结构体
    [StructLayout(LayoutKind.Sequential)]
    public struct EtherCATSharedMemory
    {
        public int shm_slave0_online_status; // 从站0在线状态
        public int shm_slave0_operational_status; // 从站0运行状态
        public int shm_slave0_al_state; // 从站0AL状态

        public int shm_slave0_rx_0x6040_control_word; // 控制字
        public int shm_slave0_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave0_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave0_tx_0x6041_status_word; // 状态字

        public int shm_slave1_online_status; // 从站1在线状态
        public int shm_slave1_operational_status; // 从站1运行状态
        public int shm_slave1_al_state; // 从站1AL状态

        public int shm_slave1_rx_0x6040_control_word; // 控制字
        public int shm_slave1_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave1_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave1_tx_0x6041_status_word; // 状态字

        public int shm_slave2_online_status; // 从站2在线状态
        public int shm_slave2_operational_status; // 从站2运行状态
        public int shm_slave2_al_state; // 从站2AL状态

        public int shm_slave2_rx_0x6040_control_word; // 控制字
        public int shm_slave2_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave2_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave2_tx_0x6041_status_word; // 状态字

        public int shm_slave3_online_status; // 从站3在线状态
        public int shm_slave3_operational_status; // 从站3运行状态
        public int shm_slave3_al_state; // 从站3AL状态

        public int shm_slave3_rx_0x6040_control_word; // 控制字
        public int shm_slave3_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave3_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave3_tx_0x6041_status_word; // 状态字

        public int shm_slave4_online_status; // 从站4在线状态
        public int shm_slave4_operational_status; // 从站4运行状态
        public int shm_slave4_al_state; // 从站4AL状态

        public int shm_slave4_rx_0x6040_control_word; // 控制字
        public int shm_slave4_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave4_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave4_tx_0x6041_status_word; // 状态字

        public int shm_slave5_online_status; // 从站5在线状态
        public int shm_slave5_operational_status; // 从站5运行状态
        public int shm_slave5_al_state; // 从站5AL状态

        public int shm_slave5_rx_0x6040_control_word; // 控制字
        public int shm_slave5_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave5_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave5_tx_0x6041_status_word; // 状态字

        public int shm_slave6_online_status; // 从站6在线状态
        public int shm_slave6_operational_status; // 从站6运行状态
        public int shm_slave6_al_state; // 从站6AL状态

        public int shm_slave6_rx_0x6040_control_word; // 控制字
        public int shm_slave6_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave6_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave6_tx_0x6041_status_word; // 状态字

        public int shm_slave7_online_status; // 从站7在线状态
        public int shm_slave7_operational_status; // 从站7运行状态
        public int shm_slave7_al_state; // 从站7AL状态

        public int shm_slave7_rx_0x6040_control_word; // 控制字
        public int shm_slave7_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave7_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave7_tx_0x6041_status_word; // 状态字

        public int shm_slave8_online_status; // 从站8在线状态
        public int shm_slave8_operational_status; // 从站8运行状态
        public int shm_slave8_al_state; // 从站8AL状态

        public int shm_slave8_rx_0x6040_control_word; // 控制字
        public int shm_slave8_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave8_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave8_tx_0x6041_status_word; // 状态字

        public int shm_slave9_online_status; // 从站9在线状态
        public int shm_slave9_operational_status; // 从站9运行状态
        public int shm_slave9_al_state; // 从站9AL状态

        public int shm_slave9_rx_0x6040_control_word; // 控制字
        public int shm_slave9_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave9_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave9_tx_0x6041_status_word; // 状态字

        public int shm_slave10_online_status; // 从站10在线状态
        public int shm_slave10_operational_status; // 从站10运行状态
        public int shm_slave10_al_state; // 从站10AL状态

        public int shm_slave10_rx_0x6040_control_word; // 控制字
        public int shm_slave10_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave10_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave10_tx_0x6041_status_word; // 状态字

        public int shm_slave11_online_status; // 从站11在线状态
        public int shm_slave11_operational_status; // 从站11运行状态
        public int shm_slave11_al_state; // 从站11AL状态

        public int shm_slave11_rx_0x6040_control_word; // 控制字
        public int shm_slave11_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave11_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave11_tx_0x6041_status_word; // 状态字

        public int shm_slave12_online_status; // 从站12在线状态
        public int shm_slave12_operational_status; // 从站12运行状态
        public int shm_slave12_al_state; // 从站12AL状态

        public int shm_slave12_rx_0x6040_control_word; // 控制字
        public int shm_slave12_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave12_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave12_tx_0x6041_status_word; // 状态字

        public int shm_slave13_online_status; // 从站13在线状态
        public int shm_slave13_operational_status; // 从站13运行状态
        public int shm_slave13_al_state; // 从站13AL状态

        public int shm_slave13_rx_0x6040_control_word; // 控制字
        public int shm_slave13_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave13_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave13_tx_0x6041_status_word; // 状态字

        public int shm_slave14_online_status; // 从站14在线状态
        public int shm_slave14_operational_status; // 从站14运行状态
        public int shm_slave14_al_state; // 从站14AL状态

        public int shm_slave14_rx_0x6040_control_word; // 控制字
        public int shm_slave14_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave14_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave14_tx_0x6041_status_word; // 状态字

        public int shm_slave15_online_status; // 从站15在线状态
        public int shm_slave15_operational_status; // 从站15运行状态
        public int shm_slave15_al_state; // 从站15AL状态

        public int shm_slave15_rx_0x6040_control_word; // 控制字
        public int shm_slave15_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave15_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave15_tx_0x6041_status_word; // 状态字

        public int shm_slave16_online_status; // 从站16在线状态
        public int shm_slave16_operational_status; // 从站16运行状态
        public int shm_slave16_al_state; // 从站16AL状态

        public int shm_slave16_rx_0x6040_control_word; // 控制字
        public int shm_slave16_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave16_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave16_tx_0x6041_status_word; // 状态字

        public int shm_slave17_online_status; // 从站17在线状态
        public int shm_slave17_operational_status; // 从站17运行状态
        public int shm_slave17_al_state; // 从站17AL状态

        public int shm_slave17_rx_0x6040_control_word; // 控制字
        public int shm_slave17_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave17_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave17_tx_0x6041_status_word; // 状态字

        public int shm_slave18_online_status; // 从站18在线状态
        public int shm_slave18_operational_status; // 从站18运行状态
        public int shm_slave18_al_state; // 从站18AL状态

        public int shm_slave18_rx_0x6040_control_word; // 控制字
        public int shm_slave18_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave18_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave18_tx_0x6041_status_word; // 状态字

        public int shm_slave19_online_status; // 从站19在线状态
        public int shm_slave19_operational_status; // 从站19运行状态
        public int shm_slave19_al_state; // 从站19AL状态

        public int shm_slave19_rx_0x6040_control_word; // 控制字
        public int shm_slave19_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave19_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave19_tx_0x6041_status_word; // 状态字

        public int shm_slave20_online_status; // 从站20在线状态
        public int shm_slave20_operational_status; // 从站20运行状态
        public int shm_slave20_al_state; // 从站20AL状态

        public int shm_slave20_rx_0x6040_control_word; // 控制字
        public int shm_slave20_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave20_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave20_tx_0x6041_status_word; // 状态字

        public int shm_slave21_online_status; // 从站21在线状态
        public int shm_slave21_operational_status; // 从站21运行状态
        public int shm_slave21_al_state; // 从站21AL状态

        public int shm_slave21_rx_0x6040_control_word; // 控制字
        public int shm_slave21_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave21_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave21_tx_0x6041_status_word; // 状态字

        public int shm_slave22_online_status; // 从站22在线状态
        public int shm_slave22_operational_status; // 从站22运行状态
        public int shm_slave22_al_state; // 从站22AL状态

        public int shm_slave22_rx_0x6040_control_word; // 控制字
        public int shm_slave22_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave22_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave22_tx_0x6041_status_word; // 状态字

        public int shm_slave23_online_status; // 从站23在线状态
        public int shm_slave23_operational_status; // 从站23运行状态
        public int shm_slave23_al_state; // 从站23AL状态

        public int shm_slave23_rx_0x6040_control_word; // 控制字
        public int shm_slave23_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave23_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave23_tx_0x6041_status_word; // 状态字

        public int shm_slave24_online_status; // 从站24在线状态
        public int shm_slave24_operational_status; // 从站24运行状态
        public int shm_slave24_al_state; // 从站24AL状态

        public int shm_slave24_rx_0x6040_control_word; // 控制字
        public int shm_slave24_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave24_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave24_tx_0x6041_status_word; // 状态字

        public int shm_slave25_online_status; // 从站25在线状态
        public int shm_slave25_operational_status; // 从站25运行状态
        public int shm_slave25_al_state; // 从站25AL状态

        public int shm_slave25_rx_0x6040_control_word; // 控制字
        public int shm_slave25_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave25_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave25_tx_0x6041_status_word; // 状态字

        public int shm_slave26_online_status; // 从站26在线状态
        public int shm_slave26_operational_status; // 从站26运行状态
        public int shm_slave26_al_state; // 从站26AL状态

        public int shm_slave26_rx_0x6040_control_word; // 控制字
        public int shm_slave26_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave26_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave26_tx_0x6041_status_word; // 状态字

        public int shm_slave27_online_status; // 从站27在线状态
        public int shm_slave27_operational_status; // 从站27运行状态
        public int shm_slave27_al_state; // 从站27AL状态

        public int shm_slave27_rx_0x6040_control_word; // 控制字
        public int shm_slave27_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave27_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave27_tx_0x6041_status_word; // 状态字

        public int shm_slave28_online_status; // 从站28在线状态
        public int shm_slave28_operational_status; // 从站28运行状态
        public int shm_slave28_al_state; // 从站28AL状态

        public int shm_slave28_rx_0x6040_control_word; // 控制字
        public int shm_slave28_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave28_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave28_tx_0x6041_status_word; // 状态字

        public int shm_slave29_online_status; // 从站29在线状态
        public int shm_slave29_operational_status; // 从站29运行状态
        public int shm_slave29_al_state; // 从站29AL状态

        public int shm_slave29_rx_0x6040_control_word; // 控制字
        public int shm_slave29_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave29_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave29_tx_0x6041_status_word; // 状态字

        public int shm_slave30_online_status; // 从站30在线状态
        public int shm_slave30_operational_status; // 从站30运行状态
        public int shm_slave30_al_state; // 从站30AL状态

        public int shm_slave30_rx_0x6040_control_word; // 控制字
        public int shm_slave30_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave30_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave30_tx_0x6041_status_word; // 状态字

        public int shm_slave31_online_status; // 从站31在线状态
        public int shm_slave31_operational_status; // 从站31运行状态
        public int shm_slave31_al_state; // 从站31AL状态

        public int shm_slave31_rx_0x6040_control_word; // 控制字
        public int shm_slave31_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave31_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave31_tx_0x6041_status_word; // 状态字

        public int shm_slave32_online_status; // 从站32在线状态
        public int shm_slave32_operational_status; // 从站32运行状态
        public int shm_slave32_al_state; // 从站32AL状态

        public int shm_slave32_rx_0x6040_control_word; // 控制字
        public int shm_slave32_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave32_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave32_tx_0x6041_status_word; // 状态字

        public int shm_slave33_online_status; // 从站33在线状态
        public int shm_slave33_operational_status; // 从站33运行状态
        public int shm_slave33_al_state; // 从站33AL状态

        public int shm_slave33_rx_0x6040_control_word; // 控制字
        public int shm_slave33_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave33_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave33_tx_0x6041_status_word; // 状态字

        public int shm_slave34_online_status; // 从站34在线状态
        public int shm_slave34_operational_status; // 从站34运行状态
        public int shm_slave34_al_state; // 从站34AL状态

        public int shm_slave34_rx_0x6040_control_word; // 控制字
        public int shm_slave34_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave34_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave34_tx_0x6041_status_word; // 状态字

        public int shm_slave35_online_status; // 从站35在线状态
        public int shm_slave35_operational_status; // 从站35运行状态
        public int shm_slave35_al_state; // 从站35AL状态

        public int shm_slave35_rx_0x6040_control_word; // 控制字
        public int shm_slave35_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave35_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave35_tx_0x6041_status_word; // 状态字

        public int shm_slave36_online_status; // 从站36在线状态
        public int shm_slave36_operational_status; // 从站36运行状态
        public int shm_slave36_al_state; // 从站36AL状态

        public int shm_slave36_rx_0x6040_control_word; // 控制字
        public int shm_slave36_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave36_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave36_tx_0x6041_status_word; // 状态字

        public int shm_slave37_online_status; // 从站37在线状态
        public int shm_slave37_operational_status; // 从站37运行状态
        public int shm_slave37_al_state; // 从站37AL状态

        public int shm_slave37_rx_0x6040_control_word; // 控制字
        public int shm_slave37_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave37_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave37_tx_0x6041_status_word; // 状态字

        public int shm_slave38_online_status; // 从站38在线状态
        public int shm_slave38_operational_status; // 从站38运行状态
        public int shm_slave38_al_state; // 从站38AL状态

        public int shm_slave38_rx_0x6040_control_word; // 控制字
        public int shm_slave38_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave38_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave38_tx_0x6041_status_word; // 状态字

        public int shm_slave39_online_status; // 从站39在线状态
        public int shm_slave39_operational_status; // 从站39运行状态
        public int shm_slave39_al_state; // 从站39AL状态

        public int shm_slave39_rx_0x6040_control_word; // 控制字
        public int shm_slave39_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave39_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave39_tx_0x6041_status_word; // 状态字

        public int shm_slave40_online_status; // 从站40在线状态
        public int shm_slave40_operational_status; // 从站40运行状态
        public int shm_slave40_al_state; // 从站40AL状态

        public int shm_slave40_rx_0x6040_control_word; // 控制字
        public int shm_slave40_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave40_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave40_tx_0x6041_status_word; // 状态字

        public int shm_slave41_online_status; // 从站41在线状态
        public int shm_slave41_operational_status; // 从站41运行状态
        public int shm_slave41_al_state; // 从站41AL状态

        public int shm_slave41_rx_0x6040_control_word; // 控制字
        public int shm_slave41_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave41_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave41_tx_0x6041_status_word; // 状态字

        public int shm_slave42_online_status; // 从站42在线状态
        public int shm_slave42_operational_status; // 从站42运行状态
        public int shm_slave42_al_state; // 从站42AL状态

        public int shm_slave42_rx_0x6040_control_word; // 控制字
        public int shm_slave42_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave42_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave42_tx_0x6041_status_word; // 状态字

        public int shm_slave43_online_status; // 从站43在线状态
        public int shm_slave43_operational_status; // 从站43运行状态
        public int shm_slave43_al_state; // 从站43AL状态

        public int shm_slave43_rx_0x6040_control_word; // 控制字
        public int shm_slave43_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave43_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave43_tx_0x6041_status_word; // 状态字

        public int shm_slave44_online_status; // 从站44在线状态
        public int shm_slave44_operational_status; // 从站44运行状态
        public int shm_slave44_al_state; // 从站44AL状态

        public int shm_slave44_rx_0x6040_control_word; // 控制字
        public int shm_slave44_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave44_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave44_tx_0x6041_status_word; // 状态字

        public int shm_slave45_online_status; // 从站45在线状态
        public int shm_slave45_operational_status; // 从站45运行状态
        public int shm_slave45_al_state; // 从站45AL状态

        public int shm_slave45_rx_0x6040_control_word; // 控制字
        public int shm_slave45_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave45_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave45_tx_0x6041_status_word; // 状态字

        public int shm_slave46_online_status; // 从站46在线状态
        public int shm_slave46_operational_status; // 从站46运行状态
        public int shm_slave46_al_state; // 从站46AL状态

        public int shm_slave46_rx_0x6040_control_word; // 控制字
        public int shm_slave46_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave46_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave46_tx_0x6041_status_word; // 状态字

        public int shm_slave47_online_status; // 从站47在线状态
        public int shm_slave47_operational_status; // 从站47运行状态
        public int shm_slave47_al_state; // 从站47AL状态

        public int shm_slave47_rx_0x6040_control_word; // 控制字
        public int shm_slave47_rx_0x60ff_target_speed; // 目标速度
        public int shm_slave47_rx_0x6060_operation_mode; // 操作模式设置
        public int shm_slave47_tx_0x6041_status_word; // 状态字
    }

    // Linux RT 相关定义
    [StructLayout(LayoutKind.Sequential)]
    public struct sched_param
    {
        public int sched_priority;
    }

    public class EtherCATController : IDisposable
    {
        private readonly MemoryMappedFile _memoryMappedFile;
        private readonly MemoryMappedViewAccessor _viewAccessor;
        private EtherCATSharedMemory _sharedMemory;

        // P/Invoke 定义
        [DllImport("libc", SetLastError = true)]
        private static extern int sched_setscheduler(int pid, int policy, ref sched_param param);
        
        [DllImport("libc", SetLastError = true)]
        private static extern int sched_get_priority_max(int policy);

        [DllImport("libc")]
        private static extern int getpid();

        public EtherCATController(string EtherCATSharedMemoryFilePath)
        {
            _memoryMappedFile = MemoryMappedFile.CreateFromFile(EtherCATSharedMemoryFilePath, FileMode.Open);
            _viewAccessor = _memoryMappedFile.CreateViewAccessor();
            _sharedMemory = new EtherCATSharedMemory();
        }

        private void UpdateSharedMemory()
        {
            _viewAccessor.Read(0, out _sharedMemory);
        }

        private void WriteSharedMemory()
        {
            _viewAccessor.Write(0, ref _sharedMemory);
        }

        // 新增通用方法 - 设置控制字
        private void SetControlWord(int slaveIndex, int value)
        {
            UpdateSharedMemory();
            switch (slaveIndex)
            {
                case 0: _sharedMemory.shm_slave0_rx_0x6040_control_word = value; break;
                case 1: _sharedMemory.shm_slave1_rx_0x6040_control_word = value; break;
                case 2: _sharedMemory.shm_slave2_rx_0x6040_control_word = value; break;
                case 3: _sharedMemory.shm_slave3_rx_0x6040_control_word = value; break;
                case 4: _sharedMemory.shm_slave4_rx_0x6040_control_word = value; break;
                case 5: _sharedMemory.shm_slave5_rx_0x6040_control_word = value; break;
                case 6: _sharedMemory.shm_slave6_rx_0x6040_control_word = value; break;
                case 7: _sharedMemory.shm_slave7_rx_0x6040_control_word = value; break;
                case 8: _sharedMemory.shm_slave8_rx_0x6040_control_word = value; break;
                case 9: _sharedMemory.shm_slave9_rx_0x6040_control_word = value; break;
                case 10: _sharedMemory.shm_slave10_rx_0x6040_control_word = value; break;
                case 11: _sharedMemory.shm_slave11_rx_0x6040_control_word = value; break;
                case 12: _sharedMemory.shm_slave12_rx_0x6040_control_word = value; break;
                case 13: _sharedMemory.shm_slave13_rx_0x6040_control_word = value; break;
                case 14: _sharedMemory.shm_slave14_rx_0x6040_control_word = value; break;
                case 15: _sharedMemory.shm_slave15_rx_0x6040_control_word = value; break;
                case 16: _sharedMemory.shm_slave16_rx_0x6040_control_word = value; break;
                case 17: _sharedMemory.shm_slave17_rx_0x6040_control_word = value; break;
                case 18: _sharedMemory.shm_slave18_rx_0x6040_control_word = value; break;
                case 19: _sharedMemory.shm_slave19_rx_0x6040_control_word = value; break;
                case 20: _sharedMemory.shm_slave20_rx_0x6040_control_word = value; break;
                case 21: _sharedMemory.shm_slave21_rx_0x6040_control_word = value; break;
                case 22: _sharedMemory.shm_slave22_rx_0x6040_control_word = value; break;
                case 23: _sharedMemory.shm_slave23_rx_0x6040_control_word = value; break;
                case 24: _sharedMemory.shm_slave24_rx_0x6040_control_word = value; break;
                case 25: _sharedMemory.shm_slave25_rx_0x6040_control_word = value; break;
                case 26: _sharedMemory.shm_slave26_rx_0x6040_control_word = value; break;
                case 27: _sharedMemory.shm_slave27_rx_0x6040_control_word = value; break;
                case 28: _sharedMemory.shm_slave28_rx_0x6040_control_word = value; break;
                case 29: _sharedMemory.shm_slave29_rx_0x6040_control_word = value; break;
                case 30: _sharedMemory.shm_slave30_rx_0x6040_control_word = value; break;
                case 31: _sharedMemory.shm_slave31_rx_0x6040_control_word = value; break;
                case 32: _sharedMemory.shm_slave32_rx_0x6040_control_word = value; break;
                case 33: _sharedMemory.shm_slave33_rx_0x6040_control_word = value; break;
                case 34: _sharedMemory.shm_slave34_rx_0x6040_control_word = value; break;
                case 35: _sharedMemory.shm_slave35_rx_0x6040_control_word = value; break;
                case 36: _sharedMemory.shm_slave36_rx_0x6040_control_word = value; break;
                case 37: _sharedMemory.shm_slave37_rx_0x6040_control_word = value; break;
                case 38: _sharedMemory.shm_slave38_rx_0x6040_control_word = value; break;
                case 39: _sharedMemory.shm_slave39_rx_0x6040_control_word = value; break;
                case 40: _sharedMemory.shm_slave40_rx_0x6040_control_word = value; break;
                case 41: _sharedMemory.shm_slave41_rx_0x6040_control_word = value; break;
                case 42: _sharedMemory.shm_slave42_rx_0x6040_control_word = value; break;
                case 43: _sharedMemory.shm_slave43_rx_0x6040_control_word = value; break;
                case 44: _sharedMemory.shm_slave44_rx_0x6040_control_word = value; break;
                case 45: _sharedMemory.shm_slave45_rx_0x6040_control_word = value; break;
                case 46: _sharedMemory.shm_slave46_rx_0x6040_control_word = value; break;
                case 47: _sharedMemory.shm_slave47_rx_0x6040_control_word = value; break;
                default: throw new ArgumentOutOfRangeException(nameof(slaveIndex), "从站索引超出范围");
            }
            WriteSharedMemory();
        }

        // 设置操作模式
        private void SetOperationMode(int slaveIndex, int value)
        {
            UpdateSharedMemory();
            switch (slaveIndex)
            {
                case 0: _sharedMemory.shm_slave0_rx_0x6060_operation_mode = value; break;
                case 1: _sharedMemory.shm_slave1_rx_0x6060_operation_mode = value; break;
                case 2: _sharedMemory.shm_slave2_rx_0x6060_operation_mode = value; break;
                case 3: _sharedMemory.shm_slave3_rx_0x6060_operation_mode = value; break;
                case 4: _sharedMemory.shm_slave4_rx_0x6060_operation_mode = value; break;
                case 5: _sharedMemory.shm_slave5_rx_0x6060_operation_mode = value; break;
                case 6: _sharedMemory.shm_slave6_rx_0x6060_operation_mode = value; break;
                case 7: _sharedMemory.shm_slave7_rx_0x6060_operation_mode = value; break;
                case 8: _sharedMemory.shm_slave8_rx_0x6060_operation_mode = value; break;
                case 9: _sharedMemory.shm_slave9_rx_0x6060_operation_mode = value; break;
                case 10: _sharedMemory.shm_slave10_rx_0x6060_operation_mode = value; break;
                case 11: _sharedMemory.shm_slave11_rx_0x6060_operation_mode = value; break;
                case 12: _sharedMemory.shm_slave12_rx_0x6060_operation_mode = value; break;
                case 13: _sharedMemory.shm_slave13_rx_0x6060_operation_mode = value; break;
                case 14: _sharedMemory.shm_slave14_rx_0x6060_operation_mode = value; break;
                case 15: _sharedMemory.shm_slave15_rx_0x6060_operation_mode = value; break;
                case 16: _sharedMemory.shm_slave16_rx_0x6060_operation_mode = value; break;
                case 17: _sharedMemory.shm_slave17_rx_0x6060_operation_mode = value; break;
                case 18: _sharedMemory.shm_slave18_rx_0x6060_operation_mode = value; break;
                case 19: _sharedMemory.shm_slave19_rx_0x6060_operation_mode = value; break;
                case 20: _sharedMemory.shm_slave20_rx_0x6060_operation_mode = value; break;
                case 21: _sharedMemory.shm_slave21_rx_0x6060_operation_mode = value; break;
                case 22: _sharedMemory.shm_slave22_rx_0x6060_operation_mode = value; break;
                case 23: _sharedMemory.shm_slave23_rx_0x6060_operation_mode = value; break;
                case 24: _sharedMemory.shm_slave24_rx_0x6060_operation_mode = value; break;
                case 25: _sharedMemory.shm_slave25_rx_0x6060_operation_mode = value; break;
                case 26: _sharedMemory.shm_slave26_rx_0x6060_operation_mode = value; break;
                case 27: _sharedMemory.shm_slave27_rx_0x6060_operation_mode = value; break;
                case 28: _sharedMemory.shm_slave28_rx_0x6060_operation_mode = value; break;
                case 29: _sharedMemory.shm_slave29_rx_0x6060_operation_mode = value; break;
                case 30: _sharedMemory.shm_slave30_rx_0x6060_operation_mode = value; break;
                case 31: _sharedMemory.shm_slave31_rx_0x6060_operation_mode = value; break;
                case 32: _sharedMemory.shm_slave32_rx_0x6060_operation_mode = value; break;
                case 33: _sharedMemory.shm_slave33_rx_0x6060_operation_mode = value; break;
                case 34: _sharedMemory.shm_slave34_rx_0x6060_operation_mode = value; break;
                case 35: _sharedMemory.shm_slave35_rx_0x6060_operation_mode = value; break;
                case 36: _sharedMemory.shm_slave36_rx_0x6060_operation_mode = value; break;
                case 37: _sharedMemory.shm_slave37_rx_0x6060_operation_mode = value; break;
                case 38: _sharedMemory.shm_slave38_rx_0x6060_operation_mode = value; break;
                case 39: _sharedMemory.shm_slave39_rx_0x6060_operation_mode = value; break;
                case 40: _sharedMemory.shm_slave40_rx_0x6060_operation_mode = value; break;
                case 41: _sharedMemory.shm_slave41_rx_0x6060_operation_mode = value; break;
                case 42: _sharedMemory.shm_slave42_rx_0x6060_operation_mode = value; break;
                case 43: _sharedMemory.shm_slave43_rx_0x6060_operation_mode = value; break;
                case 44: _sharedMemory.shm_slave44_rx_0x6060_operation_mode = value; break;
                case 45: _sharedMemory.shm_slave45_rx_0x6060_operation_mode = value; break;
                case 46: _sharedMemory.shm_slave46_rx_0x6060_operation_mode = value; break;
                case 47: _sharedMemory.shm_slave47_rx_0x6060_operation_mode = value; break;
                default: throw new ArgumentOutOfRangeException(nameof(slaveIndex), "从站索引超出范围");
            }
            WriteSharedMemory();
        }

        // 设置目标速度
        private void SetTargetSpeed(int slaveIndex, int value)
        {
            UpdateSharedMemory();
            switch (slaveIndex)
            {
                case 0: _sharedMemory.shm_slave0_rx_0x60ff_target_speed = value; break;
                case 1: _sharedMemory.shm_slave1_rx_0x60ff_target_speed = value; break;
                case 2: _sharedMemory.shm_slave2_rx_0x60ff_target_speed = value; break;
                case 3: _sharedMemory.shm_slave3_rx_0x60ff_target_speed = value; break;
                case 4: _sharedMemory.shm_slave4_rx_0x60ff_target_speed = value; break;
                case 5: _sharedMemory.shm_slave5_rx_0x60ff_target_speed = value; break;
                case 6: _sharedMemory.shm_slave6_rx_0x60ff_target_speed = value; break;
                case 7: _sharedMemory.shm_slave7_rx_0x60ff_target_speed = value; break;
                case 8: _sharedMemory.shm_slave8_rx_0x60ff_target_speed = value; break;
                case 9: _sharedMemory.shm_slave9_rx_0x60ff_target_speed = value; break;
                case 10: _sharedMemory.shm_slave10_rx_0x60ff_target_speed = value; break;
                case 11: _sharedMemory.shm_slave11_rx_0x60ff_target_speed = value; break;
                case 12: _sharedMemory.shm_slave12_rx_0x60ff_target_speed = value; break;
                case 13: _sharedMemory.shm_slave13_rx_0x60ff_target_speed = value; break;
                case 14: _sharedMemory.shm_slave14_rx_0x60ff_target_speed = value; break;
                case 15: _sharedMemory.shm_slave15_rx_0x60ff_target_speed = value; break;
                case 16: _sharedMemory.shm_slave16_rx_0x60ff_target_speed = value; break;
                case 17: _sharedMemory.shm_slave17_rx_0x60ff_target_speed = value; break;
                case 18: _sharedMemory.shm_slave18_rx_0x60ff_target_speed = value; break;
                case 19: _sharedMemory.shm_slave19_rx_0x60ff_target_speed = value; break;
                case 20: _sharedMemory.shm_slave20_rx_0x60ff_target_speed = value; break;
                case 21: _sharedMemory.shm_slave21_rx_0x60ff_target_speed = value; break;
                case 22: _sharedMemory.shm_slave22_rx_0x60ff_target_speed = value; break;
                case 23: _sharedMemory.shm_slave23_rx_0x60ff_target_speed = value; break;
                case 24: _sharedMemory.shm_slave24_rx_0x60ff_target_speed = value; break;
                case 25: _sharedMemory.shm_slave25_rx_0x60ff_target_speed = value; break;
                case 26: _sharedMemory.shm_slave26_rx_0x60ff_target_speed = value; break;
                case 27: _sharedMemory.shm_slave27_rx_0x60ff_target_speed = value; break;
                case 28: _sharedMemory.shm_slave28_rx_0x60ff_target_speed = value; break;
                case 29: _sharedMemory.shm_slave29_rx_0x60ff_target_speed = value; break;
                case 30: _sharedMemory.shm_slave30_rx_0x60ff_target_speed = value; break;
                case 31: _sharedMemory.shm_slave31_rx_0x60ff_target_speed = value; break;
                case 32: _sharedMemory.shm_slave32_rx_0x60ff_target_speed = value; break;
                case 33: _sharedMemory.shm_slave33_rx_0x60ff_target_speed = value; break;
                case 34: _sharedMemory.shm_slave34_rx_0x60ff_target_speed = value; break;
                case 35: _sharedMemory.shm_slave35_rx_0x60ff_target_speed = value; break;
                case 36: _sharedMemory.shm_slave36_rx_0x60ff_target_speed = value; break;
                case 37: _sharedMemory.shm_slave37_rx_0x60ff_target_speed = value; break;
                case 38: _sharedMemory.shm_slave38_rx_0x60ff_target_speed = value; break;
                case 39: _sharedMemory.shm_slave39_rx_0x60ff_target_speed = value; break;
                case 40: _sharedMemory.shm_slave40_rx_0x60ff_target_speed = value; break;
                case 41: _sharedMemory.shm_slave41_rx_0x60ff_target_speed = value; break;
                case 42: _sharedMemory.shm_slave42_rx_0x60ff_target_speed = value; break;
                case 43: _sharedMemory.shm_slave43_rx_0x60ff_target_speed = value; break;
                case 44: _sharedMemory.shm_slave44_rx_0x60ff_target_speed = value; break;
                case 45: _sharedMemory.shm_slave45_rx_0x60ff_target_speed = value; break;
                case 46: _sharedMemory.shm_slave46_rx_0x60ff_target_speed = value; break;
                case 47: _sharedMemory.shm_slave47_rx_0x60ff_target_speed = value; break;
                default: throw new ArgumentOutOfRangeException(nameof(slaveIndex), "从站索引超出范围");
            }
            WriteSharedMemory();
        }

        // 获取在线状态
        private bool GetSlaveOnlineStatus(int slaveIndex)
        {
            UpdateSharedMemory();
            switch (slaveIndex)
            {
                case 0: return _sharedMemory.shm_slave0_online_status == 1;
                case 1: return _sharedMemory.shm_slave1_online_status == 1;
                case 2: return _sharedMemory.shm_slave2_online_status == 1;
                case 3: return _sharedMemory.shm_slave3_online_status == 1;
                case 4: return _sharedMemory.shm_slave4_online_status == 1;
                case 5: return _sharedMemory.shm_slave5_online_status == 1;
                case 6: return _sharedMemory.shm_slave6_online_status == 1;
                case 7: return _sharedMemory.shm_slave7_online_status == 1;
                case 8: return _sharedMemory.shm_slave8_online_status == 1;
                case 9: return _sharedMemory.shm_slave9_online_status == 1;
                case 10: return _sharedMemory.shm_slave10_online_status == 1;
                case 11: return _sharedMemory.shm_slave11_online_status == 1;
                case 12: return _sharedMemory.shm_slave12_online_status == 1;
                case 13: return _sharedMemory.shm_slave13_online_status == 1;
                case 14: return _sharedMemory.shm_slave14_online_status == 1;
                case 15: return _sharedMemory.shm_slave15_online_status == 1;
                case 16: return _sharedMemory.shm_slave16_online_status == 1;
                case 17: return _sharedMemory.shm_slave17_online_status == 1;
                case 18: return _sharedMemory.shm_slave18_online_status == 1;
                case 19: return _sharedMemory.shm_slave19_online_status == 1;
                case 20: return _sharedMemory.shm_slave20_online_status == 1;
                case 21: return _sharedMemory.shm_slave21_online_status == 1;
                case 22: return _sharedMemory.shm_slave22_online_status == 1;
                case 23: return _sharedMemory.shm_slave23_online_status == 1;
                case 24: return _sharedMemory.shm_slave24_online_status == 1;
                case 25: return _sharedMemory.shm_slave25_online_status == 1;
                case 26: return _sharedMemory.shm_slave26_online_status == 1;
                case 27: return _sharedMemory.shm_slave27_online_status == 1;
                case 28: return _sharedMemory.shm_slave28_online_status == 1;
                case 29: return _sharedMemory.shm_slave29_online_status == 1;
                case 30: return _sharedMemory.shm_slave30_online_status == 1;
                case 31: return _sharedMemory.shm_slave31_online_status == 1;
                case 32: return _sharedMemory.shm_slave32_online_status == 1;
                case 33: return _sharedMemory.shm_slave33_online_status == 1;
                case 34: return _sharedMemory.shm_slave34_online_status == 1;
                case 35: return _sharedMemory.shm_slave35_online_status == 1;
                case 36: return _sharedMemory.shm_slave36_online_status == 1;
                case 37: return _sharedMemory.shm_slave37_online_status == 1;
                case 38: return _sharedMemory.shm_slave38_online_status == 1;
                case 39: return _sharedMemory.shm_slave39_online_status == 1;
                case 40: return _sharedMemory.shm_slave40_online_status == 1;
                case 41: return _sharedMemory.shm_slave41_online_status == 1;
                case 42: return _sharedMemory.shm_slave42_online_status == 1;
                case 43: return _sharedMemory.shm_slave43_online_status == 1;
                case 44: return _sharedMemory.shm_slave44_online_status == 1;
                case 45: return _sharedMemory.shm_slave45_online_status == 1;
                case 46: return _sharedMemory.shm_slave46_online_status == 1;
                case 47: return _sharedMemory.shm_slave47_online_status == 1;
                default: throw new ArgumentOutOfRangeException(nameof(slaveIndex), "从站索引超出范围");
            }
        }

        // 获取运行状态
        private bool GetSlaveOperationalStatus(int slaveIndex)
        {
            UpdateSharedMemory();
            switch (slaveIndex)
            {
                case 0: return _sharedMemory.shm_slave0_operational_status == 1;
                case 1: return _sharedMemory.shm_slave1_operational_status == 1;
                case 2: return _sharedMemory.shm_slave2_operational_status == 1;
                case 3: return _sharedMemory.shm_slave3_operational_status == 1;
                case 4: return _sharedMemory.shm_slave4_operational_status == 1;
                case 5: return _sharedMemory.shm_slave5_operational_status == 1;
                case 6: return _sharedMemory.shm_slave6_operational_status == 1;
                case 7: return _sharedMemory.shm_slave7_operational_status == 1;
                case 8: return _sharedMemory.shm_slave8_operational_status == 1;
                case 9: return _sharedMemory.shm_slave9_operational_status == 1;
                case 10: return _sharedMemory.shm_slave10_operational_status == 1;
                case 11: return _sharedMemory.shm_slave11_operational_status == 1;
                case 12: return _sharedMemory.shm_slave12_operational_status == 1;
                case 13: return _sharedMemory.shm_slave13_operational_status == 1;
                case 14: return _sharedMemory.shm_slave14_operational_status == 1;
                case 15: return _sharedMemory.shm_slave15_operational_status == 1;
                case 16: return _sharedMemory.shm_slave16_operational_status == 1;
                case 17: return _sharedMemory.shm_slave17_operational_status == 1;
                case 18: return _sharedMemory.shm_slave18_operational_status == 1;
                case 19: return _sharedMemory.shm_slave19_operational_status == 1;
                case 20: return _sharedMemory.shm_slave20_operational_status == 1;
                case 21: return _sharedMemory.shm_slave21_operational_status == 1;
                case 22: return _sharedMemory.shm_slave22_operational_status == 1;
                case 23: return _sharedMemory.shm_slave23_operational_status == 1;
                case 24: return _sharedMemory.shm_slave24_operational_status == 1;
                case 25: return _sharedMemory.shm_slave25_operational_status == 1;
                case 26: return _sharedMemory.shm_slave26_operational_status == 1;
                case 27: return _sharedMemory.shm_slave27_operational_status == 1;
                case 28: return _sharedMemory.shm_slave28_operational_status == 1;
                case 29: return _sharedMemory.shm_slave29_operational_status == 1;
                case 30: return _sharedMemory.shm_slave30_operational_status == 1;
                case 31: return _sharedMemory.shm_slave31_operational_status == 1;
                case 32: return _sharedMemory.shm_slave32_operational_status == 1;
                case 33: return _sharedMemory.shm_slave33_operational_status == 1;
                case 34: return _sharedMemory.shm_slave34_operational_status == 1;
                case 35: return _sharedMemory.shm_slave35_operational_status == 1;
                case 36: return _sharedMemory.shm_slave36_operational_status == 1;
                case 37: return _sharedMemory.shm_slave37_operational_status == 1;
                case 38: return _sharedMemory.shm_slave38_operational_status == 1;
                case 39: return _sharedMemory.shm_slave39_operational_status == 1;
                case 40: return _sharedMemory.shm_slave40_operational_status == 1;
                case 41: return _sharedMemory.shm_slave41_operational_status == 1;
                case 42: return _sharedMemory.shm_slave42_operational_status == 1;
                case 43: return _sharedMemory.shm_slave43_operational_status == 1;
                case 44: return _sharedMemory.shm_slave44_operational_status == 1;
                case 45: return _sharedMemory.shm_slave45_operational_status == 1;
                case 46: return _sharedMemory.shm_slave46_operational_status == 1;
                case 47: return _sharedMemory.shm_slave47_operational_status == 1;
                default: throw new ArgumentOutOfRangeException(nameof(slaveIndex), "从站索引超出范围");
            }
        }

        // 获取AL状态
        private int GetSlaveALState(int slaveIndex)
        {
            UpdateSharedMemory();
            switch (slaveIndex)
            {
                case 0: return _sharedMemory.shm_slave0_al_state;
                case 1: return _sharedMemory.shm_slave1_al_state;
                case 2: return _sharedMemory.shm_slave2_al_state;
                case 3: return _sharedMemory.shm_slave3_al_state;
                case 4: return _sharedMemory.shm_slave4_al_state;
                case 5: return _sharedMemory.shm_slave5_al_state;
                case 6: return _sharedMemory.shm_slave6_al_state;
                case 7: return _sharedMemory.shm_slave7_al_state;
                case 8: return _sharedMemory.shm_slave8_al_state;
                case 9: return _sharedMemory.shm_slave9_al_state;
                case 10: return _sharedMemory.shm_slave10_al_state;
                case 11: return _sharedMemory.shm_slave11_al_state;
                case 12: return _sharedMemory.shm_slave12_al_state;
                case 13: return _sharedMemory.shm_slave13_al_state;
                case 14: return _sharedMemory.shm_slave14_al_state;
                case 15: return _sharedMemory.shm_slave15_al_state;
                case 16: return _sharedMemory.shm_slave16_al_state;
                case 17: return _sharedMemory.shm_slave17_al_state;
                case 18: return _sharedMemory.shm_slave18_al_state;
                case 19: return _sharedMemory.shm_slave19_al_state;
                case 20: return _sharedMemory.shm_slave20_al_state;
                case 21: return _sharedMemory.shm_slave21_al_state;
                case 22: return _sharedMemory.shm_slave22_al_state;
                case 23: return _sharedMemory.shm_slave23_al_state;
                case 24: return _sharedMemory.shm_slave24_al_state;
                case 25: return _sharedMemory.shm_slave25_al_state;
                case 26: return _sharedMemory.shm_slave26_al_state;
                case 27: return _sharedMemory.shm_slave27_al_state;
                case 28: return _sharedMemory.shm_slave28_al_state;
                case 29: return _sharedMemory.shm_slave29_al_state;
                case 30: return _sharedMemory.shm_slave30_al_state;
                case 31: return _sharedMemory.shm_slave31_al_state;
                case 32: return _sharedMemory.shm_slave32_al_state;
                case 33: return _sharedMemory.shm_slave33_al_state;
                case 34: return _sharedMemory.shm_slave34_al_state;
                case 35: return _sharedMemory.shm_slave35_al_state;
                case 36: return _sharedMemory.shm_slave36_al_state;
                case 37: return _sharedMemory.shm_slave37_al_state;
                case 38: return _sharedMemory.shm_slave38_al_state;
                case 39: return _sharedMemory.shm_slave39_al_state;
                case 40: return _sharedMemory.shm_slave40_al_state;
                case 41: return _sharedMemory.shm_slave41_al_state;
                case 42: return _sharedMemory.shm_slave42_al_state;
                case 43: return _sharedMemory.shm_slave43_al_state;
                case 44: return _sharedMemory.shm_slave44_al_state;
                case 45: return _sharedMemory.shm_slave45_al_state;
                case 46: return _sharedMemory.shm_slave46_al_state;
                case 47: return _sharedMemory.shm_slave47_al_state;
                default: throw new ArgumentOutOfRangeException(nameof(slaveIndex), "从站索引超出范围");
            }
        }

        // 通用使能伺服方法
        public void EnableServo(int slaveIndex)
        {
            Console.WriteLine($"正在使能从站{slaveIndex}伺服...");
            
            try
            {
                // 初始化命令字
                int command = 0x004F;
                bool enabled = false;
                int timeoutMs = 10000;
                int elapsed = 0;

                // 设置初始状态
                SetControlWord(slaveIndex, 0x0080);
                SetOperationMode(slaveIndex, 0x09);
                Thread.Sleep(10);
                
                while (!enabled && elapsed < timeoutMs)
                {
                    int statusWord = GetStatusWord(slaveIndex);
                    
                    // 打印当前状态
                    Console.WriteLine($"从站{slaveIndex}当前状态字: 0x{statusWord:X4}");
                    
                    if ((statusWord & command) == 0x0040)
                    {
                        SetControlWord(slaveIndex, 0x0006);
                        command = 0x006F;
                    }
                    else if ((statusWord & command) == 0x0021)
                    {
                        SetControlWord(slaveIndex, 0x0007);
                        command = 0x006F;
                    }
                    else if ((statusWord & command) == 0x0023)
                    {
                        SetControlWord(slaveIndex, 0x000F);
                        command = 0x006F;
                    }
                    else if ((statusWord & command) == 0x0027)
                    {
                        // 伺服已经使能成功
                        Console.WriteLine($"从站{slaveIndex}伺服使能成功");
                        enabled = true;
                        break;
                    }
                    
                    Thread.Sleep(100);
                    elapsed += 100;
                }
                
                if (!enabled)
                {
                    Console.WriteLine($"从站{slaveIndex}伺服使能失败：等待就绪超时");
                    // 诊断信息
                    Console.WriteLine($"诊断信息:");
                    Console.WriteLine($"- 最终状态字: 0x{GetStatusWord(slaveIndex):X4}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"从站{slaveIndex}伺服使能过程出错: {ex.Message}");
            }
        }

        // 通用禁用伺服方法
        public void DisableServo(int slaveIndex)
        {
            Console.WriteLine($"正在禁用从站{slaveIndex}伺服...");
            SetControlWord(slaveIndex, 0x0080);
            SetControlWord(slaveIndex, 0x02);
            SetControlWord(slaveIndex, 0x00);
            Console.WriteLine($"从站{slaveIndex}伺服已禁用。");
        }

        // 批量禁用多个从站伺服
        public void BatchDisableServo(int[] slaveIndices)
        {
            Console.WriteLine($"正在批量禁用{slaveIndices.Length}个从站伺服...");
            
            // 检查是否禁用所有从站
            if (slaveIndices.Length == 48)
            {
                // 使用批量写入方法
                Console.WriteLine("批量禁用所有从站 - 阶段1: 设置控制字0x0080");
                SetControlWordAll(0x0080);
                Thread.Sleep(10);
                
                Console.WriteLine("批量禁用所有从站 - 阶段2: 设置控制字0x02");
                SetControlWordAll(0x02);
                Thread.Sleep(10);
                
                Console.WriteLine("批量禁用所有从站 - 阶段3: 设置控制字0x00");
                SetControlWordAll(0x00);
            }
            else
            {
                // 第一阶段：所有从站设置控制字0x0080
                foreach (int slaveIndex in slaveIndices)
                {
                    SetControlWord(slaveIndex, 0x0080);
                }
                Thread.Sleep(10);
                
                // 第二阶段：所有从站设置控制字0x02
                foreach (int slaveIndex in slaveIndices)
                {
                    SetControlWord(slaveIndex, 0x02);
                }
                Thread.Sleep(10);
                
                // 第三阶段：所有从站设置控制字0x00
                foreach (int slaveIndex in slaveIndices)
                {
                    SetControlWord(slaveIndex, 0x00);
                }
            }
            
            Console.WriteLine($"{slaveIndices.Length}个从站伺服已批量禁用。");
        }
        
        // 禁用所有伺服
        public void DisableAllServos()
        {
            Console.WriteLine("正在禁用所有从站伺服...");
            
            // 直接使用批量写入方法，无需循环
            Console.WriteLine("禁用所有从站 - 阶段1: 设置控制字0x0080");
            SetControlWordAll(0x0080);
            Thread.Sleep(10);
            
            Console.WriteLine("禁用所有从站 - 阶段2: 设置控制字0x02");
            SetControlWordAll(0x02);
            Thread.Sleep(10);
            
            Console.WriteLine("禁用所有从站 - 阶段3: 设置控制字0x00");
            SetControlWordAll(0x00);
            
            Console.WriteLine("所有从站伺服已禁用。");
        }

        public void SetVelocity(int velocity)
        {
            // 直接使用批量设置方法，无需循环
            SetTargetSpeedAll(velocity);
        }

        public void UpdateStatus()
        {
            UpdateSharedMemory();
        }

        public void EnterRealtimeMode()
        {
            try 
            {
                // 设置.NET线程优先级为最高
                Thread.CurrentThread.Priority = ThreadPriority.Highest;

                // 在Linux系统上设置实时调度策略
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    const int SCHED_FIFO = 1;
                    
                    // 获取当前进程ID
                    int pid = getpid();
                    Console.WriteLine($"当前进程ID: {pid}");

                    // 获取SCHED_FIFO的最高优先级
                    int maxPriority = sched_get_priority_max(SCHED_FIFO);
                    Console.WriteLine($"SCHED_FIFO最高优先级: {maxPriority}");
                    
                    var schedParam = new sched_param { sched_priority = maxPriority };
                    
                    // 设置当前进程为SCHED_FIFO策略
                    int result = sched_setscheduler(pid, SCHED_FIFO, ref schedParam);
                    if (result != 0)
                    {
                        int error = Marshal.GetLastWin32Error();
                        Console.WriteLine($"警告: 设置SCHED_FIFO调度策略失败。错误码: {error}");
                        Console.WriteLine("请尝试使用以下命令运行程序：");
                        Console.WriteLine($"sudo chrt -f {maxPriority} dotnet run");
                        Console.WriteLine("或者使用以下命令：");
                        Console.WriteLine($"sudo nice -n -{maxPriority} dotnet run");
                    }
                    else 
                    {
                        Console.WriteLine($"成功设置实时调度策略SCHED_FIFO，优先级: {maxPriority}");
                    }
                }
                // 在Windows系统上设置进程优先级
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    using var process = Process.GetCurrentProcess();
                    process.PriorityClass = ProcessPriorityClass.RealTime;
                    Console.WriteLine("成功设置Windows进程优先级为实时");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置实时模式时出错: {ex.Message}");
            }
        }

        public async Task RunControlSequence(CancellationToken cancellationToken)
        {
            try
            {
                // 设置当前线程优先级为最高
                Thread.CurrentThread.Priority = ThreadPriority.Highest;
                Console.WriteLine($"当前线程优先级已设置为: {Thread.CurrentThread.Priority}");

                // 为48个从站创建状态跟踪数组
                bool[] servoEnabled = new bool[48];
                bool[] slaveWasOnline = new bool[48];
                
                // 添加上次打印时间记录
                DateTime lastStatusPrintTime = DateTime.MinValue;

                // 创建一个高优先级的任务来处理速度更新
                var velocityUpdateTask = Task.Run(async () =>
                {
                    Thread.CurrentThread.Priority = ThreadPriority.Highest;
                    Console.WriteLine($"速度更新线程优先级已设置为: {Thread.CurrentThread.Priority}");
                    
                    while (!cancellationToken.IsCancellationRequested)
                    {
                        // 如果有任何从站伺服被使能，则设置速度
                        if (servoEnabled.Any(enabled => enabled))
                        {
                            SetVelocity(1000000);
                        }
                    }
                }, cancellationToken);

                while (!cancellationToken.IsCancellationRequested)
                {
                    UpdateSharedMemory();
                    
                    // 每秒打印一次状态信息
                    if ((DateTime.Now - lastStatusPrintTime).TotalSeconds >= 1)
                    {
                        Console.WriteLine("--------- 从站状态信息 ---------");
                        for (int i = 0; i < 48; i++)
                        {
                            bool isOnline = GetSlaveOnlineStatus(i);
                            bool isOperational = GetSlaveOperationalStatus(i);
                            int alState = GetSlaveALState(i);
                            Console.WriteLine($"从站{i} - 在线状态: {(isOnline ? 1 : 0)}, 运行状态: {(isOperational ? 1 : 0)}, AL状态: {alState}");
                        }
                        lastStatusPrintTime = DateTime.Now;
                    }

                    // 检查是否有新上线的从站，如果有则批量使能
                    bool hasNewOnlineSlaves = false;
                    List<int> newOnlineSlaves = new List<int>();
                    
                    for (int i = 0; i < 48; i++)
                    {
                        bool slaveIsOnline = GetSlaveOnlineStatus(i);
                        bool slaveIsOperational = GetSlaveOperationalStatus(i);
                        
                        // 检查是否有新上线的从站
                        if (slaveIsOnline && !slaveWasOnline[i] && slaveIsOperational)
                        {
                            Console.WriteLine($"从站{i}已上线并处于运行状态");
                            newOnlineSlaves.Add(i);
                            hasNewOnlineSlaves = true;
                            slaveWasOnline[i] = true;
                        }
                        // 检查是否有离线的从站
                        else if (!slaveIsOnline && slaveWasOnline[i])
                        {
                            Console.WriteLine($"从站{i}离线，正在禁用伺服...");
                            DisableServo(i);
                            servoEnabled[i] = false;
                            slaveWasOnline[i] = false;
                        }
                    }
                    
                    // 如果有新上线的从站，执行批量使能
                    if (hasNewOnlineSlaves && newOnlineSlaves.Count > 0)
                    {
                        Console.WriteLine($"检测到{newOnlineSlaves.Count}个新上线从站，执行批量使能...");
                        BatchEnableServo(newOnlineSlaves.ToArray());
                        
                        // 更新使能状态
                        foreach (int slaveIndex in newOnlineSlaves)
                        {
                            if (WaitForServoReady(slaveIndex, cancellationToken))
                            {
                                servoEnabled[slaveIndex] = true;
                                Console.WriteLine($"从站{slaveIndex}伺服使能成功");
                            }
                            else
                            {
                                Console.WriteLine($"从站{slaveIndex}伺服使能失败");
                                servoEnabled[slaveIndex] = false;
                            }
                        }
                    }
                }

                await velocityUpdateTask; // 等待速度更新任务完成
            }
            catch (Exception ex)
            {
                Console.WriteLine($"控制序列出错: {ex.Message}");
            }
            finally
            {
                // 使用批量禁用方法禁用所有伺服
                DisableAllServos();
                Console.WriteLine("进程已完成。");
            }
        }

        // 新增通用方法
        private int GetStatusWord(int slaveIndex)
        {
            UpdateSharedMemory();
            switch (slaveIndex)
            {
                case 0: return _sharedMemory.shm_slave0_tx_0x6041_status_word;
                case 1: return _sharedMemory.shm_slave1_tx_0x6041_status_word;
                case 2: return _sharedMemory.shm_slave2_tx_0x6041_status_word;
                case 3: return _sharedMemory.shm_slave3_tx_0x6041_status_word;
                case 4: return _sharedMemory.shm_slave4_tx_0x6041_status_word;
                case 5: return _sharedMemory.shm_slave5_tx_0x6041_status_word;
                case 6: return _sharedMemory.shm_slave6_tx_0x6041_status_word;
                case 7: return _sharedMemory.shm_slave7_tx_0x6041_status_word;
                case 8: return _sharedMemory.shm_slave8_tx_0x6041_status_word;
                case 9: return _sharedMemory.shm_slave9_tx_0x6041_status_word;
                case 10: return _sharedMemory.shm_slave10_tx_0x6041_status_word;
                case 11: return _sharedMemory.shm_slave11_tx_0x6041_status_word;
                case 12: return _sharedMemory.shm_slave12_tx_0x6041_status_word;
                case 13: return _sharedMemory.shm_slave13_tx_0x6041_status_word;
                case 14: return _sharedMemory.shm_slave14_tx_0x6041_status_word;
                case 15: return _sharedMemory.shm_slave15_tx_0x6041_status_word;
                case 16: return _sharedMemory.shm_slave16_tx_0x6041_status_word;
                case 17: return _sharedMemory.shm_slave17_tx_0x6041_status_word;
                case 18: return _sharedMemory.shm_slave18_tx_0x6041_status_word;
                case 19: return _sharedMemory.shm_slave19_tx_0x6041_status_word;
                case 20: return _sharedMemory.shm_slave20_tx_0x6041_status_word;
                case 21: return _sharedMemory.shm_slave21_tx_0x6041_status_word;
                case 22: return _sharedMemory.shm_slave22_tx_0x6041_status_word;
                case 23: return _sharedMemory.shm_slave23_tx_0x6041_status_word;
                case 24: return _sharedMemory.shm_slave24_tx_0x6041_status_word;
                case 25: return _sharedMemory.shm_slave25_tx_0x6041_status_word;
                case 26: return _sharedMemory.shm_slave26_tx_0x6041_status_word;
                case 27: return _sharedMemory.shm_slave27_tx_0x6041_status_word;
                case 28: return _sharedMemory.shm_slave28_tx_0x6041_status_word;
                case 29: return _sharedMemory.shm_slave29_tx_0x6041_status_word;
                case 30: return _sharedMemory.shm_slave30_tx_0x6041_status_word;
                case 31: return _sharedMemory.shm_slave31_tx_0x6041_status_word;
                case 32: return _sharedMemory.shm_slave32_tx_0x6041_status_word;
                case 33: return _sharedMemory.shm_slave33_tx_0x6041_status_word;
                case 34: return _sharedMemory.shm_slave34_tx_0x6041_status_word;
                case 35: return _sharedMemory.shm_slave35_tx_0x6041_status_word;
                case 36: return _sharedMemory.shm_slave36_tx_0x6041_status_word;
                case 37: return _sharedMemory.shm_slave37_tx_0x6041_status_word;
                case 38: return _sharedMemory.shm_slave38_tx_0x6041_status_word;
                case 39: return _sharedMemory.shm_slave39_tx_0x6041_status_word;
                case 40: return _sharedMemory.shm_slave40_tx_0x6041_status_word;
                case 41: return _sharedMemory.shm_slave41_tx_0x6041_status_word;
                case 42: return _sharedMemory.shm_slave42_tx_0x6041_status_word;
                case 43: return _sharedMemory.shm_slave43_tx_0x6041_status_word;
                case 44: return _sharedMemory.shm_slave44_tx_0x6041_status_word;
                case 45: return _sharedMemory.shm_slave45_tx_0x6041_status_word;
                case 46: return _sharedMemory.shm_slave46_tx_0x6041_status_word;
                case 47: return _sharedMemory.shm_slave47_tx_0x6041_status_word;
                default: throw new ArgumentOutOfRangeException(nameof(slaveIndex), "从站索引超出范围");
            }
        }

        private bool CheckStatusWord(int slaveIndex, ushort expectedStatus)
        {
            return (GetStatusWord(slaveIndex) & 0x0FFF) == expectedStatus;
        }

        private bool WaitForStatus(int slaveIndex, int expectedStatus, int timeoutMs = 1000)
        {
            int elapsed = 0;
            while (elapsed < timeoutMs)
            {
                int currentStatus = GetStatusWord(slaveIndex) & 0x0FFF;
                
                // 添加状态变化日志
                Console.WriteLine($"从站{slaveIndex}当前状态: 0x{currentStatus:X4}, 预期: 0x{expectedStatus:X4}");
                
                if (currentStatus == expectedStatus)
                {
                    return true;
                }
                Thread.Sleep(10);
                elapsed += 10;
            }
            
            // 超时时输出最后的状态
            int finalStatus = GetStatusWord(slaveIndex) & 0x0FFF;
            Console.WriteLine($"从站{slaveIndex}状态等待超时。最终状态: 0x{finalStatus:X4}, 预期: 0x{expectedStatus:X4}");
            return false;
        }

        public bool WaitForServoReady(int slaveIndex, CancellationToken cancellationToken = default)
        {
            Console.WriteLine($"等待从站{slaveIndex}伺服就绪...");
            int timeoutMs = 5000; // 5秒超时
            int elapsed = 0;
            
            while (!cancellationToken.IsCancellationRequested && elapsed < timeoutMs)
            {
                int statusWord = GetStatusWord(slaveIndex);
                
                // 详细打印状态字的每个位
                Console.WriteLine($"从站{slaveIndex}当前状态字: 0x{statusWord:X4}");
                Console.WriteLine($"Ready to switch on: {(statusWord & 0x0001) != 0}");
                Console.WriteLine($"Switched on: {(statusWord & 0x0002) != 0}");
                Console.WriteLine($"Operation enabled: {(statusWord & 0x0004) != 0}");
                Console.WriteLine($"Fault: {(statusWord & 0x0008) != 0}");
                Console.WriteLine($"Voltage enabled: {(statusWord & 0x0010) != 0}");
                Console.WriteLine($"Quick stop: {(statusWord & 0x0020) != 0}");
                Console.WriteLine($"Switch on disabled: {(statusWord & 0x0040) != 0}");
                Console.WriteLine($"Warning: {(statusWord & 0x0080) != 0}");

                // 修改判断逻辑：检查状态字是否为0x1637或0x0637
                bool isOperationEnabled = (statusWord & 0x0FFF) == 0x0637 || (statusWord & 0x0FFF) == 0x1637;
                
                if (isOperationEnabled)
                {
                    Console.WriteLine($"从站{slaveIndex}伺服已就绪");
                    return true;
                }
                
                Thread.Sleep(100);
                elapsed += 100;
            }
            
            if (elapsed >= timeoutMs)
            {
                Console.WriteLine($"等待从站{slaveIndex}伺服就绪超时，最后状态字: 0x{GetStatusWord(slaveIndex):X4}");
            }
            
            return false;
        }

        public void Dispose()
        {
            _viewAccessor?.Dispose();
            _memoryMappedFile?.Dispose();
        }

        // 批量使能所有从站
        public void BatchEnableServo(int[] slaveIndices)
        {
            Console.WriteLine($"正在批量使能{slaveIndices.Length}个从站...");
            
            try
            {
                // 第一阶段：初始化所有从站
                Console.WriteLine("阶段1: 初始化所有从站");
                
                // 如果所有从站都需要使能，使用批量写入方法
                if (slaveIndices.Length == 48)
                {
                    SetControlWordAll(0x0080);
                    SetOperationModeAll(0x09);
                }
                else
                {
                    foreach (int slaveIndex in slaveIndices)
                    {
                        SetControlWord(slaveIndex, 0x0080);
                        SetOperationMode(slaveIndex, 0x09);
                    }
                }
                Thread.Sleep(10);
                
                // 第二阶段：根据状态字分批处理
                bool allEnabled = false;
                int timeoutMs = 10000;
                int elapsed = 0;
                
                while (!allEnabled && elapsed < timeoutMs)
                {
                    // 更新共享内存获取最新状态
                    UpdateSharedMemory();
                    
                    // 每次迭代时重置标志
                    allEnabled = true;
                    
                    // 第一阶段批量处理：从状态为0x0040的从站
                    bool hasPhase1 = false;
                    foreach (int slaveIndex in slaveIndices)
                    {
                        int statusWord = GetStatusWord(slaveIndex);
                        if ((statusWord & 0x004F) == 0x0040)
                        {
                            hasPhase1 = true;
                            allEnabled = false;
                        }
                    }
                    
                    if (hasPhase1)
                    {
                        Console.WriteLine("批量处理阶段2: 设置控制字0x0006");
                        if (slaveIndices.Length == 48)
                        {
                            // 如果所有从站都需要设置，使用批量写入
                            SetControlWordAll(0x0006);
                        }
                        else
                        {
                            foreach (int slaveIndex in slaveIndices)
                            {
                                int statusWord = GetStatusWord(slaveIndex);
                                if ((statusWord & 0x004F) == 0x0040)
                                {
                                    SetControlWord(slaveIndex, 0x0006);
                                }
                            }
                        }
                        Thread.Sleep(10);
                        continue;
                    }
                    
                    // 第二阶段批量处理：从状态为0x0021的从站
                    bool hasPhase2 = false;
                    foreach (int slaveIndex in slaveIndices)
                    {
                        int statusWord = GetStatusWord(slaveIndex);
                        if ((statusWord & 0x006F) == 0x0021)
                        {
                            hasPhase2 = true;
                            allEnabled = false;
                        }
                    }
                    
                    if (hasPhase2)
                    {
                        Console.WriteLine("批量处理阶段3: 设置控制字0x0007");
                        if (slaveIndices.Length == 48)
                        {
                            // 如果所有从站都需要设置，使用批量写入
                            SetControlWordAll(0x0007);
                        }
                        else
                        {
                            foreach (int slaveIndex in slaveIndices)
                            {
                                int statusWord = GetStatusWord(slaveIndex);
                                if ((statusWord & 0x006F) == 0x0021)
                                {
                                    SetControlWord(slaveIndex, 0x0007);
                                }
                            }
                        }
                        Thread.Sleep(10);
                        continue;
                    }
                    
                    // 第三阶段批量处理：从状态为0x0023的从站
                    bool hasPhase3 = false;
                    foreach (int slaveIndex in slaveIndices)
                    {
                        int statusWord = GetStatusWord(slaveIndex);
                        if ((statusWord & 0x006F) == 0x0023)
                        {
                            hasPhase3 = true;
                            allEnabled = false;
                        }
                    }
                    
                    if (hasPhase3)
                    {
                        Console.WriteLine("批量处理阶段4: 设置控制字0x000F");
                        if (slaveIndices.Length == 48)
                        {
                            // 如果所有从站都需要设置，使用批量写入
                            SetControlWordAll(0x000F);
                        }
                        else
                        {
                            foreach (int slaveIndex in slaveIndices)
                            {
                                int statusWord = GetStatusWord(slaveIndex);
                                if ((statusWord & 0x006F) == 0x0023)
                                {
                                    SetControlWord(slaveIndex, 0x000F);
                                }
                            }
                        }
                        Thread.Sleep(10);
                        continue;
                    }
                    
                    // 检查所有从站是否都已经使能
                    foreach (int slaveIndex in slaveIndices)
                    {
                        int statusWord = GetStatusWord(slaveIndex);
                        if ((statusWord & 0x006F) != 0x0027)
                        {
                            allEnabled = false;
                            break;
                        }
                    }
                    
                    if (!allEnabled)
                    {
                        Thread.Sleep(100);
                        elapsed += 100;
                    }
                }
                
                if (allEnabled)
                {
                    Console.WriteLine("所有从站批量使能成功");
                    
                    // 打印每个从站的最终状态
                    foreach (int slaveIndex in slaveIndices)
                    {
                        int statusWord = GetStatusWord(slaveIndex);
                        Console.WriteLine($"从站{slaveIndex}最终状态字: 0x{statusWord:X4}");
                    }
                }
                else
                {
                    Console.WriteLine("批量使能超时，部分从站可能未完成使能");
                    
                    // 输出每个从站的当前状态，用于诊断
                    foreach (int slaveIndex in slaveIndices)
                    {
                        int statusWord = GetStatusWord(slaveIndex);
                        Console.WriteLine($"从站{slaveIndex}当前状态字: 0x{statusWord:X4}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"批量使能过程出错: {ex.Message}");
            }
        }
        
        // 批量使能所有在线从站
        public void BatchEnableAllOnlineServos()
        {
            List<int> onlineSlaves = new List<int>();
            
            // 检查所有从站是否在线并处于运行状态
            for (int i = 0; i < 48; i++)
            {
                if (GetSlaveOnlineStatus(i) && GetSlaveOperationalStatus(i))
                {
                    onlineSlaves.Add(i);
                }
            }
            
            if (onlineSlaves.Count > 0)
            {
                Console.WriteLine($"发现{onlineSlaves.Count}个在线从站，准备批量使能");
                BatchEnableServo(onlineSlaves.ToArray());
            }
            else
            {
                Console.WriteLine("没有发现在线从站");
            }
        }

        // 批量设置所有从站的控制字
        public void SetControlWordAll(int value)
        {
            UpdateSharedMemory();
            
            // 使用无switch的并行方式
            Parallel.For(0, 48, slaveIndex => 
            {
                // 使用反射获取对应属性并赋值
                typeof(EtherCATSharedMemory).GetField($"shm_slave{slaveIndex}_rx_0x6040_control_word")
                    .SetValue(_sharedMemory, value);
            });
            
            // 一次性写入共享内存
            WriteSharedMemory();
        }

        // 批量设置所有从站的操作模式
        public void SetOperationModeAll(int value)
        {
            UpdateSharedMemory();
            
            // 使用无switch的并行方式
            Parallel.For(0, 48, slaveIndex => 
            {
                // 使用反射获取对应属性并赋值
                typeof(EtherCATSharedMemory).GetField($"shm_slave{slaveIndex}_rx_0x6060_operation_mode")
                    .SetValue(_sharedMemory, value);
            });
            
            // 一次性写入共享内存
            WriteSharedMemory();
        }

        // 批量设置所有从站的目标速度
        public void SetTargetSpeedAll(int value)
        {
            UpdateSharedMemory();
            
            // 使用无switch的并行方式
            Parallel.For(0, 48, slaveIndex => 
            {
                // 使用反射获取对应属性并赋值
                typeof(EtherCATSharedMemory).GetField($"shm_slave{slaveIndex}_rx_0x60ff_target_speed")
                    .SetValue(_sharedMemory, value);
            });
            
            // 一次性写入共享内存
            WriteSharedMemory();
        }
    }

    class Program
    {
        static async Task Main(string[] args)
        {
            try 
            {
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json")
                );

                var requestData = new { programName = $"{Path.GetFileName(Process.GetCurrentProcess().MainModule.FileName)}"};
                var jsonContent = JsonSerializer.Serialize(requestData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(
                    "http://127.0.0.1:3000/api/programs/start-middleware", 
                    content
                );

                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"请求中间件失败: {response.StatusCode}");
                    return;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"中间件返回数据: {responseContent}");

                // 解析JSON响应
                var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);
                if (responseData.TryGetProperty("code", out var codeElement) && codeElement.GetInt32() == 0)
                {
                    if (responseData.TryGetProperty("data", out var dataElement) && dataElement.TryGetProperty("shmFile", out var shmFileElement))
                    {
                        var sharedMemoryFilePath = shmFileElement.GetString();
                        
                        // 添加空值检查
                        if (string.IsNullOrEmpty(sharedMemoryFilePath))
                        {
                            Console.WriteLine("错误：从中间件获取的共享内存路径为空");
                            return;
                        }
                        
                        Console.WriteLine($"启动EtherCAT控制程序，共享内存文件路径: {sharedMemoryFilePath}...");
                        
                        // 使用获取到的路径继续程序
                        using var controller = new EtherCATController(sharedMemoryFilePath);
                        controller.EnterRealtimeMode();

                        var cts = new CancellationTokenSource();
                        Console.CancelKeyPress += (s, e) => {
                            e.Cancel = true;
                            cts.Cancel();
                        };

                        try
                        {
                            // 程序启动后先尝试批量使能所有在线从站
                            Console.WriteLine("启动阶段：检查并批量使能所有在线从站");
                            controller.BatchEnableAllOnlineServos();
                            
                            await controller.RunControlSequence(cts.Token);
                        }
                        catch (OperationCanceledException)
                        {
                            Console.WriteLine("Operation cancelled by user.");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error: {ex.Message}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("错误：中间件响应中缺少data.shmFile字段");
                    }
                }
                else
                {
                    string errorMsg = responseData.TryGetProperty("msg", out var msgElement) 
                        ? msgElement.GetString() 
                        : "未知错误";
                    Console.WriteLine($"错误：中间件返回错误码 {codeElement.GetInt32()}，错误信息：{errorMsg}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Startup error: {ex.Message}");
            }
        }
    }
}