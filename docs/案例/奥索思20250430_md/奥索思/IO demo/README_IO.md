# EtherCAT IO 控制Demo程序文档

| 文档信息 | 描述 |
| --- | --- |
| 文档版本 | V1.0.0 |
| 最后更新 | 2025-04-30 |

| 维护人 | 角色 | 联系方式 |
| --- | --- | --- |
| 何俊言 | 系统开发 | he<PERSON><EMAIL> |
| 唐伟峰 | 系统开发 | <EMAIL> |

## 系统概述

该系统是一个基于EtherCAT总线的IO控制演示程序，通过共享内存机制与EtherCAT主站进程进行通信，实现对数字输入输出端口的监控和控制。系统采用C#语言开发，提供了多种方法用于管理设备通道状态。

## 系统功能概述

- EtherCAT从站在线状态监控
- 从站操作状态监控
- 数字输入/输出端口控制
- LED指示灯控制序列
- 批量通道控制
- 实时控制模式支持
- 异常处理和优雅退出

## 主要组件

### 1. 共享内存结构

系统通过共享内存结构体`EtherCATSharedMemory`与EtherCAT主站进程交互，包含以下主要字段：

- 从站状态：
  - `shm_slave0_online_status`: 从站0在线状态
  - `shm_slave0_operational_status`: 从站0运行状态
  - `shm_slave0_al_state`: 从站0AL状态
  - `shm_slave1_online_status`: 从站1在线状态
  - `shm_slave1_operational_status`: 从站1运行状态
  - `shm_slave1_al_state`: 从站1AL状态

- 输出变量 (RX方向):
  - 从站0: `shm_slave0_rx_0x00007010_q0_0` ~ `shm_slave0_rx_0x00007010_q1_7`
  - 从站1: `shm_slave1_rx_0x00007000_q2_0` ~ `shm_slave1_rx_0x00007000_q5_7`

- 输入变量 (TX方向):
  - 从站0: `shm_slave0_tx_0x00006010_i0_0` ~ `shm_slave0_tx_0x00006010_i1_7`
  - 从站1: `shm_slave1_tx_0x00006000_id` (ID标识)

### 2. 控制接口

`EtherCATController`类提供了以下主要方法：

- `UpdateSharedMemory()`: 从共享内存文件更新本地内存结构
- `WriteSharedMemory()`: 将本地内存结构写入共享内存文件
- `WaitForIOReady()`: 等待从站IO就绪
- `SetDigitalOutput()`: 设置单个数字输出
- `SetDigitalOutputs()`: 批量设置数字输出
- `GetDigitalInput()`: 读取数字输入状态
- `EnterRealtimeMode()`: 进入实时模式
- `RunLedSequence()`: 运行LED控制序列

### 3. 从站结构

系统支持两个从站设备：

- **从站0**：拥有16个输入通道和16个输出通道
- **从站1**：主要提供32个输出通道

## 从站通道映射

### 从站0 (Slave 0)

#### 输入通道 (16个)

| 通道编号 | 变量名 | 功能描述 |
|---------|---------|---------|
| 1 | shm_slave0_tx_0x00006010_i0_0 | 启动按钮 |
| 2 | shm_slave0_tx_0x00006010_i0_1 | 复位按钮 |
| 3 | shm_slave0_tx_0x00006010_i0_2 | 头车光电 |
| 4 | shm_slave0_tx_0x00006010_i0_3 | 测速光电1 |
| 5 | shm_slave0_tx_0x00006010_i0_4 | 测速光电2 |
| 6 | shm_slave0_tx_0x00006010_i0_5 | 拍照光电 |
| 7 | shm_slave0_tx_0x00006010_i0_6 | 上包光电 |
| 8 | shm_slave0_tx_0x00006010_i0_7 | 末端堵包光电 |
| 9 | shm_slave0_tx_0x00006010_i1_0 | 何服报警1 |
| 10 | shm_slave0_tx_0x00006010_i1_1 | 何服报警2 |
| 11 | shm_slave0_tx_0x00006010_i1_2 | 何服报警3 |
| 12 | shm_slave0_tx_0x00006010_i1_3 | 何服报警4 |
| 13 | shm_slave0_tx_0x00006010_i1_4 | 电源报警1 |
| 14 | shm_slave0_tx_0x00006010_i1_5 | 电源报警2 |
| 15 | shm_slave0_tx_0x00006010_i1_6 | 电源报警3 |
| 16 | shm_slave0_tx_0x00006010_i1_7 | 变频器脉冲反馈信号 |

#### 输出通道 (16个)

| 通道编号 | 变量名 | 功能描述 |
|---------|---------|---------|
| 1 | shm_slave0_rx_0x00007010_q0_0 | 相机触发 |
| 2 | shm_slave0_rx_0x00007010_q0_1 | 红灯 |
| 3 | shm_slave0_rx_0x00007010_q0_2 | 绿灯 |
| 4 | shm_slave0_rx_0x00007010_q0_3 | 黄灯 |
| 5 | shm_slave0_rx_0x00007010_q0_4 | 蜂鸣 |
| 6 | shm_slave0_rx_0x00007010_q0_5 | 报警复位 |
| 7 | shm_slave0_rx_0x00007010_q0_6 | 末端堵包蜂鸣 |
| 8 | shm_slave0_rx_0x00007010_q0_7 | 备用1 |
| 9 | shm_slave0_rx_0x00007010_q1_0 | 备用2 |
| 10 | shm_slave0_rx_0x00007010_q1_1 | 备用3 |
| 11 | shm_slave0_rx_0x00007010_q1_2 | 备用4 |
| 12 | shm_slave0_rx_0x00007010_q1_3 | 备用5 |
| 13 | shm_slave0_rx_0x00007010_q1_4 | 备用6 |
| 14 | shm_slave0_rx_0x00007010_q1_5 | 备用7 |
| 15 | shm_slave0_rx_0x00007010_q1_6 | 备用8 |
| 16 | shm_slave0_rx_0x00007010_q1_7 | 备用9 |

### 从站1 (Slave 1)

拥有32个输出通道，主要用于扩展控制能力：

| 通道编号 | 变量名 | 功能描述 |
|---------|---------|---------|
| 1 | shm_slave1_rx_0x00007000_q2_0 | 1DIN1 |
| 2 | shm_slave1_rx_0x00007000_q2_1 | 1DIN2 |
| 3 | shm_slave1_rx_0x00007000_q2_2 | 2DIN1 |
| 4 | shm_slave1_rx_0x00007000_q2_3 | 2DIN2 |
| 5 | shm_slave1_rx_0x00007000_q2_4 | 3DIN1 |
| 6 | shm_slave1_rx_0x00007000_q2_5 | 3DIN2 |
| 7 | shm_slave1_rx_0x00007000_q2_6 | 4DIN1 |
| 8 | shm_slave1_rx_0x00007000_q2_7 | 4DIN2 |
| 9 | shm_slave1_rx_0x00007000_q3_0 | 5DIN1 |
| 10 | shm_slave1_rx_0x00007000_q3_1 | 5DIN2 |
| ... | ... | ... |
| 17 | shm_slave1_rx_0x00007000_q4_0 | 9DIN1 |
| 18 | shm_slave1_rx_0x00007000_q4_1 | 9DIN2 |
| ... | ... | ... |

## 方法说明

### 1. 共享内存操作

#### UpdateSharedMemory
从共享内存文件中读取最新的IO状态到本地内存变量。
```csharp
public void UpdateSharedMemory()
{
    _viewAccessor.Read(0, out _sharedMemory);
}
```

#### WriteSharedMemory
将本地内存变量写入共享内存文件，用于更新输出状态。
```csharp
private void WriteSharedMemory()
{
    _viewAccessor.Write(0, ref _sharedMemory);
}
```

### 2. IO控制方法

#### WaitForIOReady
等待从站IO就绪，检查从站的在线状态和运行状态。
```csharp
public bool WaitForIOReady(CancellationToken cancellationToken = default)
```
- 返回值: 从站IO就绪状态
- 超时: 默认5000毫秒

#### SetDigitalOutput
设置单个数字输出通道的状态。
```csharp
public void SetDigitalOutput(int slaveNumber, int channelNumber, bool value)
```
- `slaveNumber`: 从站编号(0或1)
- `channelNumber`: 通道编号(1-16对于从站0，1-32对于从站1)
- `value`: 通道状态(true/false)

#### SetDigitalOutputs
批量设置一组(8个)数字输出通道的状态。
```csharp
public void SetDigitalOutputs(bool[] outputs, int slaveNumber, int groupNumber)
```
- `outputs`: 布尔数组，长度为8
- `slaveNumber`: 从站编号(0或1)
- `groupNumber`: 组编号(1或2对于从站0，1-4对于从站1)

#### GetDigitalInput
读取单个数字输入通道的状态。
```csharp
public bool GetDigitalInput(int slaveNumber, int channelNumber)
```
- `slaveNumber`: 从站编号(当前仅支持从站0)
- `channelNumber`: 通道编号(1-16)
- 返回值: 通道状态(true/false)

### 3. 控制序列

#### RunLedSequence
运行LED跑马灯控制序列，用于系统测试和演示。
```csharp
public async Task RunLedSequence(CancellationToken cancellationToken)
```
- `cancellationToken`: 用于取消操作的令牌
- 功能: 按照指定的时间间隔循环点亮从站0和从站1的第一组输出通道

### 4. 系统功能

#### EnterRealtimeMode
将程序设置为实时模式，提高IO响应实时性。
```csharp
public void EnterRealtimeMode()
```
- 功能: 使用Linux的SCHED_FIFO调度策略提高程序优先级

## 使用示例

### 基础控制示例

```csharp
// 创建控制器实例
using var controller = new EtherCATController(sharedMemoryFilePath);

// 进入实时模式
controller.EnterRealtimeMode();

// 等待IO就绪
if (controller.WaitForIOReady())
{
    // 设置单个输出通道
    controller.SetDigitalOutput(0, 1, true);  // 打开从站0的通道1(相机触发)
    
    // 读取输入状态
    bool startButtonPressed = controller.GetDigitalInput(0, 1);  // 读取从站0的通道1(启动按钮)
    
    // 批量设置一组输出
    bool[] ledPattern = new bool[8] { true, false, true, false, true, false, true, false };
    controller.SetDigitalOutputs(ledPattern, 0, 1);  // 设置从站0的第一组输出(通道1-8)
}
```

### 运行LED控制序列

```csharp
// 创建取消令牌
var cts = new CancellationTokenSource();

try
{
    // 启动LED控制序列
    await controller.RunLedSequence(cts.Token);
}
catch (OperationCanceledException)
{
    Console.WriteLine("LED控制序列被取消");
}
```

## 程序启动流程

1. 联系中间件服务获取共享内存文件路径
2. 创建EtherCAT控制器
3. 进入实时模式
4. 注册信号处理程序
5. 运行LED控制序列
6. 程序退出时自动清理资源

## 系统优化

1. **日志优化**：
   - 仅在输入状态变化时记录日志
   - 通过字典缓存上一次的输入状态
   - 提供详细的变量名和状态值

2. **状态监控**：
   - 实时监控从站在线和运行状态
   - 超时检测和错误报告
   - 支持线程安全的取消操作

3. **错误处理**：
   - 异常捕获和处理
   - SIGTERM信号处理
   - Ctrl+C取消支持
   - 资源自动释放

## 注意事项

1. 在使用前确保EtherCAT主站程序正在运行
2. 实时模式需要足够的系统权限
3. 共享内存路径由中间件服务提供
4. 程序可以通过Ctrl+C或系统信号优雅退出
5. 退出时会自动关闭所有输出通道

