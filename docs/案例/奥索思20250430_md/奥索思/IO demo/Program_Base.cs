﻿using System;
using System.IO;
using System.IO.MemoryMappedFiles;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace EtherCATControl
{
    // 共享内存结构体 (尝试简化版)
    [StructLayout(LayoutKind.Sequential)]
    public struct EtherCATSharedMemory
    {
        public int shm_slave0_online_status; // 从站0在线状态
        public int shm_slave0_operational_status; // 从站0运行状态
        public int shm_slave0_al_state; // 从站0AL状态

        public int shm_slave0_rx_0x00007000_filter_time;
        public int shm_slave0_rx_0x00007010_q0_0; // 相机触发
        public int shm_slave0_rx_0x00007010_q0_1; // 红灯
        public int shm_slave0_rx_0x00007010_q0_2; // 绿灯
        public int shm_slave0_rx_0x00007010_q0_3; // 黄灯
        public int shm_slave0_rx_0x00007010_q0_4; // 蜂鸣
        public int shm_slave0_rx_0x00007010_q0_5; // 报警复位
        public int shm_slave0_rx_0x00007010_q0_6; // 末端堵包蜂鸣
        public int shm_slave0_rx_0x00007010_q0_7; // 备用1
        public int shm_slave0_rx_0x00007010_q1_0; // 备用2
        public int shm_slave0_rx_0x00007010_q1_1; // 备用3
        public int shm_slave0_rx_0x00007010_q1_2; // 备用4
        public int shm_slave0_rx_0x00007010_q1_3; // 备用5
        public int shm_slave0_rx_0x00007010_q1_4; // 备用6
        public int shm_slave0_rx_0x00007010_q1_5; // 备用7
        public int shm_slave0_rx_0x00007010_q1_6; // 备用8
        public int shm_slave0_rx_0x00007010_q1_7; // 备用9
        public int shm_slave0_tx_0x00006000_id;
        public int shm_slave0_tx_0x00006010_i0_0; // 启动按钮
        public int shm_slave0_tx_0x00006010_i0_1; // 复位按钮
        public int shm_slave0_tx_0x00006010_i0_2; // 头车光电
        public int shm_slave0_tx_0x00006010_i0_3; // 测速光电1
        public int shm_slave0_tx_0x00006010_i0_4; // 测速光电2
        public int shm_slave0_tx_0x00006010_i0_5; // 拍照光电
        public int shm_slave0_tx_0x00006010_i0_6; // 上包光电
        public int shm_slave0_tx_0x00006010_i0_7; // 末端堵包光电
        public int shm_slave0_tx_0x00006010_i1_0; // 何服报警1
        public int shm_slave0_tx_0x00006010_i1_1; // 何服报警2
        public int shm_slave0_tx_0x00006010_i1_2; // 何服报警3
        public int shm_slave0_tx_0x00006010_i1_3; // 何服报警4
        public int shm_slave0_tx_0x00006010_i1_4; // 电源报警1
        public int shm_slave0_tx_0x00006010_i1_5; // 电源报警2
        public int shm_slave0_tx_0x00006010_i1_6; // 电源报警3
        public int shm_slave0_tx_0x00006010_i1_7; // 变频器脉冲反馈信号

        public int shm_slave1_online_status; // 从站1在线状态
        public int shm_slave1_operational_status; // 从站1运行状态
        public int shm_slave1_al_state; // 从站1AL状态

        public int shm_slave1_rx_0x00007000_q2_0; // 1DIN1
        public int shm_slave1_rx_0x00007000_q2_1; // 1DIN2
        public int shm_slave1_rx_0x00007000_q2_2; // 2DIN1
        public int shm_slave1_rx_0x00007000_q2_3; // 2DIN2
        public int shm_slave1_rx_0x00007000_q2_4; // 3DIN1
        public int shm_slave1_rx_0x00007000_q2_5; // 3DIN2
        public int shm_slave1_rx_0x00007000_q2_6; // 4DIN1
        public int shm_slave1_rx_0x00007000_q2_7; // 4DIN2
        public int shm_slave1_rx_0x00007000_q3_0; // 5DIN1
        public int shm_slave1_rx_0x00007000_q3_1; // 5DIN2
        public int shm_slave1_rx_0x00007000_q3_2; // 6DIN1
        public int shm_slave1_rx_0x00007000_q3_3; // 6DIN2
        public int shm_slave1_rx_0x00007000_q3_4; // 7DIN1
        public int shm_slave1_rx_0x00007000_q3_5; // 7DIN2
        public int shm_slave1_rx_0x00007000_q3_6; // 8DIN1
        public int shm_slave1_rx_0x00007000_q3_7; // 8DIN2
        public int shm_slave1_rx_0x00007000_q4_0; // 9DIN1
        public int shm_slave1_rx_0x00007000_q4_1; // 9DIN2
        public int shm_slave1_rx_0x00007000_q4_2;
        public int shm_slave1_rx_0x00007000_q4_3;
        public int shm_slave1_rx_0x00007000_q4_4;
        public int shm_slave1_rx_0x00007000_q4_5;
        public int shm_slave1_rx_0x00007000_q4_6;
        public int shm_slave1_rx_0x00007000_q4_7;
        public int shm_slave1_rx_0x00007000_q5_0;
        public int shm_slave1_rx_0x00007000_q5_1;
        public int shm_slave1_rx_0x00007000_q5_2;
        public int shm_slave1_rx_0x00007000_q5_3;
        public int shm_slave1_rx_0x00007000_q5_4;
        public int shm_slave1_rx_0x00007000_q5_5;
        public int shm_slave1_rx_0x00007000_q5_6;
        public int shm_slave1_rx_0x00007000_q5_7;
        public int shm_slave1_tx_0x00006000_id;
    }

    public class EtherCATController : IDisposable
    {
        private readonly MemoryMappedFile _memoryMappedFile;
        private readonly MemoryMappedViewAccessor _viewAccessor;
        private EtherCATSharedMemory _sharedMemory;
        private string lastStatus = string.Empty; // 添加lastStatus字段
        // 添加输入状态缓存
        private Dictionary<string, bool> _lastInputStatus = new Dictionary<string, bool>();

        // Linux RT 相关定义
        private enum SchedPolicy
        {
            SCHED_FIFO = 1
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct SchedParam
        {
            public int sched_priority;
        }

        [DllImport("libc", EntryPoint = "sched_setscheduler")]
        private static extern int sched_setscheduler(int pid, int policy, ref SchedParam param);

        [DllImport("libc", EntryPoint = "sched_get_priority_max")]
        private static extern int sched_get_priority_max(int policy);

        public EtherCATController(string EtherCATSharedMemoryFilePath)
        {
            _memoryMappedFile = MemoryMappedFile.CreateFromFile(EtherCATSharedMemoryFilePath, FileMode.Open);
            _viewAccessor = _memoryMappedFile.CreateViewAccessor();
            _sharedMemory = new EtherCATSharedMemory();
        }

        public void UpdateSharedMemory()
        {
            _viewAccessor.Read(0, out _sharedMemory);
        }

        private void WriteSharedMemory()
        {
            _viewAccessor.Write(0, ref _sharedMemory);
        }

        public bool WaitForIOReady(CancellationToken cancellationToken = default)
        {
            Console.WriteLine("等待从站IO就绪...");
            int timeoutMs = 5000; // 5秒超时
            int elapsed = 0;
            
            while (!cancellationToken.IsCancellationRequested && elapsed < timeoutMs)
            {
                UpdateSharedMemory();
                
                // 检查两个从站的状态
                if (_sharedMemory.shm_slave0_online_status == 1 && 
                    _sharedMemory.shm_slave0_operational_status == 1 &&
                    _sharedMemory.shm_slave1_online_status == 1 && 
                    _sharedMemory.shm_slave1_operational_status == 1)
                {
                    Console.WriteLine("所有从站IO已就绪");
                    return true;
                }
                
                Thread.Sleep(100);
                elapsed += 100;
            }
            
            if (elapsed >= timeoutMs)
            {
                Console.WriteLine("等待从站IO就绪超时");
                Console.WriteLine("从站0状态:");
                Console.WriteLine($"在线状态: {_sharedMemory.shm_slave0_online_status}");
                Console.WriteLine($"运行状态: {_sharedMemory.shm_slave0_operational_status}");
                Console.WriteLine($"AL状态: {_sharedMemory.shm_slave0_al_state}");
                Console.WriteLine("从站1状态:");
                Console.WriteLine($"在线状态: {_sharedMemory.shm_slave1_online_status}");
                Console.WriteLine($"运行状态: {_sharedMemory.shm_slave1_operational_status}");
                Console.WriteLine($"AL状态: {_sharedMemory.shm_slave1_al_state}");
            }
            
            return false;
        }

        public void SetDigitalOutput(int slaveNumber, int channelNumber, bool value)
        {
            int groupNumber = (channelNumber - 1) / 8 + 1;
            int bitPosition = (channelNumber - 1) % 8;
            
            // 用于记录变量名的字符串
            string variableName = "未知";
            
            if (slaveNumber == 0)
            {
                // 设置从站0的通道
                switch(groupNumber)
                {
                    case 1: // 第一组 (CH1-8)
                        if (bitPosition == 0) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_0 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_0";
                        }
                        else if (bitPosition == 1) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_1 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_1";
                        }
                        else if (bitPosition == 2) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_2 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_2";
                        }
                        else if (bitPosition == 3) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_3 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_3";
                        }
                        else if (bitPosition == 4) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_4 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_4";
                        }
                        else if (bitPosition == 5) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_5 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_5";
                        }
                        else if (bitPosition == 6) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_6 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_6";
                        }
                        else if (bitPosition == 7) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_7 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_7";
                        }
                        break;
                    case 2: // 第二组 (CH9-16)
                        if (bitPosition == 0) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_0 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_0";
                        }
                        else if (bitPosition == 1) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_1 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_1";
                        }
                        else if (bitPosition == 2) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_2 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_2";
                        }
                        else if (bitPosition == 3) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_3 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_3";
                        }
                        else if (bitPosition == 4) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_4 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_4";
                        }
                        else if (bitPosition == 5) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_5 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_5";
                        }
                        else if (bitPosition == 6) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_6 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_6";
                        }
                        else if (bitPosition == 7) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_7 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_7";
                        }
                        break;
                }
            }
            else if (slaveNumber == 1)
            {
                // 设置从站1的通道
                switch(groupNumber)
                {
                    case 1: // 第一组 (CH1-8)
                        if (bitPosition == 0) _sharedMemory.shm_slave1_rx_0x00007000_q2_0 = value ? 1 : 0;
                        else if (bitPosition == 1) _sharedMemory.shm_slave1_rx_0x00007000_q2_1 = value ? 1 : 0;
                        else if (bitPosition == 2) _sharedMemory.shm_slave1_rx_0x00007000_q2_2 = value ? 1 : 0;
                        else if (bitPosition == 3) _sharedMemory.shm_slave1_rx_0x00007000_q2_3 = value ? 1 : 0;
                        else if (bitPosition == 4) _sharedMemory.shm_slave1_rx_0x00007000_q2_4 = value ? 1 : 0;
                        else if (bitPosition == 5) _sharedMemory.shm_slave1_rx_0x00007000_q2_5 = value ? 1 : 0;
                        else if (bitPosition == 6) _sharedMemory.shm_slave1_rx_0x00007000_q2_6 = value ? 1 : 0;
                        else if (bitPosition == 7) _sharedMemory.shm_slave1_rx_0x00007000_q2_7 = value ? 1 : 0;
                        break;
                    case 2: // 第二组 (CH9-16)
                        if (bitPosition == 0) _sharedMemory.shm_slave1_rx_0x00007000_q3_0 = value ? 1 : 0;
                        else if (bitPosition == 1) _sharedMemory.shm_slave1_rx_0x00007000_q3_1 = value ? 1 : 0;
                        else if (bitPosition == 2) _sharedMemory.shm_slave1_rx_0x00007000_q3_2 = value ? 1 : 0;
                        else if (bitPosition == 3) _sharedMemory.shm_slave1_rx_0x00007000_q3_3 = value ? 1 : 0;
                        else if (bitPosition == 4) _sharedMemory.shm_slave1_rx_0x00007000_q3_4 = value ? 1 : 0;
                        else if (bitPosition == 5) _sharedMemory.shm_slave1_rx_0x00007000_q3_5 = value ? 1 : 0;
                        else if (bitPosition == 6) _sharedMemory.shm_slave1_rx_0x00007000_q3_6 = value ? 1 : 0;
                        else if (bitPosition == 7) _sharedMemory.shm_slave1_rx_0x00007000_q3_7 = value ? 1 : 0;
                        break;
                    case 3: // 第三组 (CH17-24)
                        if (bitPosition == 0) _sharedMemory.shm_slave1_rx_0x00007000_q4_0 = value ? 1 : 0;
                        else if (bitPosition == 1) _sharedMemory.shm_slave1_rx_0x00007000_q4_1 = value ? 1 : 0;
                        else if (bitPosition == 2) _sharedMemory.shm_slave1_rx_0x00007000_q4_2 = value ? 1 : 0;
                        else if (bitPosition == 3) _sharedMemory.shm_slave1_rx_0x00007000_q4_3 = value ? 1 : 0;
                        else if (bitPosition == 4) _sharedMemory.shm_slave1_rx_0x00007000_q4_4 = value ? 1 : 0;
                        else if (bitPosition == 5) _sharedMemory.shm_slave1_rx_0x00007000_q4_5 = value ? 1 : 0;
                        else if (bitPosition == 6) _sharedMemory.shm_slave1_rx_0x00007000_q4_6 = value ? 1 : 0;
                        else if (bitPosition == 7) _sharedMemory.shm_slave1_rx_0x00007000_q4_7 = value ? 1 : 0;
                        break;
                    case 4: // 第四组 (CH25-32)
                        if (bitPosition == 0) _sharedMemory.shm_slave1_rx_0x00007000_q5_0 = value ? 1 : 0;
                        else if (bitPosition == 1) _sharedMemory.shm_slave1_rx_0x00007000_q5_1 = value ? 1 : 0;
                        else if (bitPosition == 2) _sharedMemory.shm_slave1_rx_0x00007000_q5_2 = value ? 1 : 0;
                        else if (bitPosition == 3) _sharedMemory.shm_slave1_rx_0x00007000_q5_3 = value ? 1 : 0;
                        else if (bitPosition == 4) _sharedMemory.shm_slave1_rx_0x00007000_q5_4 = value ? 1 : 0;
                        else if (bitPosition == 5) _sharedMemory.shm_slave1_rx_0x00007000_q5_5 = value ? 1 : 0;
                        else if (bitPosition == 6) _sharedMemory.shm_slave1_rx_0x00007000_q5_6 = value ? 1 : 0;
                        else if (bitPosition == 7) _sharedMemory.shm_slave1_rx_0x00007000_q5_7 = value ? 1 : 0;
                        break;
                }
            }
            
            WriteSharedMemory();

            // 打印当前状态和实际访问的变量
            Console.WriteLine($"设置从站{slaveNumber}通道{channelNumber}={value}，访问的变量：{variableName}");
        }

        public void SetDigitalOutputs(bool[] outputs, int slaveNumber, int groupNumber)
        {
            if (outputs.Length != 8)
            {
                throw new ArgumentException("输出数组长度必须为8");
            }
            
            int baseChannelNumber = (groupNumber - 1) * 8 + 1;
            for (int i = 0; i < 8; i++)
            {
                SetDigitalOutput(slaveNumber, baseChannelNumber + i, outputs[i]);
            }
        }

        public void EnterRealtimeMode()
        {
            var param = new SchedParam { sched_priority = sched_get_priority_max((int)SchedPolicy.SCHED_FIFO) };
            if (sched_setscheduler(0, (int)SchedPolicy.SCHED_FIFO, ref param) != 0)
            {
                Console.WriteLine("Warning: Failed to set SCHED_FIFO scheduler");
            }
        }

        public bool GetDigitalInput(int slaveNumber, int channelNumber)
        {
            string variableName = "未知";
            bool result = false;
            
            if (slaveNumber == 0)
            {
                // 从站0的输入通道
                switch (channelNumber)
                {
                    case 1:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_0 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_0";
                        break;
                    case 2:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_1 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_1";
                        break;
                    case 3:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_2 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_2";
                        break;
                    case 4:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_3 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_3";
                        break;
                    case 5:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_4 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_4";
                        break;
                    case 6:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_5 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_5";
                        break;
                    case 7:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_6 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_6";
                        break;
                    case 8:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_7 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_7";
                        break;
                    case 9:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_0 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_0";
                        break;
                    case 10:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_1 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_1";
                        break;
                    case 11:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_2 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_2";
                        break;
                    case 12:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_3 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_3";
                        break;
                    case 13:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_4 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_4";
                        break;
                    case 14:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_5 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_5";
                        break;
                    case 15:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_6 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_6";
                        break;
                    case 16:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_7 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_7";
                        break;
                    default:
                        result = false;
                        break;
                }
                
                // 创建唯一标识键
                string cacheKey = $"slave{slaveNumber}_ch{channelNumber}";
                
                // 检查是否有之前的状态记录
                if (!_lastInputStatus.ContainsKey(cacheKey))
                {
                    // 第一次读取，记录并打印
                    _lastInputStatus[cacheKey] = result;
                    Console.WriteLine($"首次读取从站{slaveNumber}通道{channelNumber}={result}，访问的变量：{variableName}，值={(_sharedMemory.GetType().GetField(variableName)?.GetValue(_sharedMemory) ?? "未知")}");
                }
                else if (_lastInputStatus[cacheKey] != result)
                {
                    // 状态发生变化，更新记录并打印
                    _lastInputStatus[cacheKey] = result;
                    Console.WriteLine($"输入状态变化：从站{slaveNumber}通道{channelNumber}={result}，访问的变量：{variableName}，值={(_sharedMemory.GetType().GetField(variableName)?.GetValue(_sharedMemory) ?? "未知")}");
                }
                // 如果状态没变，不打印任何信息
            }
            
            return result;
        }

        public void Dispose()
        {
            _viewAccessor?.Dispose();
            _memoryMappedFile?.Dispose();
        }
    }

    class Program
    {
        static async Task Main(string[] args)
        {
            // 打印通道映射关系
            Console.WriteLine("\n=== 通道与共享内存变量的映射关系 ===");
            Console.WriteLine("\n从站0(Slave 0) - 输入通道:");
            Console.WriteLine("CH1 -> shm_slave0_tx_0x00006010_i0_0 (启动按钮)");
            Console.WriteLine("CH2 -> shm_slave0_tx_0x00006010_i0_1 (复位按钮)");
            Console.WriteLine("CH3 -> shm_slave0_tx_0x00006010_i0_2 (头车光电)");
            Console.WriteLine("CH4 -> shm_slave0_tx_0x00006010_i0_3 (测速光电1)");
            Console.WriteLine("CH5 -> shm_slave0_tx_0x00006010_i0_4 (测速光电2)");
            Console.WriteLine("CH6 -> shm_slave0_tx_0x00006010_i0_5 (拍照光电)");
            Console.WriteLine("CH7 -> shm_slave0_tx_0x00006010_i0_6 (上包光电)");
            Console.WriteLine("CH8 -> shm_slave0_tx_0x00006010_i0_7 (末端堵包光电)");
            Console.WriteLine("CH9 -> shm_slave0_tx_0x00006010_i1_0 (何服报警1)");
            Console.WriteLine("CH10 -> shm_slave0_tx_0x00006010_i1_1 (何服报警2)");
            Console.WriteLine("CH11 -> shm_slave0_tx_0x00006010_i1_2 (何服报警3)");
            Console.WriteLine("CH12 -> shm_slave0_tx_0x00006010_i1_3 (何服报警4)");
            Console.WriteLine("CH13 -> shm_slave0_tx_0x00006010_i1_4 (电源报警1)");
            Console.WriteLine("CH14 -> shm_slave0_tx_0x00006010_i1_5 (电源报警2)");
            Console.WriteLine("CH15 -> shm_slave0_tx_0x00006010_i1_6 (电源报警3)");
            Console.WriteLine("CH16 -> shm_slave0_tx_0x00006010_i1_7 (变频器脉冲反馈信号)");
            
            Console.WriteLine("\n从站0(Slave 0) - 输出通道:");
            Console.WriteLine("CH1 -> shm_slave0_rx_0x00007010_q0_0 (相机触发)");
            Console.WriteLine("CH2 -> shm_slave0_rx_0x00007010_q0_1 (红灯)");
            Console.WriteLine("CH3 -> shm_slave0_rx_0x00007010_q0_2 (绿灯)");
            Console.WriteLine("CH4 -> shm_slave0_rx_0x00007010_q0_3 (黄灯)");
            Console.WriteLine("CH5 -> shm_slave0_rx_0x00007010_q0_4 (蜂鸣)");
            Console.WriteLine("CH6 -> shm_slave0_rx_0x00007010_q0_5 (报警复位)");
            Console.WriteLine("CH7 -> shm_slave0_rx_0x00007010_q0_6 (末端堵包蜂鸣)");
            Console.WriteLine("CH8 -> shm_slave0_rx_0x00007010_q0_7 (备用1)");
            Console.WriteLine("CH9 -> shm_slave0_rx_0x00007010_q1_0 (备用2)");
            Console.WriteLine("CH10 -> shm_slave0_rx_0x00007010_q1_1 (备用3)");
            Console.WriteLine("CH11 -> shm_slave0_rx_0x00007010_q1_2 (备用4)");
            Console.WriteLine("CH12 -> shm_slave0_rx_0x00007010_q1_3 (备用5)");
            Console.WriteLine("CH13 -> shm_slave0_rx_0x00007010_q1_4 (备用6)");
            Console.WriteLine("CH14 -> shm_slave0_rx_0x00007010_q1_5 (备用7)");
            Console.WriteLine("CH15 -> shm_slave0_rx_0x00007010_q1_6 (备用8)");
            Console.WriteLine("CH16 -> shm_slave0_rx_0x00007010_q1_7 (备用9)");
            
            Console.WriteLine("\n从站1(Slave 1) - 输出通道:");
            for (int i = 1; i <= 32; i++)
            {
                string channelDesc = i <= 2 ? $"1DIN{i}" :
                                   i <= 4 ? $"2DIN{i-2}" :
                                   i <= 6 ? $"3DIN{i-4}" :
                                   i <= 8 ? $"4DIN{i-6}" :
                                   i <= 10 ? $"5DIN{i-8}" :
                                   i <= 12 ? $"6DIN{i-10}" :
                                   i <= 14 ? $"7DIN{i-12}" :
                                   i <= 16 ? $"8DIN{i-14}" :
                                   i <= 18 ? $"9DIN{i-16}" :
                                   i <= 20 ? $"10DIN{i-18}" :
                                   i <= 22 ? $"11DIN{i-20}" :
                                   i <= 24 ? $"12DIN{i-22}" :
                                   i <= 26 ? $"13DIN{i-24}" :
                                   i <= 28 ? $"14DIN{i-26}" :
                                   i <= 30 ? $"15DIN{i-28}" : $"16DIN{i-30}";
                
                // 根据索引计算q值和bit位
                int groupNum = (i - 1) / 8 + 2; // q2到q5
                int bitPos = (i - 1) % 8;
                Console.WriteLine($"CH{i} -> shm_slave1_rx_0x00007000_q{groupNum}_{bitPos} ({channelDesc})");
            }
            Console.WriteLine("=============================\n");
            
            try 
            {
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json")
                );

                var requestData = new { programName = $"{Path.GetFileName(Process.GetCurrentProcess().MainModule.FileName)}" };
                var jsonContent = JsonSerializer.Serialize(requestData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(
                    "http://127.0.0.1:3000/api/programs/start-middleware", 
                    content
                );

                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Failed to get shared memory path: {response.StatusCode}");
                    return;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"中间件返回数据: {responseContent}");

                // 解析JSON响应
                var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);
                if (responseData.TryGetProperty("code", out var codeElement) && codeElement.GetInt32() == 0)
                {
                    if (responseData.TryGetProperty("data", out var dataElement) && dataElement.TryGetProperty("shmFile", out var shmFileElement))
                    {
                        var sharedMemoryFilePath = shmFileElement.GetString();
                        
                        // 添加空值检查
                        if (string.IsNullOrEmpty(sharedMemoryFilePath))
                        {
                            Console.WriteLine("错误：从中间件获取的共享内存路径为空");
                            return;
                        }
                        
                        Console.WriteLine($"启动EtherCAT控制程序，共享内存文件路径: {sharedMemoryFilePath}...");
                        
                        using var controller = new EtherCATController(sharedMemoryFilePath);
                        controller.EnterRealtimeMode();

                        var cts = new CancellationTokenSource();

                        // 添加SIGTERM信号处理
                        AppDomain.CurrentDomain.ProcessExit += (s, e) => {
                            Console.WriteLine("收到SIGTERM信号，正在优雅退出...");
                            cts.Cancel();
                        };

                        Console.CancelKeyPress += (s, e) => {
                            e.Cancel = true;
                            cts.Cancel();
                        };

                        try
                        {
                            await Task.Delay(-1, cts.Token); // 保持程序运行，直到取消信号
                        }
                        catch (OperationCanceledException)
                        {
                            Console.WriteLine("程序正在停止...");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error: {ex.Message}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("错误：中间件响应中缺少data.shmFile字段");
                    }
                }
                else
                {
                    string errorMsg = responseData.TryGetProperty("msg", out var msgElement) 
                        ? msgElement.GetString() 
                        : "未知错误";
                    Console.WriteLine($"错误：中间件返回错误码 {codeElement.GetInt32()}，错误信息：{errorMsg}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Startup error: {ex.Message}");
            }
        }
    }
}