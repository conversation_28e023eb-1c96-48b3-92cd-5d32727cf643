﻿using System;
using System.IO;
using System.IO.MemoryMappedFiles;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Net;
using System.Net.Sockets;
using System.Collections.Generic;

namespace EtherCATControl
{
    // 共享内存结构体 (尝试简化版)
    [StructLayout(LayoutKind.Sequential)]
    public struct EtherCATSharedMemory
    {
        public int shm_slave0_online_status; // 从站0在线状态
        public int shm_slave0_operational_status; // 从站0运行状态
        public int shm_slave0_al_state; // 从站0AL状态

        public int shm_slave0_rx_0x00007000_filter_time;
        public int shm_slave0_rx_0x00007010_q0_0; // 相机触发
        public int shm_slave0_rx_0x00007010_q0_1; // 红灯
        public int shm_slave0_rx_0x00007010_q0_2; // 绿灯
        public int shm_slave0_rx_0x00007010_q0_3; // 黄灯
        public int shm_slave0_rx_0x00007010_q0_4; // 蜂鸣
        public int shm_slave0_rx_0x00007010_q0_5; // 报警复位
        public int shm_slave0_rx_0x00007010_q0_6; // 末端堵包蜂鸣
        public int shm_slave0_rx_0x00007010_q0_7; // 备用1
        public int shm_slave0_rx_0x00007010_q1_0; // 备用2
        public int shm_slave0_rx_0x00007010_q1_1; // 备用3
        public int shm_slave0_rx_0x00007010_q1_2; // 备用4
        public int shm_slave0_rx_0x00007010_q1_3; // 备用5
        public int shm_slave0_rx_0x00007010_q1_4; // 备用6
        public int shm_slave0_rx_0x00007010_q1_5; // 备用7
        public int shm_slave0_rx_0x00007010_q1_6; // 备用8
        public int shm_slave0_rx_0x00007010_q1_7; // 备用9
        public int shm_slave0_tx_0x00006000_id;
        public int shm_slave0_tx_0x00006010_i0_0; // 启动按钮
        public int shm_slave0_tx_0x00006010_i0_1; // 复位按钮
        public int shm_slave0_tx_0x00006010_i0_2; // 头车光电
        public int shm_slave0_tx_0x00006010_i0_3; // 测速光电1
        public int shm_slave0_tx_0x00006010_i0_4; // 测速光电2
        public int shm_slave0_tx_0x00006010_i0_5; // 拍照光电
        public int shm_slave0_tx_0x00006010_i0_6; // 上包光电
        public int shm_slave0_tx_0x00006010_i0_7; // 末端堵包光电
        public int shm_slave0_tx_0x00006010_i1_0; // 何服报警1
        public int shm_slave0_tx_0x00006010_i1_1; // 何服报警2
        public int shm_slave0_tx_0x00006010_i1_2; // 何服报警3
        public int shm_slave0_tx_0x00006010_i1_3; // 何服报警4
        public int shm_slave0_tx_0x00006010_i1_4; // 电源报警1
        public int shm_slave0_tx_0x00006010_i1_5; // 电源报警2
        public int shm_slave0_tx_0x00006010_i1_6; // 电源报警3
        public int shm_slave0_tx_0x00006010_i1_7; // 变频器脉冲反馈信号

        public int shm_slave1_online_status; // 从站1在线状态
        public int shm_slave1_operational_status; // 从站1运行状态
        public int shm_slave1_al_state; // 从站1AL状态

        public int shm_slave1_rx_0x00007000_q2_0; // 1DIN1
        public int shm_slave1_rx_0x00007000_q2_1; // 1DIN2
        public int shm_slave1_rx_0x00007000_q2_2; // 2DIN1
        public int shm_slave1_rx_0x00007000_q2_3; // 2DIN2
        public int shm_slave1_rx_0x00007000_q2_4; // 3DIN1
        public int shm_slave1_rx_0x00007000_q2_5; // 3DIN2
        public int shm_slave1_rx_0x00007000_q2_6; // 4DIN1
        public int shm_slave1_rx_0x00007000_q2_7; // 4DIN2
        public int shm_slave1_rx_0x00007000_q3_0; // 5DIN1
        public int shm_slave1_rx_0x00007000_q3_1; // 5DIN2
        public int shm_slave1_rx_0x00007000_q3_2; // 6DIN1
        public int shm_slave1_rx_0x00007000_q3_3; // 6DIN2
        public int shm_slave1_rx_0x00007000_q3_4; // 7DIN1
        public int shm_slave1_rx_0x00007000_q3_5; // 7DIN2
        public int shm_slave1_rx_0x00007000_q3_6; // 8DIN1
        public int shm_slave1_rx_0x00007000_q3_7; // 8DIN2
        public int shm_slave1_rx_0x00007000_q4_0; // 9DIN1
        public int shm_slave1_rx_0x00007000_q4_1; // 9DIN2
        public int shm_slave1_rx_0x00007000_q4_2;
        public int shm_slave1_rx_0x00007000_q4_3;
        public int shm_slave1_rx_0x00007000_q4_4;
        public int shm_slave1_rx_0x00007000_q4_5;
        public int shm_slave1_rx_0x00007000_q4_6;
        public int shm_slave1_rx_0x00007000_q4_7;
        public int shm_slave1_rx_0x00007000_q5_0;
        public int shm_slave1_rx_0x00007000_q5_1;
        public int shm_slave1_rx_0x00007000_q5_2;
        public int shm_slave1_rx_0x00007000_q5_3;
        public int shm_slave1_rx_0x00007000_q5_4;
        public int shm_slave1_rx_0x00007000_q5_5;
        public int shm_slave1_rx_0x00007000_q5_6;
        public int shm_slave1_rx_0x00007000_q5_7;
        public int shm_slave1_tx_0x00006000_id;
    }

    public class EtherCATController : IDisposable
    {
        private readonly MemoryMappedFile _memoryMappedFile;
        private readonly MemoryMappedViewAccessor _viewAccessor;
        private EtherCATSharedMemory _sharedMemory;
        private string lastStatus = string.Empty; // 添加lastStatus字段
        // 添加输入状态缓存
        private Dictionary<string, bool> _lastInputStatus = new Dictionary<string, bool>();

        // Linux RT 相关定义
        private enum SchedPolicy
        {
            SCHED_FIFO = 1
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct SchedParam
        {
            public int sched_priority;
        }

        [DllImport("libc", EntryPoint = "sched_setscheduler")]
        private static extern int sched_setscheduler(int pid, int policy, ref SchedParam param);

        [DllImport("libc", EntryPoint = "sched_get_priority_max")]
        private static extern int sched_get_priority_max(int policy);

        public EtherCATController(string EtherCATSharedMemoryFilePath)
        {
            _memoryMappedFile = MemoryMappedFile.CreateFromFile(EtherCATSharedMemoryFilePath, FileMode.Open);
            _viewAccessor = _memoryMappedFile.CreateViewAccessor();
            _sharedMemory = new EtherCATSharedMemory();
        }

        public void UpdateSharedMemory()
        {
            _viewAccessor.Read(0, out _sharedMemory);
        }

        private void WriteSharedMemory()
        {
            _viewAccessor.Write(0, ref _sharedMemory);
        }

        public bool WaitForIOReady(CancellationToken cancellationToken = default)
        {
            Console.WriteLine("等待从站IO就绪...");
            int timeoutMs = 5000; // 5秒超时
            int elapsed = 0;
            
            while (!cancellationToken.IsCancellationRequested && elapsed < timeoutMs)
            {
                UpdateSharedMemory();
                
                // 检查两个从站的状态
                if (_sharedMemory.shm_slave0_online_status == 1 && 
                    _sharedMemory.shm_slave0_operational_status == 1 &&
                    _sharedMemory.shm_slave1_online_status == 1 && 
                    _sharedMemory.shm_slave1_operational_status == 1)
                {
                    Console.WriteLine("所有从站IO已就绪");
                    return true;
                }
                
                Thread.Sleep(100);
                elapsed += 100;
            }
            
            if (elapsed >= timeoutMs)
            {
                Console.WriteLine("等待从站IO就绪超时");
                Console.WriteLine("从站0状态:");
                Console.WriteLine($"在线状态: {_sharedMemory.shm_slave0_online_status}");
                Console.WriteLine($"运行状态: {_sharedMemory.shm_slave0_operational_status}");
                Console.WriteLine($"AL状态: {_sharedMemory.shm_slave0_al_state}");
                Console.WriteLine("从站1状态:");
                Console.WriteLine($"在线状态: {_sharedMemory.shm_slave1_online_status}");
                Console.WriteLine($"运行状态: {_sharedMemory.shm_slave1_operational_status}");
                Console.WriteLine($"AL状态: {_sharedMemory.shm_slave1_al_state}");
            }
            
            return false;
        }

        public void SetDigitalOutput(int slaveNumber, int channelNumber, bool value)
        {
            int groupNumber = (channelNumber - 1) / 8 + 1;
            int bitPosition = (channelNumber - 1) % 8;
            
            // 用于记录变量名的字符串
            string variableName = "未知";
            
            // 不建议用反射性能差距很大：
            // string fieldName = slaveNumber == 0
            //     ? (groupNumber == 1 ? $"shm_slave0_rx_0x00007010_q0_{bitPosition}" : $"shm_slave0_rx_0x00007010_q1_{bitPosition}")
            //     : $"shm_slave1_rx_0x00007000_q{groupNumber+1}_{bitPosition}";
            // var field = typeof(EtherCATSharedMemory).GetField(fieldName);
            // if (field != null) field.SetValueDirect(__makeref(_sharedMemory), value ? 1 : 0);
            
            if (slaveNumber == 0)
            {
                // 设置从站0的通道
                switch(groupNumber)
                {
                    case 1: // 第一组 (CH1-8)
                        if (bitPosition == 0) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_0 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_0";
                        }
                        else if (bitPosition == 1) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_1 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_1";
                        }
                        else if (bitPosition == 2) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_2 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_2";
                        }
                        else if (bitPosition == 3) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_3 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_3";
                        }
                        else if (bitPosition == 4) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_4 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_4";
                        }
                        else if (bitPosition == 5) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_5 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_5";
                        }
                        else if (bitPosition == 6) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_6 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_6";
                        }
                        else if (bitPosition == 7) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q0_7 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q0_7";
                        }
                        break;
                    case 2: // 第二组 (CH9-16)
                        if (bitPosition == 0) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_0 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_0";
                        }
                        else if (bitPosition == 1) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_1 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_1";
                        }
                        else if (bitPosition == 2) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_2 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_2";
                        }
                        else if (bitPosition == 3) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_3 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_3";
                        }
                        else if (bitPosition == 4) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_4 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_4";
                        }
                        else if (bitPosition == 5) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_5 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_5";
                        }
                        else if (bitPosition == 6) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_6 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_6";
                        }
                        else if (bitPosition == 7) {
                            _sharedMemory.shm_slave0_rx_0x00007010_q1_7 = value ? 1 : 0;
                            variableName = "shm_slave0_rx_0x00007010_q1_7";
                        }
                        break;
                }
            }
            else if (slaveNumber == 1)
            {
                // 设置从站1的通道
                switch(groupNumber)
                {
                    case 1: // 第一组 (CH1-8)
                        if (bitPosition == 0) _sharedMemory.shm_slave1_rx_0x00007000_q2_0 = value ? 1 : 0;
                        else if (bitPosition == 1) _sharedMemory.shm_slave1_rx_0x00007000_q2_1 = value ? 1 : 0;
                        else if (bitPosition == 2) _sharedMemory.shm_slave1_rx_0x00007000_q2_2 = value ? 1 : 0;
                        else if (bitPosition == 3) _sharedMemory.shm_slave1_rx_0x00007000_q2_3 = value ? 1 : 0;
                        else if (bitPosition == 4) _sharedMemory.shm_slave1_rx_0x00007000_q2_4 = value ? 1 : 0;
                        else if (bitPosition == 5) _sharedMemory.shm_slave1_rx_0x00007000_q2_5 = value ? 1 : 0;
                        else if (bitPosition == 6) _sharedMemory.shm_slave1_rx_0x00007000_q2_6 = value ? 1 : 0;
                        else if (bitPosition == 7) _sharedMemory.shm_slave1_rx_0x00007000_q2_7 = value ? 1 : 0;
                        break;
                    case 2: // 第二组 (CH9-16)
                        if (bitPosition == 0) _sharedMemory.shm_slave1_rx_0x00007000_q3_0 = value ? 1 : 0;
                        else if (bitPosition == 1) _sharedMemory.shm_slave1_rx_0x00007000_q3_1 = value ? 1 : 0;
                        else if (bitPosition == 2) _sharedMemory.shm_slave1_rx_0x00007000_q3_2 = value ? 1 : 0;
                        else if (bitPosition == 3) _sharedMemory.shm_slave1_rx_0x00007000_q3_3 = value ? 1 : 0;
                        else if (bitPosition == 4) _sharedMemory.shm_slave1_rx_0x00007000_q3_4 = value ? 1 : 0;
                        else if (bitPosition == 5) _sharedMemory.shm_slave1_rx_0x00007000_q3_5 = value ? 1 : 0;
                        else if (bitPosition == 6) _sharedMemory.shm_slave1_rx_0x00007000_q3_6 = value ? 1 : 0;
                        else if (bitPosition == 7) _sharedMemory.shm_slave1_rx_0x00007000_q3_7 = value ? 1 : 0;
                        break;
                    case 3: // 第三组 (CH17-24)
                        if (bitPosition == 0) _sharedMemory.shm_slave1_rx_0x00007000_q4_0 = value ? 1 : 0;
                        else if (bitPosition == 1) _sharedMemory.shm_slave1_rx_0x00007000_q4_1 = value ? 1 : 0;
                        else if (bitPosition == 2) _sharedMemory.shm_slave1_rx_0x00007000_q4_2 = value ? 1 : 0;
                        else if (bitPosition == 3) _sharedMemory.shm_slave1_rx_0x00007000_q4_3 = value ? 1 : 0;
                        else if (bitPosition == 4) _sharedMemory.shm_slave1_rx_0x00007000_q4_4 = value ? 1 : 0;
                        else if (bitPosition == 5) _sharedMemory.shm_slave1_rx_0x00007000_q4_5 = value ? 1 : 0;
                        else if (bitPosition == 6) _sharedMemory.shm_slave1_rx_0x00007000_q4_6 = value ? 1 : 0;
                        else if (bitPosition == 7) _sharedMemory.shm_slave1_rx_0x00007000_q4_7 = value ? 1 : 0;
                        break;
                    case 4: // 第四组 (CH25-32)
                        if (bitPosition == 0) _sharedMemory.shm_slave1_rx_0x00007000_q5_0 = value ? 1 : 0;
                        else if (bitPosition == 1) _sharedMemory.shm_slave1_rx_0x00007000_q5_1 = value ? 1 : 0;
                        else if (bitPosition == 2) _sharedMemory.shm_slave1_rx_0x00007000_q5_2 = value ? 1 : 0;
                        else if (bitPosition == 3) _sharedMemory.shm_slave1_rx_0x00007000_q5_3 = value ? 1 : 0;
                        else if (bitPosition == 4) _sharedMemory.shm_slave1_rx_0x00007000_q5_4 = value ? 1 : 0;
                        else if (bitPosition == 5) _sharedMemory.shm_slave1_rx_0x00007000_q5_5 = value ? 1 : 0;
                        else if (bitPosition == 6) _sharedMemory.shm_slave1_rx_0x00007000_q5_6 = value ? 1 : 0;
                        else if (bitPosition == 7) _sharedMemory.shm_slave1_rx_0x00007000_q5_7 = value ? 1 : 0;
                        break;
                }
            }
            
            WriteSharedMemory();

            // 打印当前状态和实际访问的变量
            Console.WriteLine($"设置从站{slaveNumber}通道{channelNumber}={value}，访问的变量：{variableName}");
        }

        public void SetDigitalOutputs(bool[] outputs, int slaveNumber, int groupNumber)
        {
            if (outputs.Length != 8)
            {
                throw new ArgumentException("输出数组长度必须为8");
            }
            
            // 不建议用反射性能差距很大：
            // for (int i = 0; i < 8; i++)
            // {
            //     string fieldName = slaveNumber == 0
            //         ? (groupNumber == 1
            //             ? $"shm_slave0_rx_0x00007010_q0_{i}"
            //             : $"shm_slave0_rx_0x00007010_q1_{i}")
            //         : (groupNumber == 1
            //             ? $"shm_slave1_rx_0x00007000_q2_{i}"
            //             : groupNumber == 2
            //                 ? $"shm_slave1_rx_0x00007000_q3_{i}"
            //                 : groupNumber == 3
            //                     ? $"shm_slave1_rx_0x00007000_q4_{i}"
            //                     : $"shm_slave1_rx_0x00007000_q5_{i}");
            //     var field = typeof(EtherCATSharedMemory).GetField(fieldName);
            //     if (field != null) field.SetValueDirect(__makeref(_sharedMemory), outputs[i] ? 1 : 0);
            // }
            
            int baseChannelNumber = (groupNumber - 1) * 8 + 1;
            for (int i = 0; i < 8; i++)
            {
                SetDigitalOutput(slaveNumber, baseChannelNumber + i, outputs[i]);
            }
        }

        public void EnterRealtimeMode()
        {
            var param = new SchedParam { sched_priority = sched_get_priority_max((int)SchedPolicy.SCHED_FIFO) };
            if (sched_setscheduler(0, (int)SchedPolicy.SCHED_FIFO, ref param) != 0)
            {
                Console.WriteLine("Warning: Failed to set SCHED_FIFO scheduler");
            }
        }

        public async Task RunControlSequence(CancellationToken cancellationToken)
        {
            try
            {
                if (!WaitForIOReady(cancellationToken))
                {
                    Console.WriteLine("从站IO未就绪，退出控制序列");
                    return;
                }

                // 跑马灯变量 - 只用8位
                byte ledPattern = 0x01; // 从第一个LED开始
                const int UPDATE_INTERVAL_MS = 200; // 每200ms更新一次LED状态
                
                // 从站0的控制顺序：1->2
                int[] slave0GroupOrder = new int[] { 1, 2 };
                int slave0GroupIndex = 0;
                int currentSlave0Group = slave0GroupOrder[slave0GroupIndex];
                
                // 从站1的控制顺序：1->3->2->4
                int[] slave1GroupOrder = new int[] { 1, 3, 2, 4 };
                int slave1GroupIndex = 0;
                int currentSlave1Group = slave1GroupOrder[slave1GroupIndex];

                // 主循环
                while (!cancellationToken.IsCancellationRequested)
                {
                    // 将LED模式转换为布尔数组
                    bool[] outputs = new bool[8];
                    for (int i = 0; i < 8; i++)
                    {
                        outputs[i] = (ledPattern & (1 << i)) != 0;
                    }

                    // 设置从站0和从站1的数字输出
                    SetDigitalOutputs(outputs, 0, currentSlave0Group);
                    SetDigitalOutputs(outputs, 1, currentSlave1Group);

                    // 打印当前LED状态
                    StringBuilder slave0Status = new StringBuilder($"从站0第{currentSlave0Group}组LED状态: ");
                    StringBuilder slave1Status = new StringBuilder($"从站1第{currentSlave1Group}组LED状态: ");
                    for (int i = 0; i < 8; i++)
                    {
                        slave0Status.Append(outputs[i] ? "●" : "○");
                        slave1Status.Append(outputs[i] ? "●" : "○");
                    }
                    Console.WriteLine(slave0Status.ToString());
                    Console.WriteLine(slave1Status.ToString());

                    // 等待指定时间
                    await Task.Delay(UPDATE_INTERVAL_MS, cancellationToken);

                    // 更新LED模式
                    if (ledPattern == 0x80)
                    {
                        // 清除当前组的所有输出
                        SetDigitalOutputs(new bool[8], 0, currentSlave0Group);
                        SetDigitalOutputs(new bool[8], 1, currentSlave1Group);
                        
                        ledPattern = 0x01; // 重新从第一个LED开始
                        
                        // 切换到下一组
                        slave0GroupIndex = (slave0GroupIndex + 1) % slave0GroupOrder.Length;
                        currentSlave0Group = slave0GroupOrder[slave0GroupIndex];
                        
                        slave1GroupIndex = (slave1GroupIndex + 1) % slave1GroupOrder.Length;
                        currentSlave1Group = slave1GroupOrder[slave1GroupIndex];
                    }
                    else
                    {
                        ledPattern <<= 1; // 向左移位
                    }
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("\n控制序列被取消");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n控制序列错误: {ex.Message}");
            }
            finally
            {
                // 清除所有组的输出
                foreach (int group in new int[] { 1, 2 })
                {
                    SetDigitalOutputs(new bool[8], 0, group);
                }
                foreach (int group in new int[] { 1, 2, 3, 4 })
                {
                    SetDigitalOutputs(new bool[8], 1, group);
                }
                Console.WriteLine("\n控制序列结束");
            }
        }

        public bool GetDigitalInput(int slaveNumber, int channelNumber)
        {
            string variableName = "未知";
            bool result = false;
            
            // 不建议用反射性能差距很大：
            // string fieldName = slaveNumber == 0
            //     ? (channelNumber <= 8
            //         ? $"shm_slave0_tx_0x00006010_i0_{channelNumber-1}"
            //         : $"shm_slave0_tx_0x00006010_i1_{channelNumber-9}")
            //     : null; // slave1输入通道如有类似规则可补充
            // if (fieldName != null)
            // {
            //     var field = typeof(EtherCATSharedMemory).GetField(fieldName);
            //     if (field != null) result = (int)field.GetValueDirect(__makeref(_sharedMemory)) == 1;
            // }
            
            if (slaveNumber == 0)
            {
                // 从站0的输入通道
                switch (channelNumber)
                {
                    case 1:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_0 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_0";
                        break;
                    case 2:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_1 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_1";
                        break;
                    case 3:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_2 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_2";
                        break;
                    case 4:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_3 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_3";
                        break;
                    case 5:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_4 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_4";
                        break;
                    case 6:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_5 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_5";
                        break;
                    case 7:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_6 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_6";
                        break;
                    case 8:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i0_7 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i0_7";
                        break;
                    case 9:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_0 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_0";
                        break;
                    case 10:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_1 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_1";
                        break;
                    case 11:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_2 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_2";
                        break;
                    case 12:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_3 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_3";
                        break;
                    case 13:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_4 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_4";
                        break;
                    case 14:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_5 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_5";
                        break;
                    case 15:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_6 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_6";
                        break;
                    case 16:
                        result = _sharedMemory.shm_slave0_tx_0x00006010_i1_7 == 1;
                        variableName = "shm_slave0_tx_0x00006010_i1_7";
                        break;
                    default:
                        result = false;
                        break;
                }
                
                // 创建唯一标识键
                string cacheKey = $"slave{slaveNumber}_ch{channelNumber}";
                
                // 检查是否有之前的状态记录
                if (!_lastInputStatus.ContainsKey(cacheKey))
                {
                    // 第一次读取，记录并打印
                    _lastInputStatus[cacheKey] = result;
                    Console.WriteLine($"首次读取从站{slaveNumber}通道{channelNumber}={result}，访问的变量：{variableName}，值={(_sharedMemory.GetType().GetField(variableName)?.GetValue(_sharedMemory) ?? "未知")}");
                }
                else if (_lastInputStatus[cacheKey] != result)
                {
                    // 状态发生变化，更新记录并打印
                    _lastInputStatus[cacheKey] = result;
                    Console.WriteLine($"输入状态变化：从站{slaveNumber}通道{channelNumber}={result}，访问的变量：{variableName}，值={(_sharedMemory.GetType().GetField(variableName)?.GetValue(_sharedMemory) ?? "未知")}");
                }
                // 如果状态没变，不打印任何信息
            }
            
            return result;
        }

        public void Dispose()
        {
            _viewAccessor?.Dispose();
            _memoryMappedFile?.Dispose();
        }
    }

    public class TCPServer
    {
        private TcpListener _listener;
        private EtherCATController _controller;
        private CancellationToken _cancellationToken;
        private string _lastInputStatusResponse = string.Empty; // 用于记录上一次的输入状态响应

        public TCPServer(EtherCATController controller, CancellationToken cancellationToken, int port = 5000)
        {
            _controller = controller;
            _cancellationToken = cancellationToken;
            _listener = new TcpListener(IPAddress.Any, port);
        }

        public async Task StartAsync()
        {
            _listener.Start();
            Console.WriteLine($"TCP Server 启动，监听端口 {_listener.LocalEndpoint}");

            while (!_cancellationToken.IsCancellationRequested)
            {
                try
                {
                    TcpClient client = await _listener.AcceptTcpClientAsync();
                    Console.WriteLine($"接受客户端连接：{client.Client.RemoteEndPoint}");
                    _ = HandleClientAsync(client); // 使用 _ 忽略返回的Task，使其在后台运行
                }
                catch (SocketException ex) when (ex.SocketErrorCode == SocketError.Interrupted)
                {
                    // 服务器被停止
                    Console.WriteLine("TCP Server 停止监听新连接。");
                    break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"TCP Server 接受客户端连接时发生错误: {ex}");
                }
            }
        }

        private async Task HandleClientAsync(TcpClient client)
        {
            NetworkStream stream = client.GetStream();
            byte[] buffer = new byte[1024];
            string clientId = client.Client.RemoteEndPoint?.ToString() ?? "Unknown Client";
            string lastRequest = "";
            string lastResponse = "";

            try
            {
                while (client.Connected && !_cancellationToken.IsCancellationRequested)
                {
                    int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, _cancellationToken);
                    if (bytesRead == 0)
                    {
                        Console.WriteLine($"客户端 {clientId} 断开连接。");
                        break;
                    }

                    string request = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                    
                    // 只在请求变化时才打印详细信息
                    bool isRepeatRequest = request.Equals(lastRequest);
                    if (!isRepeatRequest)
                    {
                        Console.WriteLine($"收到来自客户端 {clientId} 的请求: {request.Trim()}");
                        if (!request.StartsWith("GET_INPUTS"))
                        {
                            // 对于非GET_INPUTS请求，打印更详细的信息
                            Console.WriteLine($"请求字节数: {bytesRead}, 请求内容的Hex值: {BitConverter.ToString(buffer, 0, bytesRead)}");
                        }
                        lastRequest = request;
                    }

                    string response = ProcessRequest(request.Trim());
                    byte[] responseBytes = Encoding.UTF8.GetBytes(response);
                    
                    // 只在响应变化时才打印
                    bool isRepeatResponse = response.Equals(lastResponse);
                    if (!isRepeatResponse)
                    {
                        if (!response.StartsWith("INPUT_STATUS") || !response.Contains("0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0"))
                        {
                            Console.WriteLine($"发送响应: {response.Trim()}, 字节数: {responseBytes.Length}");
                        }
                        lastResponse = response;
                    }
                    
                    await stream.WriteAsync(responseBytes, 0, responseBytes.Length, _cancellationToken);
                }
            }
            catch (IOException ex) when (ex.InnerException is SocketException socketEx && socketEx.SocketErrorCode == SocketError.ConnectionReset)
            {
                Console.WriteLine($"客户端 {clientId} 强制断开连接。");
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine($"客户端 {clientId} 连接处理任务被取消。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理客户端 {clientId} 请求时发生错误: {ex}");
            }
            finally
            {
                client.Close();
            }
        }

        private string ProcessRequest(string request)
        {
            string[] parts = request.Split(' ');
            if (parts.Length == 0) return "ERROR Invalid command format\n";

            string command = parts[0].ToUpper();

            switch (command)
            {
                case "SET_OUTPUT":
                    if (parts.Length == 4)
                    {
                        try
                        {
                            int slaveNumber = int.Parse(parts[1]);
                            int groupNumber = int.Parse(parts[2]);
                            string hexValue = parts[3];
                            if (!hexValue.StartsWith("0x")) return "ERROR Invalid hex format. Use 0xXX format.\n";
                            
                            byte value = Convert.ToByte(hexValue.Substring(2), 16);
                            bool[] outputs = new bool[8];
                            for (int i = 0; i < 8; i++)
                            {
                                outputs[i] = (value & (1 << i)) != 0;
                            }
                            
                            _controller.SetDigitalOutputs(outputs, slaveNumber, groupNumber);
                            return "OUTPUT_SET_OK\n";
                        }
                        catch (FormatException)
                        {
                            return "ERROR Invalid parameter format\n";
                        }
                        catch (Exception ex)
                        {
                            return $"ERROR Setting output failed: {ex.Message}\n";
                        }
                    }
                    else
                    {
                        return "ERROR SET_OUTPUT command requires 3 parameters: slave_number group_number value(0xXX)\n";
                    }

                case "SET_CHANNEL":
                    if (parts.Length == 4)
                    {
                        try
                        {
                            Console.WriteLine($"收到SET_CHANNEL命令: {request}");
                            int slaveNumber = int.Parse(parts[1]);
                            int channelNumber = int.Parse(parts[2]);
                            bool value = bool.Parse(parts[3].ToLower());
                            
                            Console.WriteLine($"准备设置: 从站{slaveNumber} 通道{channelNumber} 值={value}");
                            _controller.SetDigitalOutput(slaveNumber, channelNumber, value);
                            Console.WriteLine($"设置成功");
                            return "CHANNEL_SET_OK\n";
                        }
                        catch (FormatException ex)
                        {
                            Console.WriteLine($"参数格式错误: {ex.Message}");
                            return "ERROR Invalid parameter format\n";
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"设置通道失败: {ex.Message}");
                            return $"ERROR Setting channel failed: {ex.Message}\n";
                        }
                    }
                    else
                    {
                        Console.WriteLine($"SET_CHANNEL参数数量错误: {parts.Length}");
                        return "ERROR SET_CHANNEL command requires 3 parameters: slave_number channel_number value(true/false)\n";
                    }

                case "SET_ALL_CHANNELS":
                    if (parts.Length == 3)
                    {
                        try
                        {
                            int slaveNumber = int.Parse(parts[1]);
                            bool value = bool.Parse(parts[2]);
                            SetAllChannelsOutput(slaveNumber, value);
                            return "ALL_CHANNELS_SET_OK\n";
                        }
                        catch (FormatException ex)
                        {
                            Console.WriteLine($"参数格式错误: {ex.Message}");
                            return "ERROR Invalid parameter format\n";
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"设置所有通道失败: {ex.Message}");
                            return $"ERROR Setting all channels failed: {ex.Message}\n";
                        }
                    }
                    else
                    {
                        Console.WriteLine($"SET_ALL_CHANNELS参数数量错误: {parts.Length}");
                        return "ERROR SET_ALL_CHANNELS command requires 2 parameters: slave_number value(true/false)\n";
                    }

                case "GET_INPUTS":
                    try
                    {
                        return GetInputStatusResponse();
                    }
                    catch (Exception ex)
                    {
                        return $"ERROR Getting input status failed: {ex.Message}\n";
                    }

                default:
                    return "ERROR Unknown command\n";
            }
        }

        private void SetAllChannelsOutput(int slaveNumber, bool value)
        {
            try
            {
                // 对从站的所有通道进行设置
                for (int channelNum = 1; channelNum <= 16; channelNum++)
                {
                    _controller.SetDigitalOutput(slaveNumber, channelNum, value);
                }
                Console.WriteLine($"从站 {slaveNumber} 的所有通道已设置为 {(value ? "开启" : "关闭")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置所有通道状态时出错: {ex.Message}");
                throw;
            }
        }

        private string GetInputStatusResponse()
        {
            // 读取最新状态
            _controller.UpdateSharedMemory();
            
            // 构建输入状态字符串
            StringBuilder response = new StringBuilder("INPUT_STATUS ");
            
            // 不建议用反射性能差距很大：
            // for (int i = 1; i <= 16; i++)
            // {
            //     string fieldName = i <= 8
            //         ? $"shm_slave0_tx_0x00006010_i0_{i-1}"
            //         : $"shm_slave0_tx_0x00006010_i1_{i-9}";
            //     var field = typeof(EtherCATSharedMemory).GetField(fieldName);
            //     bool value = field != null && (int)field.GetValue(_sharedMemory) == 1;
            //     response.Append(value ? "1 " : "0 ");
            // }
            
            // 从站0的输入状态 (16个通道)
            response.Append("0 ");
            response.Append(_controller.GetDigitalInput(0, 1) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 2) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 3) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 4) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 5) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 6) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 7) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 8) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 9) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 10) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 11) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 12) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 13) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 14) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 15) ? "1 " : "0 ");
            response.Append(_controller.GetDigitalInput(0, 16) ? "1" : "0");
            
            string currentResponse = response.ToString();
            
            // 只有当输入状态变化时才打印
            if (_lastInputStatusResponse != currentResponse)
            {
                Console.WriteLine("输入状态变化，新响应：" + currentResponse);
                _lastInputStatusResponse = currentResponse;
            }
            
            response.Append("\n");
            return response.ToString();
        }

        public void Stop()
        {
            try
            {
                _listener.Stop(); // 停止监听
                Console.WriteLine("TCP Server 已停止。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"停止 TCP Server 时发生错误: {ex.Message}");
            }
        }
    }

    class Program
    {
        static async Task Main(string[] args)
        {
            // 打印通道映射关系
            Console.WriteLine("\n=== 通道与共享内存变量的映射关系 ===");
            Console.WriteLine("\n从站0(Slave 0) - 输入通道:");
            Console.WriteLine("CH1 -> shm_slave0_tx_0x00006010_i0_0 (启动按钮)");
            Console.WriteLine("CH2 -> shm_slave0_tx_0x00006010_i0_1 (复位按钮)");
            Console.WriteLine("CH3 -> shm_slave0_tx_0x00006010_i0_2 (头车光电)");
            Console.WriteLine("CH4 -> shm_slave0_tx_0x00006010_i0_3 (测速光电1)");
            Console.WriteLine("CH5 -> shm_slave0_tx_0x00006010_i0_4 (测速光电2)");
            Console.WriteLine("CH6 -> shm_slave0_tx_0x00006010_i0_5 (拍照光电)");
            Console.WriteLine("CH7 -> shm_slave0_tx_0x00006010_i0_6 (上包光电)");
            Console.WriteLine("CH8 -> shm_slave0_tx_0x00006010_i0_7 (末端堵包光电)");
            Console.WriteLine("CH9 -> shm_slave0_tx_0x00006010_i1_0 (何服报警1)");
            Console.WriteLine("CH10 -> shm_slave0_tx_0x00006010_i1_1 (何服报警2)");
            Console.WriteLine("CH11 -> shm_slave0_tx_0x00006010_i1_2 (何服报警3)");
            Console.WriteLine("CH12 -> shm_slave0_tx_0x00006010_i1_3 (何服报警4)");
            Console.WriteLine("CH13 -> shm_slave0_tx_0x00006010_i1_4 (电源报警1)");
            Console.WriteLine("CH14 -> shm_slave0_tx_0x00006010_i1_5 (电源报警2)");
            Console.WriteLine("CH15 -> shm_slave0_tx_0x00006010_i1_6 (电源报警3)");
            Console.WriteLine("CH16 -> shm_slave0_tx_0x00006010_i1_7 (变频器脉冲反馈信号)");
            
            Console.WriteLine("\n从站0(Slave 0) - 输出通道:");
            Console.WriteLine("CH1 -> shm_slave0_rx_0x00007010_q0_0 (相机触发)");
            Console.WriteLine("CH2 -> shm_slave0_rx_0x00007010_q0_1 (红灯)");
            Console.WriteLine("CH3 -> shm_slave0_rx_0x00007010_q0_2 (绿灯)");
            Console.WriteLine("CH4 -> shm_slave0_rx_0x00007010_q0_3 (黄灯)");
            Console.WriteLine("CH5 -> shm_slave0_rx_0x00007010_q0_4 (蜂鸣)");
            Console.WriteLine("CH6 -> shm_slave0_rx_0x00007010_q0_5 (报警复位)");
            Console.WriteLine("CH7 -> shm_slave0_rx_0x00007010_q0_6 (末端堵包蜂鸣)");
            Console.WriteLine("CH8 -> shm_slave0_rx_0x00007010_q0_7 (备用1)");
            Console.WriteLine("CH9 -> shm_slave0_rx_0x00007010_q1_0 (备用2)");
            Console.WriteLine("CH10 -> shm_slave0_rx_0x00007010_q1_1 (备用3)");
            Console.WriteLine("CH11 -> shm_slave0_rx_0x00007010_q1_2 (备用4)");
            Console.WriteLine("CH12 -> shm_slave0_rx_0x00007010_q1_3 (备用5)");
            Console.WriteLine("CH13 -> shm_slave0_rx_0x00007010_q1_4 (备用6)");
            Console.WriteLine("CH14 -> shm_slave0_rx_0x00007010_q1_5 (备用7)");
            Console.WriteLine("CH15 -> shm_slave0_rx_0x00007010_q1_6 (备用8)");
            Console.WriteLine("CH16 -> shm_slave0_rx_0x00007010_q1_7 (备用9)");
            
            Console.WriteLine("\n从站1(Slave 1) - 输出通道:");
            for (int i = 1; i <= 32; i++)
            {
                string channelDesc = i <= 2 ? $"1DIN{i}" :
                                   i <= 4 ? $"2DIN{i-2}" :
                                   i <= 6 ? $"3DIN{i-4}" :
                                   i <= 8 ? $"4DIN{i-6}" :
                                   i <= 10 ? $"5DIN{i-8}" :
                                   i <= 12 ? $"6DIN{i-10}" :
                                   i <= 14 ? $"7DIN{i-12}" :
                                   i <= 16 ? $"8DIN{i-14}" :
                                   i <= 18 ? $"9DIN{i-16}" :
                                   i <= 20 ? $"10DIN{i-18}" :
                                   i <= 22 ? $"11DIN{i-20}" :
                                   i <= 24 ? $"12DIN{i-22}" :
                                   i <= 26 ? $"13DIN{i-24}" :
                                   i <= 28 ? $"14DIN{i-26}" :
                                   i <= 30 ? $"15DIN{i-28}" : $"16DIN{i-30}";
                
                // 根据索引计算q值和bit位
                int groupNum = (i - 1) / 8 + 2; // q2到q5
                int bitPos = (i - 1) % 8;
                Console.WriteLine($"CH{i} -> shm_slave1_rx_0x00007000_q{groupNum}_{bitPos} ({channelDesc})");
            }
            Console.WriteLine("=============================\n");
            
            try 
            {
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json")
                );

                var requestData = new { programName = $"{Path.GetFileName(Process.GetCurrentProcess().MainModule.FileName)}" };
                var jsonContent = JsonSerializer.Serialize(requestData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(
                    "http://127.0.0.1:3000/api/programs/start-middleware", 
                    content
                );

                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"请求中间件失败: {response.StatusCode}");
                    return;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"中间件返回数据: {responseContent}");

                // 解析JSON响应
                var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);
                if (responseData.TryGetProperty("code", out var codeElement) && codeElement.GetInt32() == 0)
                {
                    if (responseData.TryGetProperty("data", out var dataElement) && dataElement.TryGetProperty("shmFile", out var shmFileElement))
                    {
                        var sharedMemoryFilePath = shmFileElement.GetString();
                        
                        // 添加空值检查
                        if (string.IsNullOrEmpty(sharedMemoryFilePath))
                        {
                            Console.WriteLine("错误：从中间件获取的共享内存路径为空");
                            return;
                        }
                        
                        Console.WriteLine($"启动EtherCAT控制程序，共享内存文件路径: {sharedMemoryFilePath}...");
                        
                        using var controller = new EtherCATController(sharedMemoryFilePath);
                        controller.EnterRealtimeMode();

                        int tcpPort = 5000;
                        if (args.Length > 0 && int.TryParse(args[0], out int port))
                        {
                            tcpPort = port;
                        }

                        var cts = new CancellationTokenSource();

                        // 添加SIGTERM信号处理
                        AppDomain.CurrentDomain.ProcessExit += (s, e) => {
                            Console.WriteLine("收到SIGTERM信号，正在优雅退出...");
                            cts.Cancel();
                        };

                        Console.CancelKeyPress += (s, e) => {
                            e.Cancel = true;
                            cts.Cancel();
                        };

                        try
                        {
                            var server = new TCPServer(controller, cts.Token, tcpPort);
                            await server.StartAsync();
                        }
                        catch (OperationCanceledException)
                        {
                            Console.WriteLine("服务器正在停止...");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error: {ex.Message}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("错误：中间件响应中缺少data.shmFile字段");
                    }
                }
                else
                {
                    string errorMsg = responseData.TryGetProperty("msg", out var msgElement) 
                        ? msgElement.GetString() 
                        : "未知错误";
                    Console.WriteLine($"错误：中间件返回错误码 {codeElement.GetInt32()}，错误信息：{errorMsg}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Startup error: {ex.Message}");
            }
        }
    }
}