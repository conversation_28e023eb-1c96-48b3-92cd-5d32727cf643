# IO Demo程序文档

## 程序变体说明

系统提供了三个不同的程序变体，分别针对不同的使用场景：

1. **Program_Base.cs** - 基础版本
   - 提供基本的EtherCAT控制方法
   - 包含完整的通道映射和状态监控
   - 支持中间件通信和共享内存管理

2. **Program_Loop.cs** - 循环控制版本
   - 在基础版本上增加了循环控制功能
   - 支持周期性IO状态检查和更新
   - 适用于需要持续监控和控制的场景

3. **Program_TCP.cs** - TCP服务器版本
   - 在基础版本上增加了TCP服务器功能
   - 支持远程控制和状态查询
   - 提供网络接口供客户端程序使用

## 系统架构

```mermaid
flowchart TB
    subgraph 客户端
        GUI[GUI应用程序]
        TCP_CLIENT[TCP客户端]
    end
    
    subgraph 后端服务
        TCP[TCP服务器]
        CONTROLLER[EtherCAT控制器]
        SHM[共享内存]
        MIDDLEWARE[中间件]
    end
    
    subgraph EtherCAT网络
        MASTER[EtherCAT主站]
        SLAVE0[从站0 - IO模块]
        SLAVE1[从站1 - IO模块]
    end
    
    GUI <--> |TCP/IP通信|TCP
    TCP_CLIENT <--> |TCP/IP通信|TCP
    TCP <--> |数据处理|CONTROLLER
    CONTROLLER <--> |内存读写|SHM
    CONTROLLER <--> |API调用|MIDDLEWARE
    SHM <--> |内存映射|MASTER
    MASTER <--> |EtherCAT协议|SLAVE0
    MASTER <--> |EtherCAT协议|SLAVE1
```

## 主要组件

### 1. 共享内存结构（EtherCATSharedMemory）

共享内存结构定义了与EtherCAT主站通信的内存布局，包含了从站状态和输入/输出通道的数据。

```mermaid
classDiagram
    class EtherCATSharedMemory {
        +int shm_slaveX_online_status
        +int shm_slaveX_operational_status
        +int shm_slaveX_al_state
        +int shm_slave0_rx_0x00007010_q0_0~7
        +int shm_slave0_rx_0x00007010_q1_0~7
        +int shm_slave0_tx_0x00006010_i0_0~7
        +int shm_slave0_tx_0x00006010_i1_0~7
        +int shm_slave1_rx_0x00007000_q2~q5_0~7
    }
```

系统支持两个从站：
- **从站0**：拥有16个输入通道（I0.0-I1.7）和16个输出通道（Q0.0-Q1.7）
- **从站1**：拥有32个输出通道（Q2.0-Q5.7）

### 2. EtherCAT控制器（EtherCATController）

EtherCAT控制器负责管理共享内存的读写操作，提供高级API来控制从站设备的IO状态。

主要方法：
- `UpdateSharedMemory()`: 从共享内存读取当前状态
- `WriteSharedMemory()`: 将修改后的状态写入共享内存
- `SetDigitalOutput()`: 设置单个数字输出通道的状态
- `SetDigitalOutputs()`: 批量设置一组数字输出通道的状态
- `GetDigitalInput()`: 读取单个数字输入通道的状态
- `WaitForIOReady()`: 等待从站设备就绪
- `EnterRealtimeMode()`: 设置实时调度优先级
- `RunLedSequence()`: 运行LED演示序列

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant TCP as TCP服务器
    participant Controller as EtherCAT控制器
    participant SHM as 共享内存
    participant Master as EtherCAT主站
    participant Slave as 从站设备
    
    Client->>TCP: 发送控制命令
    TCP->>Controller: 调用相应方法
    Controller->>SHM: 读取当前状态
    SHM-->>Controller: 返回状态数据
    Controller->>Controller: 处理数据
    Controller->>SHM: 写入修改后的状态
    Master->>SHM: 周期性读取
    Master->>Slave: 发送EtherCAT帧
    Slave-->>Master: 返回状态
    Master->>SHM: 更新输入状态
    SHM-->>Controller: 读取最新输入
    Controller-->>TCP: 返回处理结果
    TCP-->>Client: 发送响应
```

### 3. 中间件通信

系统通过HTTP API与中间件进行通信，主要功能包括：
- 获取共享内存文件路径
- 程序启动和停止管理
- 状态同步和错误处理

### 4. TCP服务器（TCPServer）

TCP服务器为客户端提供网络接口，使客户端能够远程控制和监控从站设备。

主要方法：
- `StartAsync()`: 启动TCP服务器并开始监听连接
- `ProcessRequest()`: 处理客户端请求
- `SetAllChannelsOutput()`: 批量设置所有通道状态
- `GetInputStatusResponse()`: 获取输入状态响应

### 5. 程序入口（Program）

程序主入口完成初始化工作，创建EtherCAT控制器和TCP服务器，并启动服务。

## 通信协议

系统使用简单的基于文本的TCP协议，支持以下命令：

### 1. 设置单个通道状态

```
SET_CHANNEL <slave_number> <channel_number> <value>
```

- `slave_number`: 从站编号（0或1）
- `channel_number`: 通道编号（1-16或1-32）
- `value`: 通道状态（true或false）

响应：
- 成功：`CHANNEL_SET_OK`
- 失败：`ERROR <error_message>`

### 2. 设置输出组状态

```
SET_OUTPUT <slave_number> <group_number> <value>
```

- `slave_number`: 从站编号（0或1）
- `group_number`: 组编号（1-2或1-4）
- `value`: 十六进制值（0x00-0xFF）

响应：
- 成功：`OUTPUT_SET_OK`
- 失败：`ERROR <error_message>`

### 3. 设置所有通道状态

```
SET_ALL_CHANNELS <slave_number> <value>
```

- `slave_number`: 从站编号（0或1）
- `value`: 通道状态（true或false）

响应：
- 成功：`ALL_CHANNELS_SET_OK`
- 失败：`ERROR <error_message>`

### 4. 获取输入状态

```
GET_INPUTS
```

响应：
```
INPUT_STATUS 0 <i0.0> <i0.1> ... <i1.7>
```
其中`<i0.0>`等表示输入通道的状态（0或1）

## 通道映射

### 从站0 (IO模块)

#### 输入通道 (16个)

| 通道编号 | 变量名 | 功能描述 |
|---------|-------|---------|
| I0.0 | shm_slave0_tx_0x00006010_i0_0 | 启动按钮 |
| I0.1 | shm_slave0_tx_0x00006010_i0_1 | 复位按钮 |
| I0.2 | shm_slave0_tx_0x00006010_i0_2 | 头车光电 |
| I0.3 | shm_slave0_tx_0x00006010_i0_3 | 测速光电1 |
| I0.4 | shm_slave0_tx_0x00006010_i0_4 | 测速光电2 |
| I0.5 | shm_slave0_tx_0x00006010_i0_5 | 拍照光电 |
| I0.6 | shm_slave0_tx_0x00006010_i0_6 | 上包光电 |
| I0.7 | shm_slave0_tx_0x00006010_i0_7 | 末端堵包光电 |
| I1.0 | shm_slave0_tx_0x00006010_i1_0 | 何服报警1 |
| I1.1 | shm_slave0_tx_0x00006010_i1_1 | 何服报警2 |
| I1.2 | shm_slave0_tx_0x00006010_i1_2 | 何服报警3 |
| I1.3 | shm_slave0_tx_0x00006010_i1_3 | 何服报警4 |
| I1.4 | shm_slave0_tx_0x00006010_i1_4 | 电源报警1 |
| I1.5 | shm_slave0_tx_0x00006010_i1_5 | 电源报警2 |
| I1.6 | shm_slave0_tx_0x00006010_i1_6 | 电源报警3 |
| I1.7 | shm_slave0_tx_0x00006010_i1_7 | 变频器脉冲反馈信号 |

#### 输出通道 (16个)

| 通道编号 | 变量名 | 功能描述 |
|---------|-------|---------|
| Q0.0 | shm_slave0_rx_0x00007010_q0_0 | 相机触发 |
| Q0.1 | shm_slave0_rx_0x00007010_q0_1 | 红灯 |
| Q0.2 | shm_slave0_rx_0x00007010_q0_2 | 绿灯 |
| Q0.3 | shm_slave0_rx_0x00007010_q0_3 | 黄灯 |
| Q0.4 | shm_slave0_rx_0x00007010_q0_4 | 蜂鸣 |
| Q0.5 | shm_slave0_rx_0x00007010_q0_5 | 报警复位 |
| Q0.6 | shm_slave0_rx_0x00007010_q0_6 | 末端堵包蜂鸣 |
| Q0.7 | shm_slave0_rx_0x00007010_q0_7 | 备用1 |
| Q1.0 | shm_slave0_rx_0x00007010_q1_0 | 备用2 |
| Q1.1 | shm_slave0_rx_0x00007010_q1_1 | 备用3 |
| Q1.2 | shm_slave0_rx_0x00007010_q1_2 | 备用4 |
| Q1.3 | shm_slave0_rx_0x00007010_q1_3 | 备用5 |
| Q1.4 | shm_slave0_rx_0x00007010_q1_4 | 备用6 |
| Q1.5 | shm_slave0_rx_0x00007010_q1_5 | 备用7 |
| Q1.6 | shm_slave0_rx_0x00007010_q1_6 | 备用8 |
| Q1.7 | shm_slave0_rx_0x00007010_q1_7 | 备用9 |

### 从站1 (扩展IO模块)

#### 输出通道 (32个)

从站1的输出通道根据通道号自动映射到相应的变量，例如：

- Q2.0-Q2.7：映射到 shm_slave1_rx_0x00007000_q2_0 ~ q2_7
- Q3.0-Q3.7：映射到 shm_slave1_rx_0x00007000_q3_0 ~ q3_7
- Q4.0-Q4.7：映射到 shm_slave1_rx_0x00007000_q4_0 ~ q4_7
- Q5.0-Q5.7：映射到 shm_slave1_rx_0x00007000_q5_0 ~ q5_7

## 方法详解

### SetDigitalOutput 和 SetDigitalOutputs 的区别

这两个方法都用于设置数字输出通道的状态，但有不同的用途：

1. **SetDigitalOutput**：
   - 设置单个通道的状态
   - 参数：从站号、通道号和值
   - 直接操作特定的共享内存变量
   - 根据从站号和通道号计算出具体要操作的位置
   - 输出详细的操作日志，包括变量名

2. **SetDigitalOutputs**：
   - 设置一组(8个)通道的状态
   - 参数：布尔数组、从站号和组号
   - 是对`SetDigitalOutput`的批量封装
   - 循环调用`SetDigitalOutput`来设置每个通道
   - 将组号转换为起始通道号，然后依次设置8个通道

```mermaid
flowchart TB
    SET_OUTPUTS[SetDigitalOutputs]
    SET_OUTPUT[SetDigitalOutput]
    WRITE_MEM[WriteSharedMemory]
    
    SET_OUTPUTS -->|循环调用| SET_OUTPUT
    SET_OUTPUT -->|计算内存位置| SET_OUTPUT
    SET_OUTPUT -->|更新数据| WRITE_MEM
```

## 系统初始化和启动流程

```mermaid
sequenceDiagram
    participant Main as 程序入口
    participant API as 中间件API
    participant Controller as EtherCAT控制器
    participant TCP as TCP服务器
    
    Main->>Main: 打印通道映射信息
    Main->>API: 请求共享内存路径
    API-->>Main: 返回共享内存文件路径
    Main->>Controller: 创建控制器实例
    Main->>Controller: 设置实时模式
    Main->>TCP: 创建TCP服务器
    Main->>TCP: 启动服务器
    TCP->>Main: 开始处理客户端请求
    Main->>Main: 等待程序退出信号
```

## 性能优化

系统包含多项性能优化：

1. **日志优化**：
   - 仅在状态变化时打印日志
   - 使用变量名缓存减少字符串拼接
   - 详细的错误信息记录

2. **状态监控**：
   - 使用`_lastInputStatus`缓存上一次的状态
   - 只对状态变化的通道进行处理
   - 实时模式支持

3. **错误处理**：
   - 完善的异常捕获和处理
   - 详细的错误信息输出
   - 优雅的退出机制

## 使用示例

### 基础版本使用

```csharp
using var controller = new EtherCATController(sharedMemoryFilePath);
controller.EnterRealtimeMode();

// 设置单个通道
controller.SetDigitalOutput(0, 1, true);  // 打开从站0的Q0.0

// 设置一组通道
bool[] outputs = new bool[8] { true, false, true, false, true, false, true, false };
controller.SetDigitalOutputs(outputs, 0, 1);  // 设置从站0的Q0.0-Q0.7

// 读取输入状态
bool inputState = controller.GetDigitalInput(0, 1);  // 读取从站0的I0.0
```

### TCP服务器版本使用

```bash
# 连接服务器
telnet 127.0.0.1 5000

# 设置通道
SET_CHANNEL 0 1 true  # 设置Q0.0
SET_OUTPUT 1 1 0xFF  # 设置Q2.0-Q2.7

# 获取状态
GET_INPUTS
```

### 循环控制版本使用

```csharp
// 运行LED演示序列
await controller.RunLedSequence(cancellationToken);
```

## 注意事项

1. 系统使用共享内存与EtherCAT主站通信，确保主站已正确配置并运行
2. 实时模式需要适当的系统权限
3. 中间件通信需要确保网络连接正常
4. 程序退出时会自动清理资源
5. 建议使用日志监控系统运行状态

## 程序执行流程

### 1. 基础版本 (Program.cs)

```mermaid
flowchart TD
    A[程序启动] --> B[打印通道映射信息]
    B --> C[创建HTTP客户端]
    C --> D[准备中间件请求数据]
    D --> E[发送启动中间件请求]
    E --> F{请求成功?}
    F -->|是| G[解析响应获取共享内存路径]
    F -->|否| H[输出错误信息并退出]
    G --> I[创建EtherCAT控制器]
    I --> J[设置实时模式]
    J --> K[等待IO就绪]
    K --> L{IO就绪?}
    L -->|是| M[保持程序运行]
    L -->|否| N[输出错误信息并退出]
    M --> O[等待退出信号]
    O --> P[清理资源并退出]
```

### 2. 循环控制版本 (Program_Loop.cs)

```mermaid
flowchart TD
    A[程序启动] --> B[打印通道映射信息]
    B --> C[创建HTTP客户端]
    C --> D[准备中间件请求数据]
    D --> E[发送启动中间件请求]
    E --> F{请求成功?}
    F -->|是| G[解析响应获取共享内存路径]
    F -->|否| H[输出错误信息并退出]
    G --> I[创建EtherCAT控制器]
    I --> J[设置实时模式]
    J --> K[等待IO就绪]
    K --> L{IO就绪?}
    L -->|是| M[创建取消令牌源]
    L -->|否| N[输出错误信息并退出]
    M --> O[注册退出信号处理]
    O --> P[启动LED演示序列]
    P --> Q[循环执行LED控制]
    Q --> R{收到取消信号?}
    R -->|是| S[清理资源并退出]
    R -->|否| Q
```

### 3. TCP服务器版本 (Program_TCP.cs)

```mermaid
flowchart TD
    A[程序启动] --> B[打印通道映射信息]
    B --> C[创建HTTP客户端]
    C --> D[准备中间件请求数据]
    D --> E[发送启动中间件请求]
    E --> F{请求成功?}
    F -->|是| G[解析响应获取共享内存路径]
    F -->|否| H[输出错误信息并退出]
    G --> I[创建EtherCAT控制器]
    I --> J[设置实时模式]
    J --> K[等待IO就绪]
    K --> L{IO就绪?}
    L -->|是| M[创建TCP服务器]
    L -->|否| N[输出错误信息并退出]
    M --> O[启动TCP服务器]
    O --> P[等待客户端连接]
    P --> Q{收到客户端请求?}
    Q -->|是| R[解析请求命令]
    R --> S[执行相应操作]
    S --> T[返回响应]
    T --> P
    Q -->|否| U{收到退出信号?}
    U -->|是| V[清理资源并退出]
    U -->|否| P
```

