# Ethercat 伺服控制Demo程序文档


| 文档信息 | 描述 |
| --- | --- |
| 文档版本 | V1.0.0 |
| 最后更新 | 2025-04-30 |

| 维护人 | 角色 | 联系方式 |
| --- | --- | --- |
| 何俊言 | 系统开发 | he<PERSON><EMAIL> |
| 唐伟峰 | 系统开发 | <EMAIL> |

## 程序说明

该程序用于控制工业自动化系统中的多个伺服驱动器，支持多达48个设备的同步控制。程序提供了完整的伺服控制功能，包括使能/取消使能、速度控制、状态监控等。

## 系统功能概述

- 多伺服设备同步控制
- 实时状态监控
- 多设备批量操作
- 高精度速度和位置控制
- 完善的错误处理和恢复机制

## 主要组件

### 1. 控制接口 (EtherCATController)

控制接口提供了一系列方法用于管理和操作伺服设备。主要功能包括状态读取、命令发送、参数设置等。

主要方法（可以自行封装）：
- `UpdateSharedMemory()`: 从存储区域读取当前状态
- `WriteSharedMemory()`: 将修改后的状态写入存储区域
- `EnableServo()`: 使能单个设备
- `DisableServo()`: 取消使能单个设备
- `BatchEnableServo()`: 批量使能多个设备
- `BatchDisableServo()`: 批量取消使能多个设备
- `BatchEnableAllOnlineServos()`: 批量使能所有在线设备
- `DisableAllServos()`: 取消使能所有设备
- `SetVelocity()`: 设置设备的目标速度
- `SetTargetSpeedAll()`: 为所有设备设置相同的目标速度
- `EnterRealtimeMode()`: 设置实时调度优先级
- `RunControlSequence()`: 运行控制序列

### 2. 设备状态参数 (EtherCATSharedMemory)

每个设备包含以下关键参数：
- **在线状态** (`shm_slaveX_online_status`): 指示设备是否在线
- **运行状态** (`shm_slaveX_operational_status`): 指示设备是否处于运行状态
- **工作状态** (`shm_slaveX_al_state`): 设备的当前状态
- **控制参数** (`shm_slaveX_rx_0x6040_control_word`): 用于控制设备的状态转换
- **目标速度** (`shm_slaveX_rx_0x60ff_target_speed`): 设置设备的目标速度
- **操作模式** (`shm_slaveX_rx_0x6060_operation_mode`): 设置设备的操作模式
- **状态反馈** (`shm_slaveX_tx_0x6041_status_word`): 读取设备的当前状态

## 设备控制流程

### 1. 设备使能流程 (EnableServo)

使能设备的一般流程：
1. 检查设备在线状态 (`GetSlaveOnlineStatus`)
2. 设置操作模式 (`SetOperationMode`)
3. 设置控制参数 (`SetControlWord`)
4. 等待状态转换完成 (`WaitForStatus`)
5. 验证使能状态 (`CheckStatusWord`)

### 2. 设备取消使能流程 (DisableServo)

取消使能设备的一般流程：
1. 检查设备在线状态 (`GetSlaveOnlineStatus`)
2. 设置控制参数 (`SetControlWord`)
3. 等待状态转换完成 (`WaitForStatus`)
4. 验证取消使能状态 (`CheckStatusWord`)

## 使用示例

### 基本控制

```csharp
using var controller = new EtherCATController(sharedMemoryFilePath);
controller.EnterRealtimeMode();

// 使能单个设备
controller.EnableServo(0);  // 使能设备0

// 批量使能多个设备
int[] slaveIndices = new int[] { 0, 1, 2, 3 };
controller.BatchEnableServo(slaveIndices);

// 设置速度
controller.SetVelocity(1000);  // 设置目标速度为1000

// 取消使能所有设备
controller.DisableAllServos();
```

### 控制序列

```csharp
// 运行控制序列
await controller.RunControlSequence(cancellationToken);
```

## 注意事项

1. 系统启动前确保硬件设备已正确配置并连接
2. 实时模式需要适当的系统权限
3. 设备使能/取消使能过程需要遵循正确的状态转换顺序
4. 建议使用日志监控系统运行状态
5. 批量操作时注意设备数量限制
6. 确保设备参数已正确配置

## 错误处理

系统包含完善的错误处理机制：

1. **状态检查**：
   - 检查设备在线状态 (`GetSlaveOnlineStatus`)
   - 检查运行状态 (`GetSlaveOperationalStatus`)
   - 检查工作状态 (`GetSlaveALState`)

2. **超时处理**：
   - 状态转换超时 (`WaitForStatus`)
   - 通信超时
   - 操作超时

3. **异常处理**：
   - 完善的异常捕获和处理
   - 详细的错误信息输出
   - 优雅的退出机制

## 性能优化

1. **实时模式**：
   - 使用实时调度优先级 (`EnterRealtimeMode`)
   - 优化资源访问
   - 减少系统调用

2. **批量操作**：
   - 支持批量使能/取消使能 (`BatchEnableServo`, `BatchDisableServo`)
   - 优化状态检查
   - 并行处理多个设备

3. **状态监控**：
   - 使用缓存减少资源访问
   - 只对状态变化的设备进行处理
   - 实时状态更新 (`UpdateStatus`) 