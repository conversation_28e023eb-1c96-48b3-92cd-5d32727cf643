# 伺服控制Demo程序文档

## 程序说明

该程序用于控制多个EtherCAT伺服驱动器，支持多达48个从站的同步控制。程序提供了完整的伺服控制功能，包括使能/失能、速度控制、状态监控等。

## 系统架构

```mermaid
flowchart TB
    subgraph 控制程序
        CONTROLLER[EtherCAT控制器]
        SHM[共享内存]
    end
    
    subgraph EtherCAT网络
        MASTER[EtherCAT主站]
        SLAVE0[从站0 - 伺服驱动器]
        SLAVE1[从站1 - 伺服驱动器]
        SLAVEN[...从站n - 伺服驱动器]
    end
    
    CONTROLLER <--> |内存读写|SHM
    SHM <--> |内存映射|MASTER
    MASTER <--> |EtherCAT协议|SLAVE0
    MASTER <--> |EtherCAT协议|SLAVE1
    MASTER <--> |EtherCAT协议|SLAVEN
```

## 主要组件

### 1. 共享内存结构（EtherCATSharedMemory）

共享内存结构定义了与EtherCAT主站通信的内存布局，包含了从站状态和控制参数。

```mermaid
classDiagram
    class EtherCATSharedMemory {
        +int shm_slaveX_online_status
        +int shm_slaveX_operational_status
        +int shm_slaveX_al_state
        +int shm_slaveX_rx_0x6040_control_word
        +int shm_slaveX_rx_0x60ff_target_speed
        +int shm_slaveX_rx_0x6060_operation_mode
        +int shm_slaveX_tx_0x6041_status_word
    }
```

每个从站包含以下关键参数：
- **在线状态**：指示从站是否在线
- **运行状态**：指示从站是否处于运行状态
- **AL状态**：从站的AL（Application Layer）状态
- **控制字**：用于控制伺服驱动器的状态转换
- **目标速度**：设置伺服驱动器的目标速度
- **操作模式**：设置伺服驱动器的操作模式
- **状态字**：读取伺服驱动器的当前状态

### 2. EtherCAT控制器（EtherCATController）

EtherCAT控制器负责管理共享内存的读写操作，提供高级API来控制伺服驱动器。

主要方法：
- `UpdateSharedMemory()`: 从共享内存读取当前状态
- `WriteSharedMemory()`: 将修改后的状态写入共享内存
- `EnableServo()`: 使能单个伺服驱动器
- `DisableServo()`: 失能单个伺服驱动器
- `BatchEnableServo()`: 批量使能多个伺服驱动器
- `BatchDisableServo()`: 批量失能多个伺服驱动器
- `SetVelocity()`: 设置伺服驱动器的目标速度
- `EnterRealtimeMode()`: 设置实时调度优先级
- `RunControlSequence()`: 运行控制序列

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as EtherCAT控制器
    participant SHM as 共享内存
    participant Master as EtherCAT主站
    participant Slave as 伺服驱动器
    
    Client->>Controller: 调用控制方法
    Controller->>SHM: 读取当前状态
    SHM-->>Controller: 返回状态数据
    Controller->>Controller: 处理数据
    Controller->>SHM: 写入修改后的状态
    Master->>SHM: 周期性读取
    Master->>Slave: 发送EtherCAT帧
    Slave-->>Master: 返回状态
    Master->>SHM: 更新状态
    SHM-->>Controller: 读取最新状态
    Controller-->>Client: 返回处理结果
```

## 伺服控制流程

### 1. 伺服使能流程

```mermaid
flowchart TD
    A[开始] --> B[检查从站在线状态]
    B --> C{从站在线?}
    C -->|否| D[输出错误信息]
    C -->|是| E[设置操作模式]
    E --> F[设置控制字]
    F --> G[等待状态转换]
    G --> H{状态转换成功?}
    H -->|否| I[输出错误信息]
    H -->|是| J[完成使能]
```

### 2. 伺服失能流程

```mermaid
flowchart TD
    A[开始] --> B[检查从站在线状态]
    B --> C{从站在线?}
    C -->|否| D[输出错误信息]
    C -->|是| E[设置控制字]
    E --> F[等待状态转换]
    F --> G{状态转换成功?}
    G -->|否| H[输出错误信息]
    G -->|是| I[完成失能]
```

## 使用示例

### 基本控制

```csharp
using var controller = new EtherCATController(sharedMemoryFilePath);
controller.EnterRealtimeMode();

// 使能单个伺服
controller.EnableServo(0);  // 使能从站0

// 批量使能多个伺服
int[] slaveIndices = new int[] { 0, 1, 2, 3 };
controller.BatchEnableServo(slaveIndices);

// 设置速度
controller.SetVelocity(1000);  // 设置目标速度为1000

// 失能所有伺服
controller.DisableAllServos();
```

### 控制序列

```csharp
// 运行控制序列
await controller.RunControlSequence(cancellationToken);
```

## 注意事项

1. 系统使用共享内存与EtherCAT主站通信，确保主站已正确配置并运行
2. 实时模式需要适当的系统权限
3. 伺服使能/失能过程需要遵循正确的状态转换顺序
4. 建议使用日志监控系统运行状态
5. 批量操作时注意从站数量限制
6. 确保伺服驱动器参数已正确配置

## 错误处理

系统包含完善的错误处理机制：

1. **状态检查**：
   - 检查从站在线状态
   - 检查运行状态
   - 检查AL状态

2. **超时处理**：
   - 状态转换超时
   - 通信超时
   - 操作超时

3. **异常处理**：
   - 完善的异常捕获和处理
   - 详细的错误信息输出
   - 优雅的退出机制

## 性能优化

1. **实时模式**：
   - 使用实时调度优先级
   - 优化内存访问
   - 减少系统调用

2. **批量操作**：
   - 支持批量使能/失能
   - 优化状态检查
   - 并行处理多个从站

3. **状态监控**：
   - 使用缓存减少内存访问
   - 只对状态变化的从站进行处理
   - 实时状态更新 