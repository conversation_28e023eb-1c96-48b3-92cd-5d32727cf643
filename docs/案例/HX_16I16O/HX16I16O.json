{"slaves": [{"index": "0", "name": "Box 1 (CTL_ECT)", "vid": "0x00000099", "pid": "0x00020310", "rx_pdo": "0x00001601", "tx_pdo": "0x00001a00, 0x00001a01", "sdos": [{"name": "Module Config 1", "index": "0x8001", "subindex": "1", "type": "uint32", "value": "0x00000181"}, {"name": "Module Config 1", "index": "0x6001", "subindex": "1", "type": "uint8", "value": "0x06"}, {"name": "Module Config 2", "index": "0x6001", "subindex": "2", "type": "uint8", "value": "0x06"}, {"name": "Module Config 3", "index": "0x6001", "subindex": "3", "type": "uint8", "value": "0x06"}, {"name": "Module Config 4", "index": "0x6001", "subindex": "4", "type": "uint8", "value": "0x06"}, {"name": "Module Config 1", "index": "0x8101", "subindex": "1", "type": "uint32", "value": "0x00000372"}, {"name": "Module Config 1", "index": "0x7102", "subindex": "1", "type": "uint8", "value": "0xff"}, {"name": "Module Config 2", "index": "0x7102", "subindex": "2", "type": "uint8", "value": "0xff"}], "rx_pdos": [{"name": "OutByte0", "index": "0x00007100", "subindex": "1", "type": "uint8"}, {"name": "OutByte1", "index": "0x00007100", "subindex": "2", "type": "uint8"}], "tx_pdos": [{"name": "InByte0", "index": "0x00006000", "subindex": "1", "type": "uint8"}, {"name": "InByte1", "index": "0x00006000", "subindex": "2", "type": "uint8"}, {"name": "Module State", "index": "0x00008002", "subindex": "0", "type": "uint16"}, {"name": "<PERSON><PERSON><PERSON>", "index": "0x00008003", "subindex": "0", "type": "uint32"}, {"name": "Module State", "index": "0x00008102", "subindex": "0", "type": "uint16"}, {"name": "<PERSON><PERSON><PERSON>", "index": "0x00008103", "subindex": "0", "type": "uint32"}], "pdo_mapping": {"rx_pdos": [{"index": "0x00001601", "entries": [{"index": "0x00007100", "subindex": "1", "name": "OutByte0", "type": "uint8"}, {"index": "0x00007100", "subindex": "2", "name": "OutByte1", "type": "uint8"}], "entryOffset": 0}], "tx_pdos": [{"index": "0x00001a00", "entries": [{"index": "0x00006000", "subindex": "1", "name": "InByte0", "type": "uint8"}, {"index": "0x00006000", "subindex": "2", "name": "InByte1", "type": "uint8"}, {"index": "0x00008002", "subindex": "0", "name": "Module State", "type": "uint16"}, {"index": "0x00008003", "subindex": "0", "name": "<PERSON><PERSON><PERSON>", "type": "uint32"}], "entryOffset": 2}, {"index": "0x00001a01", "entries": [{"index": "0x00008102", "subindex": "0", "name": "Module State", "type": "uint16"}, {"index": "0x00008103", "subindex": "0", "name": "<PERSON><PERSON><PERSON>", "type": "uint32"}], "entryOffset": 6}]}, "syncs": [{"index": 0, "direction": "OUTPUT", "pdos": [], "watchdog": "DISABLE"}, {"index": 1, "direction": "INPUT", "pdos": [], "watchdog": "DISABLE"}, {"index": 2, "direction": "OUTPUT", "pdos": ["0x00001601"], "watchdog": "ENABLE"}, {"index": 3, "direction": "INPUT", "pdos": ["0x00001a00", "0x00001a01"], "watchdog": "DISABLE"}], "exclude": {}}]}