﻿using System;
using System.IO;
using System.IO.MemoryMappedFiles;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Linq;

namespace EtherCATControl
{
    // 共享内存结构体
    [StructLayout(LayoutKind.Sequential)]
    public struct EtherCATSharedMemory
    {
        public int shm_slave0_online_status; // 从站0在线状态
        public int shm_slave0_operational_status; // 从站0运行状态
        public int shm_slave0_al_state; // 从站0AL状态

        public int shm_slave0_rx_0x00007100_outbyte0;
        public int shm_slave0_rx_0x00007100_outbyte1;
        public int shm_slave0_tx_0x00006000_inbyte0;
        public int shm_slave0_tx_0x00006000_inbyte1;
        public int shm_slave0_tx_0x00008002_module_state;
        public int shm_slave0_tx_0x00008003_module_err_num;
        public int shm_slave0_tx_0x00008102_module_state;
        public int shm_slave0_tx_0x00008103_module_err_num;
    }

    public class EtherCATController : IDisposable
    {
        private readonly MemoryMappedFile _memoryMappedFile;
        private readonly MemoryMappedViewAccessor _viewAccessor;
        private EtherCATSharedMemory _sharedMemory;

        // Linux RT 相关定义
        private enum SchedPolicy
        {
            SCHED_FIFO = 1
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct SchedParam
        {
            public int sched_priority;
        }

        [DllImport("libc", EntryPoint = "sched_setscheduler")]
        private static extern int sched_setscheduler(int pid, int policy, ref SchedParam param);

        [DllImport("libc", EntryPoint = "sched_get_priority_max")]
        private static extern int sched_get_priority_max(int policy);

        public EtherCATController(string EtherCATSharedMemoryFilePath)
        {
            _memoryMappedFile = MemoryMappedFile.CreateFromFile(EtherCATSharedMemoryFilePath, FileMode.Open);
            _viewAccessor = _memoryMappedFile.CreateViewAccessor();
            _sharedMemory = new EtherCATSharedMemory();
        }

        private void UpdateSharedMemory()
        {
            _viewAccessor.Read(0, out _sharedMemory);
        }

        private void WriteSharedMemory()
        {
            _viewAccessor.Write(0, ref _sharedMemory);
        }

        public bool WaitForIOReady(CancellationToken cancellationToken = default)
        {
            Console.WriteLine("等待远程IO就绪...");
            int timeoutMs = 5000; // 5秒超时
            int elapsed = 0;
            
            while (!cancellationToken.IsCancellationRequested && elapsed < timeoutMs)
            {
                UpdateSharedMemory();
                
                // 检查在线状态和运行状态
                if (_sharedMemory.shm_slave0_online_status == 1 && 
                    _sharedMemory.shm_slave0_operational_status == 1)
                {
                    Console.WriteLine("远程IO已就绪");
                    return true;
                }
                
                Thread.Sleep(100);
                elapsed += 100;
            }
            
            if (elapsed >= timeoutMs)
            {
                Console.WriteLine("等待远程IO就绪超时");
                Console.WriteLine($"在线状态: {_sharedMemory.shm_slave0_online_status}");
                Console.WriteLine($"运行状态: {_sharedMemory.shm_slave0_operational_status}");
                Console.WriteLine($"AL状态: {_sharedMemory.shm_slave0_al_state}");
            }
            
            return false;
        }

        // 将字节值可视化为实心圆(●)和空心圆(○)
        private string VisualizeBytePattern(byte value)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 7; i >= 0; i--)
            {
                bool isOn = (value & (1 << i)) != 0;
                sb.Append(isOn ? "●" : "○");
                if (i == 4) sb.Append(" "); // 每4位添加一个空格，增强可读性
            }
            return sb.ToString();
        }

        public void SetDigitalOutput(byte firstGroupOutputs, byte secondGroupOutputs, string description = "设置数字输出")
        {
            // 直接写入共享内存
            _sharedMemory.shm_slave0_rx_0x00007100_outbyte1 = firstGroupOutputs;   // 第一组8个输出
            _sharedMemory.shm_slave0_rx_0x00007100_outbyte0 = secondGroupOutputs;  // 第二组8个输出
            
            WriteSharedMemory();
            
            // 计算完整模式值
            int pattern = (firstGroupOutputs << 8) | secondGroupOutputs;
            
            // 显示输出状态
            Console.WriteLine($"设置数字输出: 0x{pattern:X4} (第一组:0x{firstGroupOutputs:X2}, 第二组:0x{secondGroupOutputs:X2})");
            Console.Write("状态位: ");
            for (int i = 15; i >= 0; i--)  // 从高位到低位显示，更符合阅读习惯
            {
                bool isOn = (pattern & (1 << i)) != 0;
                Console.Write(isOn ? "1" : "0");
                if (i == 8)
                    Console.Write(" ");
            }
            Console.WriteLine();
            
            // 可视化显示
            Console.WriteLine($"可视化状态: 第一组: {VisualizeBytePattern(firstGroupOutputs)} | 第二组: {VisualizeBytePattern(secondGroupOutputs)}");
            
            if (description != null)
            {
                Console.WriteLine($"描述: {description}");
            }
        }

        public (int inputPattern, byte firstGroupInputs, byte secondGroupInputs, int[] moduleStates, int[] errorNums) GetDigitalInputsAndStatus()
        {
            UpdateSharedMemory();
            
            // 直接读取输入字节
            byte secondGroupInputs = (byte)_sharedMemory.shm_slave0_tx_0x00006000_inbyte0;  // 第二组8个输入
            byte firstGroupInputs = (byte)_sharedMemory.shm_slave0_tx_0x00006000_inbyte1;   // 第一组8个输入
            
            // 合并为整数
            int inputPattern = (firstGroupInputs << 8) | secondGroupInputs;
            
            int[] moduleStates = new int[2] {
                _sharedMemory.shm_slave0_tx_0x00008002_module_state,
                _sharedMemory.shm_slave0_tx_0x00008102_module_state
            };
            
            int[] errorNums = new int[2] {
                _sharedMemory.shm_slave0_tx_0x00008003_module_err_num,
                _sharedMemory.shm_slave0_tx_0x00008103_module_err_num
            };
            
            // 显示输入状态
            Console.WriteLine($"读取数字输入: 0x{inputPattern:X4} (第一组:0x{firstGroupInputs:X2}, 第二组:0x{secondGroupInputs:X2})");
            Console.Write("输入状态位: ");
            for (int i = 15; i >= 0; i--)
            {
                bool isOn = (inputPattern & (1 << i)) != 0;
                Console.Write(isOn ? "1" : "0");
                if (i == 8)
                    Console.Write(" ");
            }
            Console.WriteLine();
            
            // 可视化显示
            Console.WriteLine($"可视化状态: 第一组: {VisualizeBytePattern(firstGroupInputs)} | 第二组: {VisualizeBytePattern(secondGroupInputs)}");
            
            return (inputPattern, firstGroupInputs, secondGroupInputs, moduleStates, errorNums);
        }

        public void EnterRealtimeMode()
        {
            var param = new SchedParam { sched_priority = sched_get_priority_max((int)SchedPolicy.SCHED_FIFO) };
            if (sched_setscheduler(0, (int)SchedPolicy.SCHED_FIFO, ref param) != 0)
            {
                Console.WriteLine("Warning: Failed to set SCHED_FIFO scheduler");
            }
        }

        public async Task RunBasicTest(CancellationToken cancellationToken)
        {
            try
            {
                if (!WaitForIOReady(cancellationToken))
                {
                    Console.WriteLine("IO未就绪，退出控制序列");
                    return;
                }

                Console.WriteLine("开始IO控制和监听...");

                // 设置默认输出状态
                SetDigitalOutput(0xDD, 0xDD, "默认输出状态 (0xDD = 11011101)");
                
                // 读取初始输入状态
                var (lastInputPattern, lastFirstGroupInputs, lastSecondGroupInputs, _, _) = GetDigitalInputsAndStatus();
                Console.WriteLine("开始监听输入变化...");
                
                // 无限循环监听输入
                while (!cancellationToken.IsCancellationRequested)
                {
                    // 短暂等待，避免CPU占用过高
                    await Task.Delay(50, cancellationToken);
                    
                    // 读取当前输入状态，但不打印，除非有变化
                    UpdateSharedMemory();
                    byte secondGroupInputs = (byte)_sharedMemory.shm_slave0_tx_0x00006000_inbyte0;
                    byte firstGroupInputs = (byte)_sharedMemory.shm_slave0_tx_0x00006000_inbyte1;
                    int currentInputPattern = (firstGroupInputs << 8) | secondGroupInputs;
                    
                    // 检查输入是否有变化
                    if (currentInputPattern != lastInputPattern)
                    {
                        Console.WriteLine("\n检测到输入变化!");
                        Console.WriteLine($"输入状态变化: 0x{lastInputPattern:X4} => 0x{currentInputPattern:X4}");
                        
                        // 显示哪些位发生了变化
                        Console.Write("变化的位: ");
                        int changedBits = lastInputPattern ^ currentInputPattern;
                        for (int i = 15; i >= 0; i--)
                        {
                            bool isChanged = (changedBits & (1 << i)) != 0;
                            Console.Write(isChanged ? "1" : "0");
                            if (i == 8)
                                Console.Write(" ");
                        }
                        Console.WriteLine();
                        
                        // 显示可视化的输入状态
                        Console.WriteLine($"当前可视化状态: 第一组: {VisualizeBytePattern(firstGroupInputs)} | 第二组: {VisualizeBytePattern(secondGroupInputs)}");
                        
                        // 更新上次状态
                        lastInputPattern = currentInputPattern;
                        lastFirstGroupInputs = firstGroupInputs;
                        lastSecondGroupInputs = secondGroupInputs;
                        
                        // 读取模块状态
                        int[] moduleStates = new int[2] {
                            _sharedMemory.shm_slave0_tx_0x00008002_module_state,
                            _sharedMemory.shm_slave0_tx_0x00008102_module_state
                        };
                        
                        int[] errorNums = new int[2] {
                            _sharedMemory.shm_slave0_tx_0x00008003_module_err_num,
                            _sharedMemory.shm_slave0_tx_0x00008103_module_err_num
                        };
                        
                        // 检查模块状态
                        for (int i = 0; i < moduleStates.Length; i++)
                        {
                            if (moduleStates[i] != 0)
                            {
                                Console.WriteLine($"模块 {i} 错误: State=0x{moduleStates[i]:X4}, ErrorNum={errorNums[i]}");
                            }
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("控制序列被取消");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"控制序列错误: {ex.Message}");
            }
            finally
            {
                // 清除所有输出
                SetDigitalOutput(0, 0, "清除所有输出");
                Console.WriteLine("控制序列结束");
            }
        }

        public void Dispose()
        {
            _viewAccessor?.Dispose();
            _memoryMappedFile?.Dispose();
        }
    }

    class Program
    {
        // 从站状态响应模型
        private class SlaveStatus
        {
            public int index { get; set; }
            public int master { get; set; }
            public string name { get; set; }
            public string state { get; set; }
            public bool online { get; set; }
            public int operationalStatus { get; set; }
            public int alState { get; set; }
            public string vendorId { get; set; }
            public string productCode { get; set; }
            //public string position { get; set; }
        }

        private class SlaveStatusResponse
        {
            public int totalCount { get; set; }
            public int opCount { get; set; }
            public int preopCount { get; set; }
            public int otherCount { get; set; }
            public List<SlaveStatus> slaves { get; set; }
        }

        private class SlaveStatusApiResponse
        {
            public int code { get; set; }
            public string msg { get; set; }
            public SlaveStatusResponse data { get; set; }
        }
        
        private class ShmFile 
        {
            public string shmFile { get; set; }
        }

        private class ApiResponse
        {
            public int code { get; set; }
            public string msg { get; set; }
            public ShmFile data { get; set; }
        }

        private static bool IsValidHexValue(string hex)
        {
            if (string.IsNullOrEmpty(hex)) return false;
            // 移除0x前缀后检查是否全为0
            string value = hex.StartsWith("0x") ? hex.Substring(2) : hex;
            return !value.All(c => c == '0');
        }

        private static async Task<bool> CheckSlavesStatus(HttpClient client)
        {
            const int MAX_RETRIES = 10;
            const int RETRY_DELAY_MS = 1000;

            for (int i = 0; i < MAX_RETRIES; i++)
            {
                try
                {
                    Console.WriteLine($"正在检查从站状态，第{i + 1}次尝试...");
                    
                    var response = await client.GetAsync(
                        "http://127.0.0.1:3000/api/ethercat/all-slaves-status?detail=1"
                    );

                    if (!response.IsSuccessStatusCode)
                    {
                        Console.WriteLine($"获取从站状态失败: {response.StatusCode}");
                        continue;
                    }

                    var responseContent = await response.Content.ReadAsStringAsync();
                    var statusResponse = JsonSerializer.Deserialize<SlaveStatusApiResponse>(responseContent);

                    if (statusResponse.code != 0)
                    {
                        Console.WriteLine($"API错误: {statusResponse.msg}");
                        continue;
                    }

                    var data = statusResponse.data;
                    
                    // 检查条件1：preopCount和totalCount是否相等
                    if (data.preopCount != data.totalCount)
                    {
                        Console.WriteLine($"从站状态不匹配: preopCount({data.preopCount}) != totalCount({data.totalCount})");
                        await Task.Delay(RETRY_DELAY_MS);
                        continue;
                    }

                    // 检查条件2：检查每个从站的vendorId和productCode
                    bool allSlavesValid = true;
                    foreach (var slave in data.slaves)
                    {
                        if (!IsValidHexValue(slave.vendorId) || !IsValidHexValue(slave.productCode))
                        {
                            Console.WriteLine($"从站 {slave.index} 的vendorId({slave.vendorId})或productCode({slave.productCode})无效");
                            allSlavesValid = false;
                            break;
                        }
                    }

                    if (allSlavesValid)
                    {
                        Console.WriteLine("从站状态检查通过");
                        return true;
                    }

                    await Task.Delay(RETRY_DELAY_MS);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"检查从站状态时发生错误: {ex.Message}");
                    await Task.Delay(RETRY_DELAY_MS);
                }
            }

            Console.WriteLine($"从站状态检查失败，已达到最大重试次数({MAX_RETRIES})");
            return false;
        }

        private static async Task<string> StartMiddleware(string programName)
        {
            try
            {
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json")
                );

                // 首先检查从站状态不再在这里进行
                // Console.WriteLine("开始检查从站状态...");
                // if (!await CheckSlavesStatus(client))
                // {
                //     Console.WriteLine("从站状态检查失败，无法继续");
                //     return null;
                // }

                var requestData = new { programName };
                var jsonContent = JsonSerializer.Serialize(requestData);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                Console.WriteLine($"正在请求EtherCAT中间层服务...");
                var response = await client.PostAsync(
                    "http://127.0.0.1:3000/api/programs/start-middleware", 
                    content
                );

                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"请求中间件失败: {response.StatusCode}");
                    return null;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"中间件返回数据: {responseContent}");

                // 解析JSON响应
                var responseData = JsonSerializer.Deserialize<ApiResponse>(responseContent);
                if (responseData.code != 0)
                {
                    string errorMsg = !string.IsNullOrEmpty(responseData.msg) ? responseData.msg : "未知错误";
                    Console.WriteLine($"错误：中间件返回错误码 {responseData.code}，错误信息：{errorMsg}");
                    return null;
                }

                if (responseData.data == null || string.IsNullOrEmpty(responseData.data.shmFile))
                {
                    Console.WriteLine("错误：从中间件获取的共享内存路径为空");
                    return null;
                }
                
                var sharedMemoryFilePath = responseData.data.shmFile;
                Console.WriteLine($"启动成功，共享内存文件路径: {sharedMemoryFilePath}");
                return sharedMemoryFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动中间层失败: {ex.Message}");
                return null;
            }
        }

        static async Task Main(string[] args)
        {
            try 
            {
                // 创建HTTP客户端
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json")
                );
                
                // 首先检查从站状态（现在直接在Main方法中调用）
                Console.WriteLine("开始检查从站状态...");
                if (!await CheckSlavesStatus(client))
                {
                    Console.WriteLine("从站状态检查失败，程序退出");
                    return;
                }
                
                string programName = Path.GetFileName(Process.GetCurrentProcess().MainModule?.FileName ?? "unknown");
                string sharedMemoryFilePath = await StartMiddleware(programName);
                
                if (string.IsNullOrEmpty(sharedMemoryFilePath))
                {
                    Console.WriteLine("无法获取共享内存路径，程序退出");
                    return;
                }
                
                Console.WriteLine($"启动EtherCAT控制程序...");
                
                using var controller = new EtherCATController(sharedMemoryFilePath);
                controller.EnterRealtimeMode();

                var cts = new CancellationTokenSource();

                // 添加SIGTERM信号处理
                AppDomain.CurrentDomain.ProcessExit += (s, e) => {
                    Console.WriteLine("收到SIGTERM信号，正在优雅退出...");
                    cts.Cancel();
                };

                Console.CancelKeyPress += (s, e) => {
                    e.Cancel = true;
                    cts.Cancel();
                };

                try
                {
                    await controller.RunBasicTest(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    Console.WriteLine("程序正在停止...");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"执行错误: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"程序启动错误: {ex.Message}");
            }
        }
    }
} 