{"format": 1, "restore": {"C:\\Users\\<USER>\\Pictures\\ServoCase\\examples\\HX_16I16O\\HX_16I16O.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Pictures\\ServoCase\\examples\\HX_16I16O\\HX_16I16O.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Pictures\\ServoCase\\examples\\HX_16I16O\\HX_16I16O.csproj", "projectName": "HX_16I16O", "projectPath": "C:\\Users\\<USER>\\Pictures\\ServoCase\\examples\\HX_16I16O\\HX_16I16O.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Pictures\\ServoCase\\examples\\HX_16I16O\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-arm64", "version": "[8.0.11, 8.0.11]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm64", "version": "[8.0.11, 8.0.11]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm64", "version": "[8.0.11, 8.0.11]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.404/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-arm64": {"#import": []}}}}}