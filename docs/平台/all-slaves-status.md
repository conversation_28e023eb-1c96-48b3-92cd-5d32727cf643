

## 从站状态接口

### 流程图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Router as 路由层<br/>/all-slaves-status
    participant Service as EthercatService
    participant System as 系统命令层

    Client->>+Router: GET /all-slaves-status<br/>?detail=0/1
    Note over Router: 解析detail参数<br/>默认值为0
    
    Router->>+Service: getAllSlavesStatus(detail)
    
    Service->>+System: 执行ethercat slaves命令
    System-->>-Service: 返回原始从站数据
    
    Service->>Service: 解析从站数据
    Note over Service: 统计从站总数量<br/>统计OP状态从站数量<br/>统计PREOP状态从站数量<br/>统计INIT状态从站数量<br/>统计未知状态从站数量
    
    alt detail = 0
        Service-->>Router: 返回统计数据
    else detail ≠ 0
        Service-->>Router: 返回详细从站信息
    end
    
    Router-->>-Client: 返回JSON响应<br/>{code, message, result}
    
    Note over Client,Router: 错误处理：<br/>1. 捕获所有异常<br/>2. 统一错误格式<br/>3. 返回适当状态码
```

### 案例

**获取从站信息**

访问

```http
http://127.0.0.1:3000/api/ethercat/all-slaves-status
```

返回

```json
{
  "code": 0,
  "message": "获取所有从站状态成功",
  "result": {
    "totalCount": 1,
    "opCount": 0,
    "preopCount": 1,
    "initCount": 0,
    "otherCount": 0
  }
}
```

**获取从站详细信息**

访问

```http
http://127.0.0.1:3000/api/ethercat/all-slaves-status?detail=1
```

返回

```json
{
  "code": 0,
  "message": "获取所有从站状态成功",
  "result": {
    "totalCount": 1,
    "opCount": 0,
    "preopCount": 1,
    "initCount": 0,
    "otherCount": 0,
    "slaves": [
      {
        "index": 0,
        "master": 0,
        "name": "Main",
        "state": "PREOP",
        "online": true,
        "operationalStatus": false,
        "alState": 2,
        "vendorId": "0x00850104",
        "productCode": "0x01030507",
        "position": "0:0"
      }
    ]
  }
}
```

