# 边缘控制器平台手册


| 文档信息 | 描述 |
| --- | --- |
| 文档版本 | V1.0.0 |
| 最后更新 | 2025-04-30 |

| 维护人 | 角色 | 联系方式 |
| --- | --- | --- |
| 唐伟峰 | 系统开发 | tang<PERSON><PERSON>@feixiansoft.com |
| 何俊言 | 系统开发 | <EMAIL> |

## 1. 功能模块概览

### 1.1 EtherCAT控制面板

![image-20250430092518614](D:\desktop\IPCEthercat\fxethercat\docs\平台手册.assets\image-20250430092518614.png)

| 功能模块 | 说明 | 是否必填 | 备注 |
| -------- | ---- | -------- | ---- |
| 服务状态 | 显示EtherCAT服务的运行状态 | - | 显示"运行中"或"已停止" |
| 主站总数 | 显示系统中配置的EtherCAT主站数量 | - | 统计信息 |
| 活动主站 | 显示当前活动的主站数量 | - | 统计信息 |
| 从站总数 | 显示连接到系统的从站设备总数 | - | 统计信息 |
| 刷新间隔 | 设置自动刷新状态的时间间隔 | 否 | 可选择手动刷新或1-10秒间隔 |
| 启动服务 | 启动EtherCAT主站服务 | - | 按钮操作 |
| 停止服务 | 停止EtherCAT主站服务 | - | 按钮操作 |
| 刷新状态 | 手动刷新EtherCAT状态信息 | - | 按钮操作 |
| 主站配置 | 配置EtherCAT主站网络设置 | - | 按钮操作，打开配置对话框 |
| 下载从站配置 | 下载当前从站配置文件 | - | 按钮操作 |

**注释说明：**

- **服务状态**：显示EtherCAT主站服务是否正在运行。绿色表示运行中，红色表示已停止。
- **主站总数**：显示当前系统中配置的EtherCAT主站总数。
- **活动主站**：显示当前处于活动状态的主站数量，用于监控主站健康状态。
- **从站总数**：显示所有连接到系统的EtherCAT从站设备总数。
- **刷新间隔**：设置自动更新EtherCAT状态信息的时间间隔，可选择手动刷新或1-10秒自动刷新。

#### 主站信息表格
显示所有主站的详细信息，包括：
- 主站序号
- 状态（如Idle等）
- 从站数量
- MAC地址
- 网口
- 连接状态（已连接/已断开）
- 统计信息（传输统计、速率统计）

**注释说明：**
- **主站信息**：显示每个主站的详细状态信息，包括状态码、从站数量、MAC地址等。

#### 从站信息表格
显示所有从站的详细信息，包括：
- 从站序号
- 设备名称
- Vendor ID
- Product code
- 状态
- 操作选项（详情、下载配置）

**注释说明：**
- **从站信息**：显示连接到系统的所有从站设备信息，包括设备型号、厂商ID、产品代码等。

### 1.2 程序管理

![image-20250430092544355](D:\desktop\IPCEthercat\fxethercat\docs\平台手册.assets\image-20250430092544355.png)

| 功能模块 | 说明 | 是否必填 | 备注 |
| -------- | ---- | -------- | ---- |
| 实时日志开关 | 控制是否显示程序的实时日志 | - | 开关按钮 |
| 程序自启 | 配置程序自动启动设置 | - | 按钮操作，打开配置对话框 |
| 刷新 | 刷新程序列表 | - | 按钮操作 |
| 上传程序 | 打开上传程序对话框 | - | 按钮操作 |

#### 程序列表表格
显示所有上传的程序信息，包括：
- 程序名称
- 主站索引
- 任务频率
- 从站JSON配置
- 上传时间
- 中间层状态
- 程序状态
- 操作选项（启动中间层、停止中间层、启动程序、停止程序、配置、下载、日志、删除）

**注释说明：**
- **中间层状态**：显示程序中间层的运行状态（运行中/已停止）。
- **程序状态**：显示用户程序的运行状态（运行中/已停止）。

#### 上传程序对话框

| 功能模块 | 说明 | 是否必填 | 备注 |
| -------- | ---- | -------- | ---- |
| 程序名称 | 输入要上传的程序名称 | 是 | 需手动输入 |
| 主站索引 | 选择主站编号（如 Master 0） | 是 | 下拉选择 |
| 任务频率 | 选择程序运行的任务频率 | 是 | 下拉选择，单位为Hz和μs |
| 程序压缩包 | 上传包含主程序及依赖文件的ZIP压缩包 | 是 | 需上传ZIP文件 |
| 配置文件（JSON） | 上传程序对应的配置文件，格式为JSON | 是 | 需上传JSON文件 |

**注释说明：**
- **程序名称**：用于标识上传的程序（需要与二进制程序名保持一致）。
- **主站索引**：指定程序将运行在哪个EtherCAT主站上。
- **任务频率**：设置程序的调度频率，影响程序的实时性能。常见选项包括：
  - 4000 Hz (250 μs)
  - 2000 Hz (500 μs)
  - 1000 Hz (1000 μs)
- **程序压缩包**：包含主程序及其所有依赖文件，必须打包为ZIP格式上传。
- **配置文件（JSON）**：程序运行所需的参数配置，必须为JSON格式文件。

#### 代码生成器

| 功能模块 | 说明 | 是否必填 | 备注 |
| -------- | ---- | -------- | ---- |
| 配置JSON | 输入从站配置JSON数据 | 是 | 文本区域输入 |
| 生成结果 | 显示生成的中间层代码 | - | 只读文本区域 |
| 复制代码 | 复制生成的代码到剪贴板 | - | 按钮操作 |
| 下载代码 | 下载生成的代码到本地 | - | 按钮操作 |

**注释说明：**
- **配置JSON**：输入从站配置的JSON数据，用于生成中间层代码。
- **生成结果**：显示根据配置生成的EtherCAT中间层代码。
- **复制/下载**：提供复制或下载生成代码的功能。

## 2. 操作流程

### 2.1 系统整体操作流程

```mermaid
%%{init: {'flowchart': {'nodeSpacing': 10, 'rankSpacing': 15}, 'theme': 'base', 'themeVariables': { 'fontSize': '10px'}}}%%
graph TD
    A["系统启动"] --> B["EtherCAT配置"]
    B --> C["启动服务(自启)"]
    C --> D{"启动成功?"}
    D -->|是| E["上传程序"]
    D -->|否| F["检查配置"]
    F --> B
    E --> G["配置参数"]
    G --> H["启动中间层"]
    H --> I{"中间层成功?"}
    I -->|是| J["启动用户程序"]
    I -->|否| K["检查错误"]
    K --> G
    J --> L["监控状态"]
    L --> M{"需要停止?"}
    M -->|是| N["停止程序"]
    N --> O["停止中间层"]
    M -->|否| L
```

### 2.2 启动EtherCAT服务流程

Ethercat服务初始化默认自启动

```mermaid
%%{init: {'flowchart': {'nodeSpacing': 8, 'rankSpacing': 12}, 'theme': 'base', 'themeVariables': { 'fontSize': '12px'}}}%%
graph LR
    A["进入EtherCAT控制面板"] --> B["点击'启动服务'按钮"]
    B --> C["等待服务启动"]
    C --> D{"状态是否变为'运行中'?"}
    D -->|是| E["查看主站和从站信息"]
    D -->|否| F["查看错误信息"]
    F --> G["检查网络配置"]
    G --> B
```

### 2.3 上传并运行程序流程

```mermaid
%%{init: {'flowchart': {'nodeSpacing': 8, 'rankSpacing': 12}, 'theme': 'base', 'themeVariables': { 'fontSize': '10px'}}}%%
graph TD
    A["进入程序管理"] --> B["点击上传按钮"]
    B --> C["填写基本信息"]
    C --> D["上传ZIP包和JSON"]
    D --> E["点击确定提交"]
    E --> F{"上传成功?"}
    F -->|是| G["启动中间层"]
    F -->|否| H["检查文件格式"]
    H --> D
    G --> I{"中间层启动成功?"}
    I -->|是| J["启动程序"]
    I -->|否| K["检查配置"]
    K --> G
    J --> L["程序运行"]
```

### 2.4 配置主站流程

```mermaid
%%{init: {'flowchart': {'nodeSpacing': 8, 'rankSpacing': 12}, 'theme': 'base', 'themeVariables': { 'fontSize': '14px'}}}%%
graph LR
    A["进入EtherCAT控制面板"] --> B["点击'主站配置'按钮"]
    B --> C["配置主站网络接口"]
    C --> D["点击'确定'保存配置"]
    D --> E["重启服务以应用更改"]
    E --> F{"配置是否生效?"}
    F -->|是| G["完成配置"]
    F -->|否| H["检查配置参数"]
    H --> C
```

## 3. 故障排除

### 3.1 服务启动失败
- 检查网络接口配置是否正确
- 查看平台日志获取详细错误信息

![image-20250430093023395](D:\desktop\IPCEthercat\fxethercat\docs\平台手册.assets\image-20250430093023395.png)

### 3.2 程序上传失败
- 检查ZIP文件格式是否正确
- 检查JSON配置文件格式是否有效
- 确保主站状态为"运行中"

### 3.3 程序运行错误
- 检查平台/程序日志信息
- 确认从站配置与实际硬件匹配
- 验证任务频率设置是否合适

## 4. 控制案例

本次控制案例中，从站为捷诺信远程IO设备，控制程序由C#语言开发。

### 4.1 前置条件

**1.控制程序二进制压缩包（zip）**

注意事项：
（1）二进制架构根据实际修改（当前为linux-arm64）

（2）压缩包格式为zip

（3）压缩包大小不超过200MB

**2.从站配置文件（json）**

（1）检查主从站信息

![image-20250430095102710](D:\desktop\IPCEthercat\fxethercat\docs\平台手册.assets\image-20250430095102710.png)

- 从站信息是否显示完整

  - （状态表格中都有数据）

  - （状态不能为未知，vendorid和productcode不能全0，此时代表信息读取不完整，重启 Ethercat 服务）

  - （从站数量是否与实际接线相等）

  ![image-20250430095129653](D:\desktop\IPCEthercat\fxethercat\docs\平台手册.assets\image-20250430095129653.png)

（2）通过从站 JSON 模板生成器生成 JSON，下载 JSON 文件

![image-20250430095149942](D:\desktop\IPCEthercat\fxethercat\docs\平台手册.assets\image-20250430095149942.png)

![image-20250430095219271](D:\desktop\IPCEthercat\fxethercat\docs\平台手册.assets\image-20250430095219271.png)

![image-20250430095247765](D:\desktop\IPCEthercat\fxethercat\docs\平台手册.assets\image-20250430095247765.png)

### 4.2 程序上传

（1）点击左侧程序管理菜单，点击上传程序按钮

![image-20250430095323984](D:\desktop\IPCEthercat\fxethercat\docs\平台手册.assets\image-20250430095323984.png)

（2）完成上传表单

![image-20250430095443579](D:\desktop\IPCEthercat\fxethercat\docs\平台手册.assets\image-20250430095443579.png)

### 4.3 程序启动

（1）点击启动程序按钮等待启动

![image-20250430095459380](D:\desktop\IPCEthercat\fxethercat\docs\平台手册.assets\image-20250430095459380.png)

![image-20250430095528278](D:\desktop\IPCEthercat\fxethercat\docs\平台手册.assets\image-20250430095528278.png)

## 5. 常见问题解答

```mermaid
%%{init: {'flowchart': {'nodeSpacing': 10, 'rankSpacing': 50}, 'theme': 'base', 'themeVariables': { 'fontSize': '26px', 'fontWeight': 'bold'}}}%%
graph TD
    A["问题诊断"] --> B{"EtherCAT服务无法启动"}
    A --> C{"程序上传失败"}
    A --> D{"程序运行异常"}
    
    B --> B1["检查网络接口配置"]
    B --> B3["查看平台日志"]
    
    C --> C1["检查ZIP文件格式"]
    C --> C2["检查JSON配置有效性"]
    C --> C3["确认主站状态"]
    
    D --> D1["查看平台/程序日志"]
    D --> D2["检查从站配置匹配"]
    D --> D3["检查任务频率设置"]
```
