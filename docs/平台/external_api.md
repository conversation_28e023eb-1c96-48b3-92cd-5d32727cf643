# 边缘控制器平台外部调用API文档

| 文档信息 | 描述 |
| --- | --- |
| 文档版本 | V1.0.0 |
| 最后更新 | 2025-04-30 |

| 维护人 | 角色 | 联系方式 |
| --- | --- | --- |
| 唐伟峰 | 系统开发 | <EMAIL> |
| 何俊言 | 系统开发 | <EMAIL> |

## 通用说明

### 基础URL
```
http://IP:3000/
```

### 返回格式
所有接口返回格式统一为：
```json
{
    "code": 0,      // 0表示成功，非0表示失败
    "msg": "",  // 成功或错误提示信息
    "data": {}    // 返回的数据，失败时为空对象
}
```

## 接口列表

### 1. 上传程序
上传EtherCAT程序及其配置文件。

- **接口**: `/api/programs/external-upload`
- **方法**: POST
- **Content-Type**: multipart/form-data

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| program | File | 是 | 程序文件（.zip格式） |
| config | File | 是 | 配置文件（.json格式） |
| name | String | 是 | 程序名称 |
| masterIndex | Number | 是 | 主站索引 |
| force | String | 否 | 是否强制上传，'1'表示是，'0'表示否 |

#### 返回示例
```json
{
    "code": 0,
    "msg": "程序上传成功",
    "data": {}
}
```

### 2. 启动中间层
启动指定程序的中间层服务。

- **接口**: `/api/programs/start-middleware`
- **方法**: POST
- **Content-Type**: application/json

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| programName | String | 是 | 程序名称 |

#### 返回示例
```json
{
    "code": 0,
    "msg": "中间层启动成功",
    "data": {
        "shmFile": "/dev/shm/ethercat_xxx"
    }
}
```

### 3. 停止中间层
停止指定程序的中间层服务。

- **接口**: `/api/programs/stop-middleware`
- **方法**: POST
- **Content-Type**: application/json

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| programName | String | 是 | 程序名称 |

#### 返回示例
```json
{
    "code": 0,
    "msg": "中间层服务已停止",
    "data": {}
}
```

### 4. 启动程序
启动指定的EtherCAT程序。

- **接口**: `/api/programs/start-program`
- **方法**: POST
- **Content-Type**: application/json

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| programName | String | 是 | 程序名称 |

#### 返回示例
```json
{
    "code": 0,
    "msg": "程序启动成功",
    "data": {}
}
```

### 5. 停止程序
停止指定的EtherCAT程序。

- **接口**: `/api/programs/stop-program`
- **方法**: POST
- **Content-Type**: application/json

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| programName | String | 是 | 程序名称 |

#### 返回示例
```json
{
    "code": 0,
    "msg": "程序已停止",
    "data": {}
}
```

### 6. 替换配置
替换指定程序的配置文件。

- **接口**: `/api/programs/replace-config`
- **方法**: POST
- **Content-Type**: multipart/form-data

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| programName | String | 是 | 程序名称 |
| config | File | 是 | 新的配置文件（.json格式） |

#### 返回示例
```json
{
    "code": 0,
    "msg": "配置更新成功",
    "data": {}
}
```

### 7. 获取从站信息
获取所有从站的状态信息统计。

- **接口**: `/api/ethercat/all-slaves-status`
- **方法**: GET
- **Content-Type**: application/json

#### 请求参数
无

#### 返回示例
```json
{
    "code": 0,
    "msg": "获取所有从站状态成功",
    "data": {
        "totalCount": 1,
        "opCount": 0,
        "preopCount": 1,
        "initCount": 0,
        "otherCount": 0
    }
}
```

#### 基础信息

| 字段       | 类型   | 说明                        |
| ---------- | ------ | --------------------------- |
| totalCount | Number | 从站总数量                  |
| opCount    | Number | 处于OP（运行）状态数量      |
| preopCount | Number | 处于PREOP（预运行）状态数量 |
| initCount  | Number | 处于INIT（初始化）状态数量  |
| otherCount | Number | 处于未知状态数量            |

### 8. 获取从站详细信息

获取所有从站的详细状态信息，包括每个从站的具体状态。

- **接口**: `/api/ethercat/all-slaves-status?detail=1`
- **方法**: GET
- **Content-Type**: application/json

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| detail | Number | 是 | 仅当设置为1时返回详细信息 |

#### 返回示例
```json
{
    "code": 0,
    "msg": "获取所有从站状态成功",
    "data": {
        "totalCount": 1,
        "opCount": 0,
        "preopCount": 1,
        "preopCount": 0,
        "otherCount": 0,
        "slaves": [
            {
                "index": 0,
                "master": 0,
                "name": "Main",
                "state": "PREOP",
                "online": true,
                "operationalStatus": false,
                "alState": 2,
                "vendorId": "0x00850104",
                "productCode": "0x01030507",
                "position": "0:0"
            }
        ]
    }
}
```

#### 详细信息

| 字段              | 类型    | 说明                                                         |
| ----------------- | ------- | ------------------------------------------------------------ |
| index             | number  | 从站索引                                                     |
| master            | number  | 主站编号                                                     |
| name              | string  | 从站名称                                                     |
| state             | string  | 从站当前状态（OP-运行，PREOP-预运行，INIT-初始化，SAFEOP-安全操作） |
| online            | boolean | 是否在线（处于preop、op或safeop状态即为在线）                |
| operationalStatus | number  | 是否处于OP状态（1表示是，0表示否                             |
| alState           | number  | AL状态码（INIT-1，PREOP-2，SAFEOP-4，OP-8，未知-0）          |
| vendorId          | string  | 厂商ID                                                       |
| productCode       | string  | 产品代码                                                     |
| position          | string  | 从站位置（格式：主站号：从站号）                             |

## 调用示例

### Python示例
```python
import requests
import os

# 上传程序示例
def upload_program(program_path, config_path, program_name, master_index, force=False):
    url = "http://127.0.0.1:3000/api/programs/external-upload"
    
    files = {
        'program': ('WC.zip', open(program_path, 'rb'), 'application/zip'),
        'config': ('slave.json', open(config_path, 'rb'), 'application/json')
    }
    
    data = {
        'name': program_name,
        'masterIndex': master_index,
        'force': '1' if force else '0'
    }
    
    try:
        response = requests.post(url, files=files, data=data)
        data = response.json()
        if data['code'] == 0:
            print("程序上传成功")
            return data
        else:
            print("程序上传失败:", data['msg'])
            return None
    except Exception as e:
        print("Error:", str(e))
        return None
    finally:
        for file in files.values():
            file[1].close()

# 启动中间层示例
def start_middleware(program_name):
    url = "http://127.0.0.1:3000/api/programs/start-middleware"
    data = {
        "programName": program_name
    }
    
    try:
        response = requests.post(url, json=data)
        data = response.json()
        if data['code'] == 0:
            print("中间层启动成功")
            return data
        else:
            print("中间层启动失败:", data['msg'])
            return None
    except Exception as e:
        print("Error:", str(e))
        return None

# 停止中间层示例
def stop_middleware(program_name):
    url = "http://127.0.0.1:3000/api/programs/stop-middleware"
    data = {
        "programName": program_name
    }
    
    try:
        response = requests.post(url, json=data)
        data = response.json()
        if data['code'] == 0:
            print("中间层停止成功")
            return data
        else:
            print("中间层停止失败:", data['msg'])
            return None
    except Exception as e:
        print("Error:", str(e))
        return None

# 获取从站信息示例
def get_slaves_status():
    url = "http://127.0.0.1:3000/api/ethercat/all-slaves-status"
    
    try:
        response = requests.get(url)
        data = response.json()
        if data['code'] == 0:
            print("获取从站信息成功")
            return data
        else:
            print("获取从站信息失败:", data['msg'])
            return None
    except Exception as e:
        print("Error:", str(e))
        return None

# 获取从站详细信息示例
def get_slaves_detail():
    url = "http://127.0.0.1:3000/api/ethercat/all-slaves-status?detail=1"
    
    try:
        response = requests.get(url)
        data = response.json()
        if data['code'] == 0:
            print("获取从站详细信息成功")
            return data
        else:
            print("获取从站详细信息失败:", data['msg'])
            return None
    except Exception as e:
        print("Error:", str(e))
        return None
```

## 注意事项
1. 接口调用需要在本地网络或者局域网网络环境中进行
2. 文件上传接口需要确保文件格式正确
3. 程序名称不能重复
4. 按照 上传程序 ->启动程序 -> 启动中间层  的顺序进行操作
6. 所有接口返回的 code 为 0 表示成功，非 0 表示失败
