
![](https://web-api.textin.com/ocr_image/external/ff03d039758c61e3.jpg)


![](https://web-api.textin.com/ocr_image/external/1f8348620eb924f4.jpg)

A  572959011 N C

(21)申请号 201880055172.5

(22)申请日 2018.08.02

(30)优先权数据

102017214893.4 2017.08.25 DE

(85)PCT国际申请进入国家阶段日

2020.02.25

(86)PCT国际申请的申请数据

PCT/EP2018/071019 2018.08.02

(87)PCT国际申请的公布数据

WO2019/038047 DE 2019.02.28

(71)申请人 伦茨自动化有限责任公司地址 德国埃尔岑

(72)发明人 A.朗格(72)发明人 A.朗格　L.屈内　T.尼曼L.屈内 T.尼曼

(74)专利代理机构 中国专利代理(香港)有限公司 72001

代理人 孙云汉　刘春元

(51)Int.Cl.

H04L 12/40(2006.01)

权利要求书1页 说明书3页 附图1页

## (54)发明名称

用于运行EtherCAT现场总线系统的方法和EtherCAT现场总线系统

## (57)摘要


![](https://web-api.textin.com/ocr_image/external/e9a149cc7581be64.jpg)

一种用于运行EtherCAT现场总线系统(1)的方法,其中所述EtherCAT现场总线系统(1)具有EtherCAT主机(2)和多个EtherCAT从机(3),其中所述EtherCAT主机(2)和所述多个EtherCAT从机(3)借助于EtherCAT现场总线(4)来彼此耦合,用于进行数据交换,其中该方法具有如下步骤:借助于EtherCAT主机(2)来确定活跃的EtherCAT从机(3);借助于所述主机(2)来询问活跃的EtherCAT从机(3)的相应的产品代码(6);借助于EtherCAT主机(2),根据所述相应的产品代码(6)来确定所述活跃的EtherCAT从机(3)的相应的设备标志符(7);而且如果所述活跃的EtherCAT从机(3)的相应的设备标志符(7)与预先给定的设备标志符(8)一致,则借助于EtherCAT主机(2)将EtherCAT现场总线系统(1)的状态切换到可运行状态。


![](https://web-api.textin.com/ocr_image/external/2ea7bb6f7f78957f.jpg)

1.一种用于运行EtherCAT现场总线系统(1)的方法,其中所述EtherCAT现场总线系统(1)具有EtherCAT主机(2)和多个EtherCAT从机(3),其中所述EtherCAT主机(2)和所述多个EtherCAT从机(3)借助于EtherCAT现场总线(4)来彼此耦合,用于进行数据交换,其中所述方法具有如下步骤:

-  借助于所述EtherCAT主机(2)来确定活跃的EtherCAT从机(3);

-  借助于所述主机(2)来询问所述活跃的EtherCAT从机(3)的相应的产品代码(6)

-  借助于所述EtherCAT主机(2),根据所述相应的产品代码(6)来确定所述活跃的EtherCAT从机(3)的相应的设备标志符(7);而且

-  如果所述活跃的EtherCAT从机(3)的相应的设备标志符(7)与预先给定的设备标志符一致,则将所述EtherCAT现场总线系统(1)的状态切换到可运行状态。

## 2.根据权利要求1所述的方法,其特征在于如下步骤

-  借助于所述EtherCAT主机(2)来读取ENI文件(5),其中在所述ENI文件(5)中包含所述多个EtherCAT从机(3)的产品代码(6),其中在所述产品代码(6)中包含所述多个EtherCAT从机(3)的相应的预先给定的设备标志符(7)。

## 3.根据权利要求1或2所述的方法,其特征在于,

-  所述相应的产品代码(6)具有所述设备标志符(7)和应用标志符(8)。

4.根据上述权利要求之一所述的方法,其特征在于

-  在借助于所述EtherCAT主机(2)来询问所述活跃的EtherCAT从机(3)的产品代码(6)时,所述活跃的EtherCAT从机(3)将它们的相应的产品代码(6)和所属的产品代码掩码(9)传输给所述EtherCAT主机(2),其中所述EtherCAT主机(2)通过相应的产品代码(6)与分别所属的产品代码掩码(9)的逻辑关联来确定所述相应的设备标志符(7)。

## 5.根据权利要求3或4所述的方法,其特征在于如下步骤:

-  借助于所述EtherCAT主机(2),根据所述相应的产品代码(6)来确定所述活跃的EtherCAT从机(3)的相应的应用标志符(8)。

## 6.一种EtherCAT现场总线系统(1),其具有:

-  EtherCAT主机(2);和

-  多个EtherCAT从机(3),

-  其中所述EtherCAT主机(2)和所述多个EtherCAT从机(3)借助于EtherCAT现场总线(4)来彼此耦合,用于进行数据交换,

其特征在于,

-  所述EtherCAT主机(2)和所述多个EtherCAT从机(3)构造为实施根据上述权利要求之一所述的方法。

## 用于运行EtherCAT现场总线系统的方法和EtherCAT现场总线

## 系统

### 技术领域

[0001]本发明涉及一种用于运行EtherCAT现场总线系统的方法和一种EtherCAT现场总线系统。

## 背景技术

[0002]对于EtherCAT(ETC)总线通信来说,EtherCAT主机需要预先给定的配置、即所谓的ENI文件,在该ENI文件中提及具有相对应的从机及其识别特征和ETC特性的总线拓扑。

[0003]常规地,EtherCAT主机对在总线上的EtherCAT从机进行识别并且将所识别出的特性与在ENI文件中的相对应的说明进行比较,以便可以用所识别出的EtherCAT从机的其它特性来对这些所识别出的EtherCAT从机进行参数化。在这种情况下,EtherCAT从机的识别特征是:供应商ID(VendorID)、产品代码(ProductCode)和修订版本(Revision)。如果所找到的关于所识别出的EtherCAT从机方面的总线配置与在ENI文件中的总线配置不匹配,则对总线的启动导致“总线不匹配(Busmismatch)”故障。此外,也应参阅有关的EtherCAT规范,尤其是参阅在那里使用的术语,尤其是有关术语EtherCAT主机、EtherCAT从机、产品代码、可运行(Operational)等等方面的术语。

## 发明内容

[0004]本发明所基于的任务在于:提供一种用于运行EtherCAT现场总线系统的方法和一种EtherCAT现场总线系统,该方法和该EtherCAT现场总线系统能够实现该EtherCAT现场总线系统的安全的启动。

[0005]本发明通过根据权利要求1所述的用于运行EtherCAT现场总线系统的方法和根据权利要求6所述的EtherCAT现场总线系统来解决该任务。

[0006]该方法用于运行该EtherCAT现场总线系统。

[0007]常规地,该EtherCAT现场总线系统具有EtherCAT主机和多个(例如1至100个)EtherCAT从机。

[0008]常规地,该EtherCAT主机和多个EtherCAT从机借助于EtherCAT现场总线来彼此耦合,用于进行数据交换。

[0009]该方法具有如下步骤。

[0010]首先,该EtherCAT主机确定在EtherCAT现场总线上的活跃的或接通的EtherCAT从机,其方式是该EtherCAT主机例如输出对EtherCAT现场总线的询问并且监控哪些EtherCAT 从机进行应答。

[0011]接着,该EtherCAT主机询问活跃的EtherCAT从机的相应的产品代码

[0012]接着,该EtherCAT主机根据相应的产品代码来确定这些活跃的EtherCAT从机的相应的设备标志符。

[0013]如果这些活跃的EtherCAT从机的(所有)相应的设备标志符与预先给定的设备标

志符一致,则EtherCAT现场总线系统整体上的状态(总线状态)可以借助于EtherCAT主机转变成可运行状态。否则,例如执行故障处理。换言之,在后续的产品代码检查成功的情况下,该EtherCAT主机切换到可运行总线状态。

[0014]预先给定的设备标志符可以对于所有EtherCAT从机来说都相同或者可以对于这些EtherCAT从机来说特定。

[0015]替选地,只有其相应的设备标志符与预先给定的设备标志符一致的那些EtherCAT 从机能借助于该EtherCAT主机转变成可运行状态。

[0016]按照一个实施方式,该EtherCAT主机读取ENI文件,其中在该ENI文件中包含所述多个EtherCAT从机的产品代码,其中在这些产品代码中包含所述多个EtherCAT从机的相应的预先给定的设备标志符。

[0017]按照一个实施方式,相应的产品代码具有设备标志符和应用标志符。

[0018]按照一个实施方式,在借助于该EtherCAT主机来询问活跃的EtherCAT从机的产品代码时,这些活跃的EtherCAT从机将它们的相应的产品代码和所属的产品代码掩码传输给该EtherCAT主机,其中该EtherCAT主机通过相应的产品代码与分别所属的产品代码掩码的逻辑关联来确定相应的设备标志符。

[0019]按照一个实施方式,该EtherCAT主机根据相应的产品代码来确定这些活跃的EtherCAT从机的相应的应用标志符。

[0020]按照本发明的EtherCAT现场总线系统具有EtherCAT主机和多个EtherCAT从机,其中该EtherCAT主机和所述多个EtherCAT从机借助于EtherCAT现场总线来彼此耦合,用于进行数据交换。该EtherCAT主机和所述多个EtherCAT从机分别构造为实施根据上述权利要求之一所述的方法。

## 附图说明

[0021]本发明随后参考附图详细地予以描述。在这种情况下:

图1示出了按照本发明的EtherCAT现场总线系统;而

图2示范性地示出了包含设备标志符和应用标志符的产品代码以及所属的产品代码掩码,它们在图1的EtherCAT现场总线系统中使用。

## 具体实施方式

[0022]图1示出了EtherCAT现场总线系统1,该EtherCAT现场总线系统具有EtherCAT主机2、多个(在当前情况下示范性地是三个)EtherCAT从机3以及诊断和/或调试装置(EtherCAT 配置工具(EtherCAT  Configuration  Tool))10,其中EtherCAT主机2和多个EtherCAT从机3借助于EtherCAT现场总线系统1的EtherCAT现场总线4来彼此耦合,用于进行数据交换。

[0023]对EtherCAT现场总线系统1的启动、起动或开动具有如下步骤。

[0024]EtherCAT主机2确定活跃的EtherCAT从机3并且接着询问活跃的EtherCAT从机3的相应的产品代码6,参见图2。

[0025]EtherCAT主机2在使用产品代码掩码9的情况下(参见图2)根据相应的产品代码6按顺序来确定活跃的EtherCAT从机3的相应的设备标志符7(参见图2),而且如果活跃的EtherCAT从机3的相应的设备标志符7与EtherCAT主机2从ENI文件5中读出的预先给定的设

备标志符一致,则将EtherCAT现场总线系统1的状态切换到可运行状态。

[0026]这些产品代码和关于EtherCAT从机3的其它信息包含在相应的EtherCAT从机信息(ESI)文件11中。常规地,借助于诊断和/或调试装置10来生成ENI文件5和ESI文件11。

[0027]按照本发明的系统总线处理基于制造商Acontis的所谓的“SuperSet  ENI”的概念,然而EtherCAT主机的产品代码检查(部分)关闭。在该方法的情况下,ENI文件具有总线的带有所要预期到的从机(例如15个)的完整版本。在EtherCAT主机初始化之后,通过总线扫描来识别当前有多少个从机接在总线上(例如3个)。在下一步,多个不存在的从机从ENI 文件中被滤出并且在接下来的启动时不继续予以考虑。按照本发明,在EtherCAT主机栈中的常规的1:1产品代码检查被停用,因为所连接的EtherCAT从机由于它们的所设定的技术应用而可能发生变化,这些技术应用在应用标志符中呈现。

[0028]参考图2,按照本发明的EtherCAT从机的产品代码6可以根据固定样式来构造。在产品代码6的第一部分中例如包含设备标志符7,而被激活的技术应用以其应用标志符8在第二部分中反映出来。

[0029]为了掩蔽应用标志符8,可以设置产品代码掩码9。产品代码掩码9也可用于标识其它设备特征,所述其它设备特征在其中一个产品代码6中编码。

[0030]每个被配置的EtherCAT从机3都提供所要预期到的“默认(default)”的产品代码6以及产品代码掩码9。借此,EtherCAT驱动程序可以将“默认”的和找到的产品代码掩蔽。在一致的情况下,涉及支持通用映射的系统总线兼容的EtherCAT从机。

[0031]按照本发明,可能的是:现场总线成员在没有ENI文件的配置的情况下、与EtherCAT从机的数目(直至最大定义数目)和这些EtherCAT从机的EtherCAT应用代码无关地并且在事先没有对这些从机的非易失性地址(例如第二站地址)进行地址分配的情况下启动到可运行状态,其中保证了也只有兼容的EtherCAT从机在EtherCAT现场总线上连接。

[0032]借此,还可以加载不同的技术应用,这些技术应用可以通过通用EtherCAT接口来激活。基于在现场总线成员上所选择的技术应用,产品代码可能发生变化,以便可以标识该现场总线成员连同所设定的技术应用。

[0033]相比于EtherCAT“HotConnect”方法,按照本发明的方法提供了优点,因为按照本发明并不需要事先对成员的非易失性地址进行地址分配。

[0034]还不需要的是:除了所分配的非易失性地址之外,所找到的从机的产品代码也必须与所配置的产品代码1:1一致。


![](https://web-api.textin.com/ocr_image/external/6969186f3cf03bf9.jpg)

<!-- 2 10 5 4 11 11 11 3 4 3 4 3  -->
![](https://web-api.textin.com/ocr_image/external/bfe96ab44794bc71.jpg)

图1

<!-- 7 9 8 1111 0000 6 1111 0101 1111 0000  -->
![](https://web-api.textin.com/ocr_image/external/0dfa730b246bac7a.jpg)

图2



