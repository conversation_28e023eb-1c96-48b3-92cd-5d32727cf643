
![](https://web-api.textin.com/ocr_image/external/2519a455bf1010d5.jpg)


![](https://web-api.textin.com/ocr_image/external/a27f81056430fad7.jpg)

<!--  U 3 2 2 0 4 5 5 0 2 N C  -->
![](https://web-api.textin.com/ocr_image/external/cd593f06226112c8.jpg)

(51)Int.Cl.

(21)申请号 201620071177.5

(22)申请日 2016.01.25

(73)专利权人海天塑机集团有限公司

地址315801 浙江省宁波市北仑区小港海天路1688 号

(72)发明人卢鸥 焦晓龙 朱宁迪 何挺谢子方 李剑日 徐百里 杨光廖丽丹 张奇之 王伟

(74)专利代理机构宁波市鄞州盛飞专利代理事务所( 普通合伙) 33243

代理人张向飞

G05B 19/418(2006.01)

(ESM)同样的发明创造已同日申请发明专利

权利要求书2页 说明书5页 附图3页

## (54) 实用新型名称

一种EtherCAT 主从站一体控制卡及控制系统

## (57) 摘要

本实用新型公开一种EtherCAT 主从站一体控制卡及控制系统,其包括:EtherCAT 从站,与第三方EtherCAT 主站设备相连,用以接收第三方EtherCAT 主站设备输出的第一数据;EtherCAT 主站,与一个或多个第三方EtherCAT 从站设备相连;EtherCAT 从站控制器以及控制模块;控制模块包括: 第一处理模块,接收EtherCAT 从站输出的第一数据将该第一数据转换为可输出至EtherCAT 从站设备的第二数据将该第二数据按照预设处理方法处理后输出至EtherCAT 主站;EtherCAT 主站将该第二数据转发至一个或多个第三方EtherCAT 从站设备。降低第三方EtherCAT 主站设备与第三方EtherCAT 从站设备在控制算法上的开发成本,有效防止企业自有的核心算法通过第三方EtherCAT 主站设备或第三方EtherCAT 从站设备泄露。


![](https://web-api.textin.com/ocr_image/external/604afb2dbee7da6f.jpg)

1.一种EtherCAT主从站一体控制卡,其特征在于:包括:

EtherCAT从站,与第三方EtherCAT主站设备相连,用以接收第三方EtherCAT主站设备输出的第一数据;

EtherCAT主站,与一个或多个第三方EtherCAT从站设备相连;

EtherCAT从站控制器以及控制模块

所述控制模块包括:

第一处理模块,接收EtherCAT从站输出的第一数据将该第一数据转换为可输出至第三方EtherCAT从站设备的第二数据,将该第二数据按照预设处理方法处理后输出至EtherCAT 主站;EtherCAT主站将该第二数据转发至一个或多个第三方EtherCAT从站设备。

2.根据权利要求1所述的EtherCAT主从站一体控制卡,其特征在于:所述EtherCAT主站还接收一个或多个第三方EtherCAT从站设备输出的第三数据;

所述控制模块还包括

第二处理模块,接收EtherCAT主站输出的第三数据并将该数据转换为可输出至第三方EtherCAT主站设备的第四数据;EtherCAT从站接收该第四数据并将该第四数据转发至第三方EtherCAT主站设备。

3.根据权利要求1所述的EtherCAT主从站一体控制卡,其特征在于:所述预设处理方法可通过外部通讯设备存入控制模块中。

4.根据权利要求1或2所述的EtherCAT主从站一体控制卡,其特征在于:所述控制模块内还存储有数据映射表,所述数据映射表将多个第三方EtherCAT从站设备的数据信息映射为控制模块自定义的数据信息。

5.根据权利要求4所述的EtherCAT主从站一体控制卡,其特征在于:所述多个第三方EtherCAT从站设备的数据信息包括多个第三方EtherCAT从站设备的运动状态数据信息和/或控制指令数据信息;所述多个第三方EtherCAT从站设备相同运动状态和/或控制指令对应的运动状态数据信息和/或控制指令数据信息相同或不同。

6.根据权利要求1所述的EtherCAT主从站一体控制卡,其特征在于:所述EtherCAT主站扫描多个第三方EtherCAT从站设备的时钟信号并获取最远端的第三方EtherCAT从站设备的时钟信号;当所述EtherCAT主站转发第二数据至多个第三方EtherCAT从站设备时,所述多个第三方EtherCAT从站设备根据各自的时钟信号与最远端的第三方EtherCAT从站设备的时钟信号比较而后延时一时间再输出控制信号以使得多个第三方EtherCAT从站设备同步输出控制信号。

7.根据权利要求1所述的EtherCAT主从站一体控制卡,其特征在于:所述第三方EtherCAT主站设备为注塑机控制器,所述第三方EtherCAT从站设备为伺服驱动器。

8.根据权利要求7所述的EtherCAT主从站一体控制卡,其特征在于:所述注塑机控制器输出注塑机压力或流量控制数据,所述第一处理模块处理后输出伺服驱动器转速或扭矩数据。

9.根据权利要求1所述的EtherCAT主从站一体控制卡,其特征在于:所述控制模块还连接有模拟量输入输出接口和/或数字量输入输出接口和/或外部SRAM扩展接口和/或CAN、RS485、RS232通讯接口和/或USB接口。

10.一种EtherCAT主从站一体控制系统,其特征在于:包括

第三方EtherCAT主站设备;

一个或多个权利要求1至9任一项所述的EtherCAT主从站一体控制卡;

一个或多个第三方EtherCAT从站设备;

所述第三方EtherCAT主站设备通过EtherCAT总线串联一个或多个EtherCAT主从站一体控制卡,所述每一EtherCAT主从站一体控制卡通过EtherCAT总线串联一个或多个第三方EtherCAT从站设备;或者所述第三方EtherCAT主站设备通过EtherCAT总线串联一个或多个EtherCAT主从站一体控制卡以及第三方EtherCAT从站设备,所述每一EtherCAT主从站一体控制卡通过EtherCAT总线串联一个或多个第三方EtherCAT从站设备。

## 一种EtherCAT主从站一体控制卡及控制系统

### 技术领域

[0001]本实用新型涉及工业现场总线控制技术领域,尤其涉及一种EtherCAT主从站一体控制卡及控制系统。

## 背景技术

[0002]EtherCAT(以太网控制自动化技术)是一个以以太网为基础的开放架构的现场总线系统,最初由德国倍福自动化有限公司(Beckhoff Automation GmbH)研发。EtherCAT为系统的实时性能和拓扑的灵活性树立了新的标准,同时,它还符合甚至降低了现场总线的使用成本。

[0003]现有的EtherCAT总线控制系统通常包括一个EtherCAT主站设备和多个EtherCAT 从站设备,多个EtherCAT从站设备通过EtherCAT总线串联后整体通过EtherCAT总线接入EtherCAT主站设备。例如公开号为CN103529804A的中国专利“一种基于EtherCAT总线的分布式控制系统”,其即采用该种控制方式。

[0004]但在使用第三方EtherCAT主站设备和EtherCAT从站设备时,若将企业的核心技术算法、工艺参数等存放于第三方EtherCAT主站设备和EtherCAT从站设备上,一方面会加大第三方EtherCAT主站设备和EtherCAT从站设备的性能要求,另一方面也存在技术秘密泄露的安全隐患。

## 实用新型内容

[0005]本实用新型所要解决的技术问题在于,针对现有技术的上述不足,提出一种可减小第三方EtherCAT主站设备和第三方EtherCAT从站设备开发性能需求,防止存放于第三方EtherCAT主站设备和第三方EtherCAT从站设备上的核心技术算法、工艺参数等泄露的EtherCAT主从站一体控制卡。

[0006]本实用新型解决其技术问题采用的技术方案是,提出一种EtherCAT主从站一体控制卡,其包括:

[0007]EtherCAT从站,与第三方EtherCAT主站设备相连,用以接收第三方EtherCAT主站设备输出的第一数据;

[0008]EtherCAT主站,与一个或多个第三方EtherCAT从站设备相连;

[0009]EtherCAT从站控制器以及控制模块;

[0010]所述控制模块包括:

[0011]第一处理模块,接收EtherCAT从站输出的第一数据将该第一数据转换为可输出至第三方EtherCAT从站设备的第二数据将该第二数据按照预设处理方法处理后输出至EtherCAT主站;EtherCAT主站将该第二数据转发至一个或多个第三方EtherCAT从站设备。

[0012]进一步地,所述EtherCAT主站还接收一个或多个第三方EtherCAT从站设备输出的第三数据;

[0013]所述控制模块还包括:

[0014]第二处理模块,接收EtherCAT主站输出的第三数据并将该数据转换为可输出至第三方EtherCAT主站设备的第四数据;EtherCAT从站接收该第四数据并将该第四数据转发至第三方EtherCAT主站设备。

[0015]进一步地,所述预设处理方法可通过外部通讯设备存入控制模块中。

[0016]进一步地,所述控制模块内还存储有数据映射表,所述数据映射表将多个第三方EtherCAT从站设备的数据信息映射为控制模块自定义的数据信息。

[0017]进一步地,所述多个第三方EtherCAT从站设备的数据信息包括多个第三方EtherCAT从站设备的运动状态数据信息和/或控制指令数据信息;所述多个第三方EtherCAT从站设备相同运动状态和/或控制指令对应的运动状态数据信息和/或控制指令数据信息相同或不同。

[0018]进一步地,所述EtherCAT主站扫描多个第三方EtherCAT从站设备的时钟信号并获取最远端的第三方EtherCAT从站设备的时钟信号;当所述EtherCAT主站转发第二数据至多个第三方EtherCAT从站设备时,所述多个第三方EtherCAT从站设备根据各自的时钟信号与最远端的第三方EtherCAT从站设备的时钟信号比较而后延时一时间再输出控制信号以使得多个第三方EtherCAT从站设备同步输出控制信号。

[0019]进一步地,所述第三方EtherCAT主站设备为注塑机控制器,所述第三方EtherCAT 从站设备为伺服驱动器。

[0020]进一步地,所述注塑机控制器输出注塑机压力或流量控制数据,所述第一处理模块处理后输出伺服驱动器转速或扭矩数据。

[0021]进一步地,所述控制模块还连接有模拟量输入输出接口和/或数字量输入输出接口和/或外部SRAM扩展接口和/或CAN、RS485、RS232通讯接口和/或USB接口。

[0022]本实用新型还提供一种EtherCAT主从站一体控制系统,其包括

[0023]第三方EtherCAT主站设备;

[0024]一个或多个上述EtherCAT主从站一体控制卡;

[0025]一个或多个第三方EtherCAT从站设备;

[0026]所述第三方EtherCAT主站设备通过EtherCAT总线串联一个或多个EtherCAT主从站一体控制卡,所述每一EtherCAT主从站一体控制卡通过EtherCAT总线串联一个或多个第三方EtherCAT从站设备;或者所述第三方EtherCAT主站设备通过EtherCAT总线串联一个或多个EtherCAT主从站一体控制卡以及第三方EtherCAT从站设备,所述每一EtherCAT主从站一体控制卡通过EtherCAT总线串联一个或多个第三方EtherCAT从站设备。

[0027]本实用新型相对现有技术具有如下有益效果

[0028]1、通过EtherCAT主从站一体控制卡将第三方EtherCAT主站设备与第三方EtherCAT从站设备隔离开,一方面降低了第三方EtherCAT主站设备与第三方EtherCAT从站设备在控制算法上的开发成本,另一方面也可有效防止企业自有的核心算法通过第三方EtherCAT主站设备或第三方EtherCAT从站设备平台泄露;

[0029]2、EtherCAT主从站一体控制卡构成一个EtherCAT环状拓扑结构使得EtherCAT总线拓扑结构柔性更强;既可以单独用作EtherCAT主站,也可以单独用作EtherCAT从站,还可以EtherCAT主从站共同使用,使得本申请应用面更广;

[0030]3、无需考虑各种不同型号的第三方EtherCAT主站设备、第三方EtherCAT从站设备

在时钟、以及数据地址上的差异,减少各种不同型号的第三方EtherCAT主站设备、第三方EtherCAT从站设备的开发、维护工作。

## 附图说明

[0031]图1为现有技术中第三方EtherCAT主站设备与第三方EtherCAT从站设备EtherCAT 总线通讯框图;

[0032]图2为本实用新型第三方EtherCAT主站设备与第三方EtherCAT从站设备EtherCAT 总线通讯框图;

[0033]图3为本实用新型EtherCAT主从站一体控制卡结构框图

[0034]图4为本实用新型第三方EtherCAT主站设备与多个EtherCAT主从站一体控制卡相连的通讯框图;

[0035]图5为本实用新型第三方EtherCAT主站设备与多个EtherCAT主从站一体控制卡以及第三方EtherCAT从站设备混合串联的通讯框图。

## 具体实施方式

[0036]以下是本实用新型的具体实施例并结合附图,对本实用新型的技术方案作进一步的描述,但本实用新型并不限于这些实施例。

[0037]请参照图1,图1为现有技术中第三方EtherCAT主站设备与第三方EtherCAT从站设备EtherCAT总线通讯框图。

[0038]本实施例中,第三方EtherCAT主站设备均以注塑机控制器为例说明,第三方EtherCAT从站设备均以伺服驱动器为例说明。但是本领域技术人员可知,第三方EtherCAT 主站设备并不局限于注塑机控制器,还可为车床控制器等,第三方EtherCAT从站设备也并不局限于伺服驱动器。

[0039]图1中,注塑机控制器直接通过EtherCAT总线控制各个不同型号的伺服驱动器。控制算法、控制逻辑、工艺参数、伺服参数等均存储在注塑机控制器内。由于不同型号的伺服驱动器的性能以及不同型号的伺服驱动器的运动状态和控制指令对应的数据信息不同,导致注塑机控制器的开发成本急剧升高且存储在注塑机控制器内的控制算法、控制逻辑、工艺参数、伺服参数等易被泄露。

[0040]为此,请参照图2,本申请将注塑机控制器与不同伺服驱动器的通讯隔离开,注塑机控制器与伺服驱动器之间通过EtherCAT主从站一体控制卡传递信号。如此,一方面使得注塑机控制器仅面向EtherCAT主从站一体控制卡,对注塑机控制器的性能要求和开发成本降低,另一方面,企业的核心控制算法、控制逻辑、工艺参数、伺服参数等被存储在EtherCAT 主从站一体的控制卡中从而有效防止该些技术秘密泄露。

[0041]请参照图3,本申请的EtherCAT主从站一体控制卡包括EtherCAT从站101,其与第三方EtherCAT主站设备200(本实施例中为注塑机控制器)相连;EtherCAT从站控制器(ET1100)102,控制模块103以及EtherCAT主站104,EtherCAT主站104与一个或多个第三方EtherCAT从站设备(300、301、302···)(本实施例中为伺服驱动器)相连。多个第三方EtherCAT从站设备(300、301、302、303···)串联连接。

[0042]本实施例中,注塑机控制器仅需发出简单的逻辑控制数据,例如注塑机所需达到

的压力、流量,伺服电机回零等;该控制数据通过EtherCAT从站传输至控制模块103,控制模块103将该控制数据转换为可输出至第三方EtherCAT从站设备的第二数据,并且将该第二数据按照预设处理方法处理为可控制伺服驱动器的信号,例如转度、扭矩信号。处理后的第二数据由EtherCAT主站转发至一个或多个伺服驱动器。

[0043]所述的预设处理方法即为各企业自有的控制算法、控制逻辑、工艺参数、伺服参数等。该些控制算法、控制逻辑、工艺参数、伺服参数等可预先通过外部通讯设备,例如电脑、手持编程器等写入控制模块内。

[0044]EtherCAT主站还接收各伺服驱动器输出的第三数据,例如各伺服电机的位置信息、扭矩信息、转速信息等;控制模块还包括第二处理模块1031,第二处理模块1031将EtherCAT主站接收的第三数据转换为可输出至第三方EtherCAT主站设备的第四数据;EtherCAT从站接收该第四数据并将该第四数据转发至第三方EtherCAT主站设备。

[0045]上述第一数据与第二数据转换以及第三数据与第四数据转换是由于各类伺服驱动器的型号、厂家不同导致各类伺服驱动器的运动状态数据信息和/或控制指令数据信息各不相同,例如A厂家的伺服回零指令对应的地址信息为A1,B厂家的伺服回零指令对应的地址信息则可能为B1;又如A厂家伺服电机的转速信息存储地址为A2,B厂家伺服电机的转速信息存储地址可能为B2。

[0046]为了方便伺服控制器、EtherCAT主从站一体的控制卡以及各伺服驱动器的信号传递,本申请的控制模块中还存储有数据映射表,所述数据映射表将多个第三方EtherCAT从站设备的数据信息映射为控制模块自定义的数据信息。当注塑机控制器与EtherCAT主从站一体的控制卡之间通信时通过自定义的数据信息传递数据即可,使得二者之间的通讯更加透明、方便。

[0047]以下表格为一个具体的数据映射表范例,仅用于方便理解,不应看做对本申请实际数据映射方式的限制。


|  |  |  |
| -- | -- | -- |
|  |  |  |
|  |  |  |
|  |  |  |
|  |  |  |


[0048]

[0049]上述序号1、2、4中,自定义地址信号一一对应伺服驱动器地址信号,上述序号3中,2个伺服驱动器地址信息均对应一个自定义地址信息。

[0050]更进一步地,由于多个伺服驱动器的时钟信号不同,为了保证伺服驱动器驱动伺服电机时的同步性,本申请的EtherCAT主站还扫描多个第三方EtherCAT从站设备的时钟信号并获取最远端的第三方EtherCAT从站设备的时钟信号;当所述EtherCAT主站转发第二控制信号至多个第三方EtherCAT从站设备时,所述多个第三方EtherCAT从站设备根据各自的时钟信号与最远端的第三方EtherCAT从站设备的时钟信号比较而后延时一时间再输出控制信号以使得多个第三方EtherCAT从站设备同步输出控制信号。

[0051]例如,当最远端的伺服驱动器的时钟信号为延迟10ms,EtherCAT主站转发动作信号至最近端的伺服驱动器时,假设最近端的伺服驱动器自身时钟延迟为2ms,则伺服驱动器再延时8ms后动作,进而使得最近端的伺服驱动器与最远端的伺服驱动器同步动作。其他伺服驱动器也同理运行。

[0052]进一步地,所述控制模块还连接有模拟量输入输出接口和/或数字量输入输出接口和/或外部SRAM(Static Random Access Memory)扩展接口和/或CAN、RS485、RS232通讯接口和/或USB接口。

[0053]本实用新型还提供一种EtherCAT主从站一体控制系统,其包括:

[0054]第三方EtherCAT主站设备;

[0055]一个或多个上述EtherCAT主从站一体控制卡;

[0056]一个或多个EtherCAT从站设备。

[0057]请参照图4,在图4中,一个第三方EtherCAT主站设备可以通过EtherCAT总线串联多个EtherCAT主从站一体控制卡,而每一EtherCAT主从站一体控制卡则串联多个第三方EtherCAT从站设备。

[0058]请参照图5,图5中,一个第三方EtherCAT主站设备可以通过EtherCAT总线串联多个EtherCAT主从站一体控制卡和多个EtherCAT从站设备;而每一EtherCAT主从站一体控制卡则串联多个第三方EtherCAT从站设备。

[0059]本实施例中,第三方EtherCAT主站设备扩展从站设备有了更大灵活性和发挥空间,不拘泥于单一的扩展EtherCAT主从站一体控制卡或第三方EtherCAT从站设备。

[0060]本文中所描述的具体实施例仅仅是对本实用新型精神作举例说明。本实用新型所属技术领域的技术人员可以对所描述的具体实施例做各种各样的修改或补充或采用类似的方式替代,但并不会偏离本实用新型的精神或者超越所附权利要求书所定义的范围。

<!-- 注塑机控制器 伺服驱动器1 人机界面 伺服驱动器2 控制逻辑 控制算法 伺服驱动器3 工艺参数 伺服参数 IO模块  -->
![](https://web-api.textin.com/ocr_image/external/7dbe2414ce79382c.jpg)

图1

<!-- 注塑机控制器 控制卡 伺服驱动器1 控制逻辑 工艺参数 伺服驱动器2 人机界面 控制算法 伺服参数 伺服驱动器3 IO模块  -->
![](https://web-api.textin.com/ocr_image/external/d9692063417b6724.jpg)

图2

<!-- 200 第三方 EtherCAT主 站设备 100 104 EtherCAT从 300 站 EtherCAT主 第三方 站 EtherCAT从 103 站设备 101 1031 控制模块 301 第三方 EtherCAT从 第一处 第二处 EtherCAT从 站控制器 理模块 理模块 站设备 302 102 1032 第三方 EtherCAT从 模拟量 CAN、 站设备 输入输 数字量 输入输 外部 SRAM RS485、 出接口 出接口 扩展 RS232通 USB接口 讯接口  -->
![](https://web-api.textin.com/ocr_image/external/de7c2a2b3b1fa883.jpg)

图3

<!-- 第三方EtherCAT EtherCAT 第三方Ethe 第三方Ethe 主站设备 主从站一体控制 卡 rCAT 从站设备 rCAT 第三方Ethe 从站设备 rCAT 从站设备 EtherCAT 主从站一体控制 第三方Ethe 卡 rCAT 第三方Ethe 从站设备 rCAT 第三方Ethe 从站设备 rCAT 从站设备 EtherCAT 主从站一体控制 第三方Ethe rCAT 第三方Ethe rCAT 第三方Ethe 卡 从站设备 从站设备 rCAT 从站设备  -->
![](https://web-api.textin.com/ocr_image/external/1fb2821205ea7e35.jpg)

图4

<!-- 第三方EtherCAT EtherCAT 第三方Ethe 第三方Ethe 第三方Ethe 主站设备 主从站一体控制 卡 rCAT 从站设备 rCAT 从站设备 rCAT 从站设备 第三方Ethe rCAT 从站设备 EtherCAT 主从站一体控制 第三方Ethe rCAT 第三方Ethe rCAT 第三方Ethe 卡 从站设备 从站设备 rCAT 从站设备  -->
![](https://web-api.textin.com/ocr_image/external/d82b682f7a048760.jpg)

图5



