# 边缘控制器-安徽工厂测试流程

本文档面向公司内部人员

场景：单件分离工厂测试

## 公司准备

**硬件准备**

T507：3台（视情况而定）

网线：至少6根

交换机：1个（调试使用）

T507电源适配器：2个

插座：1个

合信远程IO（16输入16输出，视情况而定）

IO电源适配器：1个

接线端子

万用表

卷尺

**软件准备**

控制伺服的案例程序

从站JSON文件

监控共享内存外部程序

## 接线

参考接线文档

## 测试流程

先过完完整的运行流程，再进一步对各功能进行详细测试

下列为测试核心流程，**快速**排查与处理方案

**注意：**处理时程序自启/程序启动部分优先级高于断电恢复

### 程序启动

#### 启动失败

| 故障类型               | 处理方案                                                     | 关联依据                                                     |
| ---------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| **JSON检查不通过**     | • 重启Ethercat服务<br>• 更新JSON文件（快速配置好一个从站JSON然后批量克隆） | • 获得到的从站信息是否缺失<br>• 从站数量是否一致<br>• JSON从站信息与实际是否一致（例如：pid/vid对不上） |
| **等待从站OP超时**     | • 更新JSON文件（快速配置好一个从站JSON然后批量克隆）         | • JSON配置项是否正确<br>• 共享内存结构体应用&系统是否一致（例如：控制位的顺序或数量两边对不上） |
| **程序反复重启**       | • 查看控制程序日志，需要应用组介入                           | -                                                            |
| **Ethercat服务未启动** | • 执行启动Ethercat服务命令<br>• 若无法启动，重启T507<br>（操作均由平台自动执行） | • 网口灯是否亮                                               |

### 断线恢复

需要进一步排查，优先测试或处理其他功能

### 程序自启/断电重启

#### 自启失败

参考程序启动

## 测试用例

### **程序启动测试**

**测试目的**：验证系统从启动到伺服就绪全流程的耗时和稳定性
**测试设备**：T507、伺服、网线、计时器
**前置条件**：

1. 所有设备断电，网线连接正常
2. 系统处于初始状态

| 测试步骤 | 操作                     | 测量指标                    | 预期结果       |
| :------- | :----------------------- | :-------------------------- | :------------- |
| 1        | 按下启动按钮，开始计时   | 全流程开始时间戳(T0)        | 系统开始初始化 |
| 2        | 记录从站校验完成时刻     | 从站校验耗时 = T1 - T0      | ≤              |
| 3        | 记录模板生成完成时刻     | 模板生成耗时 = T2 - T1      | ≤              |
| 4        | 记录OP操作完成时刻       | OP耗时 = T3 - T2            | ≤              |
| 5        | 记录所有伺服使能完成时刻 | 使能耗时 = T4 - T3          | ≤              |
| 6        | 记录伺服开始转动时刻     | 全流程耗时 = T5 - T0        | ≤ 60s          |
| 7        | 重复10次测试             | 成功率 = (成功次数/10)*100% | ≥ 99%          |

**通过标准**：

- 各阶段耗时符合预期阈值
- 伺服转动无异常
- 成功率 ≥ 99%

------

### 断线恢复测试

**测试目的**：验证不同位置网线故障时系统的恢复能力，特别是断网和恢复过程中从站状态变化
**测试场景**：系统正常运行中
**前置条件**：伺服已处于使能状态

**测试步骤（通用步骤，适用于三个场景）**

| 步骤 | 操作                                 | 测量指标                                             | 预期结果 |
| :--- | :----------------------------------- | :--------------------------------------------------- | :------- |
| 1    | 记录拔网线前时刻(T0)                 | -                                                    | -        |
| 2    | 拔掉目标网线（第一个/中间/最后）     | 所有从站离线时间 = T_all_off - T0                    | ≤        |
| 3    | 记录部分恢复时刻（未断网段从站在线） | 部分恢复时间 = T_partial_recover - T0                | ≤        |
| 4    | 记录插回网线前时刻(T_replug_start)   | -                                                    | -        |
| 5    | 插回网线                             | 重新组网离线时间 = T_all_off_again - T_replug_start  | ≤        |
| 6    | 记录所有从站在线时刻                 | 所有从站恢复在线时间 = T_all_online - T_replug_start | ≤        |
| 7    | 记录目标从站使能完成时刻             | 单个使能耗时 = T_target_enabled - T_all_online       | ≤        |
| 8    | 记录所有从站使能完成时刻             | 完整使能耗时 = T_all_enabled - T_all_online          | ≤        |

**关键状态说明**：

1. **拔网线后**：
   - 所有从站离线（T_all_off）
   - 未断网段从站先恢复在线（T_partial_recover）
2. **插回网线后**：
   - 所有从站再次离线（重新组网）
   - 所有从站恢复在线（T_all_online）
   - 系统逐个使能从站

**通过标准**：

- 所有从站离线时间 ≤ 
- 所有从站恢复在线时间 ≤ 
- 单个使能耗时 ≤ 
- 完整使能耗时 ≤ 
- 状态切换无错误

------

### **程序自启测试**

**测试目的**：验证T507重启后程序自动启动到伺服就绪的流程
**测试配置**：

- 程序设置开机自启
- 重启伺服和T507

| 测试步骤 | 操作                 | 测量指标               | 预期结果 |
| :------- | :------------------- | :--------------------- | :------- |
| 1        | 重启T507，开始计时   | 全流程开始时间(T0)     | -        |
| 2        | 记录从站校验完成时刻 | 从站校验耗时 = T1 - T0 | ≤        |
| 3        | 记录模板生成完成时刻 | 模板生成耗时 = T2 - T1 | ≤        |
| 4        | 记录OP操作完成时刻   | OP耗时 = T3 - T2       | ≤        |
| 5        | 记录使能完成时刻     | 使能耗时 = T4 - T3     | ≤        |
| 6        | 记录伺服转动开始时刻 | 全流程耗时 = T5 - T0   | ≤ 60s    |
| 7        | 重复5次测试          | 成功率                 | ＞99%    |

**通过标准**：

- 全流程耗时 ≤ 60s
- 成功率＞99%

------

### 断电重启测试

**测试目的**：验证系统异常断电后重新上电的自恢复能力
**测试配置**：

- 程序正常运行中
- 重启伺服与T507

| 测试步骤 | 操作                             | 测量指标               | 预期结果 |
| :------- | :------------------------------- | :--------------------- | :------- |
| 1        | 切断T507和伺服电源，开始计时(T0) | 断电开始时间           | -        |
| 2        | 等待10秒后恢复供电               | -                      | -        |
| 3        | 记录从站校验完成时刻             | 从站校验耗时 = T1 - T0 | ≤        |
| 4        | 记录模板生成完成时刻             | 模板生成耗时 = T2 - T1 | ≤        |
| 5        | 记录OP操作完成时刻               | OP耗时 = T3 - T2       | ≤        |
| 6        | 记录伺服寻零完成时刻             | 寻零耗时 = T_home - T3 | ≤        |
| 7        | 记录使能完成时刻                 | 使能耗时 = T4 - T_home | ≤        |
| 8        | 记录伺服转动开始时刻             | 全流程耗时 = T5 - T0   | ≤60s     |
| 9        | 重复5次测试                      | 成功率                 | ＞99%    |

**通过标准**：

- 全流程耗时 ≤ 60s
- 成功率 ＞99%

### 用例

#### **1. 程序启动测试**（记录从启动到伺服转动的时间）

| 测试轮次       | 全流程耗时 (≤)      | 从站校验耗时 (≤) | 模板生成耗时 (≤) | OP耗时 (≤) | 使能耗时 (≤) | 结果 (PASS/FAIL) |
| :------------- | :------------------ | :--------------- | :--------------- | :--------- | :----------- | :--------------- |
| 1              | [ ] ms              | [ ] ms           | [ ] ms           | [ ] ms     | [ ] ms       |                  |
| ...            | ...                 | ...              | ...              | ...        | ...          | ...              |
| **10次成功率** | ＞99% </br>是否达标 |                  |                  |            |              |                  |

#### **2. 断线恢复测试**（记录拔插网线后的恢复时间）

| 测试位置       | 所有从站离线时间 (≤) | 未断网段恢复时间 (≤) | 所有从站在线时间 (≤) | 完整使能耗时 (≤) | 结果 (PASS/FAIL) |
| :------------- | :------------------- | :------------------- | :------------------- | :--------------- | :--------------- |
| 第一个从站网线 | [ ] ms               | [ ] ms               | [ ] ms               | [ ] ms           |                  |
| 中间伺服网线   | [ ] ms               | [ ] ms               | [ ] ms               | [ ] ms           |                  |
| 最后伺服网线   | [ ] ms               | [ ] ms               | [ ] ms               |                  |                  |

#### **3. 自启/断电测试**（记录T507或整体断电后的恢复时间）

| 测试类型     | 全流程耗时 | 关键指标         | 特殊项 | 结果 (PASS/FAIL) |
| :----------- | :--------- | :--------------- | :----- | :--------------- |
| **程序自启** | ≤ `[ ] ms` | 启动成功率 ＞99% | -      |                  |
| **断电重启** | ≤`[ ] ms`  | 启动成功率 ＞99% | -      |                  |

