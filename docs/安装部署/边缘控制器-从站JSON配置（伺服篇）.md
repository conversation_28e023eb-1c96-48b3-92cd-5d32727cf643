# 边缘控制器-从站JSON配置操作指南

本篇操作指南适用于**所有从站都为同一型号的伺服电机**场景
下列操作步骤为默认操作流程，平台支持自定义配置

## 前提条件
- 已完成边缘控制器的物理接线工作
- 确保所有从站设备已正确连接并供电
- 请根据伺服的**官方手册**确认支持下列控制位（**符合CiA 402/DS402控制协议规范**），如不支持请跳过默认操作，观看**自定义配置**
  - 操作模式（0x6060）
  - 目标速度（0x60FF）
  - 控制字（0x6040）
  - 状态字（0x6041）
  - 错误代码（0x603F）
  - 实际反馈速度（0x606C）
- 下列型号伺服已通过测试支持上述控制位
  - 雷赛L5N系列伺服
  - 台达ASDA-E3系列伺服
  - 纬创SD710系列伺服
  - 汇川SV660N系列伺服
  - 合信A6N系列伺服

## 默认操作

### 1. 打开从站配置生成器

- 打开浏览器访问Ethercat平台，默认（http://***************:3000）

- 在边缘控制器界面中点击"从站JSON模板生成器"按钮

  ![image-20250616111445506](D:\desktop\IPCEthercat\fxethercat\docs\安装部署\边缘控制器-从站JSON配置（伺服篇）.assets\image-20250616111445506.png)

### 2. 清理初始配置
- 点击"从站去重"选项

  ![image-20250616112048068](D:\desktop\IPCEthercat\fxethercat\docs\安装部署\边缘控制器-从站JSON配置（伺服篇）.assets\image-20250616112048068.png)

- 清空RxPDOs和TxPDOs列表中的所有内容

  ![image-20250616112208411](D:\desktop\IPCEthercat\fxethercat\docs\安装部署\边缘控制器-从站JSON配置（伺服篇）.assets\image-20250616112208411.png)

### 3. 添加必要的控制参数
在右侧的"常用控制位"中，通过搜索添加以下参数：

​	操作：在搜索框中输入控制位进行过滤，点击下列框，选择添加到所有从站，下列为添加操作模式控制位案例（**其他控制位参考操作模式的添加方式**）：

- 操作模式（0x6060）

  ![image-20250616112449315](D:\desktop\IPCEthercat\fxethercat\docs\安装部署\边缘控制器-从站JSON配置（伺服篇）.assets\image-20250616112449315.png)

  ![image-20250616112507892](D:\desktop\IPCEthercat\fxethercat\docs\安装部署\边缘控制器-从站JSON配置（伺服篇）.assets\image-20250616112507892.png)

- 目标速度（0x60FF）

- 控制字（0x6040）

- 状态字（0x6041）

- 错误代码（0x603F）

- 实际反馈速度（0x606C）

**注意：上述仅为常用控制位，如需添加其他控制位，请先根据伺服手册确认控制位信息后添加**

**案例：雷赛 L5N 伺服电机存在自定义的控制位 0x503F，代表厂商自定义错误码**

### 4. 配置DC同步
- 点击"全局DC配置"按钮

  ![image-20250616112744081](D:\desktop\IPCEthercat\fxethercat\docs\安装部署\边缘控制器-从站JSON配置（伺服篇）.assets\image-20250616112744081.png)

- 启用DC同步功能

  ![image-20250616112758894](D:\desktop\IPCEthercat\fxethercat\docs\安装部署\边缘控制器-从站JSON配置（伺服篇）.assets\image-20250616112758894.png)
### 5. 下载配置文件
- 切换到"JSON"视图

  ![image-20250616113754416](D:\desktop\IPCEthercat\fxethercat\docs\安装部署\边缘控制器-从站JSON配置（伺服篇）.assets\image-20250616113754416.png)

  ![image-20250616113815230](D:\desktop\IPCEthercat\fxethercat\docs\安装部署\边缘控制器-从站JSON配置（伺服篇）.assets\image-20250616113815230.png)

- 点击"下载JSON配置"按钮保存配置文件

  ![image-20250616113836317](D:\desktop\IPCEthercat\fxethercat\docs\安装部署\边缘控制器-从站JSON配置（伺服篇）.assets\image-20250616113836317.png)

## 自定义配置

### 1.打开从站配置生成器

### 2.添加对应PDO

- 根据**官方手册**，添加需要的PDO，补充PDO信息

![image-20250616133712734](D:\desktop\IPCEthercat\fxethercat\docs\安装部署\边缘控制器-从站JSON配置（伺服篇）.assets\image-20250616133712734.png)

### 3.配置DC同步

根据**官方手册**配置DC同步（伺服可能不支持DC同步）

### 4.下载配置文件

## 注意事项

1. 确保所有从站设备已正确连接并通电
2. 添加控制参数时，请根据从站的官方手册仔细核对控制位是否正确
3. 对于需要DC同步的伺服驱动器，必须确保DC功能已正确开启
4. 保存的JSON配置文件请妥善保管，建议做好标记和备份

如果在操作过程中遇到问题，请及时联系技术支持人员获取帮助。