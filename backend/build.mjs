import * as esbuild from 'esbuild';

try {
  const result = await esbuild.build({
    entryPoints: ['src/server.ts'],
    bundle: true,
    platform: 'node',
    target: 'node18',
    outfile: 'dist/server.mjs',
    format: 'esm',
    minify: true,
    sourcemap: false,
    banner: {
      js: `
        import { createRequire } from 'module';
        import { fileURLToPath } from 'url';
        import { dirname } from 'path';
        import path from 'path';

        const require = createRequire(import.meta.url);
        const __filename = fileURLToPath(import.meta.url);
        const __dirname = dirname(__filename);
      `
    },
    loader: {
      '.ts': 'ts',
      '.js': 'js',
      '.node': 'copy'  // 处理 .node 文件
    },
    external: [
      'fsevents',  // 排除 fsevents，因为它是可选的 macOS 依赖
      '*.node'     // 排除所有 .node 文件
    ],
    bundle: true,
    metafile: true,
    treeShaking: true
  });

  console.log('Build completed successfully');
} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
}
