/// <reference path="../pb_data/types.d.ts" />

// 存储运行中的进程信息
const runningProcesses = {};

// 监听 programs 表中更新后的状态
onModelAfterUpdate(async (e) => {
    const record = e.model;

    const status = record.get("status");
    const filePath = record.get("filePath");

    if (status === "pending" && filePath) {
        // 启动程序
        try {
            await startProgram(record.get("id"), filePath);
            record.set("status", "running");
            await $app.dao().saveRecord(record);
        } catch (err) {
            console.error("启动程序失败:", err);
            // 启动失败，将状态设置为 error
            record.set("status", "error");
            await $app.dao().saveRecord(record);
        }
    } else if (status === "stopped") {
        // 如果状态被设置为 stopped，则停止相应程序
        await stopProgram(record.get("id"));
    }
}, "programs");

// 启动程序函数，接收程序的ID和文件路径
async function startProgram(programId, filePath) {
    console.log(`启动程序: ${filePath}`);

    // 使用 $os.exec 来启动程序
    const result = $os.exec(filePath);
    if (result.error) {
        throw new Error("程序启动失败：" + result.error);
    }

    runningProcesses[programId] = result.pid;

    // 监听程序状态，如果程序被停止则更新数据库中的状态
    $os.exec(`监控程序的命令 -p ${result.pid}`, (exitCode) => {
        if (exitCode !== 0) {
            console.log(`程序 ${programId} 已停止`);
            updateProgramStatus(programId, "stopped");
        }
    });
}

// 停止程序函数，根据程序ID获取并终止相应进程
async function stopProgram(programId) {
    const pid = runningProcesses[programId];
    if (pid) {
        console.log(`停止程序: ${programId} (PID: ${pid})`);
        $os.exec(`kill ${pid}`);
        delete runningProcesses[programId];
    }
}

// 更新程序状态函数，供内部调用
async function updateProgramStatus(programId, status) {
    const record = await $app.dao().findRecordById("programs", programId);
    if (record) {
        record.set("status", status);
        await $app.dao().saveRecord(record);
    }
}

// 系统启动时，将所有非 stopped 状态的程序重置为 stopped
onAfterBootstrap(async () => {
    console.log("系统启动，重置所有非 stopped 程序状态...");
    const records = await $app.dao().findRecords("programs", { filter: 'status != "stopped"' });

    for (const record of records) {
        record.set("status", "stopped");
        await $app.dao().saveRecord(record);
    }
});
