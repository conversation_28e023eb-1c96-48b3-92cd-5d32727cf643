{"template": [{"name": "L5N(COE) DS402", "all_pdos": [{"name": "error_code", "index": "0x603F", "subindex": "0", "type": "uint16", "bitlen": 16, "comment": "错误代码"}, {"name": "control_word", "index": "0x6040", "subindex": "0", "type": "uint16", "bitlen": 16, "comment": "控制字"}, {"name": "status_word", "index": "0x6041", "subindex": "0", "type": "uint16", "bitlen": 16, "comment": "状态字"}, {"name": "quick_stop_mode", "index": "0x605A", "subindex": "0", "type": "uint8", "bitlen": 8, "comment": "快速停机方式选择"}, {"name": "deceleration_stop_mode", "index": "0x605B", "subindex": "0", "type": "uint8", "bitlen": 8, "comment": "电机减速停机方式选择"}, {"name": "disable_stop_mode", "index": "0x605C", "subindex": "0", "type": "uint8", "bitlen": 8, "comment": "断使能停机方式选择"}, {"name": "pause_stop_mode", "index": "0x605D", "subindex": "0", "type": "uint8", "bitlen": 8, "comment": "暂停停机方式选择"}, {"name": "alarm_stop_code", "index": "0x605E", "subindex": "0", "type": "uint8", "bitlen": 8, "comment": "报警停止代码"}, {"name": "operation_mode_setting", "index": "0x6060", "subindex": "0", "type": "uint8", "bitlen": 8, "comment": "操作模式设置"}, {"name": "operation_mode_display", "index": "0x6061", "subindex": "0", "type": "uint8", "bitlen": 8, "comment": "操作模式显示"}, {"name": "position_command", "index": "0x6062", "subindex": "0", "type": "int32", "bitlen": 32, "comment": "位置指令"}, {"name": "internal_position", "index": "0x6063", "subindex": "0", "type": "int32", "bitlen": 32, "comment": "实际内部位置"}, {"name": "feedback_position", "index": "0x6064", "subindex": "0", "type": "int32", "bitlen": 32, "comment": "实际反馈位置"}, {"name": "position_deviation_window", "index": "0x6065", "subindex": "0", "type": "int32", "bitlen": 32, "comment": "位置偏差窗口"}, {"name": "position_deviation_time", "index": "0x6066", "subindex": "0", "type": "uint16", "bitlen": 16, "comment": "位置偏差检测时间"}, {"name": "position_window", "index": "0x6067", "subindex": "0", "type": "int32", "bitlen": 32, "comment": "位置窗口"}, {"name": "position_window_time", "index": "0x6068", "subindex": "0", "type": "uint16", "bitlen": 16, "comment": "位置窗口时间"}, {"name": "internal_speed_command", "index": "0x606B", "subindex": "0", "type": "int32", "bitlen": 32, "comment": "内部指令速度"}, {"name": "feedback_speed", "index": "0x606C", "subindex": "0", "type": "int32", "bitlen": 32, "comment": "实际反馈速度"}, {"name": "speed_window", "index": "0x606D", "subindex": "0", "type": "uint16", "bitlen": 16, "comment": "速度窗口"}, {"name": "speed_window_time", "index": "0x606E", "subindex": "0", "type": "uint16", "bitlen": 16, "comment": "速度窗口时间"}, {"name": "zero_speed_threshold", "index": "0x606F", "subindex": "0", "type": "uint16", "bitlen": 16, "comment": "零速门限"}, {"name": "zero_speed_time", "index": "0x6070", "subindex": "0", "type": "uint16", "bitlen": 16, "comment": "零速门限时间"}, {"name": "target_torque", "index": "0x6071", "subindex": "0", "type": "int16", "bitlen": 16, "comment": "目标转矩"}, {"name": "max_torque", "index": "0x6072", "subindex": "0", "type": "uint16", "bitlen": 16, "comment": "最大转矩"}, {"name": "max_current", "index": "0x6073", "subindex": "0", "type": "uint16", "bitlen": 16, "comment": "最大电流"}, {"name": "target_position", "index": "0x607A", "subindex": "0", "type": "int32", "bitlen": 32, "comment": "目标位置"}, {"name": "origin_offset", "index": "0x607C", "subindex": "0", "type": "int32", "bitlen": 32, "comment": "原点偏置"}, {"name": "soft_limit_min", "index": "0x607D", "subindex": "1", "type": "int32", "bitlen": 32, "comment": "软限位最小值"}, {"name": "soft_limit_max", "index": "0x607D", "subindex": "2", "type": "int32", "bitlen": 32, "comment": "软限位最大值"}, {"name": "max_motor_speed", "index": "0x6080", "subindex": "0", "type": "uint32", "bitlen": 32, "comment": "电机最大速度"}, {"name": "protocol_speed", "index": "0x6081", "subindex": "0", "type": "uint32", "bitlen": 32, "comment": "协议速度"}, {"name": "protocol_acceleration", "index": "0x6083", "subindex": "0", "type": "uint32", "bitlen": 32, "comment": "协议加速度"}, {"name": "protocol_deceleration", "index": "0x6084", "subindex": "0", "type": "uint32", "bitlen": 32, "comment": "协议减速度"}, {"name": "emergency_deceleration", "index": "0x6085", "subindex": "0", "type": "uint32", "bitlen": 32, "comment": "急停减速度"}, {"name": "torque_slope", "index": "0x6087", "subindex": "0", "type": "uint32", "bitlen": 32, "comment": "转矩斜率"}, {"name": "max_protocol_acceleration", "index": "0x60C5", "subindex": "0", "type": "uint32", "bitlen": 32, "comment": "协议最大加速度"}, {"name": "max_protocol_deceleration", "index": "0x60C6", "subindex": "0", "type": "uint32", "bitlen": 32, "comment": "协议最大减速度"}, {"name": "target_speed", "index": "0x60FF", "subindex": "0", "type": "int32", "bitlen": 32, "comment": "目标速度"}]}, {"name": "xxxx2", "all_pdos": [{"name": "target_speed", "index": "0x60FF", "subindex": "0", "type": "int32", "bitlen": 32, "comment": "目标速度"}]}]}