using System;
using System.IO.MemoryMappedFiles;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;

namespace EtherCATControl
{
    public class EtherCATController
    {
        private readonly MemoryMappedFile _memoryMappedFile;
        private readonly MemoryMappedViewAccessor _viewAccessor;
        private EtherCATSharedMemory _sharedMemory;

        public EtherCATController(string EtherCATSharedMemoryFilePath)
        {
            _memoryMappedFile = MemoryMappedFile.CreateFromFile(EtherCATSharedMemoryFilePath, FileMode.Open);
            _viewAccessor = _memoryMappedFile.CreateViewAccessor();
            _sharedMemory = new EtherCATSharedMemory();
        }

        private void UpdateSharedMemory()
        {
            _viewAccessor.Read(0, out _sharedMemory);
        }

        private void WriteSharedMemory()
        {
            _viewAccessor.Write(0, ref _sharedMemory);
        }

        //---------------伺服状态检查---------------------------
        // public bool WaitForServoReady(CancellationToken cancellationToken = default)
        // {
        //     while (!cancellationToken.IsCancellationRequested)
        //     {
        //         UpdateSharedMemory();
        //         if ((_sharedMemory.shm_slave0_tx_input_u16_对应变量实际变量 & 0x0433) == 1075)
        //         {
        //             Console.WriteLine("Servo is ready");
        //             return true;
        //         }
        //         Thread.Sleep(100);
        //     }
        //     return false;
        // }
        

        //----------------伺服使能   DS402逻辑-------------------------

        // public void EnableServo()
        // {
        //     Console.WriteLine("Enabling servo...");
            
        //     // Control sequence
        //     _sharedMemory.对应实际变量Slave0Controlword = 0x06;
        //     WriteSharedMemory();
        //     Thread.Sleep(100);

        //     _sharedMemory.对应实际变量Slave0Controlword = 0x07;
        //     WriteSharedMemory();
        //     Thread.Sleep(100);

        //     _sharedMemory.对应实际变量Slave0Operationmode = 0x09;
        //     _sharedMemory.对应实际变量Slave0Controlword = 0x0F;
        //     WriteSharedMemory();
        //     Thread.Sleep(100);

        //     if (WaitForServoReady())
        //     {
        //         _sharedMemory.对应实际变量Slave0Controlword = 0x1F;
        //         WriteSharedMemory();
        //     }
        // }

         //----------------伺服停止使能-------------------------
        // public void DisableServo()
        // {
        //     Console.WriteLine("Disabling servo...");
        //     _sharedMemory.对应实际变量Slave0Controlword = 0x00;
        //     WriteSharedMemory();
        //     Thread.Sleep(100);
        //     Console.WriteLine("Servo disabled.");
        // }
        //-------------------------------------------------------------------

        //----------------伺服设置速度-------------------------
        // public void SetVelocity(int velocity)
        // {
        //     _sharedMemory.Slave0TargetVelocity_对应变量实际变量 = velocity;
        //     WriteSharedMemory();
        //     Console.WriteLine($"Set velocity to: {velocity}");
        // }

        public void EnterRealtimeMode()
        {
            Thread.CurrentThread.Priority = ThreadPriority.Highest;

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var schedParam = new SchedParam
                {
                    sched_priority = sched_get_priority_max((int)SchedPolicy.SCHED_FIFO)
                };
                if (sched_setscheduler(0, (int)SchedPolicy.SCHED_FIFO, ref schedParam) != 0)
                {
                    Console.WriteLine("Warning: Failed to set SCHED_FIFO scheduling policy");
                }
            }
        }

       public async Task RunControlSequence(CancellationToken cancellationToken = default)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {

            //这里写具体控制逻辑

            //--------伺服使能----------
             // EnableServo();
            
            //--------设置速度 正负控制方向---------
                //SetVelocity(10000000);
        
            //--------远程IO控制---------
            // _sharedMemory.shm_slave0_rx_var_subindex_002_对应变量实际变量 = 1;  
            // _sharedMemory.shm_slave0_rx_var_subindex_003_对应变量实际变量 = 1;  
            // WriteSharedMemory();
            // Console.WriteLine("LEDs ON");
            
            //  Wait for 1 seconds
            // await Task.Delay(1000, cancellationToken);
            
            // _sharedMemory.shm_slave0_rx_var_subindex_002_对应变量实际变量 = 0;  
            // _sharedMemory.shm_slave0_rx_var_subindex_003_对应变量实际变量 = 0;  
            // WriteSharedMemory();
            // Console.WriteLine("LEDs OFF");
            // await Task.Delay(1000, cancellationToken);

        }
    }

    finally
    {
        Console.WriteLine("Process completed.");
    }
}

        //---------Linux-RT实时性，勿删-----------
        private enum SchedPolicy
        {
            SCHED_FIFO = 1
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct SchedParam
        {
            public int sched_priority;
        }

        [DllImport("libc", EntryPoint = "sched_setscheduler")]
        private static extern int sched_setscheduler(int pid, int policy, ref SchedParam param);

        [DllImport("libc", EntryPoint = "sched_get_priority_max")]
        private static extern int sched_get_priority_max(int policy);
        //--------------------------------------------------------------------

        //共享内存结构体，控制的变量就在这，顺序保持一致
        [StructLayout(LayoutKind.Sequential)]
        private struct EtherCATSharedMemory
        {
            主要就是生成这里
            // public int shm_slave0_rx_output_u16;
            // public int shm_slave0_rx_var_subindex_002;
            // public int shm_slave0_rx_var_subindex_003;
            // public int shm_slave0_rx_var_subindex_004;
            // public int shm_slave0_rx_var_subindex_005;
            // public int shm_slave0_rx_var_subindex_006;
            // public int shm_slave0_rx_var_subindex_007;
            // public int shm_slave0_rx_var_subindex_008;
            // public int shm_slave0_rx_var_subindex_009;
            // public int shm_slave0_rx_var_subindex_010;
            // public int shm_slave0_rx_var_subindex_011;
            // public int shm_slave0_rx_var_subindex_012;
            // public int shm_slave0_rx_var_subindex_013;
            // public int shm_slave0_rx_var_subindex_014;
            // public int shm_slave0_rx_var_subindex_015;
            // public int shm_slave0_rx_var_subindex_016;

            // public int shm_slave0_tx_input_u16;
            // public int shm_slave0_tx_var_subindex_002;
            // public int shm_slave0_tx_var_subindex_003;
            // public int shm_slave0_tx_var_subindex_004;
            // public int shm_slave0_tx_var_subindex_005;
            // public int shm_slave0_tx_var_subindex_006;
            // public int shm_slave0_tx_var_subindex_007;
            // public int shm_slave0_tx_var_subindex_008;
            // public int shm_slave0_tx_var_subindex_009;
            // public int shm_slave0_tx_var_subindex_016;
            // public int shm_slave0_tx_var_subindex_17;
            // public int shm_slave0_tx_var_subindex_18;
            // public int shm_slave0_tx_var_subindex_19;
            // public int shm_slave0_tx_var_subindex_20;
            // public int shm_slave0_tx_var_subindex_21;
            // public int shm_slave0_tx_var_subindex_22;
        }
    }



    class Program
    {
        static async Task Main(string[] args)
        {
            if (args.Length == 0)
            {
                Console.WriteLine($"Usage: ./{Path.GetFileName(Process.GetCurrentProcess().MainModule.FileName)} <shared_memory_file_path>");
                return;
            }

            //必须保留二进制传参，路径为共享内存变量
            string sharedMemoryFilePath = args[0];
            Console.WriteLine($"Starting EtherCAT Control Program with shared memory file: {sharedMemoryFilePath}...");

            var controller = new EtherCATController(sharedMemoryFilePath);

            //进入实时，必须保留
            controller.EnterRealtimeMode();

            //处理退出程序情况逻辑
            var cts = new CancellationTokenSource();
            Console.CancelKeyPress += (s, e) => {
                e.Cancel = true;
                cts.Cancel();
            };

            try
            {
                await controller.RunControlSequence(cts.Token);
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Operation cancelled by user.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }
    }
}
