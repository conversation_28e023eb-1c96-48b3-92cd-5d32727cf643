/// <reference path="../pb_data/types.d.ts" />
migrate((db) => {
  const dao = new Dao(db)
  const collection = dao.findCollectionByNameOrId("um078ovy9kbc6v1")

  // add
  collection.schema.addField(new SchemaField({
    "system": false,
    "id": "hbrrltte",
    "name": "masterIndex",
    "type": "number",
    "required": false,
    "presentable": false,
    "unique": false,
    "options": {
      "min": null,
      "max": null,
      "noDecimal": false
    }
  }))

  // add
  collection.schema.addField(new SchemaField({
    "system": false,
    "id": "wnkcxcai",
    "name": "taskFrequency",
    "type": "number",
    "required": false,
    "presentable": false,
    "unique": false,
    "options": {
      "min": null,
      "max": null,
      "noDecimal": false
    }
  }))

  // add
  collection.schema.addField(new SchemaField({
    "system": false,
    "id": "6zljiwdn",
    "name": "ethercatDir",
    "type": "text",
    "required": false,
    "presentable": false,
    "unique": false,
    "options": {
      "min": null,
      "max": null,
      "pattern": ""
    }
  }))

  return dao.saveCollection(collection)
}, (db) => {
  const dao = new Dao(db)
  const collection = dao.findCollectionByNameOrId("um078ovy9kbc6v1")

  // remove
  collection.schema.removeField("hbrrltte")

  // remove
  collection.schema.removeField("wnkcxcai")

  // remove
  collection.schema.removeField("6zljiwdn")

  return dao.saveCollection(collection)
})
