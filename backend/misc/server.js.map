{"version": 3, "sources": ["node_modules/dotenv/package.json", "node_modules/dotenv/lib/main.js", "src/server.ts", "src/config/index.ts", "src/utils/errors.ts", "src/routes/auth.routes.ts", "src/utils/async-handler.ts", "src/services/program.manager.ts", "src/services/template.service.ts", "src/routes/program.routes.ts", "src/routes/ethercat.routes.ts", "node_modules/lru-cache/src/index.ts", "src/services/ethercat.service.ts", "src/routes/settings.routes.ts", "src/services/settings.service.ts", "src/routes/generator.routes.ts", "src/services/generator.service.ts"], "sourcesContent": ["{\n  \"name\": \"dotenv\",\n  \"version\": \"16.4.5\",\n  \"description\": \"Loads environment variables from .env file\",\n  \"main\": \"lib/main.js\",\n  \"types\": \"lib/main.d.ts\",\n  \"exports\": {\n    \".\": {\n      \"types\": \"./lib/main.d.ts\",\n      \"require\": \"./lib/main.js\",\n      \"default\": \"./lib/main.js\"\n    },\n    \"./config\": \"./config.js\",\n    \"./config.js\": \"./config.js\",\n    \"./lib/env-options\": \"./lib/env-options.js\",\n    \"./lib/env-options.js\": \"./lib/env-options.js\",\n    \"./lib/cli-options\": \"./lib/cli-options.js\",\n    \"./lib/cli-options.js\": \"./lib/cli-options.js\",\n    \"./package.json\": \"./package.json\"\n  },\n  \"scripts\": {\n    \"dts-check\": \"tsc --project tests/types/tsconfig.json\",\n    \"lint\": \"standard\",\n    \"lint-readme\": \"standard-markdown\",\n    \"pretest\": \"npm run lint && npm run dts-check\",\n    \"test\": \"tap tests/*.js --100 -Rspec\",\n    \"test:coverage\": \"tap --coverage-report=lcov\",\n    \"prerelease\": \"npm test\",\n    \"release\": \"standard-version\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"git://github.com/motdotla/dotenv.git\"\n  },\n  \"funding\": \"https://dotenvx.com\",\n  \"keywords\": [\n    \"dotenv\",\n    \"env\",\n    \".env\",\n    \"environment\",\n    \"variables\",\n    \"config\",\n    \"settings\"\n  ],\n  \"readmeFilename\": \"README.md\",\n  \"license\": \"BSD-2-Clause\",\n  \"devDependencies\": {\n    \"@definitelytyped/dtslint\": \"^0.0.133\",\n    \"@types/node\": \"^18.11.3\",\n    \"decache\": \"^4.6.1\",\n    \"sinon\": \"^14.0.1\",\n    \"standard\": \"^17.0.0\",\n    \"standard-markdown\": \"^7.1.0\",\n    \"standard-version\": \"^9.5.0\",\n    \"tap\": \"^16.3.0\",\n    \"tar\": \"^6.1.11\",\n    \"typescript\": \"^4.8.4\"\n  },\n  \"engines\": {\n    \"node\": \">=12\"\n  },\n  \"browser\": {\n    \"fs\": false\n  }\n}\n", "const fs = require('fs')\nconst path = require('path')\nconst os = require('os')\nconst crypto = require('crypto')\nconst packageJson = require('../package.json')\n\nconst version = packageJson.version\n\nconst LINE = /(?:^|^)\\s*(?:export\\s+)?([\\w.-]+)(?:\\s*=\\s*?|:\\s+?)(\\s*'(?:\\\\'|[^'])*'|\\s*\"(?:\\\\\"|[^\"])*\"|\\s*`(?:\\\\`|[^`])*`|[^#\\r\\n]+)?\\s*(?:#.*)?(?:$|$)/mg\n\n// Parse src into an Object\nfunction parse (src) {\n  const obj = {}\n\n  // Convert buffer to string\n  let lines = src.toString()\n\n  // Convert line breaks to same format\n  lines = lines.replace(/\\r\\n?/mg, '\\n')\n\n  let match\n  while ((match = LINE.exec(lines)) != null) {\n    const key = match[1]\n\n    // Default undefined or null to empty string\n    let value = (match[2] || '')\n\n    // Remove whitespace\n    value = value.trim()\n\n    // Check if double quoted\n    const maybeQuote = value[0]\n\n    // Remove surrounding quotes\n    value = value.replace(/^(['\"`])([\\s\\S]*)\\1$/mg, '$2')\n\n    // Expand newlines if double quoted\n    if (maybeQuote === '\"') {\n      value = value.replace(/\\\\n/g, '\\n')\n      value = value.replace(/\\\\r/g, '\\r')\n    }\n\n    // Add to object\n    obj[key] = value\n  }\n\n  return obj\n}\n\nfunction _parseVault (options) {\n  const vaultPath = _vaultPath(options)\n\n  // Parse .env.vault\n  const result = DotenvModule.configDotenv({ path: vaultPath })\n  if (!result.parsed) {\n    const err = new Error(`MISSING_DATA: Cannot parse ${vaultPath} for an unknown reason`)\n    err.code = 'MISSING_DATA'\n    throw err\n  }\n\n  // handle scenario for comma separated keys - for use with key rotation\n  // example: DOTENV_KEY=\"dotenv://:<EMAIL>/vault/.env.vault?environment=prod,dotenv://:<EMAIL>/vault/.env.vault?environment=prod\"\n  const keys = _dotenvKey(options).split(',')\n  const length = keys.length\n\n  let decrypted\n  for (let i = 0; i < length; i++) {\n    try {\n      // Get full key\n      const key = keys[i].trim()\n\n      // Get instructions for decrypt\n      const attrs = _instructions(result, key)\n\n      // Decrypt\n      decrypted = DotenvModule.decrypt(attrs.ciphertext, attrs.key)\n\n      break\n    } catch (error) {\n      // last key\n      if (i + 1 >= length) {\n        throw error\n      }\n      // try next key\n    }\n  }\n\n  // Parse decrypted .env string\n  return DotenvModule.parse(decrypted)\n}\n\nfunction _log (message) {\n  console.log(`[dotenv@${version}][INFO] ${message}`)\n}\n\nfunction _warn (message) {\n  console.log(`[dotenv@${version}][WARN] ${message}`)\n}\n\nfunction _debug (message) {\n  console.log(`[dotenv@${version}][DEBUG] ${message}`)\n}\n\nfunction _dotenvKey (options) {\n  // prioritize developer directly setting options.DOTENV_KEY\n  if (options && options.DOTENV_KEY && options.DOTENV_KEY.length > 0) {\n    return options.DOTENV_KEY\n  }\n\n  // secondary infra already contains a DOTENV_KEY environment variable\n  if (process.env.DOTENV_KEY && process.env.DOTENV_KEY.length > 0) {\n    return process.env.DOTENV_KEY\n  }\n\n  // fallback to empty string\n  return ''\n}\n\nfunction _instructions (result, dotenvKey) {\n  // Parse DOTENV_KEY. Format is a URI\n  let uri\n  try {\n    uri = new URL(dotenvKey)\n  } catch (error) {\n    if (error.code === 'ERR_INVALID_URL') {\n      const err = new Error('INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development')\n      err.code = 'INVALID_DOTENV_KEY'\n      throw err\n    }\n\n    throw error\n  }\n\n  // Get decrypt key\n  const key = uri.password\n  if (!key) {\n    const err = new Error('INVALID_DOTENV_KEY: Missing key part')\n    err.code = 'INVALID_DOTENV_KEY'\n    throw err\n  }\n\n  // Get environment\n  const environment = uri.searchParams.get('environment')\n  if (!environment) {\n    const err = new Error('INVALID_DOTENV_KEY: Missing environment part')\n    err.code = 'INVALID_DOTENV_KEY'\n    throw err\n  }\n\n  // Get ciphertext payload\n  const environmentKey = `DOTENV_VAULT_${environment.toUpperCase()}`\n  const ciphertext = result.parsed[environmentKey] // DOTENV_VAULT_PRODUCTION\n  if (!ciphertext) {\n    const err = new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${environmentKey} in your .env.vault file.`)\n    err.code = 'NOT_FOUND_DOTENV_ENVIRONMENT'\n    throw err\n  }\n\n  return { ciphertext, key }\n}\n\nfunction _vaultPath (options) {\n  let possibleVaultPath = null\n\n  if (options && options.path && options.path.length > 0) {\n    if (Array.isArray(options.path)) {\n      for (const filepath of options.path) {\n        if (fs.existsSync(filepath)) {\n          possibleVaultPath = filepath.endsWith('.vault') ? filepath : `${filepath}.vault`\n        }\n      }\n    } else {\n      possibleVaultPath = options.path.endsWith('.vault') ? options.path : `${options.path}.vault`\n    }\n  } else {\n    possibleVaultPath = path.resolve(process.cwd(), '.env.vault')\n  }\n\n  if (fs.existsSync(possibleVaultPath)) {\n    return possibleVaultPath\n  }\n\n  return null\n}\n\nfunction _resolveHome (envPath) {\n  return envPath[0] === '~' ? path.join(os.homedir(), envPath.slice(1)) : envPath\n}\n\nfunction _configVault (options) {\n  _log('Loading env from encrypted .env.vault')\n\n  const parsed = DotenvModule._parseVault(options)\n\n  let processEnv = process.env\n  if (options && options.processEnv != null) {\n    processEnv = options.processEnv\n  }\n\n  DotenvModule.populate(processEnv, parsed, options)\n\n  return { parsed }\n}\n\nfunction configDotenv (options) {\n  const dotenvPath = path.resolve(process.cwd(), '.env')\n  let encoding = 'utf8'\n  const debug = Boolean(options && options.debug)\n\n  if (options && options.encoding) {\n    encoding = options.encoding\n  } else {\n    if (debug) {\n      _debug('No encoding is specified. UTF-8 is used by default')\n    }\n  }\n\n  let optionPaths = [dotenvPath] // default, look for .env\n  if (options && options.path) {\n    if (!Array.isArray(options.path)) {\n      optionPaths = [_resolveHome(options.path)]\n    } else {\n      optionPaths = [] // reset default\n      for (const filepath of options.path) {\n        optionPaths.push(_resolveHome(filepath))\n      }\n    }\n  }\n\n  // Build the parsed data in a temporary object (because we need to return it).  Once we have the final\n  // parsed data, we will combine it with process.env (or options.processEnv if provided).\n  let lastError\n  const parsedAll = {}\n  for (const path of optionPaths) {\n    try {\n      // Specifying an encoding returns a string instead of a buffer\n      const parsed = DotenvModule.parse(fs.readFileSync(path, { encoding }))\n\n      DotenvModule.populate(parsedAll, parsed, options)\n    } catch (e) {\n      if (debug) {\n        _debug(`Failed to load ${path} ${e.message}`)\n      }\n      lastError = e\n    }\n  }\n\n  let processEnv = process.env\n  if (options && options.processEnv != null) {\n    processEnv = options.processEnv\n  }\n\n  DotenvModule.populate(processEnv, parsedAll, options)\n\n  if (lastError) {\n    return { parsed: parsedAll, error: lastError }\n  } else {\n    return { parsed: parsedAll }\n  }\n}\n\n// Populates process.env from .env file\nfunction config (options) {\n  // fallback to original dotenv if DOTENV_KEY is not set\n  if (_dotenvKey(options).length === 0) {\n    return DotenvModule.configDotenv(options)\n  }\n\n  const vaultPath = _vaultPath(options)\n\n  // dotenvKey exists but .env.vault file does not exist\n  if (!vaultPath) {\n    _warn(`You set DOTENV_KEY but you are missing a .env.vault file at ${vaultPath}. Did you forget to build it?`)\n\n    return DotenvModule.configDotenv(options)\n  }\n\n  return DotenvModule._configVault(options)\n}\n\nfunction decrypt (encrypted, keyStr) {\n  const key = Buffer.from(keyStr.slice(-64), 'hex')\n  let ciphertext = Buffer.from(encrypted, 'base64')\n\n  const nonce = ciphertext.subarray(0, 12)\n  const authTag = ciphertext.subarray(-16)\n  ciphertext = ciphertext.subarray(12, -16)\n\n  try {\n    const aesgcm = crypto.createDecipheriv('aes-256-gcm', key, nonce)\n    aesgcm.setAuthTag(authTag)\n    return `${aesgcm.update(ciphertext)}${aesgcm.final()}`\n  } catch (error) {\n    const isRange = error instanceof RangeError\n    const invalidKeyLength = error.message === 'Invalid key length'\n    const decryptionFailed = error.message === 'Unsupported state or unable to authenticate data'\n\n    if (isRange || invalidKeyLength) {\n      const err = new Error('INVALID_DOTENV_KEY: It must be 64 characters long (or more)')\n      err.code = 'INVALID_DOTENV_KEY'\n      throw err\n    } else if (decryptionFailed) {\n      const err = new Error('DECRYPTION_FAILED: Please check your DOTENV_KEY')\n      err.code = 'DECRYPTION_FAILED'\n      throw err\n    } else {\n      throw error\n    }\n  }\n}\n\n// Populate process.env with parsed values\nfunction populate (processEnv, parsed, options = {}) {\n  const debug = Boolean(options && options.debug)\n  const override = Boolean(options && options.override)\n\n  if (typeof parsed !== 'object') {\n    const err = new Error('OBJECT_REQUIRED: Please check the processEnv argument being passed to populate')\n    err.code = 'OBJECT_REQUIRED'\n    throw err\n  }\n\n  // Set process.env\n  for (const key of Object.keys(parsed)) {\n    if (Object.prototype.hasOwnProperty.call(processEnv, key)) {\n      if (override === true) {\n        processEnv[key] = parsed[key]\n      }\n\n      if (debug) {\n        if (override === true) {\n          _debug(`\"${key}\" is already defined and WAS overwritten`)\n        } else {\n          _debug(`\"${key}\" is already defined and was NOT overwritten`)\n        }\n      }\n    } else {\n      processEnv[key] = parsed[key]\n    }\n  }\n}\n\nconst DotenvModule = {\n  configDotenv,\n  _configVault,\n  _parseVault,\n  config,\n  decrypt,\n  parse,\n  populate\n}\n\nmodule.exports.configDotenv = DotenvModule.configDotenv\nmodule.exports._configVault = DotenvModule._configVault\nmodule.exports._parseVault = DotenvModule._parseVault\nmodule.exports.config = DotenvModule.config\nmodule.exports.decrypt = DotenvModule.decrypt\nmodule.exports.parse = DotenvModule.parse\nmodule.exports.populate = DotenvModule.populate\n\nmodule.exports = DotenvModule\n", "// import express from 'express';\n// import cors from 'cors';\n// import fileUpload from 'express-fileupload';\n// import { config } from './config/index.js';\n// import { errorHandler } from './utils/errors.js';\n// import authRoutes from './routes/auth.routes.js';\n// import programRoutes from './routes/program.routes.js';\n// import ethercatRoutes from './routes/ethercat.routes.js';\n// import settingsRoutes from './routes/settings.routes.js';\n// import generatorRoutes from './routes/generator.routes.js';\n\n// const app = express();\n\n// // 中间件\n// app.use(cors());\n// app.use(express.json());\n// app.use(fileUpload({\n//   createParentPath: true,\n//   limits: { \n//     fileSize: 1000 * 1024 * 1024 // 1000MB max-file-size\n//   },\n// }));\n\n// // 路由\n// app.use('/api/auth', authRoutes);\n// app.use('/api/programs', programRoutes);\n// app.use('/api/ethercat', ethercatRoutes);\n// app.use('/api/settings', settingsRoutes);\n// app.use('/api/generator', generatorRoutes);\n\n// // 错误处理中间件\n// app.use(errorHandler);\n\n// // 启动服务器\n// const PORT = config.port;\n// app.listen(PORT, () => {\n//   console.log(`Server is running on port ${PORT}`);\n// });\n\n// export default app;\n\n\nimport express from 'express';\nimport cors from 'cors';\nimport fileUpload from 'express-fileupload';\nimport path from 'path';\nimport { config } from './config/index.js';\nimport { errorHandler } from './utils/errors.js';\nimport authRoutes from './routes/auth.routes.js';\nimport programRoutes from './routes/program.routes.js';\nimport ethercatRoutes from './routes/ethercat.routes.js';\nimport settingsRoutes from './routes/settings.routes.js';\nimport generatorRoutes from './routes/generator.routes.js';\n\nconst app = express();\n\n// 使用 process.cwd() 获取项目根目录\nconst rootDir = process.cwd();\n\n// 中间件\napp.use(cors());\napp.use(express.json());\napp.use(fileUpload({\n  createParentPath: true,\n  limits: { \n    fileSize: 1000 * 1024 * 1024 // 1000MB max-file-size\n  },\n}));\n\n// 静态文件托管\napp.use(express.static(path.join(rootDir, 'dist')));\n\n// API 路由\napp.use('/api/auth', authRoutes);\napp.use('/api/programs', programRoutes);\napp.use('/api/ethercat', ethercatRoutes);\napp.use('/api/settings', settingsRoutes);\napp.use('/api/generator', generatorRoutes);\n\n// 通配路由，用于 SPA 的前端路由\napp.get('*', (req, res) => {\n  res.sendFile(path.join(rootDir, 'dist', 'index.html'));\n});\n\n// 错误处理中间件\napp.use(errorHandler);\n\n// 启动服务器\nconst PORT = config.port;\napp.listen(PORT, () => {\n  console.log(`Server is running on port ${PORT}`);\n});\n\nexport default app;\n", "import dotenv from 'dotenv';\nimport path from 'path';\n\n// 使用 process.cwd() 替代 import.meta.url\nconst rootDir = process.cwd();\n\n// 加载环境变量\ndotenv.config({ path: path.resolve(rootDir, '.env') });\n\nexport const config = {\n  port: process.env.PORT || 3000,\n  nodeEnv: process.env.NODE_ENV || 'development',\n  pocketbaseUrl: process.env.POCKETBASE_URL || 'http://127.0.0.1:8090',\n  ethercatDir: process.env.ETHERCAT_DIR || '/dev/shm/ethercat',\n  taskFrequency: parseInt(process.env.TASK_FREQUENCY || '1000', 10),\n  pocketbase: {\n    adminEmail: process.env.POCKETBASE_ADMIN_EMAIL || '<EMAIL>',\n    adminPassword: process.env.POCKETBASE_ADMIN_PASSWORD || 'your-password'\n  }\n};", "import { Request, Response, NextFunction } from 'express';\r\n\r\nexport class ApiError extends <PERSON>rror {\r\n  constructor(\r\n    public statusCode: number,\r\n    message: string\r\n  ) {\r\n    super(message);\r\n    this.name = 'ApiError';\r\n  }\r\n}\r\n\r\nexport function errorHandler(err: Error, req: Request, res: Response, next: NextFunction) {\r\n  if (err instanceof ApiError) {\r\n    res.status(err.statusCode).json({\r\n      status: err.statusCode,\r\n      message: err.message\r\n    });\r\n    return;\r\n  }\r\n\r\n  console.error(err);\r\n  res.status(500).json({\r\n    status: 500,\r\n    message: '服务器内部错误'\r\n  });\r\n} ", "import { Router, Request, Response } from 'express';\r\nimport { async<PERSON>and<PERSON> } from '../utils/async-handler.js';\r\nimport PocketBase from 'pocketbase';\r\nimport { config } from '../config/index.js';\r\nimport { PocketBaseManager } from '../services/program.manager.js';\r\n\r\nconst router = Router();\r\n\r\nrouter.post('/login', asyncHandler(async (req: Request, res: Response) => {\r\n  const { username, password } = req.body;\r\n  const pb = new PocketBase(config.pocketbaseUrl);\r\n  \r\n  try {\r\n    const authData = await pb.collection('users').authWithPassword(username, password);\r\n    \r\n    // 保存认证 token\r\n    PocketBaseManager.setAuthToken(authData.token);\r\n    \r\n    res.json({\r\n      status: 200,\r\n      data: {\r\n        token: authData.token,\r\n        user: authData.record\r\n      }\r\n    });\r\n  } catch (error) {\r\n    console.error('Login error:', error);\r\n    res.status(401).json({\r\n      status: 401,\r\n      message: 'Login failed',\r\n      error: error.response?.data || error.message\r\n    });\r\n  }\r\n}));\r\n\r\nrouter.post('/logout', asyncHandler(async (req: Request, res: Response) => {\r\n  PocketBaseManager.clearAuth();\r\n  \r\n  res.json({\r\n    status: 200,\r\n    data: { success: true }\r\n  });\r\n}));\r\n\r\nrouter.post('/change-password/:id', asyncHandler(async (req: Request, res: Response) => {\r\n  const { id } = req.params;\r\n  const { oldPassword, password, passwordConfirm } = req.body;\r\n\r\n  try {\r\n    const pb = await PocketBaseManager.ensureAuth();\r\n    \r\n    await pb.collection('users').update(id, {\r\n      oldPassword,\r\n      password,\r\n      passwordConfirm\r\n    });\r\n\r\n    res.json({\r\n      status: 200,\r\n      message: '密码修改成功'\r\n    });\r\n  } catch (error) {\r\n    console.error('Failed to change password:', error);\r\n    res.status(400).json({\r\n      status: 400,\r\n      message: '密码修改失败',\r\n      error: error.response?.data || error.message\r\n    });\r\n  }\r\n}));\r\n\r\nexport default router; ", "import { Request, Response, NextFunction } from 'express';\r\nimport { ApiError } from './errors.js';\r\n\r\nexport const asyncHandler = (fn: Function) => (req: Request, res: Response, next: NextFunction) => {\r\n  Promise.resolve(fn(req, res, next)).catch((error) => {\r\n    if (error instanceof ApiError) {\r\n      res.status(error.statusCode).json({\r\n        status: error.statusCode,\r\n        message: error.message\r\n      });\r\n    } else {\r\n      console.error('Unhandled error:', error);\r\n      res.status(500).json({\r\n        status: 500,\r\n        message: 'Internal Server Error'\r\n      });\r\n    }\r\n  });\r\n}; ", "import { ApiError } from '../utils/errors.js';\nimport PocketBase from 'pocketbase';\nimport { config } from '../config/index.js';\nimport { UploadedFile } from 'express-fileupload';\nimport fs from 'fs/promises';\nimport path from 'path';\nimport { TemplateService } from './template.service.js';\nimport { exec } from 'child_process';\nimport { promisify } from 'util';\nimport { ProgramConfig } from '../types/index.js';\nimport archiver from 'archiver';\nimport { Readable } from 'stream';\nimport { spawn } from 'child_process';\nimport { existsSync } from 'fs';\nimport decompress from 'decompress';\nconst execAsync = promisify(exec);\n\n// 使用 PocketBaseManager 来管理 PocketBase 实例\nexport class PocketBaseManager {\n  private static instance: PocketBase | null = null;\n  private static authToken: string | null = null;\n\n  static getInstance(): PocketBase {\n    if (!this.instance) {\n      this.instance = new PocketBase(config.pocketbaseUrl);\n      if (this.authToken) {\n        try {\n          this.instance.authStore.save(this.authToken, null);\n        } catch (error) {\n          console.error('Failed to restore auth token:', error);\n          this.authToken = null;\n        }\n      }\n    }\n    return this.instance;\n  }\n\n  static async ensureAuth(): Promise<PocketBase> {\n    const pb = this.getInstance();\n    if (!pb.authStore.isValid) {\n      try {\n        await pb.collection('users').authWithPassword(\n          '<EMAIL>',  // 使用实际的用户凭证\n          'qs2022qs2022'\n        );\n      } catch (error) {\n        console.error('Auth failed:', error);\n        throw new Error('Authentication failed');\n      }\n    }\n    return pb;\n  }\n\n  static setAuthToken(token: string) {\n    this.authToken = token;\n    if (this.instance) {\n      try {\n        this.instance.authStore.save(token, null);\n      } catch (error) {\n        console.error('Failed to save auth token:', error);\n      }\n    }\n  }\n\n  static clearAuth() {\n    this.authToken = null;\n    if (this.instance) {\n      this.instance.authStore.clear();\n    }\n  }\n}\n\ninterface ProgramRecord {\n  id: string;\n  name: string;\n  config: ProgramConfig;\n}\n\nexport class ProgramManager {\n  static async getPrograms() {\n    try {\n      const pb = await PocketBaseManager.ensureAuth();\n      \n      const records = await pb.collection('programs').getList(1, 100, {\n        sort: '-uploadTime',\n        fields: 'id,name,status,uploadTime,masterIndex,taskFrequency,ethercatDir,config,filePath'\n      });\n\n      return records.items;\n    } catch (error) {\n      console.error('Failed to get programs:', error);\n      throw new ApiError(500, '获取程序列表失败');\n    }\n  }\n\n  static async uploadProgram(program: UploadedFile, config: UploadedFile, formData: any) {\n    try {\n      const pb = await PocketBaseManager.ensureAuth();\n\n      const uploadDir = path.join(process.cwd(), 'uploads');\n      await fs.mkdir(uploadDir, { recursive: true });\n\n      const programName = formData.name;\n      const archivePath = path.join(uploadDir, `${programName}.zip`);  // 压缩包临时路径\n      const programDir = path.join(uploadDir, programName);  // 程序目录\n\n      // 确保程序目录不存在\n      if (existsSync(programDir)) {\n        await fs.rm(programDir, { recursive: true, force: true });\n      }\n\n      // 1. 移动上传的压缩包到临时位置\n      await program.mv(archivePath);\n\n      try {\n        // 2. 解压缩文件\n        await decompress(archivePath, uploadDir);\n        \n        // 3. 获取解压后的目录名（可能与压缩包名不同）\n        const extractedFiles = await fs.readdir(uploadDir);\n        const extractedDir = extractedFiles.find(\n          file => (file !== `${programName}.zip`) && existsSync(path.join(uploadDir, file))\n        );\n\n        if (!extractedDir) {\n          throw new ApiError(400, '解压缩失败或压缩包为空');\n        }\n\n        // 4. 如果解压后的目录名与程序名不同，则重命名\n        const extractedPath = path.join(uploadDir, extractedDir);\n        if (extractedPath !== programDir) {\n          await fs.rename(extractedPath, programDir);\n        }\n\n        // 5. 删除临时压缩包\n        await fs.unlink(archivePath);\n\n        // 6. 解析配置文件\n        let configData;\n        try {\n          const configStr = config.data.toString('utf-8');\n          configData = JSON.parse(configStr);\n        } catch (error) {\n          console.error('Failed to parse config:', error);\n          throw new ApiError(400, '配置文件格式错误');\n        }\n\n        // 7. 查找主程序文件\n        const files = await fs.readdir(programDir);\n        const mainProgram = files.find(file => {\n          const filePath = path.join(programDir, file);\n          return existsSync(filePath) && !file.endsWith('.json') && !file.endsWith('.md');\n        });\n\n        if (!mainProgram) {\n          throw new ApiError(400, '未找到主程序文件');\n        }\n\n        const mainProgramPath = path.join(programDir, mainProgram);\n\n        // 8. 给主程序添加执行权限\n        await fs.chmod(mainProgramPath, 0o755);\n\n        // 9. 创建数据库记录\n        return await pb.collection('programs').create({\n          name: programName,\n          status: 'stopped',\n          uploadTime: new Date().toISOString(),\n          masterIndex: formData.masterIndex?.toString() || '0',\n          taskFrequency: formData.taskFrequency?.toString() || '1000',\n          config: configData,\n          filePath: mainProgramPath  // 保存主程序的完整路径\n        });\n\n      } catch (error) {\n        // 清理临时文件和目录\n        if (existsSync(archivePath)) {\n          await fs.unlink(archivePath);\n        }\n        if (existsSync(programDir)) {\n          await fs.rm(programDir, { recursive: true, force: true });\n        }\n        throw error;\n      }\n\n    } catch (error) {\n      console.error('Failed to upload program:', error);\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(500, '程序上传失败');\n    }\n  }\n\n  static async startProgram(id: string) {\n    try {\n      const pb = await PocketBaseManager.ensureAuth();\n      const program = await pb.collection('programs').getOne(id);\n      \n      const shortId = id.substring(0, 6);\n      const shmFile = `/dev/shm/${id}_${program.name}_shm`;\n      \n      console.log('Starting program with config:', {\n        programName: program.name,\n        shortId,\n        shmFile,\n        programPath: program.filePath\n      });\n\n      // 1. 生成 C 语言模板\n      const template = await this.generateTemplate(id);\n      \n      // 2. 写入临时文件\n      const tempDir = path.join(process.cwd(), 'temp');\n      await fs.mkdir(tempDir, { recursive: true });\n      const templatePath = path.join(tempDir, `ethercat_${shortId}.c`);\n      await fs.writeFile(templatePath, template);\n\n      // 3. 编译 C 程序\n      const outputPath = path.join(tempDir, `ethercat_${shortId}`);\n      try {\n        await execAsync(`gcc ${templatePath} -o ${outputPath} -lethercat`);\n      } catch (error) {\n        console.error('Compilation failed:', error);\n        throw new ApiError(500, '程序编译失败');\n      }\n\n      // 4. 给两个程序添加执行权限\n      try {\n        await fs.chmod(outputPath, 0o755);\n\n        // c#程序\n        await fs.chmod(program.filePath, 0o755);\n      } catch (error) {\n        console.error('Failed to set executable permission:', error);\n        throw new ApiError(500, '设置执行权限失败');\n      }\n\n      // 5. 先运行中间层程序（不需要参数）\n      try {\n        const middleLayerCmd = `nohup ${outputPath} > ${tempDir}/ethercat_${shortId}.log 2>&1 &`;\n        console.log('Starting middleware with command:', middleLayerCmd);\n        await execAsync(middleLayerCmd);\n        \n        await new Promise(resolve => setTimeout(resolve, 1000));\n        \n        const { stdout: middleLayerPid } = await execAsync(`pgrep -f ethercat_${shortId}`);\n        if (!middleLayerPid) {\n          throw new Error('Middle layer program failed to start');\n        }\n        console.log('Middleware started with PID:', middleLayerPid);\n\n        // 6. 运行用户程序（传入共享内存文件路径）\n        const userCmd = `nohup ${program.filePath} \"${shmFile}\" > ${tempDir}/user_${shortId}.log 2>&1 &`;\n        console.log('Starting user program with command:', userCmd);\n        await execAsync(userCmd);\n        \n        await new Promise(resolve => setTimeout(resolve, 1000));\n        \n        const { stdout: userProgramPid } = await execAsync(`pgrep -f \"${path.basename(program.filePath)}\"`);\n        if (!userProgramPid) {\n          console.error('User program failed to start, checking logs...');\n          try {\n            const { stdout: userLog } = await execAsync(`tail -n 20 ${tempDir}/user_${shortId}.log`);\n            console.error('User program log:', userLog);\n          } catch (logError) {\n            console.error('Failed to read user program log:', logError);\n          }\n          await execAsync(`pkill -f ethercat_${shortId}`);\n          throw new Error('User program failed to start');\n        }\n        console.log('User program started with PID:', userProgramPid);\n\n      } catch (error) {\n        console.error('Failed to run programs:', error);\n        try {\n          await execAsync(`pkill -f ethercat_${shortId}`);\n          await execAsync(`pkill -f \"${path.basename(program.filePath)}\"`);\n        } catch (cleanupError) {\n          console.error('Cleanup error:', cleanupError);\n        }\n        throw new ApiError(500, '程序运行失败');\n      }\n\n      // 7. 更新程序状态\n      return await pb.collection('programs').update(id, {\n        status: 'running'\n      });\n    } catch (error) {\n      console.error('Failed to start program:', error);\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(500, '启动程序失败');\n    }\n  }\n\n  static async stopProgram(id: string) {\n    const pb = await PocketBaseManager.ensureAuth();\n    const program = await pb.collection('programs').getOne(id);\n    const shortId = id.substring(0, 6);\n    const programName = program.name;\n\n    console.log('Starting program termination process...');\n    console.log(`Looking for processes: ethercat_${shortId} and ${programName}`);\n\n    const execWithLog = async (cmd: string) => {\n      try {\n        const { stdout, stderr } = await execAsync(cmd);\n        console.log(`Command executed: ${cmd}`);\n        if (stdout) console.log('stdout:', stdout);\n        if (stderr) console.log('stderr:', stderr);\n        return { stdout, stderr };\n      } catch (error) {\n        console.log(`Command failed but continuing: ${cmd}`);\n        console.log('Error:', error);\n        return { stdout: '', stderr: '' };\n      }\n    };\n\n    try {\n      // 1. 检查初始进程状态\n      console.log('Checking initial process status...');\n      const { stdout: initialCheck } = await execWithLog(`ps aux | grep -E \"ethercat_${shortId}|${programName}\" | grep -v grep`);\n      if (initialCheck) {\n        console.log('Found running processes:', initialCheck);\n      }\n\n      // 2. 先终止用户程序\n      console.log('Terminating user program...');\n      await execWithLog(`pkill -f \"${programName}\"`);\n\n      // 3. 等待用户程序退出\n      console.log('Waiting for user program to terminate...');\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // 4. 检查用户程序是否已退出\n      const { stdout: userCheck } = await execWithLog(`ps aux | grep \"${programName}\" | grep -v grep`);\n      if (userCheck) {\n        console.log('Warning: User program might still be running:', userCheck);\n      } else {\n        console.log('User program successfully terminated');\n      }\n\n      // 5. 终止中间层程序\n      console.log('Terminating middleware program...');\n      await execWithLog(`pkill -f ethercat_${shortId}`);\n\n      // 6. 等待中间层程序退出\n      console.log('Waiting for middleware program to terminate...');\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // 7. 最终检查\n      console.log('Performing final process check...');\n      const { stdout: finalCheck } = await execWithLog(`ps aux | grep -E \"ethercat_${shortId}|${programName}\" | grep -v grep`);\n      \n      if (finalCheck) {\n        console.log('Warning: Some processes might still be running:', finalCheck);\n      } else {\n        console.log('All processes successfully terminated');\n      }\n\n    } catch (error) {\n      console.error('Error during process termination:', error);\n    }\n\n    // 更新程序状态\n    console.log('Updating program status to stopped');\n    try {\n      return await pb.collection('programs').update(id, {\n        status: 'stopped'\n      });\n    } catch (error) {\n      console.error('Failed to update program status:', error);\n      throw new ApiError(500, '更新程序状态失败');\n    }\n  }\n\n  static async deleteProgram(id: string) {\n    try {\n      const pb = await PocketBaseManager.ensureAuth();\n      \n      const record = await pb.collection('programs').getOne(id);\n      \n      await Promise.all([\n        record.filePath ? fs.unlink(record.filePath).catch(console.error) : Promise.resolve(),\n        pb.collection('programs').delete(id)\n      ]);\n    } catch (error) {\n      console.error('Failed to delete program:', error);\n      throw new ApiError(500, '删除程序失败');\n    }\n  }\n\n  static async updateConfig(id: string, config: ProgramConfig) {\n    try {\n      const pb = await PocketBaseManager.ensureAuth();\n      return await pb.collection('programs').update(id, {\n        masterIndex: config.masterIndex,\n        taskFrequency: config.taskFrequency,\n        config: config.config\n      });\n    } catch (error) {\n      console.error('Failed to update config:', error);\n      throw new ApiError(500, '更新配置失败');\n    }\n  }\n\n  static async generateTemplate(id: string): Promise<string> {\n    try {\n      const pb = await PocketBaseManager.ensureAuth();\n      const program = await pb.collection('programs').getOne(id);\n      \n      // 构造模板配置\n      const config = {\n        id: program.id,  // 添加程序ID\n        name: program.name,  // 添加程序名称\n        masterIndex: program.masterIndex,\n        taskFrequency: program.taskFrequency,\n        ethercatDir: program.ethercatDir,\n        slaves: program.config.slaves.map((slave: any) => ({\n          name: slave.name,\n          index: slave.index,\n          vid: slave.vid,\n          pid: slave.pid,\n          rx_pdo: slave.rx_pdo,\n          tx_pdo: slave.tx_pdo,\n          rx_pdos: slave.rx_pdos,\n          tx_pdos: slave.tx_pdos\n        }))\n      };\n\n      // 生成模板\n      return await TemplateService.generateCTemplate(config);\n    } catch (error) {\n      console.error('Failed to generate template:', error);\n      throw new ApiError(500, '生成模板失败');\n    }\n  }\n\n  static async replaceProgram(id: string, newProgram: UploadedFile, formData: any) {\n    try {\n      const pb = await PocketBaseManager.ensureAuth();\n      \n      // 获取当前程序记录\n      const currentProgram = await pb.collection('programs').getOne(id);\n      \n      // 检查程序状态\n      if (currentProgram.status === 'running') {\n        throw new ApiError(400, '无法替换正在运行的程序，请先停止程序');\n      }\n\n      const uploadDir = path.join(process.cwd(), 'uploads');\n      await fs.mkdir(uploadDir, { recursive: true });\n\n      // 使用原来的程序名\n      const programPath = currentProgram.filePath;\n      \n      try {\n        // 删除旧程序文件\n        await fs.unlink(programPath);\n      } catch (error) {\n        console.error('Failed to delete old program:', error);\n      }\n\n      // 移动新程序文件\n      await newProgram.mv(programPath);\n      \n      // 给新程序添加执行权限\n      await fs.chmod(programPath, 0o755);\n\n      // 更新记录\n      return await pb.collection('programs').update(id, {\n        uploadTime: new Date().toISOString(),\n        masterIndex: formData.masterIndex?.toString() || currentProgram.masterIndex,\n        taskFrequency: formData.taskFrequency?.toString() || currentProgram.taskFrequency,\n        ethercatDir: formData.ethercatDir || currentProgram.ethercatDir,\n        // 保持原有配置不变\n        config: currentProgram.config,\n        filePath: programPath\n      });\n\n    } catch (error) {\n      console.error('Failed to replace program:', error);\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(500, '替换程序失败');\n    }\n  }\n\n  static async createProgram(filename: string, filePath: string, formData: any) {\n    try {\n      const pb = await PocketBaseManager.ensureAuth();\n\n      // 创建记录\n      return await pb.collection('programs').create({\n        name: filename,\n        status: 'stopped',\n        uploadTime: new Date().toISOString(),\n        masterIndex: formData.masterIndex?.toString() || '0',\n        taskFrequency: formData.taskFrequency?.toString() || '1000',\n        config: formData.config,  // 直接使用传入的配置对象\n        filePath: filePath\n      });\n    } catch (error) {\n      console.error('Failed to create program:', error);\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(500, '创建程序失败');\n    }\n  }\n\n  static async getProgram(id: string): Promise<ProgramRecord | null> {\n    try {\n      const pb = await PocketBaseManager.ensureAuth();\n      const record = await pb.collection('programs').getOne(id);\n      return {\n        id: record.id,\n        name: record.name,\n        config: record.config\n      } as ProgramRecord;\n    } catch (error) {\n      console.error('Failed to get program:', error);\n      return null;\n    }\n  }\n\n  static getProgramDir(): string {\n    return path.join(process.cwd(), 'programs');\n  }\n\n  static async downloadFullPackage(programId: string): Promise<Buffer> {\n    try {\n      // 获取程序信息\n      const pb = await PocketBaseManager.ensureAuth();\n      const record = await pb.collection('programs').getOne(programId);\n      if (!record) {\n        throw new ApiError(404, '程序不存在');\n      }\n\n      // 创建压缩文件\n      const archive = archiver('zip', {\n        zlib: { level: 0 }\n      });\n\n      // 创建一个 Promise 来处理压缩完成\n      const zipBuffer = await new Promise<Buffer>((resolve, reject) => {\n        const chunks: Buffer[] = [];\n        archive.on('data', (chunk: Buffer) => chunks.push(chunk));\n        archive.on('end', () => resolve(Buffer.concat(chunks)));\n        archive.on('error', reject);\n\n        // 添加配置文件\n        const configJson = JSON.stringify(record.config, null, 2);\n        archive.append(configJson, { name: 'slave_config.json' });\n\n        // 添加程序文件，使用 filePath\n        archive.file(record.filePath, { name: record.name });\n\n        // 完成打包\n        archive.finalize();\n      });\n\n      return zipBuffer;\n    } catch (error) {\n      console.error('Failed to create program package:', error);\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(500, '程序包生成失败');\n    }\n  }\n} ", "import { ApiError } from '../utils/errors.js';\r\nimport fs from 'fs/promises';\r\nimport path from 'path';\r\nimport { exec } from 'child_process';\r\nimport { promisify } from 'util';\r\nconst execAsync = promisify(exec);\r\n\r\ninterface SyncInfo {\r\n  index: number;\r\n  syncInfo: string;\r\n}\r\n\r\ninterface SlaveConfig {\r\n  index: number;\r\n  name: string;\r\n  vid: string;\r\n  pid: string;\r\n  rx_pdo: string;\r\n  tx_pdo: string;\r\n  rx_pdos?: PDOConfig[];\r\n  tx_pdos?: PDOConfig[];\r\n}\r\n\r\ninterface PDOConfig {\r\n  name: string;\r\n  index: string;\r\n  type: string;\r\n  subindex?: string;\r\n}\r\n\r\ninterface TemplateConfig {\r\n  masterIndex: number;\r\n  taskFrequency: number;\r\n  ethercatDir: string;\r\n  slaves: SlaveConfig[];\r\n}\r\n\r\nexport class TemplateService {\r\n  static async generateCTemplate(config: TemplateConfig): Promise<string> {\r\n    try {\r\n      // 首先获取每个从站的同步管理器信息\r\n      const syncInfos = await Promise.all(\r\n        config.slaves.map(async (slave: SlaveConfig, index: number) => {\r\n          try {\r\n            const { stdout } = await execAsync(`ethercat cstruct | sed -n '/ec_sync_info_t slave_${slave.index}_syncs\\\\[\\\\]/,/};/p'`);\r\n            if (!stdout.trim()) {\r\n              throw new Error(`No sync info found for slave ${index} (position ${slave.index})`);\r\n            }\r\n\r\n            // 修改变量名以匹配我们的命名规则\r\n            const modifiedSyncInfo = stdout.trim()\r\n              .replace(\r\n                `ec_sync_info_t slave_${slave.index}_syncs[]`,\r\n                `ec_sync_info_t slave${index}_syncs[]`\r\n              )\r\n              .replace(\r\n                new RegExp(`slave_${slave.index}_pdos`, 'g'),\r\n                `slave${index}_pdos`\r\n              );\r\n\r\n            return {\r\n              index,\r\n              syncInfo: modifiedSyncInfo\r\n            } as SyncInfo;\r\n          } catch (error) {\r\n            if (error instanceof Error) {\r\n              throw new Error(`Failed to get sync info for slave ${index} (position ${slave.index}): ${error.message}`);\r\n            }\r\n            throw new Error(`Failed to get sync info for slave ${index} (position ${slave.index}). Please check slave connection.`);\r\n          }\r\n        })\r\n      );\r\n\r\n      let template = '';\r\n      template += this.generateHeaders();\r\n      template += this.generateShmStructure(config);\r\n      template += this.generateAppParams(config);\r\n      template += this.generateEtherCATConfig(config);\r\n      template += this.generatePDOMapping(config, syncInfos);  // 传入 syncInfos\r\n      template += this.generateShmFunctions();\r\n      template += this.generateCyclicTask(config);\r\n      template += this.generateMainFunction(config);\r\n      \r\n      return template;\r\n    } catch (error: any) {\r\n      console.error('Failed to generate template:', error);\r\n      throw new ApiError(500, error.message || '生成模板失败');\r\n    }\r\n  }\r\n\r\n  private static generateHeaders(): string {\r\n    return `\r\n#include <errno.h>\r\n#include <signal.h>\r\n#include <stdio.h>\r\n#include <string.h>\r\n#include <sys/resource.h>\r\n#include <sys/time.h>\r\n#include <sys/types.h>\r\n#include <unistd.h>\r\n#include <sys/mman.h>\r\n#include <fcntl.h>\r\n#include <sched.h>\r\n#include <sys/stat.h>\r\n#include <sys/shm.h>\r\n\r\n#include \"ecrt.h\"\r\n\r\n// Forward declarations\r\nvoid cleanup_shm(void);\r\nvoid signal_handler(int sig);\r\n\r\n// Signal handler implementation\r\nvoid signal_handler(int sig) {\r\n    printf(\"\\\\nSignal %d received, cleaning up...\\\\n\", sig);\r\n    cleanup_shm();\r\n    exit(sig);\r\n}\r\n`;\r\n  }\r\n\r\n  private static generateShmStructure(config: any): string {\r\n    const shmFileName = `${config.id}_${config.name}_shm`;\r\n\r\n    let struct = `\r\n/* Shared memory configuration */\r\n#define ETHERCAT_SHM_FILE \"${shmFileName}\"\r\n#define ETHERCAT_SHM_SIZE (sizeof(ethercat_shm_t))\r\n\r\n/* Shared memory structure */\r\ntypedef struct {\r\n`;\r\n\r\n    // 为每个从站的每个 PDO 添加变量，使用 shm_ 前缀和 rx/tx 区分\r\n    config.slaves.forEach((slave: any, index: number) => {\r\n      slave.rx_pdos?.forEach((pdo: any) => {\r\n        const varName = this.sanitizeVariableName(pdo, index, 'rx');\r\n        const comment = pdo.comment ? ` /* ${pdo.comment} */` : '';\r\n        struct += `    int shm_slave${index}_rx_${varName};${comment}\\n`;\r\n      });\r\n      slave.tx_pdos?.forEach((pdo: any) => {\r\n        const varName = this.sanitizeVariableName(pdo, index, 'tx');\r\n        const comment = pdo.comment ? ` /* ${pdo.comment} */` : '';\r\n        struct += `    int shm_slave${index}_tx_${varName};${comment}\\n`;\r\n      });\r\n    });\r\n\r\n    struct += `} ethercat_shm_t;\r\n\r\nstatic ethercat_shm_t *ethercat_shm = NULL;\r\n`;\r\n\r\n    return struct;\r\n  }\r\n\r\n  private static sanitizeVariableName(pdo: any, slaveIndex: number, direction: 'rx' | 'tx'): string {\r\n    if (!pdo || !pdo.index || !pdo.name) {\r\n      throw new Error('Invalid PDO data');\r\n    }\r\n    // 构造变量名: shm_slave{index}_rx/tx_index_name\r\n    const baseName = `shm_slave${slaveIndex}_${direction}_${pdo.index.toLowerCase()}_${pdo.name.toLowerCase()}`;\r\n    return baseName.replace(/[^a-z0-9_]/g, '_');\r\n  }\r\n\r\n  private static generateShmFunctions(): string {\r\n    return `\r\nvoid create_shm() {\r\n    // Create and open shared memory\r\n    int fd = shm_open(ETHERCAT_SHM_FILE, O_CREAT | O_RDWR, 0666);\r\n    if (fd < 0) {\r\n        perror(\"shm_open failed\");\r\n        exit(EXIT_FAILURE);\r\n    }\r\n\r\n    // Set the size of shared memory\r\n    if (ftruncate(fd, ETHERCAT_SHM_SIZE) < 0) {\r\n        perror(\"ftruncate failed\");\r\n        exit(EXIT_FAILURE);\r\n    }\r\n\r\n    // Map shared memory\r\n    ethercat_shm = (ethercat_shm_t *)mmap(NULL, ETHERCAT_SHM_SIZE, \r\n                                         PROT_READ | PROT_WRITE, \r\n                                         MAP_SHARED, fd, 0);\r\n    if (ethercat_shm == MAP_FAILED) {\r\n        perror(\"mmap failed\");\r\n        exit(EXIT_FAILURE);\r\n    }\r\n\r\n    // Initialize shared memory to zero\r\n    memset(ethercat_shm, 0, ETHERCAT_SHM_SIZE);\r\n    \r\n    // Close file descriptor (mapping remains valid)\r\n    close(fd);\r\n}\r\n\r\nvoid cleanup_shm() {\r\n    if (ethercat_shm != NULL) {\r\n        munmap(ethercat_shm, ETHERCAT_SHM_SIZE);\r\n        shm_unlink(ETHERCAT_SHM_FILE);\r\n    }\r\n}\r\n`;\r\n  }\r\n\r\n  private static generateAppParams(config: TemplateConfig): string {\r\n    return `\r\n/* Application Parameters */\r\n#define TASK_FREQUENCY ${config.taskFrequency || 1000} /*Hz*/\r\n`;\r\n  }\r\n\r\n  private static generateEtherCATConfig(config: TemplateConfig): string {\r\n    let conf = `\\n/* EtherCAT configurations */\\n`;\r\n    \r\n    // 添加主站索引定义\r\n    conf += `#define MASTER_INDEX ${config.masterIndex || 0}\\n\\n`;\r\n    \r\n    conf += `static ec_master_t *master = NULL;\\n`;\r\n    conf += `static ec_master_state_t master_state = {};\\n`;\r\n    conf += `static ec_domain_t *domain1 = NULL;\\n`;\r\n    conf += `static ec_domain_state_t domain1_state = {};\\n`;\r\n    \r\n    // 为每个从站创建独立的 sc 对象\r\n    config.slaves.forEach((slave: any, index: number) => {\r\n      conf += `static ec_slave_config_t *sc_slave${index} = NULL;\\n`;\r\n    });\r\n    \r\n    conf += `static ec_slave_config_state_t sc_state = {};\\n`;\r\n    conf += `static uint8_t *domain1_pd = NULL;\\n\\n`;\r\n\r\n    // 为每个从站生成位置和VID_PID定义\r\n    config.slaves.forEach((slave: any, index: number) => {\r\n      conf += `#define slave${index}_POS 0,${slave.index}\\n`;\r\n      conf += `#define slave${index}_VID_PID ${slave.vid},${slave.pid}\\n`;\r\n    });\r\n\r\n    return conf;\r\n  }\r\n\r\n  private static generatePDOMapping(config: any, syncInfos: SyncInfo[]): string {\r\n    let mapping = `\\n/* PDO mapping */\\n`;\r\n    \r\n    // 生成 PDO 偏移结构，使用 pdo_ 前缀和 rx/tx 区分\r\n    mapping += `static struct {\\n`;\r\n    config.slaves.forEach((slave: any, index: number) => {\r\n      slave.rx_pdos?.forEach((pdo: any) => {\r\n        const varName = this.sanitizeVariableName(pdo, index, 'rx');\r\n        if (pdo.type === 'bool') {\r\n          mapping += `    unsigned int pdo_slave${index}_rx_${varName}_off;\\n`;\r\n          mapping += `    unsigned int pdo_slave${index}_rx_${varName}_bit;\\n`;\r\n        } else {\r\n          mapping += `    unsigned int pdo_slave${index}_rx_${varName};\\n`;\r\n        }\r\n      });\r\n      slave.tx_pdos?.forEach((pdo: any) => {\r\n        const varName = this.sanitizeVariableName(pdo, index, 'tx');\r\n        if (pdo.type === 'bool') {\r\n          mapping += `    unsigned int pdo_slave${index}_tx_${varName}_off;\\n`;\r\n          mapping += `    unsigned int pdo_slave${index}_tx_${varName}_bit;\\n`;\r\n        } else {\r\n          mapping += `    unsigned int pdo_slave${index}_tx_${varName};\\n`;\r\n        }\r\n      });\r\n    });\r\n    mapping += `} offset;\\n\\n`;\r\n\r\n    // 生成 PDO 条目注册，使相应的前缀\r\n    mapping += `const static ec_pdo_entry_reg_t domain1_regs[] = {\\n`;\r\n    config.slaves.forEach((slave: any, index: number) => {\r\n      slave.rx_pdos?.forEach((pdo: any) => {\r\n        const varName = this.sanitizeVariableName(pdo, index, 'rx');\r\n        if (pdo.type === 'bool') {\r\n          mapping += `    {slave${index}_POS, slave${index}_VID_PID, ${pdo.index}, ${pdo.subindex}, &offset.pdo_slave${index}_rx_${varName}_off, &offset.pdo_slave${index}_rx_${varName}_bit},\\n`;\r\n        } else {\r\n          mapping += `    {slave${index}_POS, slave${index}_VID_PID, ${pdo.index}, ${pdo.subindex}, &offset.pdo_slave${index}_rx_${varName}},\\n`;\r\n        }\r\n      });\r\n      slave.tx_pdos?.forEach((pdo: any) => {\r\n        const varName = this.sanitizeVariableName(pdo, index, 'tx');\r\n        if (pdo.type === 'bool') {\r\n          mapping += `    {slave${index}_POS, slave${index}_VID_PID, ${pdo.index}, ${pdo.subindex}, &offset.pdo_slave${index}_tx_${varName}_off, &offset.pdo_slave${index}_tx_${varName}_bit},\\n`;\r\n        } else {\r\n          mapping += `    {slave${index}_POS, slave${index}_VID_PID, ${pdo.index}, ${pdo.subindex}, &offset.pdo_slave${index}_tx_${varName}},\\n`;\r\n        }\r\n      });\r\n    });\r\n    mapping += `    {}\\n};\\n\\n`;\r\n\r\n    // 为每个从站生成 PDO 条目信息\r\n    config.slaves.forEach((slave: any, index: number) => {\r\n      mapping += `static ec_pdo_entry_info_t slave${index}_pdo_entries[] = {\\n`;\r\n      slave.rx_pdos?.forEach((pdo: any) => {\r\n        mapping += `    {${pdo.index}, ${pdo.subindex}, ${this.getTypeBits(pdo)}},  /* ${pdo.name} */\\n`;\r\n      });\r\n      slave.tx_pdos?.forEach((pdo: any) => {\r\n        mapping += `    {${pdo.index}, ${pdo.subindex}, ${this.getTypeBits(pdo)}},  /* ${pdo.name} */\\n`;\r\n      });\r\n      mapping += `};\\n\\n`;\r\n\r\n      // 生成 PDO 信息\r\n      mapping += `static ec_pdo_info_t slave${index}_pdos[] = {\\n`;\r\n      const rxCount = slave.rx_pdos?.length || 0;\r\n      const txCount = slave.tx_pdos?.length || 0;\r\n      mapping += `    {${slave.rx_pdo}, ${rxCount}, slave${index}_pdo_entries + 0},  /* RxPDO */\\n`;\r\n      mapping += `    {${slave.tx_pdo}, ${txCount}, slave${index}_pdo_entries + ${rxCount}},  /* TxPDO */\\n`;\r\n      mapping += `};\\n\\n`;\r\n\r\n      // 使用从 ethercat cstruct 获取的同步管理器信息\r\n      const syncInfo = syncInfos.find(si => si.index === index);\r\n      if (syncInfo && syncInfo.syncInfo) {\r\n        mapping += syncInfo.syncInfo + '\\n\\n';\r\n      } else {\r\n        throw new Error(`Missing sync info for slave ${slave.index}`);\r\n      }\r\n    });\r\n\r\n    return mapping;\r\n  }\r\n\r\n  private static getTypeBits(pdo: any): number {\r\n    // 如果有 bitlen，直接使用\r\n    if (pdo.bitlen !== undefined) {\r\n      return pdo.bitlen;\r\n    }\r\n\r\n    // 否则根据类型提供默认值\r\n    switch (pdo.type.toLowerCase()) {\r\n      case 'bool':\r\n        return 1;\r\n      case 'int8':\r\n      case 'uint8':\r\n        return 8;\r\n      case 'uint16':\r\n      case 'int16':\r\n        return 16;\r\n      case 'uint32':\r\n      case 'int32':\r\n        return 32;\r\n      case 'uint64':\r\n      case 'int64':\r\n      case 'double':\r\n        return 64;\r\n      default:\r\n        return 16;  // 默认使用 16 位\r\n    }\r\n  }\r\n\r\n  private static getPDOReadFunction(type: string): string {\r\n    switch (type.toLowerCase()) {\r\n      case 'bool':\r\n        return 'EC_READ_BIT';\r\n      case 'uint8':\r\n        return 'EC_READ_U8';\r\n      case 'int8':\r\n        return 'EC_READ_S8';\r\n      case 'uint16':\r\n        return 'EC_READ_U16';\r\n      case 'int16':\r\n        return 'EC_READ_S16';\r\n      case 'uint32':\r\n        return 'EC_READ_U32';\r\n      case 'int32':\r\n        return 'EC_READ_S32';\r\n      case 'uint64':\r\n        return 'EC_READ_U64';\r\n      case 'int64':\r\n        return 'EC_READ_S64';\r\n      case 'double':\r\n        return 'EC_READ_LREAL';\r\n      default:\r\n        return 'EC_READ_U16';\r\n    }\r\n  }\r\n\r\n  private static getPDOWriteFunction(type: string): string {\r\n    switch (type.toLowerCase()) {\r\n      case 'bool':\r\n        return 'EC_WRITE_BIT';\r\n      case 'uint8':\r\n        return 'EC_WRITE_U8';\r\n      case 'int8':\r\n        return 'EC_WRITE_S8';\r\n      case 'uint16':\r\n        return 'EC_WRITE_U16';\r\n      case 'int16':\r\n        return 'EC_WRITE_S16';\r\n      case 'uint32':\r\n        return 'EC_WRITE_U32';\r\n      case 'int32':\r\n        return 'EC_WRITE_S32';\r\n      case 'uint64':\r\n        return 'EC_WRITE_U64';\r\n      case 'int64':\r\n        return 'EC_WRITE_S64';\r\n      case 'double':\r\n        return 'EC_WRITE_LREAL';\r\n      default:\r\n        return 'EC_WRITE_U16';\r\n    }\r\n  }\r\n\r\n  private static generateCyclicTask(config: any): string {\r\n    const lines: string[] = [];\r\n    \r\n    // 函数声明\r\n    lines.push('void cyclic_task(void)');\r\n    lines.push('{');\r\n    \r\n    // 读取过程数据\r\n    lines.push('    // Read process data');\r\n    lines.push('    ecrt_master_receive(master);');\r\n    lines.push('    ecrt_domain_process(domain1);');\r\n    lines.push('');\r\n    lines.push('    // Update shared memory with status');\r\n\r\n    // 添加 TxPDO 读取，使用正确的前缀和 subindex\r\n    config.slaves.forEach((slave: any, index: number) => {\r\n      slave.tx_pdos?.forEach((pdo: any) => {\r\n        const varName = this.sanitizeVariableName(pdo, index, 'tx');\r\n        if (pdo.type === 'bool') {\r\n          lines.push(`    ethercat_shm->shm_slave${index}_tx_${varName} = EC_READ_BIT(domain1_pd + offset.pdo_slave${index}_tx_${varName}_off, offset.pdo_slave${index}_tx_${varName}_bit);`);\r\n        } else {\r\n          const readFunc = this.getPDOReadFunction(pdo.type);\r\n          lines.push(`    ethercat_shm->shm_slave${index}_tx_${varName} = ${readFunc}(domain1_pd + offset.pdo_slave${index}_tx_${varName});`);\r\n        }\r\n      });\r\n    });\r\n\r\n    lines.push('');\r\n    lines.push('    // Write to EtherCAT');\r\n    \r\n    // 添加 RxPDO 写入，使用正确的前缀和 subindex\r\n    config.slaves.forEach((slave: any, index: number) => {\r\n      slave.rx_pdos?.forEach((pdo: any) => {\r\n        const varName = this.sanitizeVariableName(pdo, index, 'rx');\r\n        if (pdo.type === 'bool') {\r\n          lines.push(`    EC_WRITE_BIT(domain1_pd + offset.pdo_slave${index}_rx_${varName}_off, offset.pdo_slave${index}_rx_${varName}_bit, ethercat_shm->shm_slave${index}_rx_${varName});`);\r\n        } else {\r\n          const writeFunc = this.getPDOWriteFunction(pdo.type);\r\n          lines.push(`    ${writeFunc}(domain1_pd + offset.pdo_slave${index}_rx_${varName}, ethercat_shm->shm_slave${index}_rx_${varName});`);\r\n        }\r\n      });\r\n    });\r\n\r\n    lines.push('');\r\n    lines.push('    // Send process data');\r\n    lines.push('    ecrt_domain_queue(domain1);');\r\n    lines.push('    ecrt_master_send(master);');\r\n    lines.push('}');\r\n\r\n    return lines.join('\\n');\r\n  }\r\n\r\n  private static generateMainFunction(config: TemplateConfig): string {\r\n    return `\r\nint main(int argc, char **argv) {\r\n    // Set up signal handler for cleanup\r\n    signal(SIGINT, signal_handler);\r\n    signal(SIGTERM, signal_handler);\r\n\r\n    // Lock memory to prevent paging\r\n    if (mlockall(MCL_CURRENT | MCL_FUTURE) == -1) {\r\n        perror(\"mlockall failed\");\r\n        return -1;\r\n    }\r\n\r\n    // Create shared memory\r\n    create_shm();\r\n    \r\n    // Initialize EtherCAT master\r\n    printf(\"Requesting master...\\\\n\");\r\n    master = ecrt_request_master(MASTER_INDEX);\r\n    if (!master) exit(EXIT_FAILURE);\r\n    \r\n    domain1 = ecrt_master_create_domain(master);\r\n    if (!domain1) exit(EXIT_FAILURE);\r\n\r\n    // Configure slaves\r\n    printf(\"Configuring PDOs...\\\\n\");\r\n\r\n${config.slaves.map((slave: any, index: number) => `\r\n    if (!(sc_slave${index} = ecrt_master_slave_config(master, slave${index}_POS, slave${index}_VID_PID))) {\r\n        fprintf(stderr, \"Failed to get slave${index} configuration!\\\\n\");\r\n        exit(EXIT_FAILURE);\r\n    }\r\n\r\n    printf(\"Configuring slave${index} PDOs...\\\\n\");\r\n    if (ecrt_slave_config_pdos(sc_slave${index}, EC_END, slave${index}_syncs)) {\r\n        fprintf(stderr, \"Failed to configure slave${index} PDOs!\\\\n\");\r\n        exit(EXIT_FAILURE);\r\n    }\r\n`).join('\\n')}\r\n\r\n    if (ecrt_domain_reg_pdo_entry_list(domain1, domain1_regs)) {\r\n        fprintf(stderr, \"PDO entry registration failed!\\\\n\");\r\n        exit(EXIT_FAILURE);\r\n    }\r\n\r\n    printf(\"Activating master...\\\\n\");\r\n    if (ecrt_master_activate(master)) {\r\n        exit(EXIT_FAILURE);\r\n    }\r\n\r\n    if (!(domain1_pd = ecrt_domain_data(domain1))) {\r\n        exit(EXIT_FAILURE);\r\n    }\r\n\r\n    // Set real-time priority\r\n    struct sched_param param = {};\r\n    param.sched_priority = sched_get_priority_max(SCHED_FIFO);\r\n    printf(\"Using priority %i.\\\\n\", param.sched_priority);\r\n    if (sched_setscheduler(0, SCHED_FIFO, &param) == -1) {\r\n        perror(\"sched_setscheduler failed\");\r\n    }\r\n\r\n    printf(\"Started.\\\\n\");\r\n    printf(\"Shared memory interface created at %s\\\\n\", ETHERCAT_SHM_FILE);\r\n    \r\n    // Main loop\r\n    while (1) {\r\n        usleep(1000000/TASK_FREQUENCY);\r\n        cyclic_task();\r\n    }\r\n\r\n    cleanup_shm();\r\n    return EXIT_SUCCESS;\r\n}\r\n`;\r\n  }\r\n\r\n  private static generateCStruct(config: any): string {\r\n    const lines: string[] = [];\r\n    \r\n    if (config.slaves && config.slaves.length > 0) {\r\n      // 遍历所有从站\r\n      config.slaves.forEach((slave: any, index: number) => {\r\n        // 处理 RxPDOs\r\n        if (slave.rx_pdos) {\r\n          slave.rx_pdos.forEach((pdo: any) => {\r\n            if (pdo && pdo.index && pdo.name) {  // 添加空值检查\r\n              const varName = this.sanitizeVariableName(pdo, index, 'rx');\r\n              const comment = pdo.comment ? ` /* ${pdo.comment} */` : '';\r\n              lines.push(`    int ${varName};${comment}`);\r\n            }\r\n          });\r\n        }\r\n\r\n        // 处理 TxPDOs\r\n        if (slave.tx_pdos) {\r\n          slave.tx_pdos.forEach((pdo: any) => {\r\n            if (pdo && pdo.index && pdo.name) {  // 添加空值检查\r\n              const varName = this.sanitizeVariableName(pdo, index, 'tx');\r\n              const comment = pdo.comment ? ` /* ${pdo.comment} */` : '';\r\n              lines.push(`    int ${varName};${comment}`);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    return lines.join('\\n');\r\n  }\r\n} ", "import { Router } from 'express';\r\nimport { ProgramManager } from '../services/program.manager.js';\r\nimport { asyncHandler } from '../utils/async-handler.js';\r\nimport type { Request, Response } from 'express';\r\nimport type { UploadedFile } from 'express-fileupload';\r\nimport path from 'path';\r\nimport fs from 'fs';\r\nimport { promises as fsPromises } from 'fs';\r\nimport { createWriteStream } from 'fs';\r\n\r\nconst router = Router();\r\n\r\n// 获取程序列表\r\nrouter.get('/', asyncHandler(async (req: Request, res: Response) => {\r\n  const programs = await ProgramManager.getPrograms();\r\n  res.json({\r\n    status: 200,\r\n    data: programs\r\n  });\r\n}));\r\n\r\n// 上传程序\r\nrouter.post('/', asyncHandler(async (req: Request, res: Response) => {\r\n  if (!req.files || !req.files.program || !req.files.config) {\r\n    throw new Error('Missing required files');\r\n  }\r\n\r\n  const program = req.files.program as UploadedFile;\r\n  const config = req.files.config as UploadedFile;\r\n  const formData = req.body;\r\n\r\n  const result = await ProgramManager.uploadProgram(program, config, formData);\r\n  res.json({\r\n    status: 200,\r\n    data: result\r\n  });\r\n}));\r\n\r\nrouter.post('/:id/start', asyncHandler(async (req: Request, res: Response) => {\r\n  await ProgramManager.startProgram(req.params.id);\r\n  res.json({\r\n    status: 200,\r\n    data: null\r\n  });\r\n}));\r\n\r\nrouter.post('/:id/stop', asyncHandler(async (req: Request, res: Response) => {\r\n  await ProgramManager.stopProgram(req.params.id);\r\n  res.json({\r\n    status: 200,\r\n    data: null\r\n  });\r\n}));\r\n\r\nrouter.delete('/:id', asyncHandler(async (req: Request, res: Response) => {\r\n  await ProgramManager.deleteProgram(req.params.id);\r\n  res.json({\r\n    status: 200,\r\n    data: null\r\n  });\r\n}));\r\n\r\nrouter.put('/:id/config', asyncHandler(async (req: Request, res: Response) => {\r\n  const result = await ProgramManager.updateConfig(req.params.id, req.body);\r\n  res.json({\r\n    status: 200,\r\n    data: result\r\n  });\r\n}));\r\n\r\nrouter.get('/:id/template', asyncHandler(async (req: Request, res: Response) => {\r\n  const { id } = req.params;\r\n  const template = await ProgramManager.generateTemplate(id);\r\n  \r\n  // 直接发送模板内容，不要包装在对象中\r\n  res.setHeader('Content-Type', 'text/plain');\r\n  res.setHeader('Content-Disposition', `attachment; filename=ethercat_${id}.c`);\r\n  res.send(template);\r\n}));\r\n\r\nrouter.post('/:id/replace', asyncHandler(async (req: Request, res: Response) => {\r\n  const { id } = req.params;\r\n  const program = req.files?.program as UploadedFile;\r\n  const formData = req.body;\r\n\r\n  if (!program) {\r\n    throw new ApiError(400, '未找到程序文件');\r\n  }\r\n\r\n  const result = await ProgramManager.replaceProgram(id, program, formData);\r\n  res.json({\r\n    status: 200,\r\n    data: result\r\n  });\r\n}));\r\n\r\n// 处理分片上传（仅用于程序文件）\r\nrouter.post('/chunk', asyncHandler(async (req: Request, res: Response) => {\r\n  if (!req.files?.chunk) {\r\n    throw new Error('No chunk file');\r\n  }\r\n\r\n  const chunk = req.files.chunk as UploadedFile;\r\n  const { hash, filename, index } = req.body;\r\n\r\n  // 保存分片\r\n  const chunkDir = path.join(process.cwd(), 'uploads', 'chunks', hash);\r\n  await fsPromises.mkdir(chunkDir, { recursive: true });\r\n  await chunk.mv(path.join(chunkDir, index));\r\n\r\n  res.json({ status: 200 });\r\n}));\r\n\r\n// 合并分片并完成上传\r\nrouter.post('/merge', asyncHandler(async (req: Request, res: Response) => {\r\n  if (!req.files?.config) {\r\n    throw new Error('Missing config file');\r\n  }\r\n\r\n  const configFile = req.files.config as UploadedFile;\r\n  const { filename, hash, size, masterIndex, taskFrequency } = req.body;\r\n  \r\n  // 读取配置文件内容\r\n  let configData;\r\n  try {\r\n    const configStr = configFile.data.toString('utf-8');\r\n    configData = JSON.parse(configStr);\r\n  } catch (error) {\r\n    console.error('Failed to parse config:', error);\r\n    throw new ApiError(400, '配置文件格式错误');\r\n  }\r\n\r\n  // 合并程序文件\r\n  const chunkDir = path.join(process.cwd(), 'uploads', 'chunks', hash);\r\n  const filePath = path.join(process.cwd(), 'uploads', filename);\r\n  \r\n  try {\r\n    // 按顺序合并分片\r\n    const writeStream = createWriteStream(filePath);\r\n    \r\n    for (let i = 0; i < size; i++) {\r\n      const chunkPath = path.join(chunkDir, i.toString());\r\n      const chunkData = await fsPromises.readFile(chunkPath);\r\n      writeStream.write(chunkData);\r\n    }\r\n    \r\n    // 等待写入完成\r\n    await new Promise((resolve, reject) => {\r\n      writeStream.on('finish', resolve);\r\n      writeStream.on('error', reject);\r\n      writeStream.end();\r\n    });\r\n\r\n    // 清理分片文件\r\n    try {\r\n      await fsPromises.rm(chunkDir, { recursive: true, force: true });\r\n    } catch (error) {\r\n      console.log('Chunk directory already cleaned up');\r\n    }\r\n\r\n    // 创建程序记录\r\n    const result = await ProgramManager.createProgram(filename, filePath, {\r\n      masterIndex,\r\n      taskFrequency,\r\n      config: configData  // 使用解析后的配置数据\r\n    });\r\n    \r\n    res.json({ status: 200, data: result });\r\n  } catch (error) {\r\n    // 清理临时文件\r\n    try {\r\n      await fsPromises.unlink(filePath);\r\n      if (await fsPromises.access(chunkDir).then(() => true).catch(() => false)) {\r\n        await fsPromises.rm(chunkDir, { recursive: true, force: true });\r\n      }\r\n    } catch (cleanupError) {\r\n      console.error('Cleanup error:', cleanupError);\r\n    }\r\n    throw error;\r\n  }\r\n}));\r\n\r\n// 添加程序包下载路由\r\nrouter.get('/:id/package', asyncHandler(async (req: Request, res: Response) => {\r\n  const { id } = req.params;\r\n  const packageBuffer = await ProgramManager.downloadFullPackage(id);\r\n  \r\n  res.setHeader('Content-Type', 'application/zip');\r\n  res.setHeader('Content-Disposition', `attachment; filename=program_package.zip`);\r\n  res.send(packageBuffer);\r\n}));\r\n\r\nexport default router; ", "import { Router, Request, Response } from 'express';\r\nimport { asyncHandler } from '../utils/async-handler.js';\r\nimport { EthercatService } from '../services/ethercat.service.js';\r\nimport { exec } from 'child_process';\r\nimport { promisify } from 'util';\r\nconst execAsync = promisify(exec);\r\n\r\nconst router = Router();\r\n\r\nrouter.get('/status', asyncHandler(async (req: Request, res: Response) => {\r\n  try {\r\n    const status = await EthercatService.getStatus();\r\n   // console.log('Sending EtherCAT status:', status);\r\n    res.json({\r\n      status: 200,\r\n      data: status\r\n    });\r\n  } catch (error) {\r\n    console.error('Error getting EtherCAT status:', error);\r\n    throw error;\r\n  }\r\n}));\r\n\r\nrouter.get('/slaves', asyncHandler(async (req: Request, res: Response) => {\r\n  const slaves = await EthercatService.getSlaves();\r\n  res.json({\r\n    status: 200,\r\n    data: slaves\r\n  });\r\n}));\r\n\r\nrouter.post('/start', asyncHandler(async (req: Request, res: Response) => {\r\n  await EthercatService.startService();\r\n  res.json({\r\n    status: 200,\r\n    data: { success: true }\r\n  });\r\n}));\r\n\r\nrouter.post('/stop', asyncHandler(async (req: Request, res: Response) => {\r\n  await EthercatService.stopService();\r\n  res.json({\r\n    status: 200,\r\n    data: { success: true }\r\n  });\r\n}));\r\n\r\nrouter.get('/xml/:master/:slave', asyncHandler(async (req: Request, res: Response) => {\r\n  const master = parseInt(req.params.master);\r\n  const slave = parseInt(req.params.slave);\r\n  const xml = await EthercatService.getSlaveXml(master, slave);\r\n  res.json({\r\n    status: 200,\r\n    data: xml\r\n  });\r\n}));\r\n\r\nrouter.get('/topology/:master', asyncHandler(async (req: Request, res: Response) => {\r\n  const master = parseInt(req.params.master);\r\n  const svg = await EthercatService.generateTopology(master);\r\n  res.setHeader('Content-Type', 'image/svg+xml');\r\n  res.send(svg);\r\n}));\r\n\r\nrouter.get('/masters', asyncHandler(async (req: Request, res: Response) => {\r\n  const masters = await EthercatService.getAvailableMasters();\r\n  res.json({\r\n    status: 200,\r\n    data: masters\r\n  });\r\n}));\r\n\r\nrouter.get('/network-interfaces', asyncHandler(async (req: Request, res: Response) => {\r\n  const { stdout } = await execAsync(\"ip link show | grep -E 'eth|end' | cut -d: -f2 | awk '{print $1}'\");\r\n  const interfaces = stdout.trim().split('\\n');\r\n  res.json({\r\n    status: 200,\r\n    data: interfaces\r\n  });\r\n}));\r\n\r\nrouter.get('/config', asyncHandler(async (req: Request, res: Response) => {\r\n  const config = await EthercatService.getConfig();\r\n  res.json({\r\n    status: 200,\r\n    data: config\r\n  });\r\n}));\r\n\r\nrouter.post('/config', asyncHandler(async (req: Request, res: Response) => {\r\n  await EthercatService.updateConfig(req.body);\r\n  res.json({\r\n    status: 200,\r\n    message: '配置已更新'\r\n  });\r\n}));\r\n\r\nrouter.post('/restart', asyncHandler(async (req: Request, res: Response) => {\r\n  await EthercatService.restartService();\r\n  res.json({\r\n    status: 200,\r\n    message: '服务已重启'\r\n  });\r\n}));\r\n\r\nrouter.get('/slave-config', asyncHandler(async (req: Request, res: Response) => {\r\n  const config = await EthercatService.getSlaveConfig();\r\n  res.json({\r\n    status: 200,\r\n    data: config\r\n  });\r\n}));\r\n\r\nrouter.get('/template-config', asyncHandler(async (req: Request, res: Response) => {\r\n  const config = await EthercatService.getTemplateConfig();\r\n  res.json({\r\n    status: 200,\r\n    data: config\r\n  });\r\n}));\r\n\r\nrouter.get('/bus-topology', asyncHandler(async (req: Request, res: Response) => {\r\n  const format = req.query.format as string;\r\n  const topology = await EthercatService.getBusTopology();\r\n  \r\n  if (format === 'png') {\r\n    res.setHeader('Content-Type', 'image/png');\r\n    res.setHeader('Content-Disposition', 'attachment; filename=\"topology.png\"');\r\n    res.send(topology.image);\r\n  } else {\r\n    res.json({\r\n      status: 200,\r\n      data: {\r\n        dot: topology.dot,\r\n        imageBase64: topology.image.toString('base64')\r\n      }\r\n    });\r\n  }\r\n}));\r\n\r\nexport default router; ", "/**\n * @module LRUCache\n */\n\n// module-private names and types\ntype Perf = { now: () => number }\nconst perf: Perf =\n  typeof performance === 'object' &&\n  performance &&\n  typeof performance.now === 'function'\n    ? performance\n    : Date\n\nconst warned = new Set<string>()\n\n// either a function or a class\ntype ForC = ((...a: any[]) => any) | { new (...a: any[]): any }\n\n/* c8 ignore start */\nconst PROCESS = (\n  typeof process === 'object' && !!process ? process : {}\n) as { [k: string]: any }\n/* c8 ignore start */\n\nconst emitWarning = (\n  msg: string,\n  type: string,\n  code: string,\n  fn: ForC\n) => {\n  typeof PROCESS.emitWarning === 'function'\n    ? PROCESS.emitWarning(msg, type, code, fn)\n    : console.error(`[${code}] ${type}: ${msg}`)\n}\n\nlet AC = globalThis.AbortController\nlet AS = globalThis.AbortSignal\n\n/* c8 ignore start */\nif (typeof AC === 'undefined') {\n  //@ts-ignore\n  AS = class AbortSignal {\n    onabort?: (...a: any[]) => any\n    _onabort: ((...a: any[]) => any)[] = []\n    reason?: any\n    aborted: boolean = false\n    addEventListener(_: string, fn: (...a: any[]) => any) {\n      this._onabort.push(fn)\n    }\n  }\n  //@ts-ignore\n  AC = class AbortController {\n    constructor() {\n      warnACPolyfill()\n    }\n    signal = new AS()\n    abort(reason: any) {\n      if (this.signal.aborted) return\n      //@ts-ignore\n      this.signal.reason = reason\n      //@ts-ignore\n      this.signal.aborted = true\n      //@ts-ignore\n      for (const fn of this.signal._onabort) {\n        fn(reason)\n      }\n      this.signal.onabort?.(reason)\n    }\n  }\n  let printACPolyfillWarning =\n    PROCESS.env?.LRU_CACHE_IGNORE_AC_WARNING !== '1'\n  const warnACPolyfill = () => {\n    if (!printACPolyfillWarning) return\n    printACPolyfillWarning = false\n    emitWarning(\n      'AbortController is not defined. If using lru-cache in ' +\n        'node 14, load an AbortController polyfill from the ' +\n        '`node-abort-controller` package. A minimal polyfill is ' +\n        'provided for use by LRUCache.fetch(), but it should not be ' +\n        'relied upon in other contexts (eg, passing it to other APIs that ' +\n        'use AbortController/AbortSignal might have undesirable effects). ' +\n        'You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.',\n      'NO_ABORT_CONTROLLER',\n      'ENOTSUP',\n      warnACPolyfill\n    )\n  }\n}\n/* c8 ignore stop */\n\nconst shouldWarn = (code: string) => !warned.has(code)\n\nconst TYPE = Symbol('type')\nexport type PosInt = number & { [TYPE]: 'Positive Integer' }\nexport type Index = number & { [TYPE]: 'LRUCache Index' }\n\nconst isPosInt = (n: any): n is PosInt =>\n  n && n === Math.floor(n) && n > 0 && isFinite(n)\n\nexport type UintArray = Uint8Array | Uint16Array | Uint32Array\nexport type NumberArray = UintArray | number[]\n\n/* c8 ignore start */\n// This is a little bit ridiculous, tbh.\n// The maximum array length is 2^32-1 or thereabouts on most JS impls.\n// And well before that point, you're caching the entire world, I mean,\n// that's ~32GB of just integers for the next/prev links, plus whatever\n// else to hold that many keys and values.  Just filling the memory with\n// zeroes at init time is brutal when you get that big.\n// But why not be complete?\n// Maybe in the future, these limits will have expanded.\nconst getUintArray = (max: number) =>\n  !isPosInt(max)\n    ? null\n    : max <= Math.pow(2, 8)\n    ? Uint8Array\n    : max <= Math.pow(2, 16)\n    ? Uint16Array\n    : max <= Math.pow(2, 32)\n    ? Uint32Array\n    : max <= Number.MAX_SAFE_INTEGER\n    ? ZeroArray\n    : null\n/* c8 ignore stop */\n\nclass ZeroArray extends Array<number> {\n  constructor(size: number) {\n    super(size)\n    this.fill(0)\n  }\n}\nexport type { ZeroArray }\nexport type { Stack }\n\nexport type StackLike = Stack | Index[]\nclass Stack {\n  heap: NumberArray\n  length: number\n  // private constructor\n  static #constructing: boolean = false\n  static create(max: number): StackLike {\n    const HeapCls = getUintArray(max)\n    if (!HeapCls) return []\n    Stack.#constructing = true\n    const s = new Stack(max, HeapCls)\n    Stack.#constructing = false\n    return s\n  }\n  constructor(\n    max: number,\n    HeapCls: { new (n: number): NumberArray }\n  ) {\n    /* c8 ignore start */\n    if (!Stack.#constructing) {\n      throw new TypeError('instantiate Stack using Stack.create(n)')\n    }\n    /* c8 ignore stop */\n    this.heap = new HeapCls(max)\n    this.length = 0\n  }\n  push(n: Index) {\n    this.heap[this.length++] = n\n  }\n  pop(): Index {\n    return this.heap[--this.length] as Index\n  }\n}\n\n/**\n * Promise representing an in-progress {@link LRUCache#fetch} call\n */\nexport type BackgroundFetch<V> = Promise<V | undefined> & {\n  __returned: BackgroundFetch<V> | undefined\n  __abortController: AbortController\n  __staleWhileFetching: V | undefined\n}\n\nexport type DisposeTask<K, V> = [\n  value: V,\n  key: K,\n  reason: LRUCache.DisposeReason\n]\n\nexport namespace LRUCache {\n  /**\n   * An integer greater than 0, reflecting the calculated size of items\n   */\n  export type Size = number\n\n  /**\n   * Integer greater than 0, representing some number of milliseconds, or the\n   * time at which a TTL started counting from.\n   */\n  export type Milliseconds = number\n\n  /**\n   * An integer greater than 0, reflecting a number of items\n   */\n  export type Count = number\n\n  /**\n   * The reason why an item was removed from the cache, passed\n   * to the {@link Disposer} methods.\n   *\n   * - `evict`: The item was evicted because it is the least recently used,\n   *   and the cache is full.\n   * - `set`: A new value was set, overwriting the old value being disposed.\n   * - `delete`: The item was explicitly deleted, either by calling\n   *   {@link LRUCache#delete}, {@link LRUCache#clear}, or\n   *   {@link LRUCache#set} with an undefined value.\n   * - `expire`: The item was removed due to exceeding its TTL.\n   * - `fetch`: A {@link OptionsBase#fetchMethod} operation returned\n   *   `undefined` or was aborted, causing the item to be deleted.\n   */\n  export type DisposeReason =\n    | 'evict'\n    | 'set'\n    | 'delete'\n    | 'expire'\n    | 'fetch'\n  /**\n   * A method called upon item removal, passed as the\n   * {@link OptionsBase.dispose} and/or\n   * {@link OptionsBase.disposeAfter} options.\n   */\n  export type Disposer<K, V> = (\n    value: V,\n    key: K,\n    reason: DisposeReason\n  ) => void\n\n  /**\n   * A function that returns the effective calculated size\n   * of an entry in the cache.\n   */\n  export type SizeCalculator<K, V> = (value: V, key: K) => Size\n\n  /**\n   * Options provided to the\n   * {@link OptionsBase.fetchMethod} function.\n   */\n  export interface FetcherOptions<K, V, FC = unknown> {\n    signal: AbortSignal\n    options: FetcherFetchOptions<K, V, FC>\n    /**\n     * Object provided in the {@link FetchOptions.context} option to\n     * {@link LRUCache#fetch}\n     */\n    context: FC\n  }\n\n  /**\n   * Occasionally, it may be useful to track the internal behavior of the\n   * cache, particularly for logging, debugging, or for behavior within the\n   * `fetchMethod`. To do this, you can pass a `status` object to the\n   * {@link LRUCache#fetch}, {@link LRUCache#get}, {@link LRUCache#set},\n   * {@link LRUCache#memo}, and {@link LRUCache#has} methods.\n   *\n   * The `status` option should be a plain JavaScript object. The following\n   * fields will be set on it appropriately, depending on the situation.\n   */\n  export interface Status<V> {\n    /**\n     * The status of a set() operation.\n     *\n     * - add: the item was not found in the cache, and was added\n     * - update: the item was in the cache, with the same value provided\n     * - replace: the item was in the cache, and replaced\n     * - miss: the item was not added to the cache for some reason\n     */\n    set?: 'add' | 'update' | 'replace' | 'miss'\n\n    /**\n     * the ttl stored for the item, or undefined if ttls are not used.\n     */\n    ttl?: Milliseconds\n\n    /**\n     * the start time for the item, or undefined if ttls are not used.\n     */\n    start?: Milliseconds\n\n    /**\n     * The timestamp used for TTL calculation\n     */\n    now?: Milliseconds\n\n    /**\n     * the remaining ttl for the item, or undefined if ttls are not used.\n     */\n    remainingTTL?: Milliseconds\n\n    /**\n     * The calculated size for the item, if sizes are used.\n     */\n    entrySize?: Size\n\n    /**\n     * The total calculated size of the cache, if sizes are used.\n     */\n    totalCalculatedSize?: Size\n\n    /**\n     * A flag indicating that the item was not stored, due to exceeding the\n     * {@link OptionsBase.maxEntrySize}\n     */\n    maxEntrySizeExceeded?: true\n\n    /**\n     * The old value, specified in the case of `set:'update'` or\n     * `set:'replace'`\n     */\n    oldValue?: V\n\n    /**\n     * The results of a {@link LRUCache#has} operation\n     *\n     * - hit: the item was found in the cache\n     * - stale: the item was found in the cache, but is stale\n     * - miss: the item was not found in the cache\n     */\n    has?: 'hit' | 'stale' | 'miss'\n\n    /**\n     * The status of a {@link LRUCache#fetch} operation.\n     * Note that this can change as the underlying fetch() moves through\n     * various states.\n     *\n     * - inflight: there is another fetch() for this key which is in process\n     * - get: there is no {@link OptionsBase.fetchMethod}, so\n     *   {@link LRUCache#get} was called.\n     * - miss: the item is not in cache, and will be fetched.\n     * - hit: the item is in the cache, and was resolved immediately.\n     * - stale: the item is in the cache, but stale.\n     * - refresh: the item is in the cache, and not stale, but\n     *   {@link FetchOptions.forceRefresh} was specified.\n     */\n    fetch?: 'get' | 'inflight' | 'miss' | 'hit' | 'stale' | 'refresh'\n\n    /**\n     * The {@link OptionsBase.fetchMethod} was called\n     */\n    fetchDispatched?: true\n\n    /**\n     * The cached value was updated after a successful call to\n     * {@link OptionsBase.fetchMethod}\n     */\n    fetchUpdated?: true\n\n    /**\n     * The reason for a fetch() rejection.  Either the error raised by the\n     * {@link OptionsBase.fetchMethod}, or the reason for an\n     * AbortSignal.\n     */\n    fetchError?: Error\n\n    /**\n     * The fetch received an abort signal\n     */\n    fetchAborted?: true\n\n    /**\n     * The abort signal received was ignored, and the fetch was allowed to\n     * continue.\n     */\n    fetchAbortIgnored?: true\n\n    /**\n     * The fetchMethod promise resolved successfully\n     */\n    fetchResolved?: true\n\n    /**\n     * The fetchMethod promise was rejected\n     */\n    fetchRejected?: true\n\n    /**\n     * The status of a {@link LRUCache#get} operation.\n     *\n     * - fetching: The item is currently being fetched.  If a previous value\n     *   is present and allowed, that will be returned.\n     * - stale: The item is in the cache, and is stale.\n     * - hit: the item is in the cache\n     * - miss: the item is not in the cache\n     */\n    get?: 'stale' | 'hit' | 'miss'\n\n    /**\n     * A fetch or get operation returned a stale value.\n     */\n    returnedStale?: true\n  }\n\n  /**\n   * options which override the options set in the LRUCache constructor\n   * when calling {@link LRUCache#fetch}.\n   *\n   * This is the union of {@link GetOptions} and {@link SetOptions}, plus\n   * {@link OptionsBase.noDeleteOnFetchRejection},\n   * {@link OptionsBase.allowStaleOnFetchRejection},\n   * {@link FetchOptions.forceRefresh}, and\n   * {@link FetcherOptions.context}\n   *\n   * Any of these may be modified in the {@link OptionsBase.fetchMethod}\n   * function, but the {@link GetOptions} fields will of course have no\n   * effect, as the {@link LRUCache#get} call already happened by the time\n   * the fetchMethod is called.\n   */\n  export interface FetcherFetchOptions<K, V, FC = unknown>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      | 'allowStale'\n      | 'updateAgeOnGet'\n      | 'noDeleteOnStaleGet'\n      | 'sizeCalculation'\n      | 'ttl'\n      | 'noDisposeOnSet'\n      | 'noUpdateTTL'\n      | 'noDeleteOnFetchRejection'\n      | 'allowStaleOnFetchRejection'\n      | 'ignoreFetchAbort'\n      | 'allowStaleOnFetchAbort'\n    > {\n    status?: Status<V>\n    size?: Size\n  }\n\n  /**\n   * Options that may be passed to the {@link LRUCache#fetch} method.\n   */\n  export interface FetchOptions<K, V, FC>\n    extends FetcherFetchOptions<K, V, FC> {\n    /**\n     * Set to true to force a re-load of the existing data, even if it\n     * is not yet stale.\n     */\n    forceRefresh?: boolean\n    /**\n     * Context provided to the {@link OptionsBase.fetchMethod} as\n     * the {@link FetcherOptions.context} param.\n     *\n     * If the FC type is specified as unknown (the default),\n     * undefined or void, then this is optional.  Otherwise, it will\n     * be required.\n     */\n    context?: FC\n    signal?: AbortSignal\n    status?: Status<V>\n  }\n  /**\n   * Options provided to {@link LRUCache#fetch} when the FC type is something\n   * other than `unknown`, `undefined`, or `void`\n   */\n  export interface FetchOptionsWithContext<K, V, FC>\n    extends FetchOptions<K, V, FC> {\n    context: FC\n  }\n  /**\n   * Options provided to {@link LRUCache#fetch} when the FC type is\n   * `undefined` or `void`\n   */\n  export interface FetchOptionsNoContext<K, V>\n    extends FetchOptions<K, V, undefined> {\n    context?: undefined\n  }\n\n  export interface MemoOptions<K, V, FC = unknown>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      | 'allowStale'\n      | 'updateAgeOnGet'\n      | 'noDeleteOnStaleGet'\n      | 'sizeCalculation'\n      | 'ttl'\n      | 'noDisposeOnSet'\n      | 'noUpdateTTL'\n      | 'noDeleteOnFetchRejection'\n      | 'allowStaleOnFetchRejection'\n      | 'ignoreFetchAbort'\n      | 'allowStaleOnFetchAbort'\n    > {\n    /**\n     * Set to true to force a re-load of the existing data, even if it\n     * is not yet stale.\n     */\n    forceRefresh?: boolean\n    /**\n     * Context provided to the {@link OptionsBase.memoMethod} as\n     * the {@link MemoizerOptions.context} param.\n     *\n     * If the FC type is specified as unknown (the default),\n     * undefined or void, then this is optional.  Otherwise, it will\n     * be required.\n     */\n    context?: FC\n    status?: Status<V>\n  }\n  /**\n   * Options provided to {@link LRUCache#memo} when the FC type is something\n   * other than `unknown`, `undefined`, or `void`\n   */\n  export interface MemoOptionsWithContext<K, V, FC>\n    extends MemoOptions<K, V, FC> {\n    context: FC\n  }\n  /**\n   * Options provided to {@link LRUCache#memo} when the FC type is\n   * `undefined` or `void`\n   */\n  export interface MemoOptionsNoContext<K, V>\n    extends MemoOptions<K, V, undefined> {\n    context?: undefined\n  }\n\n  /**\n   * Options provided to the\n   * {@link OptionsBase.memoMethod} function.\n   */\n  export interface MemoizerOptions<K, V, FC = unknown> {\n    options: MemoizerMemoOptions<K, V, FC>\n    /**\n     * Object provided in the {@link MemoOptions.context} option to\n     * {@link LRUCache#memo}\n     */\n    context: FC\n  }\n\n  /**\n   * options which override the options set in the LRUCache constructor\n   * when calling {@link LRUCache#memo}.\n   *\n   * This is the union of {@link GetOptions} and {@link SetOptions}, plus\n   * {@link MemoOptions.forceRefresh}, and\n   * {@link MemoerOptions.context}\n   *\n   * Any of these may be modified in the {@link OptionsBase.memoMethod}\n   * function, but the {@link GetOptions} fields will of course have no\n   * effect, as the {@link LRUCache#get} call already happened by the time\n   * the memoMethod is called.\n   */\n  export interface MemoizerMemoOptions<K, V, FC = unknown>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      | 'allowStale'\n      | 'updateAgeOnGet'\n      | 'noDeleteOnStaleGet'\n      | 'sizeCalculation'\n      | 'ttl'\n      | 'noDisposeOnSet'\n      | 'noUpdateTTL'\n    > {\n    status?: Status<V>\n    size?: Size\n    start?: Milliseconds\n  }\n\n  /**\n   * Options that may be passed to the {@link LRUCache#has} method.\n   */\n  export interface HasOptions<K, V, FC>\n    extends Pick<OptionsBase<K, V, FC>, 'updateAgeOnHas'> {\n    status?: Status<V>\n  }\n\n  /**\n   * Options that may be passed to the {@link LRUCache#get} method.\n   */\n  export interface GetOptions<K, V, FC>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      'allowStale' | 'updateAgeOnGet' | 'noDeleteOnStaleGet'\n    > {\n    status?: Status<V>\n  }\n\n  /**\n   * Options that may be passed to the {@link LRUCache#peek} method.\n   */\n  export interface PeekOptions<K, V, FC>\n    extends Pick<OptionsBase<K, V, FC>, 'allowStale'> {}\n\n  /**\n   * Options that may be passed to the {@link LRUCache#set} method.\n   */\n  export interface SetOptions<K, V, FC>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      'sizeCalculation' | 'ttl' | 'noDisposeOnSet' | 'noUpdateTTL'\n    > {\n    /**\n     * If size tracking is enabled, then setting an explicit size\n     * in the {@link LRUCache#set} call will prevent calling the\n     * {@link OptionsBase.sizeCalculation} function.\n     */\n    size?: Size\n    /**\n     * If TTL tracking is enabled, then setting an explicit start\n     * time in the {@link LRUCache#set} call will override the\n     * default time from `performance.now()` or `Date.now()`.\n     *\n     * Note that it must be a valid value for whichever time-tracking\n     * method is in use.\n     */\n    start?: Milliseconds\n    status?: Status<V>\n  }\n\n  /**\n   * The type signature for the {@link OptionsBase.fetchMethod} option.\n   */\n  export type Fetcher<K, V, FC = unknown> = (\n    key: K,\n    staleValue: V | undefined,\n    options: FetcherOptions<K, V, FC>\n  ) => Promise<V | undefined | void> | V | undefined | void\n\n  /**\n   * the type signature for the {@link OptionsBase.memoMethod} option.\n   */\n  export type Memoizer<K, V, FC = unknown> = (\n    key: K,\n    staleValue: V | undefined,\n    options: MemoizerOptions<K, V, FC>\n  ) => V\n\n  /**\n   * Options which may be passed to the {@link LRUCache} constructor.\n   *\n   * Most of these may be overridden in the various options that use\n   * them.\n   *\n   * Despite all being technically optional, the constructor requires that\n   * a cache is at minimum limited by one or more of {@link OptionsBase.max},\n   * {@link OptionsBase.ttl}, or {@link OptionsBase.maxSize}.\n   *\n   * If {@link OptionsBase.ttl} is used alone, then it is strongly advised\n   * (and in fact required by the type definitions here) that the cache\n   * also set {@link OptionsBase.ttlAutopurge}, to prevent potentially\n   * unbounded storage.\n   *\n   * All options are also available on the {@link LRUCache} instance, making\n   * it safe to pass an LRUCache instance as the options argumemnt to\n   * make another empty cache of the same type.\n   *\n   * Some options are marked as read-only, because changing them after\n   * instantiation is not safe. Changing any of the other options will of\n   * course only have an effect on subsequent method calls.\n   */\n  export interface OptionsBase<K, V, FC> {\n    /**\n     * The maximum number of items to store in the cache before evicting\n     * old entries. This is read-only on the {@link LRUCache} instance,\n     * and may not be overridden.\n     *\n     * If set, then storage space will be pre-allocated at construction\n     * time, and the cache will perform significantly faster.\n     *\n     * Note that significantly fewer items may be stored, if\n     * {@link OptionsBase.maxSize} and/or {@link OptionsBase.ttl} are also\n     * set.\n     *\n     * **It is strongly recommended to set a `max` to prevent unbounded growth\n     * of the cache.**\n     */\n    max?: Count\n\n    /**\n     * Max time in milliseconds for items to live in cache before they are\n     * considered stale.  Note that stale items are NOT preemptively removed by\n     * default, and MAY live in the cache, contributing to its LRU max, long\n     * after they have expired, unless {@link OptionsBase.ttlAutopurge} is\n     * set.\n     *\n     * If set to `0` (the default value), then that means \"do not track\n     * TTL\", not \"expire immediately\".\n     *\n     * Also, as this cache is optimized for LRU/MRU operations, some of\n     * the staleness/TTL checks will reduce performance, as they will incur\n     * overhead by deleting items.\n     *\n     * This is not primarily a TTL cache, and does not make strong TTL\n     * guarantees. There is no pre-emptive pruning of expired items, but you\n     * _may_ set a TTL on the cache, and it will treat expired items as missing\n     * when they are fetched, and delete them.\n     *\n     * Optional, but must be a non-negative integer in ms if specified.\n     *\n     * This may be overridden by passing an options object to `cache.set()`.\n     *\n     * At least one of `max`, `maxSize`, or `TTL` is required. This must be a\n     * positive integer if set.\n     *\n     * Even if ttl tracking is enabled, **it is strongly recommended to set a\n     * `max` to prevent unbounded growth of the cache.**\n     *\n     * If ttl tracking is enabled, and `max` and `maxSize` are not set,\n     * and `ttlAutopurge` is not set, then a warning will be emitted\n     * cautioning about the potential for unbounded memory consumption.\n     * (The TypeScript definitions will also discourage this.)\n     */\n    ttl?: Milliseconds\n\n    /**\n     * Minimum amount of time in ms in which to check for staleness.\n     * Defaults to 1, which means that the current time is checked\n     * at most once per millisecond.\n     *\n     * Set to 0 to check the current time every time staleness is tested.\n     * (This reduces performance, and is theoretically unnecessary.)\n     *\n     * Setting this to a higher value will improve performance somewhat\n     * while using ttl tracking, albeit at the expense of keeping stale\n     * items around a bit longer than their TTLs would indicate.\n     *\n     * @default 1\n     */\n    ttlResolution?: Milliseconds\n\n    /**\n     * Preemptively remove stale items from the cache.\n     *\n     * Note that this may *significantly* degrade performance, especially if\n     * the cache is storing a large number of items. It is almost always best\n     * to just leave the stale items in the cache, and let them fall out as new\n     * items are added.\n     *\n     * Note that this means that {@link OptionsBase.allowStale} is a bit\n     * pointless, as stale items will be deleted almost as soon as they\n     * expire.\n     *\n     * Use with caution!\n     */\n    ttlAutopurge?: boolean\n\n    /**\n     * When using time-expiring entries with `ttl`, setting this to `true` will\n     * make each item's age reset to 0 whenever it is retrieved from cache with\n     * {@link LRUCache#get}, causing it to not expire. (It can still fall out\n     * of cache based on recency of use, of course.)\n     *\n     * Has no effect if {@link OptionsBase.ttl} is not set.\n     *\n     * This may be overridden by passing an options object to `cache.get()`.\n     */\n    updateAgeOnGet?: boolean\n\n    /**\n     * When using time-expiring entries with `ttl`, setting this to `true` will\n     * make each item's age reset to 0 whenever its presence in the cache is\n     * checked with {@link LRUCache#has}, causing it to not expire. (It can\n     * still fall out of cache based on recency of use, of course.)\n     *\n     * Has no effect if {@link OptionsBase.ttl} is not set.\n     */\n    updateAgeOnHas?: boolean\n\n    /**\n     * Allow {@link LRUCache#get} and {@link LRUCache#fetch} calls to return\n     * stale data, if available.\n     *\n     * By default, if you set `ttl`, stale items will only be deleted from the\n     * cache when you `get(key)`. That is, it's not preemptively pruning items,\n     * unless {@link OptionsBase.ttlAutopurge} is set.\n     *\n     * If you set `allowStale:true`, it'll return the stale value *as well as*\n     * deleting it. If you don't set this, then it'll return `undefined` when\n     * you try to get a stale entry.\n     *\n     * Note that when a stale entry is fetched, _even if it is returned due to\n     * `allowStale` being set_, it is removed from the cache immediately. You\n     * can suppress this behavior by setting\n     * {@link OptionsBase.noDeleteOnStaleGet}, either in the constructor, or in\n     * the options provided to {@link LRUCache#get}.\n     *\n     * This may be overridden by passing an options object to `cache.get()`.\n     * The `cache.has()` method will always return `false` for stale items.\n     *\n     * Only relevant if a ttl is set.\n     */\n    allowStale?: boolean\n\n    /**\n     * Function that is called on items when they are dropped from the\n     * cache, as `dispose(value, key, reason)`.\n     *\n     * This can be handy if you want to close file descriptors or do\n     * other cleanup tasks when items are no longer stored in the cache.\n     *\n     * **NOTE**: It is called _before_ the item has been fully removed\n     * from the cache, so if you want to put it right back in, you need\n     * to wait until the next tick. If you try to add it back in during\n     * the `dispose()` function call, it will break things in subtle and\n     * weird ways.\n     *\n     * Unlike several other options, this may _not_ be overridden by\n     * passing an option to `set()`, for performance reasons.\n     *\n     * The `reason` will be one of the following strings, corresponding\n     * to the reason for the item's deletion:\n     *\n     * - `evict` Item was evicted to make space for a new addition\n     * - `set` Item was overwritten by a new value\n     * - `expire` Item expired its TTL\n     * - `fetch` Item was deleted due to a failed or aborted fetch, or a\n     *   fetchMethod returning `undefined.\n     * - `delete` Item was removed by explicit `cache.delete(key)`,\n     *   `cache.clear()`, or `cache.set(key, undefined)`.\n     */\n    dispose?: Disposer<K, V>\n\n    /**\n     * The same as {@link OptionsBase.dispose}, but called *after* the entry\n     * is completely removed and the cache is once again in a clean state.\n     *\n     * It is safe to add an item right back into the cache at this point.\n     * However, note that it is *very* easy to inadvertently create infinite\n     * recursion this way.\n     */\n    disposeAfter?: Disposer<K, V>\n\n    /**\n     * Set to true to suppress calling the\n     * {@link OptionsBase.dispose} function if the entry key is\n     * still accessible within the cache.\n     *\n     * This may be overridden by passing an options object to\n     * {@link LRUCache#set}.\n     *\n     * Only relevant if `dispose` or `disposeAfter` are set.\n     */\n    noDisposeOnSet?: boolean\n\n    /**\n     * Boolean flag to tell the cache to not update the TTL when setting a new\n     * value for an existing key (ie, when updating a value rather than\n     * inserting a new value).  Note that the TTL value is _always_ set (if\n     * provided) when adding a new entry into the cache.\n     *\n     * Has no effect if a {@link OptionsBase.ttl} is not set.\n     *\n     * May be passed as an option to {@link LRUCache#set}.\n     */\n    noUpdateTTL?: boolean\n\n    /**\n     * Set to a positive integer to track the sizes of items added to the\n     * cache, and automatically evict items in order to stay below this size.\n     * Note that this may result in fewer than `max` items being stored.\n     *\n     * Attempting to add an item to the cache whose calculated size is greater\n     * that this amount will be a no-op. The item will not be cached, and no\n     * other items will be evicted.\n     *\n     * Optional, must be a positive integer if provided.\n     *\n     * Sets `maxEntrySize` to the same value, unless a different value is\n     * provided for `maxEntrySize`.\n     *\n     * At least one of `max`, `maxSize`, or `TTL` is required. This must be a\n     * positive integer if set.\n     *\n     * Even if size tracking is enabled, **it is strongly recommended to set a\n     * `max` to prevent unbounded growth of the cache.**\n     *\n     * Note also that size tracking can negatively impact performance,\n     * though for most cases, only minimally.\n     */\n    maxSize?: Size\n\n    /**\n     * The maximum allowed size for any single item in the cache.\n     *\n     * If a larger item is passed to {@link LRUCache#set} or returned by a\n     * {@link OptionsBase.fetchMethod} or {@link OptionsBase.memoMethod}, then\n     * it will not be stored in the cache.\n     *\n     * Attempting to add an item whose calculated size is greater than\n     * this amount will not cache the item or evict any old items, but\n     * WILL delete an existing value if one is already present.\n     *\n     * Optional, must be a positive integer if provided. Defaults to\n     * the value of `maxSize` if provided.\n     */\n    maxEntrySize?: Size\n\n    /**\n     * A function that returns a number indicating the item's size.\n     *\n     * Requires {@link OptionsBase.maxSize} to be set.\n     *\n     * If not provided, and {@link OptionsBase.maxSize} or\n     * {@link OptionsBase.maxEntrySize} are set, then all\n     * {@link LRUCache#set} calls **must** provide an explicit\n     * {@link SetOptions.size} or sizeCalculation param.\n     */\n    sizeCalculation?: SizeCalculator<K, V>\n\n    /**\n     * Method that provides the implementation for {@link LRUCache#fetch}\n     *\n     * ```ts\n     * fetchMethod(key, staleValue, { signal, options, context })\n     * ```\n     *\n     * If `fetchMethod` is not provided, then `cache.fetch(key)` is equivalent\n     * to `Promise.resolve(cache.get(key))`.\n     *\n     * If at any time, `signal.aborted` is set to `true`, or if the\n     * `signal.onabort` method is called, or if it emits an `'abort'` event\n     * which you can listen to with `addEventListener`, then that means that\n     * the fetch should be abandoned. This may be passed along to async\n     * functions aware of AbortController/AbortSignal behavior.\n     *\n     * The `fetchMethod` should **only** return `undefined` or a Promise\n     * resolving to `undefined` if the AbortController signaled an `abort`\n     * event. In all other cases, it should return or resolve to a value\n     * suitable for adding to the cache.\n     *\n     * The `options` object is a union of the options that may be provided to\n     * `set()` and `get()`. If they are modified, then that will result in\n     * modifying the settings to `cache.set()` when the value is resolved, and\n     * in the case of\n     * {@link OptionsBase.noDeleteOnFetchRejection} and\n     * {@link OptionsBase.allowStaleOnFetchRejection}, the handling of\n     * `fetchMethod` failures.\n     *\n     * For example, a DNS cache may update the TTL based on the value returned\n     * from a remote DNS server by changing `options.ttl` in the `fetchMethod`.\n     */\n    fetchMethod?: Fetcher<K, V, FC>\n\n    /**\n     * Method that provides the implementation for {@link LRUCache#memo}\n     */\n    memoMethod?: Memoizer<K, V, FC>\n\n    /**\n     * Set to true to suppress the deletion of stale data when a\n     * {@link OptionsBase.fetchMethod} returns a rejected promise.\n     */\n    noDeleteOnFetchRejection?: boolean\n\n    /**\n     * Do not delete stale items when they are retrieved with\n     * {@link LRUCache#get}.\n     *\n     * Note that the `get` return value will still be `undefined`\n     * unless {@link OptionsBase.allowStale} is true.\n     *\n     * When using time-expiring entries with `ttl`, by default stale\n     * items will be removed from the cache when the key is accessed\n     * with `cache.get()`.\n     *\n     * Setting this option will cause stale items to remain in the cache, until\n     * they are explicitly deleted with `cache.delete(key)`, or retrieved with\n     * `noDeleteOnStaleGet` set to `false`.\n     *\n     * This may be overridden by passing an options object to `cache.get()`.\n     *\n     * Only relevant if a ttl is used.\n     */\n    noDeleteOnStaleGet?: boolean\n\n    /**\n     * Set to true to allow returning stale data when a\n     * {@link OptionsBase.fetchMethod} throws an error or returns a rejected\n     * promise.\n     *\n     * This differs from using {@link OptionsBase.allowStale} in that stale\n     * data will ONLY be returned in the case that the {@link LRUCache#fetch}\n     * fails, not any other times.\n     *\n     * If a `fetchMethod` fails, and there is no stale value available, the\n     * `fetch()` will resolve to `undefined`. Ie, all `fetchMethod` errors are\n     * suppressed.\n     *\n     * Implies `noDeleteOnFetchRejection`.\n     *\n     * This may be set in calls to `fetch()`, or defaulted on the constructor,\n     * or overridden by modifying the options object in the `fetchMethod`.\n     */\n    allowStaleOnFetchRejection?: boolean\n\n    /**\n     * Set to true to return a stale value from the cache when the\n     * `AbortSignal` passed to the {@link OptionsBase.fetchMethod} dispatches\n     * an `'abort'` event, whether user-triggered, or due to internal cache\n     * behavior.\n     *\n     * Unless {@link OptionsBase.ignoreFetchAbort} is also set, the underlying\n     * {@link OptionsBase.fetchMethod} will still be considered canceled, and\n     * any value it returns will be ignored and not cached.\n     *\n     * Caveat: since fetches are aborted when a new value is explicitly\n     * set in the cache, this can lead to fetch returning a stale value,\n     * since that was the fallback value _at the moment the `fetch()` was\n     * initiated_, even though the new updated value is now present in\n     * the cache.\n     *\n     * For example:\n     *\n     * ```ts\n     * const cache = new LRUCache<string, any>({\n     *   ttl: 100,\n     *   fetchMethod: async (url, oldValue, { signal }) =>  {\n     *     const res = await fetch(url, { signal })\n     *     return await res.json()\n     *   }\n     * })\n     * cache.set('https://example.com/', { some: 'data' })\n     * // 100ms go by...\n     * const result = cache.fetch('https://example.com/')\n     * cache.set('https://example.com/', { other: 'thing' })\n     * console.log(await result) // { some: 'data' }\n     * console.log(cache.get('https://example.com/')) // { other: 'thing' }\n     * ```\n     */\n    allowStaleOnFetchAbort?: boolean\n\n    /**\n     * Set to true to ignore the `abort` event emitted by the `AbortSignal`\n     * object passed to {@link OptionsBase.fetchMethod}, and still cache the\n     * resulting resolution value, as long as it is not `undefined`.\n     *\n     * When used on its own, this means aborted {@link LRUCache#fetch} calls\n     * are not immediately resolved or rejected when they are aborted, and\n     * instead take the full time to await.\n     *\n     * When used with {@link OptionsBase.allowStaleOnFetchAbort}, aborted\n     * {@link LRUCache#fetch} calls will resolve immediately to their stale\n     * cached value or `undefined`, and will continue to process and eventually\n     * update the cache when they resolve, as long as the resulting value is\n     * not `undefined`, thus supporting a \"return stale on timeout while\n     * refreshing\" mechanism by passing `AbortSignal.timeout(n)` as the signal.\n     *\n     * For example:\n     *\n     * ```ts\n     * const c = new LRUCache({\n     *   ttl: 100,\n     *   ignoreFetchAbort: true,\n     *   allowStaleOnFetchAbort: true,\n     *   fetchMethod: async (key, oldValue, { signal }) => {\n     *     // note: do NOT pass the signal to fetch()!\n     *     // let's say this fetch can take a long time.\n     *     const res = await fetch(`https://slow-backend-server/${key}`)\n     *     return await res.json()\n     *   },\n     * })\n     *\n     * // this will return the stale value after 100ms, while still\n     * // updating in the background for next time.\n     * const val = await c.fetch('key', { signal: AbortSignal.timeout(100) })\n     * ```\n     *\n     * **Note**: regardless of this setting, an `abort` event _is still\n     * emitted on the `AbortSignal` object_, so may result in invalid results\n     * when passed to other underlying APIs that use AbortSignals.\n     *\n     * This may be overridden in the {@link OptionsBase.fetchMethod} or the\n     * call to {@link LRUCache#fetch}.\n     */\n    ignoreFetchAbort?: boolean\n  }\n\n  export interface OptionsMaxLimit<K, V, FC>\n    extends OptionsBase<K, V, FC> {\n    max: Count\n  }\n  export interface OptionsTTLLimit<K, V, FC>\n    extends OptionsBase<K, V, FC> {\n    ttl: Milliseconds\n    ttlAutopurge: boolean\n  }\n  export interface OptionsSizeLimit<K, V, FC>\n    extends OptionsBase<K, V, FC> {\n    maxSize: Size\n  }\n\n  /**\n   * The valid safe options for the {@link LRUCache} constructor\n   */\n  export type Options<K, V, FC> =\n    | OptionsMaxLimit<K, V, FC>\n    | OptionsSizeLimit<K, V, FC>\n    | OptionsTTLLimit<K, V, FC>\n\n  /**\n   * Entry objects used by {@link LRUCache#load} and {@link LRUCache#dump},\n   * and returned by {@link LRUCache#info}.\n   */\n  export interface Entry<V> {\n    value: V\n    ttl?: Milliseconds\n    size?: Size\n    start?: Milliseconds\n  }\n}\n\n/**\n * Default export, the thing you're using this module to get.\n *\n * The `K` and `V` types define the key and value types, respectively. The\n * optional `FC` type defines the type of the `context` object passed to\n * `cache.fetch()` and `cache.memo()`.\n *\n * Keys and values **must not** be `null` or `undefined`.\n *\n * All properties from the options object (with the exception of `max`,\n * `maxSize`, `fetchMethod`, `memoMethod`, `dispose` and `disposeAfter`) are\n * added as normal public members. (The listed options are read-only getters.)\n *\n * Changing any of these will alter the defaults for subsequent method calls.\n */\nexport class LRUCache<K extends {}, V extends {}, FC = unknown>\n  implements Map<K, V>\n{\n  // options that cannot be changed without disaster\n  readonly #max: LRUCache.Count\n  readonly #maxSize: LRUCache.Size\n  readonly #dispose?: LRUCache.Disposer<K, V>\n  readonly #disposeAfter?: LRUCache.Disposer<K, V>\n  readonly #fetchMethod?: LRUCache.Fetcher<K, V, FC>\n  readonly #memoMethod?: LRUCache.Memoizer<K, V, FC>\n\n  /**\n   * {@link LRUCache.OptionsBase.ttl}\n   */\n  ttl: LRUCache.Milliseconds\n\n  /**\n   * {@link LRUCache.OptionsBase.ttlResolution}\n   */\n  ttlResolution: LRUCache.Milliseconds\n  /**\n   * {@link LRUCache.OptionsBase.ttlAutopurge}\n   */\n  ttlAutopurge: boolean\n  /**\n   * {@link LRUCache.OptionsBase.updateAgeOnGet}\n   */\n  updateAgeOnGet: boolean\n  /**\n   * {@link LRUCache.OptionsBase.updateAgeOnHas}\n   */\n  updateAgeOnHas: boolean\n  /**\n   * {@link LRUCache.OptionsBase.allowStale}\n   */\n  allowStale: boolean\n\n  /**\n   * {@link LRUCache.OptionsBase.noDisposeOnSet}\n   */\n  noDisposeOnSet: boolean\n  /**\n   * {@link LRUCache.OptionsBase.noUpdateTTL}\n   */\n  noUpdateTTL: boolean\n  /**\n   * {@link LRUCache.OptionsBase.maxEntrySize}\n   */\n  maxEntrySize: LRUCache.Size\n  /**\n   * {@link LRUCache.OptionsBase.sizeCalculation}\n   */\n  sizeCalculation?: LRUCache.SizeCalculator<K, V>\n  /**\n   * {@link LRUCache.OptionsBase.noDeleteOnFetchRejection}\n   */\n  noDeleteOnFetchRejection: boolean\n  /**\n   * {@link LRUCache.OptionsBase.noDeleteOnStaleGet}\n   */\n  noDeleteOnStaleGet: boolean\n  /**\n   * {@link LRUCache.OptionsBase.allowStaleOnFetchAbort}\n   */\n  allowStaleOnFetchAbort: boolean\n  /**\n   * {@link LRUCache.OptionsBase.allowStaleOnFetchRejection}\n   */\n  allowStaleOnFetchRejection: boolean\n  /**\n   * {@link LRUCache.OptionsBase.ignoreFetchAbort}\n   */\n  ignoreFetchAbort: boolean\n\n  // computed properties\n  #size: LRUCache.Count\n  #calculatedSize: LRUCache.Size\n  #keyMap: Map<K, Index>\n  #keyList: (K | undefined)[]\n  #valList: (V | BackgroundFetch<V> | undefined)[]\n  #next: NumberArray\n  #prev: NumberArray\n  #head: Index\n  #tail: Index\n  #free: StackLike\n  #disposed?: DisposeTask<K, V>[]\n  #sizes?: ZeroArray\n  #starts?: ZeroArray\n  #ttls?: ZeroArray\n\n  #hasDispose: boolean\n  #hasFetchMethod: boolean\n  #hasDisposeAfter: boolean\n\n  /**\n   * Do not call this method unless you need to inspect the\n   * inner workings of the cache.  If anything returned by this\n   * object is modified in any way, strange breakage may occur.\n   *\n   * These fields are private for a reason!\n   *\n   * @internal\n   */\n  static unsafeExposeInternals<\n    K extends {},\n    V extends {},\n    FC extends unknown = unknown\n  >(c: LRUCache<K, V, FC>) {\n    return {\n      // properties\n      starts: c.#starts,\n      ttls: c.#ttls,\n      sizes: c.#sizes,\n      keyMap: c.#keyMap as Map<K, number>,\n      keyList: c.#keyList,\n      valList: c.#valList,\n      next: c.#next,\n      prev: c.#prev,\n      get head() {\n        return c.#head\n      },\n      get tail() {\n        return c.#tail\n      },\n      free: c.#free,\n      // methods\n      isBackgroundFetch: (p: any) => c.#isBackgroundFetch(p),\n      backgroundFetch: (\n        k: K,\n        index: number | undefined,\n        options: LRUCache.FetchOptions<K, V, FC>,\n        context: any\n      ): BackgroundFetch<V> =>\n        c.#backgroundFetch(\n          k,\n          index as Index | undefined,\n          options,\n          context\n        ),\n      moveToTail: (index: number): void =>\n        c.#moveToTail(index as Index),\n      indexes: (options?: { allowStale: boolean }) =>\n        c.#indexes(options),\n      rindexes: (options?: { allowStale: boolean }) =>\n        c.#rindexes(options),\n      isStale: (index: number | undefined) =>\n        c.#isStale(index as Index),\n    }\n  }\n\n  // Protected read-only members\n\n  /**\n   * {@link LRUCache.OptionsBase.max} (read-only)\n   */\n  get max(): LRUCache.Count {\n    return this.#max\n  }\n  /**\n   * {@link LRUCache.OptionsBase.maxSize} (read-only)\n   */\n  get maxSize(): LRUCache.Count {\n    return this.#maxSize\n  }\n  /**\n   * The total computed size of items in the cache (read-only)\n   */\n  get calculatedSize(): LRUCache.Size {\n    return this.#calculatedSize\n  }\n  /**\n   * The number of items stored in the cache (read-only)\n   */\n  get size(): LRUCache.Count {\n    return this.#size\n  }\n  /**\n   * {@link LRUCache.OptionsBase.fetchMethod} (read-only)\n   */\n  get fetchMethod(): LRUCache.Fetcher<K, V, FC> | undefined {\n    return this.#fetchMethod\n  }\n  get memoMethod(): LRUCache.Memoizer<K, V, FC> | undefined {\n    return this.#memoMethod\n  }\n  /**\n   * {@link LRUCache.OptionsBase.dispose} (read-only)\n   */\n  get dispose() {\n    return this.#dispose\n  }\n  /**\n   * {@link LRUCache.OptionsBase.disposeAfter} (read-only)\n   */\n  get disposeAfter() {\n    return this.#disposeAfter\n  }\n\n  constructor(\n    options: LRUCache.Options<K, V, FC> | LRUCache<K, V, FC>\n  ) {\n    const {\n      max = 0,\n      ttl,\n      ttlResolution = 1,\n      ttlAutopurge,\n      updateAgeOnGet,\n      updateAgeOnHas,\n      allowStale,\n      dispose,\n      disposeAfter,\n      noDisposeOnSet,\n      noUpdateTTL,\n      maxSize = 0,\n      maxEntrySize = 0,\n      sizeCalculation,\n      fetchMethod,\n      memoMethod,\n      noDeleteOnFetchRejection,\n      noDeleteOnStaleGet,\n      allowStaleOnFetchRejection,\n      allowStaleOnFetchAbort,\n      ignoreFetchAbort,\n    } = options\n\n    if (max !== 0 && !isPosInt(max)) {\n      throw new TypeError('max option must be a nonnegative integer')\n    }\n\n    const UintArray = max ? getUintArray(max) : Array\n    if (!UintArray) {\n      throw new Error('invalid max value: ' + max)\n    }\n\n    this.#max = max\n    this.#maxSize = maxSize\n    this.maxEntrySize = maxEntrySize || this.#maxSize\n    this.sizeCalculation = sizeCalculation\n    if (this.sizeCalculation) {\n      if (!this.#maxSize && !this.maxEntrySize) {\n        throw new TypeError(\n          'cannot set sizeCalculation without setting maxSize or maxEntrySize'\n        )\n      }\n      if (typeof this.sizeCalculation !== 'function') {\n        throw new TypeError('sizeCalculation set to non-function')\n      }\n    }\n\n    if (\n      memoMethod !== undefined &&\n      typeof memoMethod !== 'function'\n    ) {\n      throw new TypeError('memoMethod must be a function if defined')\n    }\n    this.#memoMethod = memoMethod\n\n    if (\n      fetchMethod !== undefined &&\n      typeof fetchMethod !== 'function'\n    ) {\n      throw new TypeError(\n        'fetchMethod must be a function if specified'\n      )\n    }\n    this.#fetchMethod = fetchMethod\n    this.#hasFetchMethod = !!fetchMethod\n\n    this.#keyMap = new Map()\n    this.#keyList = new Array(max).fill(undefined)\n    this.#valList = new Array(max).fill(undefined)\n    this.#next = new UintArray(max)\n    this.#prev = new UintArray(max)\n    this.#head = 0 as Index\n    this.#tail = 0 as Index\n    this.#free = Stack.create(max)\n    this.#size = 0\n    this.#calculatedSize = 0\n\n    if (typeof dispose === 'function') {\n      this.#dispose = dispose\n    }\n    if (typeof disposeAfter === 'function') {\n      this.#disposeAfter = disposeAfter\n      this.#disposed = []\n    } else {\n      this.#disposeAfter = undefined\n      this.#disposed = undefined\n    }\n    this.#hasDispose = !!this.#dispose\n    this.#hasDisposeAfter = !!this.#disposeAfter\n\n    this.noDisposeOnSet = !!noDisposeOnSet\n    this.noUpdateTTL = !!noUpdateTTL\n    this.noDeleteOnFetchRejection = !!noDeleteOnFetchRejection\n    this.allowStaleOnFetchRejection = !!allowStaleOnFetchRejection\n    this.allowStaleOnFetchAbort = !!allowStaleOnFetchAbort\n    this.ignoreFetchAbort = !!ignoreFetchAbort\n\n    // NB: maxEntrySize is set to maxSize if it's set\n    if (this.maxEntrySize !== 0) {\n      if (this.#maxSize !== 0) {\n        if (!isPosInt(this.#maxSize)) {\n          throw new TypeError(\n            'maxSize must be a positive integer if specified'\n          )\n        }\n      }\n      if (!isPosInt(this.maxEntrySize)) {\n        throw new TypeError(\n          'maxEntrySize must be a positive integer if specified'\n        )\n      }\n      this.#initializeSizeTracking()\n    }\n\n    this.allowStale = !!allowStale\n    this.noDeleteOnStaleGet = !!noDeleteOnStaleGet\n    this.updateAgeOnGet = !!updateAgeOnGet\n    this.updateAgeOnHas = !!updateAgeOnHas\n    this.ttlResolution =\n      isPosInt(ttlResolution) || ttlResolution === 0\n        ? ttlResolution\n        : 1\n    this.ttlAutopurge = !!ttlAutopurge\n    this.ttl = ttl || 0\n    if (this.ttl) {\n      if (!isPosInt(this.ttl)) {\n        throw new TypeError(\n          'ttl must be a positive integer if specified'\n        )\n      }\n      this.#initializeTTLTracking()\n    }\n\n    // do not allow completely unbounded caches\n    if (this.#max === 0 && this.ttl === 0 && this.#maxSize === 0) {\n      throw new TypeError(\n        'At least one of max, maxSize, or ttl is required'\n      )\n    }\n    if (!this.ttlAutopurge && !this.#max && !this.#maxSize) {\n      const code = 'LRU_CACHE_UNBOUNDED'\n      if (shouldWarn(code)) {\n        warned.add(code)\n        const msg =\n          'TTL caching without ttlAutopurge, max, or maxSize can ' +\n          'result in unbounded memory consumption.'\n        emitWarning(msg, 'UnboundedCacheWarning', code, LRUCache)\n      }\n    }\n  }\n\n  /**\n   * Return the number of ms left in the item's TTL. If item is not in cache,\n   * returns `0`. Returns `Infinity` if item is in cache without a defined TTL.\n   */\n  getRemainingTTL(key: K) {\n    return this.#keyMap.has(key) ? Infinity : 0\n  }\n\n  #initializeTTLTracking() {\n    const ttls = new ZeroArray(this.#max)\n    const starts = new ZeroArray(this.#max)\n    this.#ttls = ttls\n    this.#starts = starts\n\n    this.#setItemTTL = (index, ttl, start = perf.now()) => {\n      starts[index] = ttl !== 0 ? start : 0\n      ttls[index] = ttl\n      if (ttl !== 0 && this.ttlAutopurge) {\n        const t = setTimeout(() => {\n          if (this.#isStale(index)) {\n            this.#delete(this.#keyList[index] as K, 'expire')\n          }\n        }, ttl + 1)\n        // unref() not supported on all platforms\n        /* c8 ignore start */\n        if (t.unref) {\n          t.unref()\n        }\n        /* c8 ignore stop */\n      }\n    }\n\n    this.#updateItemAge = index => {\n      starts[index] = ttls[index] !== 0 ? perf.now() : 0\n    }\n\n    this.#statusTTL = (status, index) => {\n      if (ttls[index]) {\n        const ttl = ttls[index]\n        const start = starts[index]\n        /* c8 ignore next */\n        if (!ttl || !start) return\n        status.ttl = ttl\n        status.start = start\n        status.now = cachedNow || getNow()\n        const age = status.now - start\n        status.remainingTTL = ttl - age\n      }\n    }\n\n    // debounce calls to perf.now() to 1s so we're not hitting\n    // that costly call repeatedly.\n    let cachedNow = 0\n    const getNow = () => {\n      const n = perf.now()\n      if (this.ttlResolution > 0) {\n        cachedNow = n\n        const t = setTimeout(\n          () => (cachedNow = 0),\n          this.ttlResolution\n        )\n        // not available on all platforms\n        /* c8 ignore start */\n        if (t.unref) {\n          t.unref()\n        }\n        /* c8 ignore stop */\n      }\n      return n\n    }\n\n    this.getRemainingTTL = key => {\n      const index = this.#keyMap.get(key)\n      if (index === undefined) {\n        return 0\n      }\n      const ttl = ttls[index]\n      const start = starts[index]\n      if (!ttl || !start) {\n        return Infinity\n      }\n      const age = (cachedNow || getNow()) - start\n      return ttl - age\n    }\n\n    this.#isStale = index => {\n      const s = starts[index]\n      const t = ttls[index]\n      return !!t && !!s && (cachedNow || getNow()) - s > t\n    }\n  }\n\n  // conditionally set private methods related to TTL\n  #updateItemAge: (index: Index) => void = () => {}\n  #statusTTL: (status: LRUCache.Status<V>, index: Index) => void =\n    () => {}\n  #setItemTTL: (\n    index: Index,\n    ttl: LRUCache.Milliseconds,\n    start?: LRUCache.Milliseconds\n    // ignore because we never call this if we're not already in TTL mode\n    /* c8 ignore start */\n  ) => void = () => {}\n  /* c8 ignore stop */\n\n  #isStale: (index: Index) => boolean = () => false\n\n  #initializeSizeTracking() {\n    const sizes = new ZeroArray(this.#max)\n    this.#calculatedSize = 0\n    this.#sizes = sizes\n    this.#removeItemSize = index => {\n      this.#calculatedSize -= sizes[index] as number\n      sizes[index] = 0\n    }\n    this.#requireSize = (k, v, size, sizeCalculation) => {\n      // provisionally accept background fetches.\n      // actual value size will be checked when they return.\n      if (this.#isBackgroundFetch(v)) {\n        return 0\n      }\n      if (!isPosInt(size)) {\n        if (sizeCalculation) {\n          if (typeof sizeCalculation !== 'function') {\n            throw new TypeError('sizeCalculation must be a function')\n          }\n          size = sizeCalculation(v, k)\n          if (!isPosInt(size)) {\n            throw new TypeError(\n              'sizeCalculation return invalid (expect positive integer)'\n            )\n          }\n        } else {\n          throw new TypeError(\n            'invalid size value (must be positive integer). ' +\n              'When maxSize or maxEntrySize is used, sizeCalculation ' +\n              'or size must be set.'\n          )\n        }\n      }\n      return size\n    }\n    this.#addItemSize = (\n      index: Index,\n      size: LRUCache.Size,\n      status?: LRUCache.Status<V>\n    ) => {\n      sizes[index] = size\n      if (this.#maxSize) {\n        const maxSize = this.#maxSize - (sizes[index] as number)\n        while (this.#calculatedSize > maxSize) {\n          this.#evict(true)\n        }\n      }\n      this.#calculatedSize += sizes[index] as number\n      if (status) {\n        status.entrySize = size\n        status.totalCalculatedSize = this.#calculatedSize\n      }\n    }\n  }\n\n  #removeItemSize: (index: Index) => void = _i => {}\n  #addItemSize: (\n    index: Index,\n    size: LRUCache.Size,\n    status?: LRUCache.Status<V>\n  ) => void = (_i, _s, _st) => {}\n  #requireSize: (\n    k: K,\n    v: V | BackgroundFetch<V>,\n    size?: LRUCache.Size,\n    sizeCalculation?: LRUCache.SizeCalculator<K, V>\n  ) => LRUCache.Size = (\n    _k: K,\n    _v: V | BackgroundFetch<V>,\n    size?: LRUCache.Size,\n    sizeCalculation?: LRUCache.SizeCalculator<K, V>\n  ) => {\n    if (size || sizeCalculation) {\n      throw new TypeError(\n        'cannot set size without setting maxSize or maxEntrySize on cache'\n      )\n    }\n    return 0\n  };\n\n  *#indexes({ allowStale = this.allowStale } = {}) {\n    if (this.#size) {\n      for (let i = this.#tail; true; ) {\n        if (!this.#isValidIndex(i)) {\n          break\n        }\n        if (allowStale || !this.#isStale(i)) {\n          yield i\n        }\n        if (i === this.#head) {\n          break\n        } else {\n          i = this.#prev[i] as Index\n        }\n      }\n    }\n  }\n\n  *#rindexes({ allowStale = this.allowStale } = {}) {\n    if (this.#size) {\n      for (let i = this.#head; true; ) {\n        if (!this.#isValidIndex(i)) {\n          break\n        }\n        if (allowStale || !this.#isStale(i)) {\n          yield i\n        }\n        if (i === this.#tail) {\n          break\n        } else {\n          i = this.#next[i] as Index\n        }\n      }\n    }\n  }\n\n  #isValidIndex(index: Index) {\n    return (\n      index !== undefined &&\n      this.#keyMap.get(this.#keyList[index] as K) === index\n    )\n  }\n\n  /**\n   * Return a generator yielding `[key, value]` pairs,\n   * in order from most recently used to least recently used.\n   */\n  *entries() {\n    for (const i of this.#indexes()) {\n      if (\n        this.#valList[i] !== undefined &&\n        this.#keyList[i] !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield [this.#keyList[i], this.#valList[i]] as [K, V]\n      }\n    }\n  }\n\n  /**\n   * Inverse order version of {@link LRUCache.entries}\n   *\n   * Return a generator yielding `[key, value]` pairs,\n   * in order from least recently used to most recently used.\n   */\n  *rentries() {\n    for (const i of this.#rindexes()) {\n      if (\n        this.#valList[i] !== undefined &&\n        this.#keyList[i] !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield [this.#keyList[i], this.#valList[i]]\n      }\n    }\n  }\n\n  /**\n   * Return a generator yielding the keys in the cache,\n   * in order from most recently used to least recently used.\n   */\n  *keys() {\n    for (const i of this.#indexes()) {\n      const k = this.#keyList[i]\n      if (\n        k !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield k\n      }\n    }\n  }\n\n  /**\n   * Inverse order version of {@link LRUCache.keys}\n   *\n   * Return a generator yielding the keys in the cache,\n   * in order from least recently used to most recently used.\n   */\n  *rkeys() {\n    for (const i of this.#rindexes()) {\n      const k = this.#keyList[i]\n      if (\n        k !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield k\n      }\n    }\n  }\n\n  /**\n   * Return a generator yielding the values in the cache,\n   * in order from most recently used to least recently used.\n   */\n  *values() {\n    for (const i of this.#indexes()) {\n      const v = this.#valList[i]\n      if (\n        v !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield this.#valList[i] as V\n      }\n    }\n  }\n\n  /**\n   * Inverse order version of {@link LRUCache.values}\n   *\n   * Return a generator yielding the values in the cache,\n   * in order from least recently used to most recently used.\n   */\n  *rvalues() {\n    for (const i of this.#rindexes()) {\n      const v = this.#valList[i]\n      if (\n        v !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield this.#valList[i]\n      }\n    }\n  }\n\n  /**\n   * Iterating over the cache itself yields the same results as\n   * {@link LRUCache.entries}\n   */\n  [Symbol.iterator]() {\n    return this.entries()\n  }\n\n  /**\n   * A String value that is used in the creation of the default string\n   * description of an object. Called by the built-in method\n   * `Object.prototype.toString`.\n   */\n  [Symbol.toStringTag] = 'LRUCache'\n\n  /**\n   * Find a value for which the supplied fn method returns a truthy value,\n   * similar to `Array.find()`. fn is called as `fn(value, key, cache)`.\n   */\n  find(\n    fn: (v: V, k: K, self: LRUCache<K, V, FC>) => boolean,\n    getOptions: LRUCache.GetOptions<K, V, FC> = {}\n  ) {\n    for (const i of this.#indexes()) {\n      const v = this.#valList[i]\n      const value = this.#isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      if (fn(value, this.#keyList[i] as K, this)) {\n        return this.get(this.#keyList[i] as K, getOptions)\n      }\n    }\n  }\n\n  /**\n   * Call the supplied function on each item in the cache, in order from most\n   * recently used to least recently used.\n   *\n   * `fn` is called as `fn(value, key, cache)`.\n   *\n   * If `thisp` is provided, function will be called in the `this`-context of\n   * the provided object, or the cache if no `thisp` object is provided.\n   *\n   * Does not update age or recenty of use, or iterate over stale values.\n   */\n  forEach(\n    fn: (v: V, k: K, self: LRUCache<K, V, FC>) => any,\n    thisp: any = this\n  ) {\n    for (const i of this.#indexes()) {\n      const v = this.#valList[i]\n      const value = this.#isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      fn.call(thisp, value, this.#keyList[i] as K, this)\n    }\n  }\n\n  /**\n   * The same as {@link LRUCache.forEach} but items are iterated over in\n   * reverse order.  (ie, less recently used items are iterated over first.)\n   */\n  rforEach(\n    fn: (v: V, k: K, self: LRUCache<K, V, FC>) => any,\n    thisp: any = this\n  ) {\n    for (const i of this.#rindexes()) {\n      const v = this.#valList[i]\n      const value = this.#isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      fn.call(thisp, value, this.#keyList[i] as K, this)\n    }\n  }\n\n  /**\n   * Delete any stale entries. Returns true if anything was removed,\n   * false otherwise.\n   */\n  purgeStale() {\n    let deleted = false\n    for (const i of this.#rindexes({ allowStale: true })) {\n      if (this.#isStale(i)) {\n        this.#delete(this.#keyList[i] as K, 'expire')\n        deleted = true\n      }\n    }\n    return deleted\n  }\n\n  /**\n   * Get the extended info about a given entry, to get its value, size, and\n   * TTL info simultaneously. Returns `undefined` if the key is not present.\n   *\n   * Unlike {@link LRUCache#dump}, which is designed to be portable and survive\n   * serialization, the `start` value is always the current timestamp, and the\n   * `ttl` is a calculated remaining time to live (negative if expired).\n   *\n   * Always returns stale values, if their info is found in the cache, so be\n   * sure to check for expirations (ie, a negative {@link LRUCache.Entry#ttl})\n   * if relevant.\n   */\n  info(key: K): LRUCache.Entry<V> | undefined {\n    const i = this.#keyMap.get(key)\n    if (i === undefined) return undefined\n    const v = this.#valList[i]\n    const value: V | undefined = this.#isBackgroundFetch(v)\n      ? v.__staleWhileFetching\n      : v\n    if (value === undefined) return undefined\n    const entry: LRUCache.Entry<V> = { value }\n    if (this.#ttls && this.#starts) {\n      const ttl = this.#ttls[i]\n      const start = this.#starts[i]\n      if (ttl && start) {\n        const remain = ttl - (perf.now() - start)\n        entry.ttl = remain\n        entry.start = Date.now()\n      }\n    }\n    if (this.#sizes) {\n      entry.size = this.#sizes[i]\n    }\n    return entry\n  }\n\n  /**\n   * Return an array of [key, {@link LRUCache.Entry}] tuples which can be\n   * passed to {@link LRLUCache#load}.\n   *\n   * The `start` fields are calculated relative to a portable `Date.now()`\n   * timestamp, even if `performance.now()` is available.\n   *\n   * Stale entries are always included in the `dump`, even if\n   * {@link LRUCache.OptionsBase.allowStale} is false.\n   *\n   * Note: this returns an actual array, not a generator, so it can be more\n   * easily passed around.\n   */\n  dump() {\n    const arr: [K, LRUCache.Entry<V>][] = []\n    for (const i of this.#indexes({ allowStale: true })) {\n      const key = this.#keyList[i]\n      const v = this.#valList[i]\n      const value: V | undefined = this.#isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined || key === undefined) continue\n      const entry: LRUCache.Entry<V> = { value }\n      if (this.#ttls && this.#starts) {\n        entry.ttl = this.#ttls[i]\n        // always dump the start relative to a portable timestamp\n        // it's ok for this to be a bit slow, it's a rare operation.\n        const age = perf.now() - (this.#starts[i] as number)\n        entry.start = Math.floor(Date.now() - age)\n      }\n      if (this.#sizes) {\n        entry.size = this.#sizes[i]\n      }\n      arr.unshift([key, entry])\n    }\n    return arr\n  }\n\n  /**\n   * Reset the cache and load in the items in entries in the order listed.\n   *\n   * The shape of the resulting cache may be different if the same options are\n   * not used in both caches.\n   *\n   * The `start` fields are assumed to be calculated relative to a portable\n   * `Date.now()` timestamp, even if `performance.now()` is available.\n   */\n  load(arr: [K, LRUCache.Entry<V>][]) {\n    this.clear()\n    for (const [key, entry] of arr) {\n      if (entry.start) {\n        // entry.start is a portable timestamp, but we may be using\n        // node's performance.now(), so calculate the offset, so that\n        // we get the intended remaining TTL, no matter how long it's\n        // been on ice.\n        //\n        // it's ok for this to be a bit slow, it's a rare operation.\n        const age = Date.now() - entry.start\n        entry.start = perf.now() - age\n      }\n      this.set(key, entry.value, entry)\n    }\n  }\n\n  /**\n   * Add a value to the cache.\n   *\n   * Note: if `undefined` is specified as a value, this is an alias for\n   * {@link LRUCache#delete}\n   *\n   * Fields on the {@link LRUCache.SetOptions} options param will override\n   * their corresponding values in the constructor options for the scope\n   * of this single `set()` operation.\n   *\n   * If `start` is provided, then that will set the effective start\n   * time for the TTL calculation. Note that this must be a previous\n   * value of `performance.now()` if supported, or a previous value of\n   * `Date.now()` if not.\n   *\n   * Options object may also include `size`, which will prevent\n   * calling the `sizeCalculation` function and just use the specified\n   * number if it is a positive integer, and `noDisposeOnSet` which\n   * will prevent calling a `dispose` function in the case of\n   * overwrites.\n   *\n   * If the `size` (or return value of `sizeCalculation`) for a given\n   * entry is greater than `maxEntrySize`, then the item will not be\n   * added to the cache.\n   *\n   * Will update the recency of the entry.\n   *\n   * If the value is `undefined`, then this is an alias for\n   * `cache.delete(key)`. `undefined` is never stored in the cache.\n   */\n  set(\n    k: K,\n    v: V | BackgroundFetch<V> | undefined,\n    setOptions: LRUCache.SetOptions<K, V, FC> = {}\n  ) {\n    if (v === undefined) {\n      this.delete(k)\n      return this\n    }\n    const {\n      ttl = this.ttl,\n      start,\n      noDisposeOnSet = this.noDisposeOnSet,\n      sizeCalculation = this.sizeCalculation,\n      status,\n    } = setOptions\n    let { noUpdateTTL = this.noUpdateTTL } = setOptions\n\n    const size = this.#requireSize(\n      k,\n      v,\n      setOptions.size || 0,\n      sizeCalculation\n    )\n    // if the item doesn't fit, don't do anything\n    // NB: maxEntrySize set to maxSize by default\n    if (this.maxEntrySize && size > this.maxEntrySize) {\n      if (status) {\n        status.set = 'miss'\n        status.maxEntrySizeExceeded = true\n      }\n      // have to delete, in case something is there already.\n      this.#delete(k, 'set')\n      return this\n    }\n    let index = this.#size === 0 ? undefined : this.#keyMap.get(k)\n    if (index === undefined) {\n      // addition\n      index = (\n        this.#size === 0\n          ? this.#tail\n          : this.#free.length !== 0\n          ? this.#free.pop()\n          : this.#size === this.#max\n          ? this.#evict(false)\n          : this.#size\n      ) as Index\n      this.#keyList[index] = k\n      this.#valList[index] = v\n      this.#keyMap.set(k, index)\n      this.#next[this.#tail] = index\n      this.#prev[index] = this.#tail\n      this.#tail = index\n      this.#size++\n      this.#addItemSize(index, size, status)\n      if (status) status.set = 'add'\n      noUpdateTTL = false\n    } else {\n      // update\n      this.#moveToTail(index)\n      const oldVal = this.#valList[index] as V | BackgroundFetch<V>\n      if (v !== oldVal) {\n        if (this.#hasFetchMethod && this.#isBackgroundFetch(oldVal)) {\n          oldVal.__abortController.abort(new Error('replaced'))\n          const { __staleWhileFetching: s } = oldVal\n          if (s !== undefined && !noDisposeOnSet) {\n            if (this.#hasDispose) {\n              this.#dispose?.(s as V, k, 'set')\n            }\n            if (this.#hasDisposeAfter) {\n              this.#disposed?.push([s as V, k, 'set'])\n            }\n          }\n        } else if (!noDisposeOnSet) {\n          if (this.#hasDispose) {\n            this.#dispose?.(oldVal as V, k, 'set')\n          }\n          if (this.#hasDisposeAfter) {\n            this.#disposed?.push([oldVal as V, k, 'set'])\n          }\n        }\n        this.#removeItemSize(index)\n        this.#addItemSize(index, size, status)\n        this.#valList[index] = v\n        if (status) {\n          status.set = 'replace'\n          const oldValue =\n            oldVal && this.#isBackgroundFetch(oldVal)\n              ? oldVal.__staleWhileFetching\n              : oldVal\n          if (oldValue !== undefined) status.oldValue = oldValue\n        }\n      } else if (status) {\n        status.set = 'update'\n      }\n    }\n    if (ttl !== 0 && !this.#ttls) {\n      this.#initializeTTLTracking()\n    }\n    if (this.#ttls) {\n      if (!noUpdateTTL) {\n        this.#setItemTTL(index, ttl, start)\n      }\n      if (status) this.#statusTTL(status, index)\n    }\n    if (!noDisposeOnSet && this.#hasDisposeAfter && this.#disposed) {\n      const dt = this.#disposed\n      let task: DisposeTask<K, V> | undefined\n      while ((task = dt?.shift())) {\n        this.#disposeAfter?.(...task)\n      }\n    }\n    return this\n  }\n\n  /**\n   * Evict the least recently used item, returning its value or\n   * `undefined` if cache is empty.\n   */\n  pop(): V | undefined {\n    try {\n      while (this.#size) {\n        const val = this.#valList[this.#head]\n        this.#evict(true)\n        if (this.#isBackgroundFetch(val)) {\n          if (val.__staleWhileFetching) {\n            return val.__staleWhileFetching\n          }\n        } else if (val !== undefined) {\n          return val\n        }\n      }\n    } finally {\n      if (this.#hasDisposeAfter && this.#disposed) {\n        const dt = this.#disposed\n        let task: DisposeTask<K, V> | undefined\n        while ((task = dt?.shift())) {\n          this.#disposeAfter?.(...task)\n        }\n      }\n    }\n  }\n\n  #evict(free: boolean) {\n    const head = this.#head\n    const k = this.#keyList[head] as K\n    const v = this.#valList[head] as V\n    if (this.#hasFetchMethod && this.#isBackgroundFetch(v)) {\n      v.__abortController.abort(new Error('evicted'))\n    } else if (this.#hasDispose || this.#hasDisposeAfter) {\n      if (this.#hasDispose) {\n        this.#dispose?.(v, k, 'evict')\n      }\n      if (this.#hasDisposeAfter) {\n        this.#disposed?.push([v, k, 'evict'])\n      }\n    }\n    this.#removeItemSize(head)\n    // if we aren't about to use the index, then null these out\n    if (free) {\n      this.#keyList[head] = undefined\n      this.#valList[head] = undefined\n      this.#free.push(head)\n    }\n    if (this.#size === 1) {\n      this.#head = this.#tail = 0 as Index\n      this.#free.length = 0\n    } else {\n      this.#head = this.#next[head] as Index\n    }\n    this.#keyMap.delete(k)\n    this.#size--\n    return head\n  }\n\n  /**\n   * Check if a key is in the cache, without updating the recency of use.\n   * Will return false if the item is stale, even though it is technically\n   * in the cache.\n   *\n   * Check if a key is in the cache, without updating the recency of\n   * use. Age is updated if {@link LRUCache.OptionsBase.updateAgeOnHas} is set\n   * to `true` in either the options or the constructor.\n   *\n   * Will return `false` if the item is stale, even though it is technically in\n   * the cache. The difference can be determined (if it matters) by using a\n   * `status` argument, and inspecting the `has` field.\n   *\n   * Will not update item age unless\n   * {@link LRUCache.OptionsBase.updateAgeOnHas} is set.\n   */\n  has(k: K, hasOptions: LRUCache.HasOptions<K, V, FC> = {}) {\n    const { updateAgeOnHas = this.updateAgeOnHas, status } =\n      hasOptions\n    const index = this.#keyMap.get(k)\n    if (index !== undefined) {\n      const v = this.#valList[index]\n      if (\n        this.#isBackgroundFetch(v) &&\n        v.__staleWhileFetching === undefined\n      ) {\n        return false\n      }\n      if (!this.#isStale(index)) {\n        if (updateAgeOnHas) {\n          this.#updateItemAge(index)\n        }\n        if (status) {\n          status.has = 'hit'\n          this.#statusTTL(status, index)\n        }\n        return true\n      } else if (status) {\n        status.has = 'stale'\n        this.#statusTTL(status, index)\n      }\n    } else if (status) {\n      status.has = 'miss'\n    }\n    return false\n  }\n\n  /**\n   * Like {@link LRUCache#get} but doesn't update recency or delete stale\n   * items.\n   *\n   * Returns `undefined` if the item is stale, unless\n   * {@link LRUCache.OptionsBase.allowStale} is set.\n   */\n  peek(k: K, peekOptions: LRUCache.PeekOptions<K, V, FC> = {}) {\n    const { allowStale = this.allowStale } = peekOptions\n    const index = this.#keyMap.get(k)\n    if (\n      index === undefined ||\n      (!allowStale && this.#isStale(index))\n    ) {\n      return\n    }\n    const v = this.#valList[index]\n    // either stale and allowed, or forcing a refresh of non-stale value\n    return this.#isBackgroundFetch(v) ? v.__staleWhileFetching : v\n  }\n\n  #backgroundFetch(\n    k: K,\n    index: Index | undefined,\n    options: LRUCache.FetchOptions<K, V, FC>,\n    context: any\n  ): BackgroundFetch<V> {\n    const v = index === undefined ? undefined : this.#valList[index]\n    if (this.#isBackgroundFetch(v)) {\n      return v\n    }\n\n    const ac = new AC()\n    const { signal } = options\n    // when/if our AC signals, then stop listening to theirs.\n    signal?.addEventListener('abort', () => ac.abort(signal.reason), {\n      signal: ac.signal,\n    })\n\n    const fetchOpts = {\n      signal: ac.signal,\n      options,\n      context,\n    }\n\n    const cb = (\n      v: V | undefined,\n      updateCache = false\n    ): V | undefined => {\n      const { aborted } = ac.signal\n      const ignoreAbort = options.ignoreFetchAbort && v !== undefined\n      if (options.status) {\n        if (aborted && !updateCache) {\n          options.status.fetchAborted = true\n          options.status.fetchError = ac.signal.reason\n          if (ignoreAbort) options.status.fetchAbortIgnored = true\n        } else {\n          options.status.fetchResolved = true\n        }\n      }\n      if (aborted && !ignoreAbort && !updateCache) {\n        return fetchFail(ac.signal.reason)\n      }\n      // either we didn't abort, and are still here, or we did, and ignored\n      const bf = p as BackgroundFetch<V>\n      if (this.#valList[index as Index] === p) {\n        if (v === undefined) {\n          if (bf.__staleWhileFetching) {\n            this.#valList[index as Index] = bf.__staleWhileFetching\n          } else {\n            this.#delete(k, 'fetch')\n          }\n        } else {\n          if (options.status) options.status.fetchUpdated = true\n          this.set(k, v, fetchOpts.options)\n        }\n      }\n      return v\n    }\n\n    const eb = (er: any) => {\n      if (options.status) {\n        options.status.fetchRejected = true\n        options.status.fetchError = er\n      }\n      return fetchFail(er)\n    }\n\n    const fetchFail = (er: any): V | undefined => {\n      const { aborted } = ac.signal\n      const allowStaleAborted =\n        aborted && options.allowStaleOnFetchAbort\n      const allowStale =\n        allowStaleAborted || options.allowStaleOnFetchRejection\n      const noDelete = allowStale || options.noDeleteOnFetchRejection\n      const bf = p as BackgroundFetch<V>\n      if (this.#valList[index as Index] === p) {\n        // if we allow stale on fetch rejections, then we need to ensure that\n        // the stale value is not removed from the cache when the fetch fails.\n        const del = !noDelete || bf.__staleWhileFetching === undefined\n        if (del) {\n          this.#delete(k, 'fetch')\n        } else if (!allowStaleAborted) {\n          // still replace the *promise* with the stale value,\n          // since we are done with the promise at this point.\n          // leave it untouched if we're still waiting for an\n          // aborted background fetch that hasn't yet returned.\n          this.#valList[index as Index] = bf.__staleWhileFetching\n        }\n      }\n      if (allowStale) {\n        if (options.status && bf.__staleWhileFetching !== undefined) {\n          options.status.returnedStale = true\n        }\n        return bf.__staleWhileFetching\n      } else if (bf.__returned === bf) {\n        throw er\n      }\n    }\n\n    const pcall = (\n      res: (v: V | undefined) => void,\n      rej: (e: any) => void\n    ) => {\n      const fmp = this.#fetchMethod?.(k, v, fetchOpts)\n      if (fmp && fmp instanceof Promise) {\n        fmp.then(v => res(v === undefined ? undefined : v), rej)\n      }\n      // ignored, we go until we finish, regardless.\n      // defer check until we are actually aborting,\n      // so fetchMethod can override.\n      ac.signal.addEventListener('abort', () => {\n        if (\n          !options.ignoreFetchAbort ||\n          options.allowStaleOnFetchAbort\n        ) {\n          res(undefined)\n          // when it eventually resolves, update the cache.\n          if (options.allowStaleOnFetchAbort) {\n            res = v => cb(v, true)\n          }\n        }\n      })\n    }\n\n    if (options.status) options.status.fetchDispatched = true\n    const p = new Promise(pcall).then(cb, eb)\n    const bf: BackgroundFetch<V> = Object.assign(p, {\n      __abortController: ac,\n      __staleWhileFetching: v,\n      __returned: undefined,\n    })\n\n    if (index === undefined) {\n      // internal, don't expose status.\n      this.set(k, bf, { ...fetchOpts.options, status: undefined })\n      index = this.#keyMap.get(k)\n    } else {\n      this.#valList[index] = bf\n    }\n    return bf\n  }\n\n  #isBackgroundFetch(p: any): p is BackgroundFetch<V> {\n    if (!this.#hasFetchMethod) return false\n    const b = p as BackgroundFetch<V>\n    return (\n      !!b &&\n      b instanceof Promise &&\n      b.hasOwnProperty('__staleWhileFetching') &&\n      b.__abortController instanceof AC\n    )\n  }\n\n  /**\n   * Make an asynchronous cached fetch using the\n   * {@link LRUCache.OptionsBase.fetchMethod} function.\n   *\n   * If the value is in the cache and not stale, then the returned\n   * Promise resolves to the value.\n   *\n   * If not in the cache, or beyond its TTL staleness, then\n   * `fetchMethod(key, staleValue, { options, signal, context })` is\n   * called, and the value returned will be added to the cache once\n   * resolved.\n   *\n   * If called with `allowStale`, and an asynchronous fetch is\n   * currently in progress to reload a stale value, then the former\n   * stale value will be returned.\n   *\n   * If called with `forceRefresh`, then the cached item will be\n   * re-fetched, even if it is not stale. However, if `allowStale` is also\n   * set, then the old value will still be returned. This is useful\n   * in cases where you want to force a reload of a cached value. If\n   * a background fetch is already in progress, then `forceRefresh`\n   * has no effect.\n   *\n   * If multiple fetches for the same key are issued, then they will all be\n   * coalesced into a single call to fetchMethod.\n   *\n   * Note that this means that handling options such as\n   * {@link LRUCache.OptionsBase.allowStaleOnFetchAbort},\n   * {@link LRUCache.FetchOptions.signal},\n   * and {@link LRUCache.OptionsBase.allowStaleOnFetchRejection} will be\n   * determined by the FIRST fetch() call for a given key.\n   *\n   * This is a known (fixable) shortcoming which will be addresed on when\n   * someone complains about it, as the fix would involve added complexity and\n   * may not be worth the costs for this edge case.\n   *\n   * If {@link LRUCache.OptionsBase.fetchMethod} is not specified, then this is\n   * effectively an alias for `Promise.resolve(cache.get(key))`.\n   *\n   * When the fetch method resolves to a value, if the fetch has not\n   * been aborted due to deletion, eviction, or being overwritten,\n   * then it is added to the cache using the options provided.\n   *\n   * If the key is evicted or deleted before the `fetchMethod`\n   * resolves, then the AbortSignal passed to the `fetchMethod` will\n   * receive an `abort` event, and the promise returned by `fetch()`\n   * will reject with the reason for the abort.\n   *\n   * If a `signal` is passed to the `fetch()` call, then aborting the\n   * signal will abort the fetch and cause the `fetch()` promise to\n   * reject with the reason provided.\n   *\n   * **Setting `context`**\n   *\n   * If an `FC` type is set to a type other than `unknown`, `void`, or\n   * `undefined` in the {@link LRUCache} constructor, then all\n   * calls to `cache.fetch()` _must_ provide a `context` option. If\n   * set to `undefined` or `void`, then calls to fetch _must not_\n   * provide a `context` option.\n   *\n   * The `context` param allows you to provide arbitrary data that\n   * might be relevant in the course of fetching the data. It is only\n   * relevant for the course of a single `fetch()` operation, and\n   * discarded afterwards.\n   *\n   * **Note: `fetch()` calls are inflight-unique**\n   *\n   * If you call `fetch()` multiple times with the same key value,\n   * then every call after the first will resolve on the same\n   * promise<sup>1</sup>,\n   * _even if they have different settings that would otherwise change\n   * the behavior of the fetch_, such as `noDeleteOnFetchRejection`\n   * or `ignoreFetchAbort`.\n   *\n   * In most cases, this is not a problem (in fact, only fetching\n   * something once is what you probably want, if you're caching in\n   * the first place). If you are changing the fetch() options\n   * dramatically between runs, there's a good chance that you might\n   * be trying to fit divergent semantics into a single object, and\n   * would be better off with multiple cache instances.\n   *\n   * **1**: Ie, they're not the \"same Promise\", but they resolve at\n   * the same time, because they're both waiting on the same\n   * underlying fetchMethod response.\n   */\n\n  fetch(\n    k: K,\n    fetchOptions: unknown extends FC\n      ? LRUCache.FetchOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.FetchOptionsNoContext<K, V>\n      : LRUCache.FetchOptionsWithContext<K, V, FC>\n  ): Promise<undefined | V>\n\n  // this overload not allowed if context is required\n  fetch(\n    k: unknown extends FC\n      ? K\n      : FC extends undefined | void\n      ? K\n      : never,\n    fetchOptions?: unknown extends FC\n      ? LRUCache.FetchOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.FetchOptionsNoContext<K, V>\n      : never\n  ): Promise<undefined | V>\n\n  async fetch(\n    k: K,\n    fetchOptions: LRUCache.FetchOptions<K, V, FC> = {}\n  ): Promise<undefined | V> {\n    const {\n      // get options\n      allowStale = this.allowStale,\n      updateAgeOnGet = this.updateAgeOnGet,\n      noDeleteOnStaleGet = this.noDeleteOnStaleGet,\n      // set options\n      ttl = this.ttl,\n      noDisposeOnSet = this.noDisposeOnSet,\n      size = 0,\n      sizeCalculation = this.sizeCalculation,\n      noUpdateTTL = this.noUpdateTTL,\n      // fetch exclusive options\n      noDeleteOnFetchRejection = this.noDeleteOnFetchRejection,\n      allowStaleOnFetchRejection = this.allowStaleOnFetchRejection,\n      ignoreFetchAbort = this.ignoreFetchAbort,\n      allowStaleOnFetchAbort = this.allowStaleOnFetchAbort,\n      context,\n      forceRefresh = false,\n      status,\n      signal,\n    } = fetchOptions\n\n    if (!this.#hasFetchMethod) {\n      if (status) status.fetch = 'get'\n      return this.get(k, {\n        allowStale,\n        updateAgeOnGet,\n        noDeleteOnStaleGet,\n        status,\n      })\n    }\n\n    const options = {\n      allowStale,\n      updateAgeOnGet,\n      noDeleteOnStaleGet,\n      ttl,\n      noDisposeOnSet,\n      size,\n      sizeCalculation,\n      noUpdateTTL,\n      noDeleteOnFetchRejection,\n      allowStaleOnFetchRejection,\n      allowStaleOnFetchAbort,\n      ignoreFetchAbort,\n      status,\n      signal,\n    }\n\n    let index = this.#keyMap.get(k)\n    if (index === undefined) {\n      if (status) status.fetch = 'miss'\n      const p = this.#backgroundFetch(k, index, options, context)\n      return (p.__returned = p)\n    } else {\n      // in cache, maybe already fetching\n      const v = this.#valList[index]\n      if (this.#isBackgroundFetch(v)) {\n        const stale =\n          allowStale && v.__staleWhileFetching !== undefined\n        if (status) {\n          status.fetch = 'inflight'\n          if (stale) status.returnedStale = true\n        }\n        return stale ? v.__staleWhileFetching : (v.__returned = v)\n      }\n\n      // if we force a refresh, that means do NOT serve the cached value,\n      // unless we are already in the process of refreshing the cache.\n      const isStale = this.#isStale(index)\n      if (!forceRefresh && !isStale) {\n        if (status) status.fetch = 'hit'\n        this.#moveToTail(index)\n        if (updateAgeOnGet) {\n          this.#updateItemAge(index)\n        }\n        if (status) this.#statusTTL(status, index)\n        return v\n      }\n\n      // ok, it is stale or a forced refresh, and not already fetching.\n      // refresh the cache.\n      const p = this.#backgroundFetch(k, index, options, context)\n      const hasStale = p.__staleWhileFetching !== undefined\n      const staleVal = hasStale && allowStale\n      if (status) {\n        status.fetch = isStale ? 'stale' : 'refresh'\n        if (staleVal && isStale) status.returnedStale = true\n      }\n      return staleVal ? p.__staleWhileFetching : (p.__returned = p)\n    }\n  }\n\n  /**\n   * In some cases, `cache.fetch()` may resolve to `undefined`, either because\n   * a {@link LRUCache.OptionsBase#fetchMethod} was not provided (turning\n   * `cache.fetch(k)` into just an async wrapper around `cache.get(k)`) or\n   * because `ignoreFetchAbort` was specified (either to the constructor or\n   * in the {@link LRUCache.FetchOptions}). Also, the\n   * {@link OptionsBase.fetchMethod} may return `undefined` or `void`, making\n   * the test even more complicated.\n   *\n   * Because inferring the cases where `undefined` might be returned are so\n   * cumbersome, but testing for `undefined` can also be annoying, this method\n   * can be used, which will reject if `this.fetch()` resolves to undefined.\n   */\n  forceFetch(\n    k: K,\n    fetchOptions: unknown extends FC\n      ? LRUCache.FetchOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.FetchOptionsNoContext<K, V>\n      : LRUCache.FetchOptionsWithContext<K, V, FC>\n  ): Promise<V>\n  // this overload not allowed if context is required\n  forceFetch(\n    k: unknown extends FC\n      ? K\n      : FC extends undefined | void\n      ? K\n      : never,\n    fetchOptions?: unknown extends FC\n      ? LRUCache.FetchOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.FetchOptionsNoContext<K, V>\n      : never\n  ): Promise<V>\n  async forceFetch(\n    k: K,\n    fetchOptions: LRUCache.FetchOptions<K, V, FC> = {}\n  ): Promise<V> {\n    const v = await this.fetch(\n      k,\n      fetchOptions as unknown extends FC\n        ? LRUCache.FetchOptions<K, V, FC>\n        : FC extends undefined | void\n        ? LRUCache.FetchOptionsNoContext<K, V>\n        : LRUCache.FetchOptionsWithContext<K, V, FC>\n    )\n    if (v === undefined) throw new Error('fetch() returned undefined')\n    return v\n  }\n\n  /**\n   * If the key is found in the cache, then this is equivalent to\n   * {@link LRUCache#get}. If not, in the cache, then calculate the value using\n   * the {@link LRUCache.OptionsBase.memoMethod}, and add it to the cache.\n   *\n   * If an `FC` type is set to a type other than `unknown`, `void`, or\n   * `undefined` in the LRUCache constructor, then all calls to `cache.memo()`\n   * _must_ provide a `context` option. If set to `undefined` or `void`, then\n   * calls to memo _must not_ provide a `context` option.\n   *\n   * The `context` param allows you to provide arbitrary data that might be\n   * relevant in the course of fetching the data. It is only relevant for the\n   * course of a single `memo()` operation, and discarded afterwards.\n   */\n  memo(\n    k: K,\n    memoOptions: unknown extends FC\n      ? LRUCache.MemoOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.MemoOptionsNoContext<K, V>\n      : LRUCache.MemoOptionsWithContext<K, V, FC>\n  ): V\n  // this overload not allowed if context is required\n  memo(\n    k: unknown extends FC\n      ? K\n      : FC extends undefined | void\n      ? K\n      : never,\n    memoOptions?: unknown extends FC\n      ? LRUCache.MemoOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.MemoOptionsNoContext<K, V>\n      : never\n  ): V\n  memo(k: K, memoOptions: LRUCache.MemoOptions<K, V, FC> = {}) {\n    const memoMethod = this.#memoMethod\n    if (!memoMethod) {\n      throw new Error('no memoMethod provided to constructor')\n    }\n    const { context, forceRefresh, ...options } = memoOptions\n    const v = this.get(k, options)\n    if (!forceRefresh && v !== undefined) return v\n    const vv = memoMethod(k, v, {\n      options,\n      context,\n    } as LRUCache.MemoizerOptions<K, V, FC>)\n    this.set(k, vv, options)\n    return vv\n  }\n\n  /**\n   * Return a value from the cache. Will update the recency of the cache\n   * entry found.\n   *\n   * If the key is not found, get() will return `undefined`.\n   */\n  get(k: K, getOptions: LRUCache.GetOptions<K, V, FC> = {}) {\n    const {\n      allowStale = this.allowStale,\n      updateAgeOnGet = this.updateAgeOnGet,\n      noDeleteOnStaleGet = this.noDeleteOnStaleGet,\n      status,\n    } = getOptions\n    const index = this.#keyMap.get(k)\n    if (index !== undefined) {\n      const value = this.#valList[index]\n      const fetching = this.#isBackgroundFetch(value)\n      if (status) this.#statusTTL(status, index)\n      if (this.#isStale(index)) {\n        if (status) status.get = 'stale'\n        // delete only if not an in-flight background fetch\n        if (!fetching) {\n          if (!noDeleteOnStaleGet) {\n            this.#delete(k, 'expire')\n          }\n          if (status && allowStale) status.returnedStale = true\n          return allowStale ? value : undefined\n        } else {\n          if (\n            status &&\n            allowStale &&\n            value.__staleWhileFetching !== undefined\n          ) {\n            status.returnedStale = true\n          }\n          return allowStale ? value.__staleWhileFetching : undefined\n        }\n      } else {\n        if (status) status.get = 'hit'\n        // if we're currently fetching it, we don't actually have it yet\n        // it's not stale, which means this isn't a staleWhileRefetching.\n        // If it's not stale, and fetching, AND has a __staleWhileFetching\n        // value, then that means the user fetched with {forceRefresh:true},\n        // so it's safe to return that value.\n        if (fetching) {\n          return value.__staleWhileFetching\n        }\n        this.#moveToTail(index)\n        if (updateAgeOnGet) {\n          this.#updateItemAge(index)\n        }\n        return value\n      }\n    } else if (status) {\n      status.get = 'miss'\n    }\n  }\n\n  #connect(p: Index, n: Index) {\n    this.#prev[n] = p\n    this.#next[p] = n\n  }\n\n  #moveToTail(index: Index): void {\n    // if tail already, nothing to do\n    // if head, move head to next[index]\n    // else\n    //   move next[prev[index]] to next[index] (head has no prev)\n    //   move prev[next[index]] to prev[index]\n    // prev[index] = tail\n    // next[tail] = index\n    // tail = index\n    if (index !== this.#tail) {\n      if (index === this.#head) {\n        this.#head = this.#next[index] as Index\n      } else {\n        this.#connect(\n          this.#prev[index] as Index,\n          this.#next[index] as Index\n        )\n      }\n      this.#connect(this.#tail, index)\n      this.#tail = index\n    }\n  }\n\n  /**\n   * Deletes a key out of the cache.\n   *\n   * Returns true if the key was deleted, false otherwise.\n   */\n  delete(k: K) {\n    return this.#delete(k, 'delete')\n  }\n\n  #delete(k: K, reason: LRUCache.DisposeReason) {\n    let deleted = false\n    if (this.#size !== 0) {\n      const index = this.#keyMap.get(k)\n      if (index !== undefined) {\n        deleted = true\n        if (this.#size === 1) {\n          this.#clear(reason)\n        } else {\n          this.#removeItemSize(index)\n          const v = this.#valList[index]\n          if (this.#isBackgroundFetch(v)) {\n            v.__abortController.abort(new Error('deleted'))\n          } else if (this.#hasDispose || this.#hasDisposeAfter) {\n            if (this.#hasDispose) {\n              this.#dispose?.(v as V, k, reason)\n            }\n            if (this.#hasDisposeAfter) {\n              this.#disposed?.push([v as V, k, reason])\n            }\n          }\n          this.#keyMap.delete(k)\n          this.#keyList[index] = undefined\n          this.#valList[index] = undefined\n          if (index === this.#tail) {\n            this.#tail = this.#prev[index] as Index\n          } else if (index === this.#head) {\n            this.#head = this.#next[index] as Index\n          } else {\n            const pi = this.#prev[index] as number\n            this.#next[pi] = this.#next[index] as number\n            const ni = this.#next[index] as number\n            this.#prev[ni] = this.#prev[index] as number\n          }\n          this.#size--\n          this.#free.push(index)\n        }\n      }\n    }\n    if (this.#hasDisposeAfter && this.#disposed?.length) {\n      const dt = this.#disposed\n      let task: DisposeTask<K, V> | undefined\n      while ((task = dt?.shift())) {\n        this.#disposeAfter?.(...task)\n      }\n    }\n    return deleted\n  }\n\n  /**\n   * Clear the cache entirely, throwing away all values.\n   */\n  clear() {\n    return this.#clear('delete')\n  }\n  #clear(reason: LRUCache.DisposeReason) {\n    for (const index of this.#rindexes({ allowStale: true })) {\n      const v = this.#valList[index]\n      if (this.#isBackgroundFetch(v)) {\n        v.__abortController.abort(new Error('deleted'))\n      } else {\n        const k = this.#keyList[index]\n        if (this.#hasDispose) {\n          this.#dispose?.(v as V, k as K, reason)\n        }\n        if (this.#hasDisposeAfter) {\n          this.#disposed?.push([v as V, k as K, reason])\n        }\n      }\n    }\n\n    this.#keyMap.clear()\n    this.#valList.fill(undefined)\n    this.#keyList.fill(undefined)\n    if (this.#ttls && this.#starts) {\n      this.#ttls.fill(0)\n      this.#starts.fill(0)\n    }\n    if (this.#sizes) {\n      this.#sizes.fill(0)\n    }\n    this.#head = 0 as Index\n    this.#tail = 0 as Index\n    this.#free.length = 0\n    this.#calculatedSize = 0\n    this.#size = 0\n    if (this.#hasDisposeAfter && this.#disposed) {\n      const dt = this.#disposed\n      let task: DisposeTask<K, V> | undefined\n      while ((task = dt?.shift())) {\n        this.#disposeAfter?.(...task)\n      }\n    }\n  }\n}\n", "import { LRUCache } from 'lru-cache';\r\nimport { exec } from 'child_process';\r\nimport { promisify } from 'util';\r\nimport { ApiError } from '../utils/errors.js';\r\nimport fs from 'fs/promises';\r\nimport path from 'path';\r\nimport { execAsync } from '../utils/exec.js';\r\n\r\nconst execAsync = promisify(exec);\r\n\r\ninterface Master {\r\n  index: number;\r\n  phase: string;\r\n  active: boolean;\r\n  slaveCount: number;\r\n  mac: string;\r\n  interface: string;\r\n  link: string;\r\n  txFrames: number;\r\n  rxFrames: number;\r\n  txBytes: number;\r\n  rxBytes: number;\r\n  lostFrames: number;\r\n  txFrameRate: number[];\r\n  txRate: number[];\r\n  rxFrameRate: number[];\r\n  rxRate: number[];\r\n  lossRate: number[];\r\n  frameLoss: number[];\r\n}\r\n\r\ninterface PDOEntry {\r\n  index: string;\r\n  subIndex: string;\r\n  bits: number;\r\n  name: string;\r\n}\r\n\r\ninterface PDO {\r\n  index: string;\r\n  name: string;\r\n  entries: PDOEntry[];\r\n  type: 'RxPDO' | 'TxPDO';\r\n}\r\n\r\ninterface SyncManager {\r\n  index: number;\r\n  physAddr: string;\r\n  defaultSize: number;\r\n  controlRegister: string;\r\n  enable: boolean;\r\n  pdos: PDO[];\r\n}\r\n\r\ninterface Slave {\r\n  master: number;\r\n  position: string;\r\n  index: number;\r\n  name: string;\r\n  state: string;\r\n  vendorId: string;\r\n  productCode: string;\r\n  revision: string;\r\n  serial: string;\r\n  group: string;\r\n  orderNumber: string;\r\n  deviceName: string;\r\n  currentConsumption: number;\r\n  fmmuBitOperation: boolean;\r\n  distributedClocks: string;\r\n  dcSystemTimeDelay: number;\r\n  ports: {\r\n    index: number;\r\n    type: string;\r\n    link: string;\r\n    loop: string;\r\n    signal: string;\r\n    nextSlave: string;\r\n    rxTime: number;\r\n    diff: number;\r\n    nextDc: number;\r\n  }[];\r\n  syncManagers?: SyncManager[];\r\n}\r\n\r\ninterface SlaveGroup {\r\n  master: number;\r\n  slaves: Slave[];\r\n}\r\n\r\ninterface EtherCATConfig {\r\n  master0Device: string;\r\n  master1Device: string | null;\r\n}\r\n\r\ninterface SlaveConfig {\r\n  position: string;\r\n  name: string;\r\n  vendorId: number;\r\n  productCode: number;\r\n}\r\n\r\n// 配置缓存选项\r\nconst cacheOptions = {\r\n  max: 100,                    // 最大缓存数量\r\n  ttl: 1000,                   // 缓存有效期 1 秒\r\n  updateAgeOnGet: true,        // 访问时更新时间\r\n  allowStale: true,            // 允许返回过期数据（在更新时）\r\n};\r\n\r\n// 创建缓存实例\r\nconst statusCache = new LRUCache(cacheOptions);\r\nconst mastersCache = new LRUCache(cacheOptions);\r\nconst slavesCache = new LRUCache(cacheOptions);\r\n\r\nexport class EthercatService {\r\n  static async getStatus() {\r\n    try {\r\n      // 尝试从缓存获取\r\n      const cachedStatus = statusCache.get('status');\r\n      if (cachedStatus) {\r\n        return cachedStatus;\r\n      }\r\n\r\n      const { stdout } = await execAsync('ethercat master -v');\r\n      \r\n      const running = stdout.length > 0 && !stdout.includes('Failed to open master device');\r\n      \r\n      if (!running) {\r\n        const status = {\r\n          running: false,\r\n          masters: [],\r\n          slaves: [],\r\n          slaveCount: 0,\r\n          lastUpdateTime: new Date().toISOString()\r\n        };\r\n        statusCache.set('status', status);\r\n        return status;\r\n      }\r\n      \r\n      const masters = await this.getMasters();\r\n      const slaves = await this.getSlaves();\r\n      \r\n      const status = {\r\n        running,\r\n        masters,\r\n        slaves,\r\n        slaveCount: masters.reduce((sum, m) => sum + m.slaveCount, 0),\r\n        lastUpdateTime: new Date().toISOString()\r\n      };\r\n\r\n      // 保存到缓存\r\n      statusCache.set('status', status);\r\n      return status;\r\n    } catch (error: unknown) {\r\n      const err = error as Error;\r\n      if (err.message?.includes('No such file or directory')) {\r\n        return {\r\n          running: false,\r\n          masters: [],\r\n          slaves: [],\r\n          slaveCount: 0,\r\n          lastUpdateTime: new Date().toISOString()\r\n        };\r\n      }\r\n      console.error('Failed to get EtherCAT status:', error);\r\n      throw new ApiError(500, '获取 EtherCAT 状态失败');\r\n    }\r\n  }\r\n\r\n  static async getMasters(): Promise<Master[]> {\r\n    try {\r\n      // 尝试从缓存获取\r\n      const cachedMasters = mastersCache.get('masters');\r\n      if (cachedMasters) {\r\n        return cachedMasters;\r\n      }\r\n\r\n      const { stdout } = await execAsync('ethercat master -v');\r\n      const masters: Master[] = [];\r\n      let currentMaster: Partial<Master> = {};\r\n      let inCommonStats = false;\r\n      \r\n      // 获取 MAC 地址���网口的映射\r\n      const macToIface = await this.getMacToInterfaceMap();\r\n      \r\n      const lines = stdout.split('\\n');\r\n      for (const line of lines) {\r\n        if (line.startsWith('Master')) {\r\n          if (Object.keys(currentMaster).length > 0) {\r\n            masters.push(currentMaster as Master);\r\n          }\r\n          currentMaster = {\r\n            index: parseInt(line.match(/Master(\\d+)/)?.[1] || '0'),\r\n            phase: '',\r\n            active: false,\r\n            slaveCount: 0,\r\n            mac: '',\r\n            interface: '', // 添加网口字段\r\n            link: '',\r\n            txFrames: 0,\r\n            rxFrames: 0,\r\n            txBytes: 0,\r\n            rxBytes: 0,\r\n            lostFrames: 0,\r\n            txFrameRate: [0, 0, 0],\r\n            txRate: [0, 0, 0],\r\n            rxFrameRate: [0, 0, 0],\r\n            rxRate: [0, 0, 0],\r\n            lossRate: [0, 0, 0],\r\n            frameLoss: [0, 0, 0]\r\n          };\r\n        } else if (line.trim().startsWith('Phase:')) {\r\n          currentMaster.phase = line.split(':')[1].trim();\r\n        } else if (line.trim().startsWith('Active:')) {\r\n          currentMaster.active = line.includes('yes');\r\n        } else if (line.trim().startsWith('Slaves:')) {\r\n          currentMaster.slaveCount = parseInt(line.split(':')[1].trim());\r\n        } else if (line.includes('Main:')) {\r\n          // 提取 MAC 地址并查找对应的网口\r\n          const macMatch = line.match(/([0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2})/);\r\n          if (macMatch) {\r\n            const mac = macMatch[1].toLowerCase();\r\n            currentMaster.mac = mac;\r\n            currentMaster.interface = macToIface.get(mac) || ''; // 设置对应的网口\r\n          }\r\n        } else if (line.trim().startsWith('Link:')) {\r\n          currentMaster.link = line.split(':')[1].trim();\r\n        } else if (line.includes('Common:')) {\r\n          inCommonStats = true;\r\n        } else if (inCommonStats) {\r\n          if (line.includes('Tx frames:')) {\r\n            currentMaster.txFrames = parseInt(line.split(':')[1].trim());\r\n          } else if (line.includes('Rx frames:')) {\r\n            currentMaster.rxFrames = parseInt(line.split(':')[1].trim());\r\n          } else if (line.includes('Tx bytes:')) {\r\n            currentMaster.txBytes = parseInt(line.split(':')[1].trim());\r\n          } else if (line.includes('Rx bytes:')) {\r\n            currentMaster.rxBytes = parseInt(line.split(':')[1].trim());\r\n          } else if (line.includes('Lost frames:')) {\r\n            currentMaster.lostFrames = parseInt(line.split(':')[1].trim());\r\n          } else if (line.includes('Tx frame rate [1/s]:')) {\r\n            currentMaster.txFrameRate = line.split(':')[1].trim().split(/\\s+/).map(Number);\r\n          } else if (line.includes('Tx rate [KByte/s]:')) {\r\n            currentMaster.txRate = line.split(':')[1].trim().split(/\\s+/).map(Number);\r\n          } else if (line.includes('Rx frame rate [1/s]:')) {\r\n            currentMaster.rxFrameRate = line.split(':')[1].trim().split(/\\s+/).map(Number);\r\n          } else if (line.includes('Rx rate [KByte/s]:')) {\r\n            currentMaster.rxRate = line.split(':')[1].trim().split(/\\s+/).map(Number);\r\n          } else if (line.includes('Loss rate [1/s]:')) {\r\n            currentMaster.lossRate = line.split(':')[1].trim().split(/\\s+/).map(Number);\r\n          } else if (line.includes('Frame loss [%]:')) {\r\n            currentMaster.frameLoss = line.split(':')[1].trim().split(/\\s+/).map(Number);\r\n          }\r\n        }\r\n      }\r\n      \r\n      if (Object.keys(currentMaster).length > 0) {\r\n        masters.push(currentMaster as Master);\r\n      }\r\n      \r\n      // 保存到缓存\r\n      mastersCache.set('masters', masters);\r\n      return masters;\r\n    } catch (error) {\r\n      console.error('Failed to get masters:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  static async getSlaves(): Promise<Slave[]> {\r\n    try {\r\n      // 尝试从缓存获取\r\n      const cachedSlaves = slavesCache.get('slaves');\r\n      if (cachedSlaves) {\r\n        return cachedSlaves;\r\n      }\r\n\r\n      const { stdout } = await execAsync('ethercat slaves -v');\r\n      const slaves: Slave[] = [];\r\n      let currentSlave: Partial<Slave> = {};\r\n      let currentMaster = 0;\r\n      let inPorts = false;\r\n      let currentPorts: any[] = [];\r\n      \r\n      const lines = stdout.split('\\n');\r\n      for (const line of lines) {\r\n        if (line.startsWith('=== Master')) {\r\n          if (Object.keys(currentSlave).length > 0) {\r\n            currentSlave.ports = currentPorts;\r\n            slaves.push(currentSlave as Slave);\r\n          }\r\n          currentSlave = {};\r\n          currentPorts = [];\r\n          inPorts = false;\r\n          const matches = line.match(/Master (\\d+), Slave (\\d+)/);\r\n          if (matches) {\r\n            currentMaster = parseInt(matches[1], 10);\r\n            currentSlave.master = currentMaster;\r\n            currentSlave.index = parseInt(matches[2], 10);\r\n          }\r\n        } else if (line.startsWith('Device:')) {\r\n          currentSlave.name = line.split(':')[1].trim();\r\n        } else if (line.startsWith('State:')) {\r\n          currentSlave.state = line.split(':')[1].trim();\r\n        } else if (line.includes('Vendor Id:')) {\r\n          currentSlave.vendorId = line.split(':')[1].trim();\r\n        } else if (line.includes('Product code:')) {\r\n          currentSlave.productCode = line.split(':')[1].trim();\r\n        } else if (line.includes('Revision number:')) {\r\n          currentSlave.revision = line.split(':')[1].trim();\r\n        } else if (line.includes('Serial number:')) {\r\n          currentSlave.serial = line.split(':')[1].trim();\r\n        } else if (line.includes('Group:')) {\r\n          currentSlave.group = line.split(':')[1].trim();\r\n        } else if (line.includes('Order number:')) {\r\n          currentSlave.orderNumber = line.split(':')[1].trim();\r\n        } else if (line.includes('Device name:')) {\r\n          currentSlave.deviceName = line.split(':')[1].trim();\r\n        } else if (line.includes('Current consumption:')) {\r\n          currentSlave.currentConsumption = parseInt(line.split(':')[1].trim());\r\n        } else if (line.includes('FMMU bit operation:')) {\r\n          currentSlave.fmmuBitOperation = line.includes('yes');\r\n        } else if (line.includes('Distributed clocks:')) {\r\n          currentSlave.distributedClocks = line.split(':')[1].trim();\r\n        } else if (line.includes('DC system time transmission delay:')) {\r\n          currentSlave.dcSystemTimeDelay = parseInt(line.split(':')[1].trim());\r\n        } else if (line.trim().startsWith('Port')) {\r\n          inPorts = true;\r\n        } else if (inPorts && line.trim().match(/^\\d/)) {\r\n          const [index, type, link, loop, signal, nextSlave, rxTime, diff, nextDc] = \r\n            line.trim().split(/\\s+/);\r\n          currentPorts.push({\r\n            index: parseInt(index),\r\n            type,\r\n            link,\r\n            loop,\r\n            signal,\r\n            nextSlave,\r\n            rxTime: rxTime === '-' ? 0 : parseInt(rxTime),\r\n            diff: diff === '-' ? 0 : parseInt(diff),\r\n            nextDc: nextDc === '-' ? 0 : parseInt(nextDc)\r\n          });\r\n        }\r\n      }\r\n      \r\n      if (Object.keys(currentSlave).length > 0) {\r\n        currentSlave.ports = currentPorts;\r\n        slaves.push(currentSlave as Slave);\r\n      }\r\n      \r\n      // 获取每个从站的 PDO 信息\r\n      for (const slave of slaves) {\r\n        try {\r\n          const { stdout: pdoOut } = await execAsync(`ethercat pdos -m ${slave.master} -p ${slave.index}`);\r\n          const syncManagers: SyncManager[] = [];\r\n          let currentSM: Partial<SyncManager> = {};\r\n          let currentPDO: Partial<PDO> = {};\r\n          \r\n          const lines = pdoOut.split('\\n');\r\n          for (const line of lines) {\r\n            if (line.startsWith('SM')) {\r\n              // 保存前一个 SM 的最一个 PDO\r\n              if (Object.keys(currentPDO).length > 0) {\r\n                currentSM.pdos?.push(currentPDO as PDO);\r\n                currentPDO = {};\r\n              }\r\n              // 保存前一个 SM\r\n              if (Object.keys(currentSM).length > 0) {\r\n                syncManagers.push(currentSM as SyncManager);\r\n              }\r\n              const matches = line.match(/SM(\\d+): PhysAddr (0x\\w+), DefaultSize\\s+(\\d+), ControlRegister (0x\\w+), Enable (\\d)/);\r\n              if (matches) {\r\n                currentSM = {\r\n                  index: parseInt(matches[1]),\r\n                  physAddr: matches[2],\r\n                  defaultSize: parseInt(matches[3]),\r\n                  controlRegister: matches[4],\r\n                  enable: matches[5] === '1',\r\n                  pdos: []\r\n                };\r\n              }\r\n            } else if (line.trim().startsWith('RxPDO') || line.trim().startsWith('TxPDO')) {\r\n              // 保存前一个 PDO\r\n              if (Object.keys(currentPDO).length > 0) {\r\n                currentSM.pdos?.push(currentPDO as PDO);\r\n              }\r\n              const matches = line.match(/(Rx|Tx)PDO (0x\\w+) \"(.*)\"/);\r\n              if (matches) {\r\n                currentPDO = {\r\n                  type: `${matches[1]}PDO` as 'RxPDO' | 'TxPDO',\r\n                  index: matches[2],\r\n                  name: matches[3],\r\n                  entries: []\r\n                };\r\n              }\r\n            } else if (line.trim().startsWith('PDO entry')) {\r\n              const matches = line.match(/PDO entry (0x\\w+):(\\w+),\\s+(\\d+) bit, \"(.*)\"/);\r\n              if (matches && currentPDO.entries) {\r\n                const entry: PDOEntry = {\r\n                  index: matches[1],\r\n                  subIndex: matches[2],\r\n                  bits: parseInt(matches[3]),\r\n                  name: matches[4]\r\n                };\r\n                currentPDO.entries.push(entry);\r\n              }\r\n            }\r\n          }\r\n          \r\n          // 保存最后一个 PDO 和 SM\r\n          if (Object.keys(currentPDO).length > 0) {\r\n            currentSM.pdos?.push(currentPDO as PDO);\r\n          }\r\n          if (Object.keys(currentSM).length > 0) {\r\n            syncManagers.push(currentSM as SyncManager);\r\n          }\r\n          \r\n          slave.syncManagers = syncManagers;\r\n        } catch (error) {\r\n          console.error(`Failed to get PDOs for slave ${slave.index}:`, error);\r\n        }\r\n      }\r\n\r\n      // 存到缓存\r\n      slavesCache.set('slaves', slaves);\r\n      return slaves;\r\n    } catch (error) {\r\n      console.error('Failed to get slaves:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  static async startService(): Promise<void> {\r\n    try {\r\n      await execAsync('ethercatctl start');\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n    } catch (error) {\r\n      console.error('Failed to start service:', error);\r\n      throw new ApiError(500, '启动服务失败');\r\n    }\r\n  }\r\n\r\n  static async stopService(): Promise<void> {\r\n    try {\r\n      await execAsync('ethercatctl stop');\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n    } catch (error) {\r\n      console.error('Failed to stop service:', error);\r\n      throw new ApiError(500, '停止服务失败');\r\n    }\r\n  }\r\n\r\n  static async getSlaveXml(master: number, slave: number): Promise<string> {\r\n    try {\r\n      const { stdout } = await execAsync(`ethercat xml -m ${master} -p ${slave}`);\r\n      return stdout;\r\n    } catch (error) {\r\n      console.error('Failed to get slave XML:', error);\r\n      throw new ApiError(500, '获取从站 XML 配置失败');\r\n    }\r\n  }\r\n\r\n  static async generateTopology(master: number): Promise<string> {\r\n    try {\r\n      const { stdout } = await execAsync(`ethercat graph -m ${master}`);\r\n      return stdout;\r\n    } catch (error) {\r\n      console.error('Failed to generate topology:', error);\r\n      throw new ApiError(500, '生成拓扑图失败');\r\n    }\r\n  }\r\n\r\n  static async getAvailableMasters(): Promise<number[]> {\r\n    try {\r\n      const { stdout } = await execAsync('ethercat master');\r\n      const masters: number[] = [];\r\n      \r\n      // 解析输，提取主站索引\r\n      const lines = stdout.split('\\n');\r\n      for (const line of lines) {\r\n        const match = line.match(/^Master(\\d+)/);\r\n        if (match) {\r\n          masters.push(parseInt(match[1]));\r\n        }\r\n      }\r\n      \r\n      return masters;\r\n    } catch (error) {\r\n      console.error('Failed to get available masters:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  // 添加清除缓存方法\r\n  static clearCache() {\r\n    statusCache.clear();\r\n    mastersCache.clear();\r\n    slavesCache.clear();\r\n  }\r\n\r\n  static async getNetworkInterfaces(): Promise<string[]> {\r\n    // 直接返回固定的网口列表\r\n    return ['eth0', 'eth1'];\r\n  }\r\n\r\n  // 添加 MAC 地址反查网口的方法\r\n  static async getMacToInterfaceMap(): Promise<Map<string, string>> {\r\n    try {\r\n      const { stdout } = await execAsync(\"ip link | grep -E '^[0-9]+: (eth|end)' -A 1\");\r\n      const lines = stdout.split('\\n');\r\n      const macToIface = new Map<string, string>();\r\n      \r\n      let currentIface = '';\r\n      for (const line of lines) {\r\n        if (line.includes(': eth') || line.includes(': end')) {\r\n          currentIface = line.split(': ')[1].split('@')[0];\r\n        } else if (line.includes('link/ether') && currentIface) {\r\n          const mac = line.split('link/ether ')[1].split(' ')[0].toLowerCase();\r\n          macToIface.set(mac, currentIface);\r\n        }\r\n      }\r\n      \r\n      return macToIface;\r\n    } catch (error) {\r\n      console.error('Failed to get MAC to interface map:', error);\r\n      return new Map();\r\n    }\r\n  }\r\n\r\n  static async getConfig(): Promise<EtherCATConfig> {\r\n    try {\r\n      const content = await fs.readFile('/etc/ethercat.conf', 'utf-8');\r\n      const lines = content.split('\\n');\r\n      \r\n      let master0Device = '';\r\n      let master1Device: string | null = null;\r\n\r\n      for (const line of lines) {\r\n        // 移除行首尾空白\r\n        const trimmedLine = line.trim();\r\n        // 跳过注释和空行\r\n        if (trimmedLine.startsWith('#') || !trimmedLine) continue;\r\n\r\n        // 匹配 MASTER0_DEVICE=\"xxx\" 或 MASTER0_DEVICE=xxx 格式\r\n        const master0Match = trimmedLine.match(/^MASTER0_DEVICE=[\"']?([^\"']+)[\"']?/);\r\n        if (master0Match) {\r\n          master0Device = master0Match[1];\r\n        }\r\n\r\n        // 匹配 MASTER1_DEVICE=\"xxx\" 或 MASTER1_DEVICE=xxx 格式\r\n        const master1Match = trimmedLine.match(/^MASTER1_DEVICE=[\"']?([^\"']+)[\"']?/);\r\n        if (master1Match) {\r\n          master1Device = master1Match[1];\r\n        }\r\n      }\r\n\r\n      // 如果配置文件中没有找到必需的 MASTER0_DEVICE\r\n      if (!master0Device) {\r\n        throw new ApiError(500, '配置文件中未找到 MASTER0_DEVICE');\r\n      }\r\n\r\n      return {\r\n        master0Device,\r\n        master1Device\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to read config:', error);\r\n      if (error instanceof ApiError) {\r\n        throw error;\r\n      }\r\n      throw new ApiError(500, '读取配置失败');\r\n    }\r\n  }\r\n\r\n  static async updateConfig(config: EtherCATConfig): Promise<void> {\r\n    try {\r\n      if (!config.master0Device) {\r\n        throw new ApiError(400, 'MASTER0_DEVICE 是必需的');\r\n      }\r\n\r\n      // 读取原配置文件内容\r\n      const originalContent = await fs.readFile('/etc/ethercat.conf', 'utf-8');\r\n      const lines = originalContent.split('\\n');\r\n      \r\n      // 保存所有非 MASTER 设备的配置行\r\n      const otherConfigs = lines.filter(line => {\r\n        const trimmedLine = line.trim();\r\n        return trimmedLine && \r\n               !trimmedLine.startsWith('#') && \r\n               !trimmedLine.startsWith('MASTER0_DEVICE=') && \r\n               !trimmedLine.startsWith('MASTER1_DEVICE=');\r\n      });\r\n\r\n      // 构建新的配置内容\r\n      const newLines = [\r\n        // 添加 MASTER 设备配置\r\n        `MASTER0_DEVICE=\"${config.master0Device}\"`,\r\n        config.master1Device ? `MASTER1_DEVICE=\"${config.master1Device}\"` : '',\r\n        \r\n        // 添加其他配置\r\n        ...otherConfigs\r\n      ].filter(line => line); // 移除空行\r\n\r\n      // 写入配置文件\r\n      await fs.writeFile('/etc/ethercat.conf', newLines.join('\\n') + '\\n', 'utf-8');\r\n    } catch (error) {\r\n      console.error('Failed to update config:', error);\r\n      throw new ApiError(500, '更新置失败');\r\n    }\r\n  }\r\n\r\n  static async restartService(): Promise<void> {\r\n    try {\r\n      // 执行重启命令\r\n      await execAsync('ethercatctl restart');\r\n      \r\n      // 等待服务重启\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n      \r\n      // 使用 ethercat master 检查服务状态\r\n      const { stdout } = await execAsync('ethercat master -v');\r\n      const running = stdout.length > 0 && !stdout.includes('Failed to open master device');\r\n      \r\n      if (!running) {\r\n        throw new Error('Service failed to start');\r\n      }\r\n\r\n      // 清除缓存，确保获取最新状态\r\n      this.clearCache();\r\n    } catch (error) {\r\n      console.error('Failed to restart service:', error);\r\n      throw new ApiError(500, '重启服务失败，请检查网口配置');\r\n    }\r\n  }\r\n\r\n  static async getServiceStatus(): Promise<boolean> {\r\n    try {\r\n      const { stdout } = await execAsync('ethercatctl status');\r\n      return stdout.includes('running');\r\n    } catch (error) {\r\n      console.error('Failed to get service status:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  static async getSlaveConfig(): Promise<any> {\r\n    try {\r\n      const slaves = await this.getSlaves();\r\n      const config = {\r\n        slaves: await Promise.all(slaves.map(async (slave: Slave) => {\r\n          try {\r\n            const { stdout: xmlContent } = await execAsync(`ethercat xml -m ${slave.master} -p ${slave.index}`);\r\n            \r\n            // 提取设备名称\r\n            const deviceNameMatch = xmlContent.match(/<Name>\\s*<!\\[CDATA\\[(.*?)\\]\\]>\\s*<\\/Name>/);\r\n            const deviceName = deviceNameMatch ? deviceNameMatch[1].trim() : slave.name;\r\n\r\n            // 提取 RxPdo 的 Index\r\n            const rxPdoMatch = xmlContent.match(/<RxPdo[^>]*>[\\s\\S]*?<Index>#x([0-9a-fA-F]+)<\\/Index>/);\r\n            const rxPdoIndex = rxPdoMatch ? rxPdoMatch[1] : '1600';\r\n            \r\n            // 提取 TxPdo 的 Index\r\n            const txPdoMatch = xmlContent.match(/<TxPdo[^>]*>[\\s\\S]*?<Index>#x([0-9a-fA-F]+)<\\/Index>/);\r\n            const txPdoIndex = txPdoMatch ? txPdoMatch[1] : '1a00';\r\n\r\n            // 提取所有 RxPdo entries\r\n            const rxPdos = [];\r\n            const rxPdoSection = xmlContent.match(/<RxPdo[\\s\\S]*?<\\/RxPdo>/);\r\n            if (rxPdoSection) {\r\n              const entries = rxPdoSection[0].matchAll(/<Entry>[\\s\\S]*?<Index>#x([0-9a-fA-F]+)<\\/Index>[\\s\\S]*?<SubIndex>(\\d+)<\\/SubIndex>[\\s\\S]*?<BitLen>(\\d+)<\\/BitLen>[\\s\\S]*?<Name>([^<]*)<\\/Name>[\\s\\S]*?<DataType>([^<]*)<\\/DataType>[\\s\\S]*?<\\/Entry>/g);\r\n              for (const entry of entries) {\r\n                rxPdos.push({\r\n                  name: entry[4].trim() || `SubIndex ${entry[2]}`,\r\n                  index: `0x${entry[1]}`,\r\n                  subindex: entry[2],\r\n                  type: EthercatService.getTypeFromDataType(entry[5], parseInt(entry[3])),\r\n                  bitlen: parseInt(entry[3])\r\n                });\r\n              }\r\n            }\r\n\r\n            // 提取所有 TxPdo entries\r\n            const txPdos = [];\r\n            const txPdoSection = xmlContent.match(/<TxPdo[\\s\\S]*?<\\/TxPdo>/);\r\n            if (txPdoSection) {\r\n              const entries = txPdoSection[0].matchAll(/<Entry>[\\s\\S]*?<Index>#x([0-9a-fA-F]+)<\\/Index>[\\s\\S]*?<SubIndex>(\\d+)<\\/SubIndex>[\\s\\S]*?<BitLen>(\\d+)<\\/BitLen>[\\s\\S]*?<Name>([^<]*)<\\/Name>[\\s\\S]*?<DataType>([^<]*)<\\/DataType>[\\s\\S]*?<\\/Entry>/g);\r\n              for (const entry of entries) {\r\n                txPdos.push({\r\n                  name: entry[4].trim() || `SubIndex ${entry[2]}`,\r\n                  index: `0x${entry[1]}`,\r\n                  subindex: entry[2],\r\n                  type: EthercatService.getTypeFromDataType(entry[5], parseInt(entry[3])),\r\n                  bitlen: parseInt(entry[3])\r\n                });\r\n              }\r\n            }\r\n\r\n            return {\r\n              index: slave.index.toString(),\r\n              name: deviceName,\r\n              vid: slave.vendorId,\r\n              pid: slave.productCode,\r\n              rx_pdo: `0x${rxPdoIndex}`,\r\n              tx_pdo: `0x${txPdoIndex}`,\r\n              rx_pdos: rxPdos,\r\n              tx_pdos: txPdos\r\n            };\r\n          } catch (error) {\r\n            console.error(`Failed to get config for slave ${slave.master}:${slave.index}:`, error);\r\n            return null;\r\n          }\r\n        }))\r\n      };\r\n\r\n      // 过滤掉获取失败的从站配置\r\n      config.slaves = config.slaves.filter(slave => slave !== null);\r\n\r\n      return config;\r\n    } catch (error) {\r\n      console.error('Failed to get slave config:', error);\r\n      throw new ApiError(500, '获取从站配置失败');\r\n    }\r\n  }\r\n\r\n  private static getTypeFromDataType(dataType: string, bitLen: number): string {\r\n    switch (dataType.toUpperCase()) {\r\n      case 'BOOL':\r\n        return 'bool';\r\n      case 'INT8':\r\n      case 'INT':\r\n      case 'SINT':\r\n        return 'int8';\r\n      case 'BYTE':\r\n      case 'USINT':\r\n        return 'uint8';\r\n      case 'UINT16':\r\n      case 'WORD':\r\n        return 'uint16';\r\n      case 'INT16':\r\n        return 'int16';\r\n      case 'UINT32':\r\n      case 'DWORD':\r\n        return 'uint32';\r\n      case 'INT32':\r\n        return 'int32';\r\n      case 'UINT64':\r\n        return 'uint64';\r\n      case 'INT64':\r\n        return 'int64';\r\n      case 'LREAL':\r\n        return 'double';\r\n      default:\r\n        return bitLen <= 8 ? 'uint8' : \r\n               bitLen <= 16 ? 'uint16' : \r\n               bitLen <= 32 ? 'int32' :\r\n               bitLen <= 64 ? 'int64' : 'double';\r\n    }\r\n  }\r\n\r\n  static async getTemplateConfig(): Promise<any> {\r\n    try {\r\n      const templatePath = path.join(process.cwd(), 'template', 'slave_template.json');\r\n      const templateContent = await fs.readFile(templatePath, 'utf-8');\r\n      return JSON.parse(templateContent);\r\n    } catch (error) {\r\n      console.error('Failed to read template file:', error);\r\n      throw new ApiError(500, '获取模板配置失败');\r\n    }\r\n  }\r\n\r\n  static async getBusTopology(): Promise<{ dot: string; image: Buffer }> {\r\n    try {\r\n      // 获取 dot 格式的拓扑图\r\n      const { stdout: dotContent } = await execAsync('ethercat graph -m 0');\r\n      \r\n      // 使用 graphviz 将 dot 转换为 PNG\r\n      const image = await new Promise<Buffer>((resolve, reject) => {\r\n        const dot = spawn('dot', ['-Tpng']);\r\n        const chunks: Buffer[] = [];\r\n\r\n        dot.stdout.on('data', (chunk) => chunks.push(chunk));\r\n        dot.stderr.on('data', (data) => console.error(`dot error: ${data}`));\r\n        dot.on('close', (code) => {\r\n          if (code === 0) {\r\n            resolve(Buffer.concat(chunks));\r\n          } else {\r\n            reject(new Error(`dot process exited with code ${code}`));\r\n          }\r\n        });\r\n\r\n        dot.stdin.write(dotContent);\r\n        dot.stdin.end();\r\n      });\r\n\r\n      return { dot: dotContent, image };\r\n    } catch (error) {\r\n      console.error('Failed to get bus topology:', error);\r\n      throw new ApiError(500, '获取总线拓扑图失败');\r\n    }\r\n  }\r\n} ", "import { Router } from 'express';\r\nimport { SettingsService } from '../services/settings.service.js';\r\nimport { asyncHandler } from '../utils/async-handler.js';\r\nimport type { Request, Response } from 'express';\r\n\r\nconst router = Router();\r\n\r\nrouter.get('/menu', asyncHandler(async (req: Request, res: Response) => {\r\n  const settings = await SettingsService.getMenuSettings();\r\n  res.json({\r\n    status: 200,\r\n    data: settings\r\n  });\r\n}));\r\n\r\nrouter.post('/menu', asyncHandler(async (req: Request, res: Response) => {\r\n  await SettingsService.updateMenuSettings(req.body);\r\n  res.json({\r\n    status: 200,\r\n    message: '设置已更新'\r\n  });\r\n}));\r\n\r\nrouter.get('/network', asyncHandler(async (req: Request, res: Response) => {\r\n  const config = await SettingsService.getNetworkConfig();\r\n  res.json({\r\n    status: 200,\r\n    data: config\r\n  });\r\n}));\r\n\r\nrouter.post('/network', asyncHandler(async (req: Request, res: Response) => {\r\n  await SettingsService.updateNetworkConfig(req.body);\r\n  res.json({\r\n    status: 200,\r\n    message: '网络设置已更新'\r\n  });\r\n}));\r\n\r\nexport default router; ", "import { PocketBaseManager } from './program.manager.js';\r\nimport { ApiError } from '../utils/errors.js';\r\nimport { ClientResponseError } from 'pocketbase';\r\nimport fs from 'fs/promises';\r\nimport { exec } from 'child_process';\r\nimport { promisify } from 'util';\r\nconst execAsync = promisify(exec);\r\n\r\ninterface MenuSettings {\r\n  dashboard: boolean;\r\n  ethercat: boolean;\r\n  programs: boolean;\r\n  users: boolean;\r\n  settings: boolean;\r\n}\r\n\r\nexport class SettingsService {\r\n  static async getMenuSettings(): Promise<MenuSettings> {\r\n    try {\r\n      const pb = await PocketBaseManager.ensureAuth();\r\n      \r\n      try {\r\n        const record = await pb.collection('settings').getFirstListItem('type=\"menu\"');\r\n        return record.settings as MenuSettings;\r\n      } catch (error) {\r\n        if (error instanceof ClientResponseError && error.status === 404) {\r\n          const defaultSettings: MenuSettings = {\r\n            dashboard: true,\r\n            ethercat: true,\r\n            programs: true,\r\n            users: true,\r\n            settings: true\r\n          };\r\n          \r\n          await this.updateMenuSettings(defaultSettings);\r\n          return defaultSettings;\r\n        }\r\n        throw error;\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to get menu settings:', error);\r\n      throw new ApiError(500, '获取设置失败');\r\n    }\r\n  }\r\n\r\n  static async updateMenuSettings(settings: MenuSettings): Promise<void> {\r\n    try {\r\n      const pb = await PocketBaseManager.ensureAuth();\r\n      \r\n      try {\r\n        const record = await pb.collection('settings').getFirstListItem('type=\"menu\"');\r\n        await pb.collection('settings').update(record.id, {\r\n          settings: settings\r\n        });\r\n      } catch (error) {\r\n        if (error instanceof ClientResponseError && error.status === 404) {\r\n          // 如果设置不存在，创建新设置\r\n          await pb.collection('settings').create({\r\n            type: 'menu',\r\n            settings: settings\r\n          });\r\n        } else {\r\n          throw error;\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to update menu settings:', error);\r\n      throw new ApiError(500, '更新设置失败');\r\n    }\r\n  }\r\n\r\n  static async getNetworkConfig(): Promise<any> {\r\n    try {\r\n      const content = await fs.readFile('/etc/network/interfaces', 'utf-8');\r\n      return {\r\n        content\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to read network config:', error);\r\n      throw new ApiError(500, '获取网络配置失败');\r\n    }\r\n  }\r\n\r\n  static async updateNetworkConfig(config: { content: string }): Promise<void> {\r\n    try {\r\n      // 先写入配置文件\r\n      await fs.writeFile('/etc/network/interfaces', config.content, 'utf-8');\r\n      \r\n      // 在后台执行网络重启命令\r\n      execAsync('service networking reload').catch(error => {\r\n        console.error('Failed to reload networking service:', error);\r\n      });\r\n      \r\n      // 直接返回成功\r\n      return;\r\n    } catch (error) {\r\n      console.error('Failed to update network config:', error);\r\n      throw new ApiError(500, '更新网络配置失败');\r\n    }\r\n  }\r\n\r\n  private static parseInterfacesFile(content: string): any {\r\n    const lines = content.split('\\n');\r\n    const interfaces: any = {};\r\n    let currentIface = '';\r\n\r\n    for (const line of lines) {\r\n      const trimmedLine = line.trim();\r\n      if (trimmedLine.startsWith('#') || !trimmedLine) continue;\r\n\r\n      if (trimmedLine.startsWith('iface')) {\r\n        const parts = trimmedLine.split(' ');\r\n        currentIface = parts[1];\r\n        interfaces[currentIface] = {\r\n          interface: currentIface,\r\n          type: parts[3] === 'dhcp' ? 'dhcp' : 'static',\r\n          address: '',\r\n          netmask: '',\r\n          gateway: '',\r\n          dns: ''\r\n        };\r\n      } else if (currentIface && interfaces[currentIface]) {\r\n        if (trimmedLine.startsWith('address')) {\r\n          interfaces[currentIface].address = trimmedLine.split(' ')[1];\r\n        } else if (trimmedLine.startsWith('netmask')) {\r\n          interfaces[currentIface].netmask = trimmedLine.split(' ')[1];\r\n        } else if (trimmedLine.startsWith('gateway')) {\r\n          interfaces[currentIface].gateway = trimmedLine.split(' ')[1];\r\n        } else if (trimmedLine.startsWith('dns-nameserver')) {\r\n          interfaces[currentIface].dns = trimmedLine.split(' ')[1];\r\n        }\r\n      }\r\n    }\r\n\r\n    // 返回 eth0 的配置\r\n    return interfaces['eth0'] || {\r\n      interface: 'eth0',\r\n      type: 'dhcp',\r\n      address: '',\r\n      netmask: '',\r\n      gateway: '',\r\n      dns: ''\r\n    };\r\n  }\r\n\r\n  private static generateInterfacesFile(config: any): string {\r\n    const lines = [\r\n      'auto lo',\r\n      'iface lo inet loopback',\r\n      '',\r\n      'auto eth0',\r\n      'allow-hotplug eth0',\r\n      `iface eth0 inet ${config.type}`\r\n    ];\r\n\r\n    if (config.type === 'static') {\r\n      lines.push(`address ${config.address}`);\r\n      lines.push(`netmask ${config.netmask}`);\r\n      if (config.gateway) {\r\n        lines.push(`gateway ${config.gateway}`);\r\n      }\r\n      if (config.dns) {\r\n        lines.push(`dns-nameserver ${config.dns}`);\r\n      }\r\n    }\r\n\r\n    // 保持 eth1 的配置不变\r\n    lines.push('');\r\n    lines.push('auto eth1');\r\n    lines.push('allow-hotplug eth1');\r\n    lines.push('iface eth1 inet static');\r\n    lines.push('address ***************');\r\n    lines.push('netmask *************');\r\n\r\n    return lines.join('\\n') + '\\n';\r\n  }\r\n} ", "import { Router } from 'express';\r\nimport { GeneratorService } from '../services/generator.service.js';\r\nimport { asyncHandler } from '../utils/async-handler.js';\r\nimport type { Request, Response } from 'express';\r\n\r\nconst router = Router();\r\n\r\nrouter.post('/generate', asyncHandler(async (req: Request, res: Response) => {\r\n  const { config, language } = req.body;\r\n  const code = await GeneratorService.generateCode(config, language);\r\n  res.json({\r\n    status: 200,\r\n    data: code\r\n  });\r\n}));\r\n\r\nexport default router; ", "import fs from 'fs/promises';\r\nimport path from 'path';\r\nimport { ApiError } from '../utils/errors.js';\r\n\r\nexport class GeneratorService {\r\n  static async generateCode(config: any, language: string): Promise<string> {\r\n    try {\r\n      switch (language) {\r\n        case 'cs':\r\n          return await this.generateCSharpCode(config);\r\n        // ... 其他语言的情况\r\n        default:\r\n          throw new ApiError(400, '不支持的语言类型');\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to generate code:', error);\r\n      throw new ApiError(500, '代码生成失败');\r\n    }\r\n  }\r\n\r\n  private static async generateCSharpCode(config: any): Promise<string> {\r\n    try {\r\n      // 读取 C# 模板文件\r\n      const templatePath = path.join(process.cwd(), 'template', 'ethercat-control.cs');\r\n      let template = await fs.readFile(templatePath, 'utf-8');\r\n\r\n      // 生成共享内存结构体\r\n      const structContent = this.generateCSharpStruct(config);\r\n      \r\n      // 替换模板中的结构体部分\r\n      template = template.replace(\r\n        /private struct EtherCATSharedMemory\\s*{[^}]*}/s,\r\n        `private struct EtherCATSharedMemory\\n    {\\n${structContent}\\n    }`\r\n      );\r\n\r\n      return template;\r\n    } catch (error) {\r\n      console.error('Failed to generate C# code:', error);\r\n      throw new ApiError(500, '生成C#代码失败');\r\n    }\r\n  }\r\n\r\n  private static generateCSharpStruct(config: any): string {\r\n    const lines: string[] = [];\r\n    \r\n    if (config.slaves && config.slaves.length > 0) {\r\n      // 遍历所有从站\r\n      config.slaves.forEach((slave: any, index: number) => {\r\n        // 处理 RxPDOs\r\n        if (slave.rx_pdos) {\r\n          slave.rx_pdos.forEach((pdo: any) => {\r\n            const varName = this.sanitizeVariableName(pdo, index, 'rx');\r\n            const comment = this.generateComment(pdo);\r\n            lines.push(`        public int ${varName};${comment}`);\r\n          });\r\n        }\r\n\r\n        // 处理 TxPDOs\r\n        if (slave.tx_pdos) {\r\n          slave.tx_pdos.forEach((pdo: any) => {\r\n            const varName = this.sanitizeVariableName(pdo, index, 'tx');\r\n            const comment = this.generateComment(pdo);\r\n            lines.push(`        public int ${varName};${comment}`);\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    return lines.join('\\n');\r\n  }\r\n\r\n  private static sanitizeVariableName(pdo: any, slaveIndex: number, direction: 'rx' | 'tx'): string {\r\n    // 构造变量名: shm_slave{index}_rx/tx_index_name\r\n    const baseName = `shm_slave${slaveIndex}_${direction}_${pdo.index.toLowerCase()}_${pdo.name.toLowerCase()}`;\r\n    return baseName.replace(/[^a-z0-9_]/g, '_');\r\n  }\r\n\r\n  private static generateComment(pdo: any): string {\r\n    // 只使用 comment 作为注释\r\n    return pdo.comment ? ` // ${pdo.comment}` : '';\r\n  }\r\n} "], "mappings": "mxBAAA,IAAAA,GAAAC,GAAA,CAAAC,GAAAC,KAAA,CAAAA,GAAA,SACE,KAAQ,SACR,QAAW,SACX,YAAe,6CACf,KAAQ,cACR,MAAS,gBACT,QAAW,CACT,IAAK,CACH,MAAS,kBACT,QAAW,gBACX,QAAW,eACb,EACA,WAAY,cACZ,cAAe,cACf,oBAAqB,uBACrB,uBAAwB,uBACxB,oBAAqB,uBACrB,uBAAwB,uBACxB,iBAAkB,gBACpB,EACA,QAAW,CACT,YAAa,0CACb,KAAQ,WACR,cAAe,oBACf,QAAW,oCACX,KAAQ,8BACR,gBAAiB,6BACjB,WAAc,WACd,QAAW,kBACb,EACA,WAAc,CACZ,KAAQ,MACR,IAAO,sCACT,EACA,QAAW,sBACX,SAAY,CACV,SACA,MACA,OACA,cACA,YACA,SACA,UACF,EACA,eAAkB,YAClB,QAAW,eACX,gBAAmB,CACjB,2BAA4B,WAC5B,cAAe,WACf,QAAW,SACX,MAAS,UACT,SAAY,UACZ,oBAAqB,SACrB,mBAAoB,SACpB,IAAO,UACP,IAAO,UACP,WAAc,QAChB,EACA,QAAW,CACT,KAAQ,MACV,EACA,QAAW,CACT,GAAM,EACR,CACF,IChEA,IAAAC,GAAAC,GAAA,CAAAC,GAAAC,IAAA,KAAMC,GAAK,EAAQ,IAAI,EACjBC,GAAO,EAAQ,MAAM,EACrBC,GAAK,EAAQ,IAAI,EACjBC,GAAS,EAAQ,QAAQ,EACzBC,GAAc,KAEdC,GAAUD,GAAY,QAEtBE,GAAO,+IAGb,SAASC,GAAOC,EAAK,CACnB,IAAMC,EAAM,CAAC,EAGTC,EAAQF,EAAI,SAAS,EAGzBE,EAAQA,EAAM,QAAQ,UAAW;AAAA,CAAI,EAErC,IAAIC,EACJ,MAAQA,EAAQL,GAAK,KAAKI,CAAK,IAAM,MAAM,CACzC,IAAME,EAAMD,EAAM,CAAC,EAGfE,EAASF,EAAM,CAAC,GAAK,GAGzBE,EAAQA,EAAM,KAAK,EAGnB,IAAMC,EAAaD,EAAM,CAAC,EAG1BA,EAAQA,EAAM,QAAQ,yBAA0B,IAAI,EAGhDC,IAAe,MACjBD,EAAQA,EAAM,QAAQ,OAAQ;AAAA,CAAI,EAClCA,EAAQA,EAAM,QAAQ,OAAQ,IAAI,GAIpCJ,EAAIG,CAAG,EAAIC,CACb,CAEA,OAAOJ,CACT,CAEA,SAASM,GAAaC,EAAS,CAC7B,IAAMC,EAAYC,GAAWF,CAAO,EAG9BG,EAASC,EAAa,aAAa,CAAE,KAAMH,CAAU,CAAC,EAC5D,GAAI,CAACE,EAAO,OAAQ,CAClB,IAAME,EAAM,IAAI,MAAM,8BAA8BJ,CAAS,wBAAwB,EACrF,MAAAI,EAAI,KAAO,eACLA,CACR,CAIA,IAAMC,EAAOC,GAAWP,CAAO,EAAE,MAAM,GAAG,EACpCQ,EAASF,EAAK,OAEhBG,EACJ,QAAS,EAAI,EAAG,EAAID,EAAQ,IAC1B,GAAI,CAEF,IAAMZ,EAAMU,EAAK,CAAC,EAAE,KAAK,EAGnBI,EAAQC,GAAcR,EAAQP,CAAG,EAGvCa,EAAYL,EAAa,QAAQM,EAAM,WAAYA,EAAM,GAAG,EAE5D,KACF,OAASE,EAAO,CAEd,GAAI,EAAI,GAAKJ,EACX,MAAMI,CAGV,CAIF,OAAOR,EAAa,MAAMK,CAAS,CACrC,CAEA,SAASI,GAAMC,EAAS,CACtB,QAAQ,IAAI,WAAWzB,EAAO,WAAWyB,CAAO,EAAE,CACpD,CAEA,SAASC,GAAOD,EAAS,CACvB,QAAQ,IAAI,WAAWzB,EAAO,WAAWyB,CAAO,EAAE,CACpD,CAEA,SAASE,EAAQF,EAAS,CACxB,QAAQ,IAAI,WAAWzB,EAAO,YAAYyB,CAAO,EAAE,CACrD,CAEA,SAASP,GAAYP,EAAS,CAE5B,OAAIA,GAAWA,EAAQ,YAAcA,EAAQ,WAAW,OAAS,EACxDA,EAAQ,WAIb,QAAQ,IAAI,YAAc,QAAQ,IAAI,WAAW,OAAS,EACrD,QAAQ,IAAI,WAId,EACT,CAEA,SAASW,GAAeR,EAAQc,EAAW,CAEzC,IAAIC,EACJ,GAAI,CACFA,EAAM,IAAI,IAAID,CAAS,CACzB,OAASL,EAAO,CACd,GAAIA,EAAM,OAAS,kBAAmB,CACpC,IAAMP,EAAM,IAAI,MAAM,4IAA4I,EAClK,MAAAA,EAAI,KAAO,qBACLA,CACR,CAEA,MAAMO,CACR,CAGA,IAAMhB,EAAMsB,EAAI,SAChB,GAAI,CAACtB,EAAK,CACR,IAAMS,EAAM,IAAI,MAAM,sCAAsC,EAC5D,MAAAA,EAAI,KAAO,qBACLA,CACR,CAGA,IAAMc,EAAcD,EAAI,aAAa,IAAI,aAAa,EACtD,GAAI,CAACC,EAAa,CAChB,IAAMd,EAAM,IAAI,MAAM,8CAA8C,EACpE,MAAAA,EAAI,KAAO,qBACLA,CACR,CAGA,IAAMe,EAAiB,gBAAgBD,EAAY,YAAY,CAAC,GAC1DE,EAAalB,EAAO,OAAOiB,CAAc,EAC/C,GAAI,CAACC,EAAY,CACf,IAAMhB,EAAM,IAAI,MAAM,2DAA2De,CAAc,2BAA2B,EAC1H,MAAAf,EAAI,KAAO,+BACLA,CACR,CAEA,MAAO,CAAE,WAAAgB,EAAY,IAAAzB,CAAI,CAC3B,CAEA,SAASM,GAAYF,EAAS,CAC5B,IAAIsB,EAAoB,KAExB,GAAItB,GAAWA,EAAQ,MAAQA,EAAQ,KAAK,OAAS,EACnD,GAAI,MAAM,QAAQA,EAAQ,IAAI,EAC5B,QAAWuB,KAAYvB,EAAQ,KACzBhB,GAAG,WAAWuC,CAAQ,IACxBD,EAAoBC,EAAS,SAAS,QAAQ,EAAIA,EAAW,GAAGA,CAAQ,eAI5ED,EAAoBtB,EAAQ,KAAK,SAAS,QAAQ,EAAIA,EAAQ,KAAO,GAAGA,EAAQ,IAAI,cAGtFsB,EAAoBrC,GAAK,QAAQ,QAAQ,IAAI,EAAG,YAAY,EAG9D,OAAID,GAAG,WAAWsC,CAAiB,EAC1BA,EAGF,IACT,CAEA,SAASE,GAAcC,EAAS,CAC9B,OAAOA,EAAQ,CAAC,IAAM,IAAMxC,GAAK,KAAKC,GAAG,QAAQ,EAAGuC,EAAQ,MAAM,CAAC,CAAC,EAAIA,CAC1E,CAEA,SAASC,GAAc1B,EAAS,CAC9Ba,GAAK,uCAAuC,EAE5C,IAAMc,EAASvB,EAAa,YAAYJ,CAAO,EAE3C4B,EAAa,QAAQ,IACzB,OAAI5B,GAAWA,EAAQ,YAAc,OACnC4B,EAAa5B,EAAQ,YAGvBI,EAAa,SAASwB,EAAYD,EAAQ3B,CAAO,EAE1C,CAAE,OAAA2B,CAAO,CAClB,CAEA,SAASE,GAAc7B,EAAS,CAC9B,IAAM8B,EAAa7C,GAAK,QAAQ,QAAQ,IAAI,EAAG,MAAM,EACjD8C,EAAW,OACTC,EAAQ,GAAQhC,GAAWA,EAAQ,OAErCA,GAAWA,EAAQ,SACrB+B,EAAW/B,EAAQ,SAEfgC,GACFhB,EAAO,oDAAoD,EAI/D,IAAIiB,EAAc,CAACH,CAAU,EAC7B,GAAI9B,GAAWA,EAAQ,KACrB,GAAI,CAAC,MAAM,QAAQA,EAAQ,IAAI,EAC7BiC,EAAc,CAACT,GAAaxB,EAAQ,IAAI,CAAC,MACpC,CACLiC,EAAc,CAAC,EACf,QAAWV,KAAYvB,EAAQ,KAC7BiC,EAAY,KAAKT,GAAaD,CAAQ,CAAC,CAE3C,CAKF,IAAIW,EACEC,EAAY,CAAC,EACnB,QAAWlD,KAAQgD,EACjB,GAAI,CAEF,IAAMN,EAASvB,EAAa,MAAMpB,GAAG,aAAaC,EAAM,CAAE,SAAA8C,CAAS,CAAC,CAAC,EAErE3B,EAAa,SAAS+B,EAAWR,EAAQ3B,CAAO,CAClD,OAASoC,EAAG,CACNJ,GACFhB,EAAO,kBAAkB/B,CAAI,IAAImD,EAAE,OAAO,EAAE,EAE9CF,EAAYE,CACd,CAGF,IAAIR,EAAa,QAAQ,IAOzB,OANI5B,GAAWA,EAAQ,YAAc,OACnC4B,EAAa5B,EAAQ,YAGvBI,EAAa,SAASwB,EAAYO,EAAWnC,CAAO,EAEhDkC,EACK,CAAE,OAAQC,EAAW,MAAOD,CAAU,EAEtC,CAAE,OAAQC,CAAU,CAE/B,CAGA,SAASE,GAAQrC,EAAS,CAExB,GAAIO,GAAWP,CAAO,EAAE,SAAW,EACjC,OAAOI,EAAa,aAAaJ,CAAO,EAG1C,IAAMC,EAAYC,GAAWF,CAAO,EAGpC,OAAKC,EAMEG,EAAa,aAAaJ,CAAO,GALtCe,GAAM,+DAA+Dd,CAAS,+BAA+B,EAEtGG,EAAa,aAAaJ,CAAO,EAI5C,CAEA,SAASsC,GAASC,EAAWC,EAAQ,CACnC,IAAM5C,EAAM,OAAO,KAAK4C,EAAO,MAAM,GAAG,EAAG,KAAK,EAC5CnB,EAAa,OAAO,KAAKkB,EAAW,QAAQ,EAE1CE,EAAQpB,EAAW,SAAS,EAAG,EAAE,EACjCqB,EAAUrB,EAAW,SAAS,GAAG,EACvCA,EAAaA,EAAW,SAAS,GAAI,GAAG,EAExC,GAAI,CACF,IAAMsB,EAASxD,GAAO,iBAAiB,cAAeS,EAAK6C,CAAK,EAChE,OAAAE,EAAO,WAAWD,CAAO,EAClB,GAAGC,EAAO,OAAOtB,CAAU,CAAC,GAAGsB,EAAO,MAAM,CAAC,EACtD,OAAS/B,EAAO,CACd,IAAMgC,EAAUhC,aAAiB,WAC3BiC,EAAmBjC,EAAM,UAAY,qBACrCkC,EAAmBlC,EAAM,UAAY,mDAE3C,GAAIgC,GAAWC,EAAkB,CAC/B,IAAMxC,EAAM,IAAI,MAAM,6DAA6D,EACnF,MAAAA,EAAI,KAAO,qBACLA,CACR,SAAWyC,EAAkB,CAC3B,IAAMzC,EAAM,IAAI,MAAM,iDAAiD,EACvE,MAAAA,EAAI,KAAO,oBACLA,CACR,KACE,OAAMO,CAEV,CACF,CAGA,SAASmC,GAAUnB,EAAYD,EAAQ3B,EAAU,CAAC,EAAG,CACnD,IAAMgC,EAAQ,GAAQhC,GAAWA,EAAQ,OACnCgD,EAAW,GAAQhD,GAAWA,EAAQ,UAE5C,GAAI,OAAO2B,GAAW,SAAU,CAC9B,IAAMtB,EAAM,IAAI,MAAM,gFAAgF,EACtG,MAAAA,EAAI,KAAO,kBACLA,CACR,CAGA,QAAWT,KAAO,OAAO,KAAK+B,CAAM,EAC9B,OAAO,UAAU,eAAe,KAAKC,EAAYhC,CAAG,GAClDoD,IAAa,KACfpB,EAAWhC,CAAG,EAAI+B,EAAO/B,CAAG,GAG1BoC,GAEAhB,EADEgC,IAAa,GACR,IAAIpD,CAAG,2CAEP,IAAIA,CAAG,8CAF0C,GAM5DgC,EAAWhC,CAAG,EAAI+B,EAAO/B,CAAG,CAGlC,CAEA,IAAMQ,EAAe,CACnB,aAAAyB,GACA,aAAAH,GACA,YAAA3B,GACA,OAAAsC,GACA,QAAAC,GACA,MAAA/C,GACA,SAAAwD,EACF,EAEAhE,EAAO,QAAQ,aAAeqB,EAAa,aAC3CrB,EAAO,QAAQ,aAAeqB,EAAa,aAC3CrB,EAAO,QAAQ,YAAcqB,EAAa,YAC1CrB,EAAO,QAAQ,OAASqB,EAAa,OACrCrB,EAAO,QAAQ,QAAUqB,EAAa,QACtCrB,EAAO,QAAQ,MAAQqB,EAAa,MACpCrB,EAAO,QAAQ,SAAWqB,EAAa,SAEvCrB,EAAO,QAAUqB,IC9TjB,OAAO6C,OAAa,UACpB,OAAOC,OAAU,OACjB,OAAOC,OAAgB,qBACvB,OAAOC,OAAU,OC7CjB,IAAAC,GAAmB,WACnB,OAAOC,OAAU,OAGjB,IAAMC,GAAU,QAAQ,IAAI,EAG5B,GAAAC,QAAO,OAAO,CAAE,KAAMF,GAAK,QAAQC,GAAS,MAAM,CAAE,CAAC,EAE9C,IAAME,EAAS,CACpB,KAAM,QAAQ,IAAI,MAAQ,IAC1B,QAAS,QAAQ,IAAI,UAAY,cACjC,cAAe,QAAQ,IAAI,gBAAkB,wBAC7C,YAAa,QAAQ,IAAI,cAAgB,oBACzC,cAAe,SAAS,QAAQ,IAAI,gBAAkB,OAAQ,EAAE,EAChE,WAAY,CACV,WAAY,QAAQ,IAAI,wBAA0B,oBAClD,cAAe,QAAQ,IAAI,2BAA6B,eAC1D,CACF,ECjBO,IAAMC,EAAN,cAAuB,KAAM,CAClC,YACSC,EACPC,EACA,CACA,MAAMA,CAAO,EAHN,gBAAAD,EAIP,KAAK,KAAO,UACd,CACF,EAEO,SAASE,GAAaC,EAAYC,EAAcC,EAAeC,EAAoB,CACxF,GAAIH,aAAeJ,EAAU,CAC3BM,EAAI,OAAOF,EAAI,UAAU,EAAE,KAAK,CAC9B,OAAQA,EAAI,WACZ,QAASA,EAAI,OACf,CAAC,EACD,MACF,CAEA,QAAQ,MAAMA,CAAG,EACjBE,EAAI,OAAO,GAAG,EAAE,KAAK,CACnB,OAAQ,IACR,QAAS,4CACX,CAAC,CACH,CC1BA,OAAS,UAAAE,OAAiC,UCGnC,IAAMC,EAAgBC,GAAiB,CAACC,EAAcC,EAAeC,IAAuB,CACjG,QAAQ,QAAQH,EAAGC,EAAKC,EAAKC,CAAI,CAAC,EAAE,MAAOC,GAAU,CAC/CA,aAAiBC,EACnBH,EAAI,OAAOE,EAAM,UAAU,EAAE,KAAK,CAChC,OAAQA,EAAM,WACd,QAASA,EAAM,OACjB,CAAC,GAED,QAAQ,MAAM,mBAAoBA,CAAK,EACvCF,EAAI,OAAO,GAAG,EAAE,KAAK,CACnB,OAAQ,IACR,QAAS,uBACX,CAAC,EAEL,CAAC,CACH,EDhBA,OAAOI,OAAgB,aEDvB,OAAOC,OAAgB,aAGvB,OAAOC,MAAQ,cACf,OAAOC,MAAU,OCFjB,OAAS,QAAAC,OAAY,gBACrB,OAAS,aAAAC,OAAiB,OAC1B,IAAMC,GAAYD,GAAUD,EAAI,EAgCnBG,EAAN,KAAsB,CAC3B,aAAa,kBAAkBC,EAAyC,CACtE,GAAI,CAEF,IAAMC,EAAY,MAAM,QAAQ,IAC9BD,EAAO,OAAO,IAAI,MAAOE,EAAoBC,IAAkB,CAC7D,GAAI,CACF,GAAM,CAAE,OAAAC,CAAO,EAAI,MAAMN,GAAU,oDAAoDI,EAAM,KAAK,sBAAsB,EACxH,GAAI,CAACE,EAAO,KAAK,EACf,MAAM,IAAI,MAAM,gCAAgCD,CAAK,cAAcD,EAAM,KAAK,GAAG,EAInF,IAAMG,EAAmBD,EAAO,KAAK,EAClC,QACC,wBAAwBF,EAAM,KAAK,WACnC,uBAAuBC,CAAK,UAC9B,EACC,QACC,IAAI,OAAO,SAASD,EAAM,KAAK,QAAS,GAAG,EAC3C,QAAQC,CAAK,OACf,EAEF,MAAO,CACL,MAAAA,EACA,SAAUE,CACZ,CACF,OAASC,EAAO,CACd,MAAIA,aAAiB,MACb,IAAI,MAAM,qCAAqCH,CAAK,cAAcD,EAAM,KAAK,MAAMI,EAAM,OAAO,EAAE,EAEpG,IAAI,MAAM,qCAAqCH,CAAK,cAAcD,EAAM,KAAK,mCAAmC,CACxH,CACF,CAAC,CACH,EAEIK,EAAW,GACf,OAAAA,GAAY,KAAK,gBAAgB,EACjCA,GAAY,KAAK,qBAAqBP,CAAM,EAC5CO,GAAY,KAAK,kBAAkBP,CAAM,EACzCO,GAAY,KAAK,uBAAuBP,CAAM,EAC9CO,GAAY,KAAK,mBAAmBP,EAAQC,CAAS,EACrDM,GAAY,KAAK,qBAAqB,EACtCA,GAAY,KAAK,mBAAmBP,CAAM,EAC1CO,GAAY,KAAK,qBAAqBP,CAAM,EAErCO,CACT,OAASD,EAAY,CACnB,cAAQ,MAAM,+BAAgCA,CAAK,EAC7C,IAAIE,EAAS,IAAKF,EAAM,SAAW,sCAAQ,CACnD,CACF,CAEA,OAAe,iBAA0B,CACvC,MAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CA4BT,CAEA,OAAe,qBAAqBN,EAAqB,CAGvD,IAAIS,EAAS;AAAA;AAAA,6BAFO,GAAGT,EAAO,EAAE,IAAIA,EAAO,IAAI,MAIX;AAAA;AAAA;AAAA;AAAA;AAAA,EAQpC,OAAAA,EAAO,OAAO,QAAQ,CAACE,EAAYC,IAAkB,CACnDD,EAAM,SAAS,QAASQ,GAAa,CACnC,IAAMC,EAAU,KAAK,qBAAqBD,EAAKP,EAAO,IAAI,EACpDS,EAAUF,EAAI,QAAU,OAAOA,EAAI,OAAO,MAAQ,GACxDD,GAAU,oBAAoBN,CAAK,OAAOQ,CAAO,IAAIC,CAAO;AAAA,CAC9D,CAAC,EACDV,EAAM,SAAS,QAASQ,GAAa,CACnC,IAAMC,EAAU,KAAK,qBAAqBD,EAAKP,EAAO,IAAI,EACpDS,EAAUF,EAAI,QAAU,OAAOA,EAAI,OAAO,MAAQ,GACxDD,GAAU,oBAAoBN,CAAK,OAAOQ,CAAO,IAAIC,CAAO;AAAA,CAC9D,CAAC,CACH,CAAC,EAEDH,GAAU;AAAA;AAAA;AAAA,EAKHA,CACT,CAEA,OAAe,qBAAqBC,EAAUG,EAAoBC,EAAgC,CAChG,GAAI,CAACJ,GAAO,CAACA,EAAI,OAAS,CAACA,EAAI,KAC7B,MAAM,IAAI,MAAM,kBAAkB,EAIpC,MADiB,YAAYG,CAAU,IAAIC,CAAS,IAAIJ,EAAI,MAAM,YAAY,CAAC,IAAIA,EAAI,KAAK,YAAY,CAAC,GACzF,QAAQ,cAAe,GAAG,CAC5C,CAEA,OAAe,sBAA+B,CAC5C,MAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAsCT,CAEA,OAAe,kBAAkBV,EAAgC,CAC/D,MAAO;AAAA;AAAA,yBAEcA,EAAO,eAAiB,GAAI;AAAA,CAEnD,CAEA,OAAe,uBAAuBA,EAAgC,CACpE,IAAIe,EAAO;AAAA;AAAA,EAGX,OAAAA,GAAQ,wBAAwBf,EAAO,aAAe,CAAC;AAAA;AAAA,EAEvDe,GAAQ;AAAA,EACRA,GAAQ;AAAA,EACRA,GAAQ;AAAA,EACRA,GAAQ;AAAA,EAGRf,EAAO,OAAO,QAAQ,CAACE,EAAYC,IAAkB,CACnDY,GAAQ,qCAAqCZ,CAAK;AAAA,CACpD,CAAC,EAEDY,GAAQ;AAAA,EACRA,GAAQ;AAAA;AAAA,EAGRf,EAAO,OAAO,QAAQ,CAACE,EAAYC,IAAkB,CACnDY,GAAQ,gBAAgBZ,CAAK,UAAUD,EAAM,KAAK;AAAA,EAClDa,GAAQ,gBAAgBZ,CAAK,YAAYD,EAAM,GAAG,IAAIA,EAAM,GAAG;AAAA,CACjE,CAAC,EAEMa,CACT,CAEA,OAAe,mBAAmBf,EAAaC,EAA+B,CAC5E,IAAIe,EAAU;AAAA;AAAA,EAGd,OAAAA,GAAW;AAAA,EACXhB,EAAO,OAAO,QAAQ,CAACE,EAAYC,IAAkB,CACnDD,EAAM,SAAS,QAASQ,GAAa,CACnC,IAAMC,EAAU,KAAK,qBAAqBD,EAAKP,EAAO,IAAI,EACtDO,EAAI,OAAS,QACfM,GAAW,6BAA6Bb,CAAK,OAAOQ,CAAO;AAAA,EAC3DK,GAAW,6BAA6Bb,CAAK,OAAOQ,CAAO;AAAA,GAE3DK,GAAW,6BAA6Bb,CAAK,OAAOQ,CAAO;AAAA,CAE/D,CAAC,EACDT,EAAM,SAAS,QAASQ,GAAa,CACnC,IAAMC,EAAU,KAAK,qBAAqBD,EAAKP,EAAO,IAAI,EACtDO,EAAI,OAAS,QACfM,GAAW,6BAA6Bb,CAAK,OAAOQ,CAAO;AAAA,EAC3DK,GAAW,6BAA6Bb,CAAK,OAAOQ,CAAO;AAAA,GAE3DK,GAAW,6BAA6Bb,CAAK,OAAOQ,CAAO;AAAA,CAE/D,CAAC,CACH,CAAC,EACDK,GAAW;AAAA;AAAA,EAGXA,GAAW;AAAA,EACXhB,EAAO,OAAO,QAAQ,CAACE,EAAYC,IAAkB,CACnDD,EAAM,SAAS,QAASQ,GAAa,CACnC,IAAMC,EAAU,KAAK,qBAAqBD,EAAKP,EAAO,IAAI,EACtDO,EAAI,OAAS,OACfM,GAAW,aAAab,CAAK,cAAcA,CAAK,aAAaO,EAAI,KAAK,KAAKA,EAAI,QAAQ,sBAAsBP,CAAK,OAAOQ,CAAO,0BAA0BR,CAAK,OAAOQ,CAAO;AAAA,EAE7KK,GAAW,aAAab,CAAK,cAAcA,CAAK,aAAaO,EAAI,KAAK,KAAKA,EAAI,QAAQ,sBAAsBP,CAAK,OAAOQ,CAAO;AAAA,CAEpI,CAAC,EACDT,EAAM,SAAS,QAASQ,GAAa,CACnC,IAAMC,EAAU,KAAK,qBAAqBD,EAAKP,EAAO,IAAI,EACtDO,EAAI,OAAS,OACfM,GAAW,aAAab,CAAK,cAAcA,CAAK,aAAaO,EAAI,KAAK,KAAKA,EAAI,QAAQ,sBAAsBP,CAAK,OAAOQ,CAAO,0BAA0BR,CAAK,OAAOQ,CAAO;AAAA,EAE7KK,GAAW,aAAab,CAAK,cAAcA,CAAK,aAAaO,EAAI,KAAK,KAAKA,EAAI,QAAQ,sBAAsBP,CAAK,OAAOQ,CAAO;AAAA,CAEpI,CAAC,CACH,CAAC,EACDK,GAAW;AAAA;AAAA;AAAA,EAGXhB,EAAO,OAAO,QAAQ,CAACE,EAAYC,IAAkB,CACnDa,GAAW,mCAAmCb,CAAK;AAAA,EACnDD,EAAM,SAAS,QAASQ,GAAa,CACnCM,GAAW,QAAQN,EAAI,KAAK,KAAKA,EAAI,QAAQ,KAAK,KAAK,YAAYA,CAAG,CAAC,UAAUA,EAAI,IAAI;AAAA,CAC3F,CAAC,EACDR,EAAM,SAAS,QAASQ,GAAa,CACnCM,GAAW,QAAQN,EAAI,KAAK,KAAKA,EAAI,QAAQ,KAAK,KAAK,YAAYA,CAAG,CAAC,UAAUA,EAAI,IAAI;AAAA,CAC3F,CAAC,EACDM,GAAW;AAAA;AAAA,EAGXA,GAAW,6BAA6Bb,CAAK;AAAA,EAC7C,IAAMc,EAAUf,EAAM,SAAS,QAAU,EACnCgB,EAAUhB,EAAM,SAAS,QAAU,EACzCc,GAAW,QAAQd,EAAM,MAAM,KAAKe,CAAO,UAAUd,CAAK;AAAA,EAC1Da,GAAW,QAAQd,EAAM,MAAM,KAAKgB,CAAO,UAAUf,CAAK,kBAAkBc,CAAO;AAAA,EACnFD,GAAW;AAAA;AAAA,EAGX,IAAMG,EAAWlB,EAAU,KAAKmB,GAAMA,EAAG,QAAUjB,CAAK,EACxD,GAAIgB,GAAYA,EAAS,SACvBH,GAAWG,EAAS,SAAW;AAAA;AAAA,MAE/B,OAAM,IAAI,MAAM,+BAA+BjB,EAAM,KAAK,EAAE,CAEhE,CAAC,EAEMc,CACT,CAEA,OAAe,YAAYN,EAAkB,CAE3C,GAAIA,EAAI,SAAW,OACjB,OAAOA,EAAI,OAIb,OAAQA,EAAI,KAAK,YAAY,EAAG,CAC9B,IAAK,OACH,MAAO,GACT,IAAK,OACL,IAAK,QACH,MAAO,GACT,IAAK,SACL,IAAK,QACH,MAAO,IACT,IAAK,SACL,IAAK,QACH,MAAO,IACT,IAAK,SACL,IAAK,QACL,IAAK,SACH,MAAO,IACT,QACE,MAAO,GACX,CACF,CAEA,OAAe,mBAAmBW,EAAsB,CACtD,OAAQA,EAAK,YAAY,EAAG,CAC1B,IAAK,OACH,MAAO,cACT,IAAK,QACH,MAAO,aACT,IAAK,OACH,MAAO,aACT,IAAK,SACH,MAAO,cACT,IAAK,QACH,MAAO,cACT,IAAK,SACH,MAAO,cACT,IAAK,QACH,MAAO,cACT,IAAK,SACH,MAAO,cACT,IAAK,QACH,MAAO,cACT,IAAK,SACH,MAAO,gBACT,QACE,MAAO,aACX,CACF,CAEA,OAAe,oBAAoBA,EAAsB,CACvD,OAAQA,EAAK,YAAY,EAAG,CAC1B,IAAK,OACH,MAAO,eACT,IAAK,QACH,MAAO,cACT,IAAK,OACH,MAAO,cACT,IAAK,SACH,MAAO,eACT,IAAK,QACH,MAAO,eACT,IAAK,SACH,MAAO,eACT,IAAK,QACH,MAAO,eACT,IAAK,SACH,MAAO,eACT,IAAK,QACH,MAAO,eACT,IAAK,SACH,MAAO,iBACT,QACE,MAAO,cACX,CACF,CAEA,OAAe,mBAAmBrB,EAAqB,CACrD,IAAMsB,EAAkB,CAAC,EAGzB,OAAAA,EAAM,KAAK,wBAAwB,EACnCA,EAAM,KAAK,GAAG,EAGdA,EAAM,KAAK,0BAA0B,EACrCA,EAAM,KAAK,kCAAkC,EAC7CA,EAAM,KAAK,mCAAmC,EAC9CA,EAAM,KAAK,EAAE,EACbA,EAAM,KAAK,yCAAyC,EAGpDtB,EAAO,OAAO,QAAQ,CAACE,EAAYC,IAAkB,CACnDD,EAAM,SAAS,QAASQ,GAAa,CACnC,IAAMC,EAAU,KAAK,qBAAqBD,EAAKP,EAAO,IAAI,EAC1D,GAAIO,EAAI,OAAS,OACfY,EAAM,KAAK,8BAA8BnB,CAAK,OAAOQ,CAAO,+CAA+CR,CAAK,OAAOQ,CAAO,yBAAyBR,CAAK,OAAOQ,CAAO,QAAQ,MAC7K,CACL,IAAMY,EAAW,KAAK,mBAAmBb,EAAI,IAAI,EACjDY,EAAM,KAAK,8BAA8BnB,CAAK,OAAOQ,CAAO,MAAMY,CAAQ,iCAAiCpB,CAAK,OAAOQ,CAAO,IAAI,CACpI,CACF,CAAC,CACH,CAAC,EAEDW,EAAM,KAAK,EAAE,EACbA,EAAM,KAAK,0BAA0B,EAGrCtB,EAAO,OAAO,QAAQ,CAACE,EAAYC,IAAkB,CACnDD,EAAM,SAAS,QAASQ,GAAa,CACnC,IAAMC,EAAU,KAAK,qBAAqBD,EAAKP,EAAO,IAAI,EAC1D,GAAIO,EAAI,OAAS,OACfY,EAAM,KAAK,iDAAiDnB,CAAK,OAAOQ,CAAO,yBAAyBR,CAAK,OAAOQ,CAAO,gCAAgCR,CAAK,OAAOQ,CAAO,IAAI,MAC7K,CACL,IAAMa,EAAY,KAAK,oBAAoBd,EAAI,IAAI,EACnDY,EAAM,KAAK,OAAOE,CAAS,iCAAiCrB,CAAK,OAAOQ,CAAO,4BAA4BR,CAAK,OAAOQ,CAAO,IAAI,CACpI,CACF,CAAC,CACH,CAAC,EAEDW,EAAM,KAAK,EAAE,EACbA,EAAM,KAAK,0BAA0B,EACrCA,EAAM,KAAK,iCAAiC,EAC5CA,EAAM,KAAK,+BAA+B,EAC1CA,EAAM,KAAK,GAAG,EAEPA,EAAM,KAAK;AAAA,CAAI,CACxB,CAEA,OAAe,qBAAqBtB,EAAgC,CAClE,MAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BTA,EAAO,OAAO,IAAI,CAACE,EAAYC,IAAkB;AAAA,oBAC/BA,CAAK,4CAA4CA,CAAK,cAAcA,CAAK;AAAA,8CAC/CA,CAAK;AAAA;AAAA;AAAA;AAAA,+BAIpBA,CAAK;AAAA,yCACKA,CAAK,kBAAkBA,CAAK;AAAA,oDACjBA,CAAK;AAAA;AAAA;AAAA,CAGxD,EAAE,KAAK;AAAA,CAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAqCX,CAEA,OAAe,gBAAgBH,EAAqB,CAClD,IAAMsB,EAAkB,CAAC,EAEzB,OAAItB,EAAO,QAAUA,EAAO,OAAO,OAAS,GAE1CA,EAAO,OAAO,QAAQ,CAACE,EAAYC,IAAkB,CAE/CD,EAAM,SACRA,EAAM,QAAQ,QAASQ,GAAa,CAClC,GAAIA,GAAOA,EAAI,OAASA,EAAI,KAAM,CAChC,IAAMC,EAAU,KAAK,qBAAqBD,EAAKP,EAAO,IAAI,EACpDS,EAAUF,EAAI,QAAU,OAAOA,EAAI,OAAO,MAAQ,GACxDY,EAAM,KAAK,WAAWX,CAAO,IAAIC,CAAO,EAAE,CAC5C,CACF,CAAC,EAICV,EAAM,SACRA,EAAM,QAAQ,QAASQ,GAAa,CAClC,GAAIA,GAAOA,EAAI,OAASA,EAAI,KAAM,CAChC,IAAMC,EAAU,KAAK,qBAAqBD,EAAKP,EAAO,IAAI,EACpDS,EAAUF,EAAI,QAAU,OAAOA,EAAI,OAAO,MAAQ,GACxDY,EAAM,KAAK,WAAWX,CAAO,IAAIC,CAAO,EAAE,CAC5C,CACF,CAAC,CAEL,CAAC,EAGIU,EAAM,KAAK;AAAA,CAAI,CACxB,CACF,ED5iBA,OAAS,QAAAG,OAAY,gBACrB,OAAS,aAAAC,OAAiB,OAE1B,OAAOC,OAAc,WAGrB,OAAS,cAAAC,MAAkB,KAC3B,OAAOC,OAAgB,aACvB,IAAMC,EAAYJ,GAAUD,EAAI,EAGnBM,EAAN,KAAwB,CAC7B,YAAe,SAA8B,KAC7C,YAAe,UAA2B,KAE1C,OAAO,aAA0B,CAC/B,GAAI,CAAC,KAAK,WACR,KAAK,SAAW,IAAIC,GAAWC,EAAO,aAAa,EAC/C,KAAK,WACP,GAAI,CACF,KAAK,SAAS,UAAU,KAAK,KAAK,UAAW,IAAI,CACnD,OAASC,EAAO,CACd,QAAQ,MAAM,gCAAiCA,CAAK,EACpD,KAAK,UAAY,IACnB,CAGJ,OAAO,KAAK,QACd,CAEA,aAAa,YAAkC,CAC7C,IAAMC,EAAK,KAAK,YAAY,EAC5B,GAAI,CAACA,EAAG,UAAU,QAChB,GAAI,CACF,MAAMA,EAAG,WAAW,OAAO,EAAE,iBAC3B,eACA,cACF,CACF,OAASD,EAAO,CACd,cAAQ,MAAM,eAAgBA,CAAK,EAC7B,IAAI,MAAM,uBAAuB,CACzC,CAEF,OAAOC,CACT,CAEA,OAAO,aAAaC,EAAe,CAEjC,GADA,KAAK,UAAYA,EACb,KAAK,SACP,GAAI,CACF,KAAK,SAAS,UAAU,KAAKA,EAAO,IAAI,CAC1C,OAASF,EAAO,CACd,QAAQ,MAAM,6BAA8BA,CAAK,CACnD,CAEJ,CAEA,OAAO,WAAY,CACjB,KAAK,UAAY,KACb,KAAK,UACP,KAAK,SAAS,UAAU,MAAM,CAElC,CACF,EAQaG,EAAN,KAAqB,CAC1B,aAAa,aAAc,CACzB,GAAI,CAQF,OALgB,MAFL,MAAMN,EAAkB,WAAW,GAErB,WAAW,UAAU,EAAE,QAAQ,EAAG,IAAK,CAC9D,KAAM,cACN,OAAQ,iFACV,CAAC,GAEc,KACjB,OAASG,EAAO,CACd,cAAQ,MAAM,0BAA2BA,CAAK,EACxC,IAAII,EAAS,IAAK,kDAAU,CACpC,CACF,CAEA,aAAa,cAAcC,EAAuBN,EAAsBO,EAAe,CACrF,GAAI,CACF,IAAML,EAAK,MAAMJ,EAAkB,WAAW,EAExCU,EAAYC,EAAK,KAAK,QAAQ,IAAI,EAAG,SAAS,EACpD,MAAMC,EAAG,MAAMF,EAAW,CAAE,UAAW,EAAK,CAAC,EAE7C,IAAMG,EAAcJ,EAAS,KACvBK,EAAcH,EAAK,KAAKD,EAAW,GAAGG,CAAW,MAAM,EACvDE,EAAaJ,EAAK,KAAKD,EAAWG,CAAW,EAG/ChB,EAAWkB,CAAU,GACvB,MAAMH,EAAG,GAAGG,EAAY,CAAE,UAAW,GAAM,MAAO,EAAK,CAAC,EAI1D,MAAMP,EAAQ,GAAGM,CAAW,EAE5B,GAAI,CAEF,MAAMhB,GAAWgB,EAAaJ,CAAS,EAIvC,IAAMM,GADiB,MAAMJ,EAAG,QAAQF,CAAS,GACb,KAClCO,GAASA,IAAS,GAAGJ,CAAW,QAAWhB,EAAWc,EAAK,KAAKD,EAAWO,CAAI,CAAC,CAClF,EAEA,GAAI,CAACD,EACH,MAAM,IAAIT,EAAS,IAAK,oEAAa,EAIvC,IAAMW,EAAgBP,EAAK,KAAKD,EAAWM,CAAY,EACnDE,IAAkBH,GACpB,MAAMH,EAAG,OAAOM,EAAeH,CAAU,EAI3C,MAAMH,EAAG,OAAOE,CAAW,EAG3B,IAAIK,EACJ,GAAI,CACF,IAAMC,EAAYlB,EAAO,KAAK,SAAS,OAAO,EAC9CiB,EAAa,KAAK,MAAMC,CAAS,CACnC,OAASjB,EAAO,CACd,cAAQ,MAAM,0BAA2BA,CAAK,EACxC,IAAII,EAAS,IAAK,kDAAU,CACpC,CAIA,IAAMc,GADQ,MAAMT,EAAG,QAAQG,CAAU,GACf,KAAKE,GAAQ,CACrC,IAAMK,EAAWX,EAAK,KAAKI,EAAYE,CAAI,EAC3C,OAAOpB,EAAWyB,CAAQ,GAAK,CAACL,EAAK,SAAS,OAAO,GAAK,CAACA,EAAK,SAAS,KAAK,CAChF,CAAC,EAED,GAAI,CAACI,EACH,MAAM,IAAId,EAAS,IAAK,kDAAU,EAGpC,IAAMgB,EAAkBZ,EAAK,KAAKI,EAAYM,CAAW,EAGzD,aAAMT,EAAG,MAAMW,EAAiB,GAAK,EAG9B,MAAMnB,EAAG,WAAW,UAAU,EAAE,OAAO,CAC5C,KAAMS,EACN,OAAQ,UACR,WAAY,IAAI,KAAK,EAAE,YAAY,EACnC,YAAaJ,EAAS,aAAa,SAAS,GAAK,IACjD,cAAeA,EAAS,eAAe,SAAS,GAAK,OACrD,OAAQU,EACR,SAAUI,CACZ,CAAC,CAEH,OAASpB,EAAO,CAEd,MAAIN,EAAWiB,CAAW,GACxB,MAAMF,EAAG,OAAOE,CAAW,EAEzBjB,EAAWkB,CAAU,GACvB,MAAMH,EAAG,GAAGG,EAAY,CAAE,UAAW,GAAM,MAAO,EAAK,CAAC,EAEpDZ,CACR,CAEF,OAASA,EAAO,CAEd,MADA,QAAQ,MAAM,4BAA6BA,CAAK,EAC5CA,aAAiBI,EACbJ,EAEF,IAAII,EAAS,IAAK,sCAAQ,CAClC,CACF,CAEA,aAAa,aAAaiB,EAAY,CACpC,GAAI,CACF,IAAMpB,EAAK,MAAMJ,EAAkB,WAAW,EACxCQ,EAAU,MAAMJ,EAAG,WAAW,UAAU,EAAE,OAAOoB,CAAE,EAEnDC,EAAUD,EAAG,UAAU,EAAG,CAAC,EAC3BE,EAAU,YAAYF,CAAE,IAAIhB,EAAQ,IAAI,OAE9C,QAAQ,IAAI,gCAAiC,CAC3C,YAAaA,EAAQ,KACrB,QAAAiB,EACA,QAAAC,EACA,YAAalB,EAAQ,QACvB,CAAC,EAGD,IAAMmB,EAAW,MAAM,KAAK,iBAAiBH,CAAE,EAGzCI,EAAUjB,EAAK,KAAK,QAAQ,IAAI,EAAG,MAAM,EAC/C,MAAMC,EAAG,MAAMgB,EAAS,CAAE,UAAW,EAAK,CAAC,EAC3C,IAAMC,EAAelB,EAAK,KAAKiB,EAAS,YAAYH,CAAO,IAAI,EAC/D,MAAMb,EAAG,UAAUiB,EAAcF,CAAQ,EAGzC,IAAMG,EAAanB,EAAK,KAAKiB,EAAS,YAAYH,CAAO,EAAE,EAC3D,GAAI,CACF,MAAM1B,EAAU,OAAO8B,CAAY,OAAOC,CAAU,aAAa,CACnE,OAAS3B,EAAO,CACd,cAAQ,MAAM,sBAAuBA,CAAK,EACpC,IAAII,EAAS,IAAK,sCAAQ,CAClC,CAGA,GAAI,CACF,MAAMK,EAAG,MAAMkB,EAAY,GAAK,EAGhC,MAAMlB,EAAG,MAAMJ,EAAQ,SAAU,GAAK,CACxC,OAASL,EAAO,CACd,cAAQ,MAAM,uCAAwCA,CAAK,EACrD,IAAII,EAAS,IAAK,kDAAU,CACpC,CAGA,GAAI,CACF,IAAMwB,EAAiB,SAASD,CAAU,MAAMF,CAAO,aAAaH,CAAO,cAC3E,QAAQ,IAAI,oCAAqCM,CAAc,EAC/D,MAAMhC,EAAUgC,CAAc,EAE9B,MAAM,IAAI,QAAQC,GAAW,WAAWA,EAAS,GAAI,CAAC,EAEtD,GAAM,CAAE,OAAQC,CAAe,EAAI,MAAMlC,EAAU,qBAAqB0B,CAAO,EAAE,EACjF,GAAI,CAACQ,EACH,MAAM,IAAI,MAAM,sCAAsC,EAExD,QAAQ,IAAI,+BAAgCA,CAAc,EAG1D,IAAMC,EAAU,SAAS1B,EAAQ,QAAQ,KAAKkB,CAAO,OAAOE,CAAO,SAASH,CAAO,cACnF,QAAQ,IAAI,sCAAuCS,CAAO,EAC1D,MAAMnC,EAAUmC,CAAO,EAEvB,MAAM,IAAI,QAAQF,GAAW,WAAWA,EAAS,GAAI,CAAC,EAEtD,GAAM,CAAE,OAAQG,CAAe,EAAI,MAAMpC,EAAU,aAAaY,EAAK,SAASH,EAAQ,QAAQ,CAAC,GAAG,EAClG,GAAI,CAAC2B,EAAgB,CACnB,QAAQ,MAAM,gDAAgD,EAC9D,GAAI,CACF,GAAM,CAAE,OAAQC,CAAQ,EAAI,MAAMrC,EAAU,cAAc6B,CAAO,SAASH,CAAO,MAAM,EACvF,QAAQ,MAAM,oBAAqBW,CAAO,CAC5C,OAASC,EAAU,CACjB,QAAQ,MAAM,mCAAoCA,CAAQ,CAC5D,CACA,YAAMtC,EAAU,qBAAqB0B,CAAO,EAAE,EACxC,IAAI,MAAM,8BAA8B,CAChD,CACA,QAAQ,IAAI,iCAAkCU,CAAc,CAE9D,OAAShC,EAAO,CACd,QAAQ,MAAM,0BAA2BA,CAAK,EAC9C,GAAI,CACF,MAAMJ,EAAU,qBAAqB0B,CAAO,EAAE,EAC9C,MAAM1B,EAAU,aAAaY,EAAK,SAASH,EAAQ,QAAQ,CAAC,GAAG,CACjE,OAAS8B,EAAc,CACrB,QAAQ,MAAM,iBAAkBA,CAAY,CAC9C,CACA,MAAM,IAAI/B,EAAS,IAAK,sCAAQ,CAClC,CAGA,OAAO,MAAMH,EAAG,WAAW,UAAU,EAAE,OAAOoB,EAAI,CAChD,OAAQ,SACV,CAAC,CACH,OAASrB,EAAO,CAEd,MADA,QAAQ,MAAM,2BAA4BA,CAAK,EAC3CA,aAAiBI,EACbJ,EAEF,IAAII,EAAS,IAAK,sCAAQ,CAClC,CACF,CAEA,aAAa,YAAYiB,EAAY,CACnC,IAAMpB,EAAK,MAAMJ,EAAkB,WAAW,EACxCQ,EAAU,MAAMJ,EAAG,WAAW,UAAU,EAAE,OAAOoB,CAAE,EACnDC,EAAUD,EAAG,UAAU,EAAG,CAAC,EAC3BX,EAAcL,EAAQ,KAE5B,QAAQ,IAAI,yCAAyC,EACrD,QAAQ,IAAI,mCAAmCiB,CAAO,QAAQZ,CAAW,EAAE,EAE3E,IAAM0B,EAAc,MAAOC,GAAgB,CACzC,GAAI,CACF,GAAM,CAAE,OAAAC,EAAQ,OAAAC,CAAO,EAAI,MAAM3C,EAAUyC,CAAG,EAC9C,eAAQ,IAAI,qBAAqBA,CAAG,EAAE,EAClCC,GAAQ,QAAQ,IAAI,UAAWA,CAAM,EACrCC,GAAQ,QAAQ,IAAI,UAAWA,CAAM,EAClC,CAAE,OAAAD,EAAQ,OAAAC,CAAO,CAC1B,OAASvC,EAAO,CACd,eAAQ,IAAI,kCAAkCqC,CAAG,EAAE,EACnD,QAAQ,IAAI,SAAUrC,CAAK,EACpB,CAAE,OAAQ,GAAI,OAAQ,EAAG,CAClC,CACF,EAEA,GAAI,CAEF,QAAQ,IAAI,oCAAoC,EAChD,GAAM,CAAE,OAAQwC,CAAa,EAAI,MAAMJ,EAAY,8BAA8Bd,CAAO,IAAIZ,CAAW,kBAAkB,EACrH8B,GACF,QAAQ,IAAI,2BAA4BA,CAAY,EAItD,QAAQ,IAAI,6BAA6B,EACzC,MAAMJ,EAAY,aAAa1B,CAAW,GAAG,EAG7C,QAAQ,IAAI,0CAA0C,EACtD,MAAM,IAAI,QAAQmB,GAAW,WAAWA,EAAS,GAAI,CAAC,EAGtD,GAAM,CAAE,OAAQY,CAAU,EAAI,MAAML,EAAY,kBAAkB1B,CAAW,kBAAkB,EAC3F+B,EACF,QAAQ,IAAI,gDAAiDA,CAAS,EAEtE,QAAQ,IAAI,sCAAsC,EAIpD,QAAQ,IAAI,mCAAmC,EAC/C,MAAML,EAAY,qBAAqBd,CAAO,EAAE,EAGhD,QAAQ,IAAI,gDAAgD,EAC5D,MAAM,IAAI,QAAQO,GAAW,WAAWA,EAAS,GAAI,CAAC,EAGtD,QAAQ,IAAI,mCAAmC,EAC/C,GAAM,CAAE,OAAQa,CAAW,EAAI,MAAMN,EAAY,8BAA8Bd,CAAO,IAAIZ,CAAW,kBAAkB,EAEnHgC,EACF,QAAQ,IAAI,kDAAmDA,CAAU,EAEzE,QAAQ,IAAI,uCAAuC,CAGvD,OAAS1C,EAAO,CACd,QAAQ,MAAM,oCAAqCA,CAAK,CAC1D,CAGA,QAAQ,IAAI,oCAAoC,EAChD,GAAI,CACF,OAAO,MAAMC,EAAG,WAAW,UAAU,EAAE,OAAOoB,EAAI,CAChD,OAAQ,SACV,CAAC,CACH,OAASrB,EAAO,CACd,cAAQ,MAAM,mCAAoCA,CAAK,EACjD,IAAII,EAAS,IAAK,kDAAU,CACpC,CACF,CAEA,aAAa,cAAciB,EAAY,CACrC,GAAI,CACF,IAAMpB,EAAK,MAAMJ,EAAkB,WAAW,EAExC8C,EAAS,MAAM1C,EAAG,WAAW,UAAU,EAAE,OAAOoB,CAAE,EAExD,MAAM,QAAQ,IAAI,CAChBsB,EAAO,SAAWlC,EAAG,OAAOkC,EAAO,QAAQ,EAAE,MAAM,QAAQ,KAAK,EAAI,QAAQ,QAAQ,EACpF1C,EAAG,WAAW,UAAU,EAAE,OAAOoB,CAAE,CACrC,CAAC,CACH,OAASrB,EAAO,CACd,cAAQ,MAAM,4BAA6BA,CAAK,EAC1C,IAAII,EAAS,IAAK,sCAAQ,CAClC,CACF,CAEA,aAAa,aAAaiB,EAAYtB,EAAuB,CAC3D,GAAI,CAEF,OAAO,MADI,MAAMF,EAAkB,WAAW,GAC9B,WAAW,UAAU,EAAE,OAAOwB,EAAI,CAChD,YAAatB,EAAO,YACpB,cAAeA,EAAO,cACtB,OAAQA,EAAO,MACjB,CAAC,CACH,OAASC,EAAO,CACd,cAAQ,MAAM,2BAA4BA,CAAK,EACzC,IAAII,EAAS,IAAK,sCAAQ,CAClC,CACF,CAEA,aAAa,iBAAiBiB,EAA6B,CACzD,GAAI,CAEF,IAAMhB,EAAU,MADL,MAAMR,EAAkB,WAAW,GACrB,WAAW,UAAU,EAAE,OAAOwB,CAAE,EAGnDtB,EAAS,CACb,GAAIM,EAAQ,GACZ,KAAMA,EAAQ,KACd,YAAaA,EAAQ,YACrB,cAAeA,EAAQ,cACvB,YAAaA,EAAQ,YACrB,OAAQA,EAAQ,OAAO,OAAO,IAAKuC,IAAgB,CACjD,KAAMA,EAAM,KACZ,MAAOA,EAAM,MACb,IAAKA,EAAM,IACX,IAAKA,EAAM,IACX,OAAQA,EAAM,OACd,OAAQA,EAAM,OACd,QAASA,EAAM,QACf,QAASA,EAAM,OACjB,EAAE,CACJ,EAGA,OAAO,MAAMC,EAAgB,kBAAkB9C,CAAM,CACvD,OAASC,EAAO,CACd,cAAQ,MAAM,+BAAgCA,CAAK,EAC7C,IAAII,EAAS,IAAK,sCAAQ,CAClC,CACF,CAEA,aAAa,eAAeiB,EAAYyB,EAA0BxC,EAAe,CAC/E,GAAI,CACF,IAAML,EAAK,MAAMJ,EAAkB,WAAW,EAGxCkD,EAAiB,MAAM9C,EAAG,WAAW,UAAU,EAAE,OAAOoB,CAAE,EAGhE,GAAI0B,EAAe,SAAW,UAC5B,MAAM,IAAI3C,EAAS,IAAK,8GAAoB,EAG9C,IAAMG,EAAYC,EAAK,KAAK,QAAQ,IAAI,EAAG,SAAS,EACpD,MAAMC,EAAG,MAAMF,EAAW,CAAE,UAAW,EAAK,CAAC,EAG7C,IAAMyC,EAAcD,EAAe,SAEnC,GAAI,CAEF,MAAMtC,EAAG,OAAOuC,CAAW,CAC7B,OAAShD,EAAO,CACd,QAAQ,MAAM,gCAAiCA,CAAK,CACtD,CAGA,aAAM8C,EAAW,GAAGE,CAAW,EAG/B,MAAMvC,EAAG,MAAMuC,EAAa,GAAK,EAG1B,MAAM/C,EAAG,WAAW,UAAU,EAAE,OAAOoB,EAAI,CAChD,WAAY,IAAI,KAAK,EAAE,YAAY,EACnC,YAAaf,EAAS,aAAa,SAAS,GAAKyC,EAAe,YAChE,cAAezC,EAAS,eAAe,SAAS,GAAKyC,EAAe,cACpE,YAAazC,EAAS,aAAeyC,EAAe,YAEpD,OAAQA,EAAe,OACvB,SAAUC,CACZ,CAAC,CAEH,OAAShD,EAAO,CAEd,MADA,QAAQ,MAAM,6BAA8BA,CAAK,EAC7CA,aAAiBI,EACbJ,EAEF,IAAII,EAAS,IAAK,sCAAQ,CAClC,CACF,CAEA,aAAa,cAAc6C,EAAkB9B,EAAkBb,EAAe,CAC5E,GAAI,CAIF,OAAO,MAHI,MAAMT,EAAkB,WAAW,GAG9B,WAAW,UAAU,EAAE,OAAO,CAC5C,KAAMoD,EACN,OAAQ,UACR,WAAY,IAAI,KAAK,EAAE,YAAY,EACnC,YAAa3C,EAAS,aAAa,SAAS,GAAK,IACjD,cAAeA,EAAS,eAAe,SAAS,GAAK,OACrD,OAAQA,EAAS,OACjB,SAAUa,CACZ,CAAC,CACH,OAASnB,EAAO,CAEd,MADA,QAAQ,MAAM,4BAA6BA,CAAK,EAC5CA,aAAiBI,EACbJ,EAEF,IAAII,EAAS,IAAK,sCAAQ,CAClC,CACF,CAEA,aAAa,WAAWiB,EAA2C,CACjE,GAAI,CAEF,IAAMsB,EAAS,MADJ,MAAM9C,EAAkB,WAAW,GACtB,WAAW,UAAU,EAAE,OAAOwB,CAAE,EACxD,MAAO,CACL,GAAIsB,EAAO,GACX,KAAMA,EAAO,KACb,OAAQA,EAAO,MACjB,CACF,OAAS3C,EAAO,CACd,eAAQ,MAAM,yBAA0BA,CAAK,EACtC,IACT,CACF,CAEA,OAAO,eAAwB,CAC7B,OAAOQ,EAAK,KAAK,QAAQ,IAAI,EAAG,UAAU,CAC5C,CAEA,aAAa,oBAAoB0C,EAAoC,CACnE,GAAI,CAGF,IAAMP,EAAS,MADJ,MAAM9C,EAAkB,WAAW,GACtB,WAAW,UAAU,EAAE,OAAOqD,CAAS,EAC/D,GAAI,CAACP,EACH,MAAM,IAAIvC,EAAS,IAAK,gCAAO,EAIjC,IAAM+C,EAAU1D,GAAS,MAAO,CAC9B,KAAM,CAAE,MAAO,CAAE,CACnB,CAAC,EAoBD,OAjBkB,MAAM,IAAI,QAAgB,CAACoC,EAASuB,IAAW,CAC/D,IAAMC,EAAmB,CAAC,EAC1BF,EAAQ,GAAG,OAASG,GAAkBD,EAAO,KAAKC,CAAK,CAAC,EACxDH,EAAQ,GAAG,MAAO,IAAMtB,EAAQ,OAAO,OAAOwB,CAAM,CAAC,CAAC,EACtDF,EAAQ,GAAG,QAASC,CAAM,EAG1B,IAAMG,EAAa,KAAK,UAAUZ,EAAO,OAAQ,KAAM,CAAC,EACxDQ,EAAQ,OAAOI,EAAY,CAAE,KAAM,mBAAoB,CAAC,EAGxDJ,EAAQ,KAAKR,EAAO,SAAU,CAAE,KAAMA,EAAO,IAAK,CAAC,EAGnDQ,EAAQ,SAAS,CACnB,CAAC,CAGH,OAASnD,EAAO,CAEd,MADA,QAAQ,MAAM,oCAAqCA,CAAK,EACpDA,aAAiBI,EACbJ,EAEF,IAAII,EAAS,IAAK,4CAAS,CACnC,CACF,CACF,EFxjBA,IAAMoD,EAASC,GAAO,EAEtBD,EAAO,KAAK,SAAUE,EAAa,MAAOC,EAAcC,IAAkB,CACxE,GAAM,CAAE,SAAAC,EAAU,SAAAC,CAAS,EAAIH,EAAI,KAC7BI,EAAK,IAAIC,GAAWC,EAAO,aAAa,EAE9C,GAAI,CACF,IAAMC,EAAW,MAAMH,EAAG,WAAW,OAAO,EAAE,iBAAiBF,EAAUC,CAAQ,EAGjFK,EAAkB,aAAaD,EAAS,KAAK,EAE7CN,EAAI,KAAK,CACP,OAAQ,IACR,KAAM,CACJ,MAAOM,EAAS,MAChB,KAAMA,EAAS,MACjB,CACF,CAAC,CACH,OAASE,EAAO,CACd,QAAQ,MAAM,eAAgBA,CAAK,EACnCR,EAAI,OAAO,GAAG,EAAE,KAAK,CACnB,OAAQ,IACR,QAAS,eACT,MAAOQ,EAAM,UAAU,MAAQA,EAAM,OACvC,CAAC,CACH,CACF,CAAC,CAAC,EAEFZ,EAAO,KAAK,UAAWE,EAAa,MAAOC,EAAcC,IAAkB,CACzEO,EAAkB,UAAU,EAE5BP,EAAI,KAAK,CACP,OAAQ,IACR,KAAM,CAAE,QAAS,EAAK,CACxB,CAAC,CACH,CAAC,CAAC,EAEFJ,EAAO,KAAK,uBAAwBE,EAAa,MAAOC,EAAcC,IAAkB,CACtF,GAAM,CAAE,GAAAS,CAAG,EAAIV,EAAI,OACb,CAAE,YAAAW,EAAa,SAAAR,EAAU,gBAAAS,CAAgB,EAAIZ,EAAI,KAEvD,GAAI,CAGF,MAFW,MAAMQ,EAAkB,WAAW,GAErC,WAAW,OAAO,EAAE,OAAOE,EAAI,CACtC,YAAAC,EACA,SAAAR,EACA,gBAAAS,CACF,CAAC,EAEDX,EAAI,KAAK,CACP,OAAQ,IACR,QAAS,sCACX,CAAC,CACH,OAASQ,EAAO,CACd,QAAQ,MAAM,6BAA8BA,CAAK,EACjDR,EAAI,OAAO,GAAG,EAAE,KAAK,CACnB,OAAQ,IACR,QAAS,uCACT,MAAOQ,EAAM,UAAU,MAAQA,EAAM,OACvC,CAAC,CACH,CACF,CAAC,CAAC,EAEF,IAAOI,GAAQhB,EIvEf,OAAS,UAAAiB,OAAc,UAKvB,OAAOC,MAAU,OAEjB,OAAS,YAAYC,MAAkB,KACvC,OAAS,qBAAAC,OAAyB,KAElC,IAAMC,EAASC,GAAO,EAGtBD,EAAO,IAAI,IAAKE,EAAa,MAAOC,EAAcC,IAAkB,CAClE,IAAMC,EAAW,MAAMC,EAAe,YAAY,EAClDF,EAAI,KAAK,CACP,OAAQ,IACR,KAAMC,CACR,CAAC,CACH,CAAC,CAAC,EAGFL,EAAO,KAAK,IAAKE,EAAa,MAAOC,EAAcC,IAAkB,CACnE,GAAI,CAACD,EAAI,OAAS,CAACA,EAAI,MAAM,SAAW,CAACA,EAAI,MAAM,OACjD,MAAM,IAAI,MAAM,wBAAwB,EAG1C,IAAMI,EAAUJ,EAAI,MAAM,QACpBK,EAASL,EAAI,MAAM,OACnBM,EAAWN,EAAI,KAEfO,EAAS,MAAMJ,EAAe,cAAcC,EAASC,EAAQC,CAAQ,EAC3EL,EAAI,KAAK,CACP,OAAQ,IACR,KAAMM,CACR,CAAC,CACH,CAAC,CAAC,EAEFV,EAAO,KAAK,aAAcE,EAAa,MAAOC,EAAcC,IAAkB,CAC5E,MAAME,EAAe,aAAaH,EAAI,OAAO,EAAE,EAC/CC,EAAI,KAAK,CACP,OAAQ,IACR,KAAM,IACR,CAAC,CACH,CAAC,CAAC,EAEFJ,EAAO,KAAK,YAAaE,EAAa,MAAOC,EAAcC,IAAkB,CAC3E,MAAME,EAAe,YAAYH,EAAI,OAAO,EAAE,EAC9CC,EAAI,KAAK,CACP,OAAQ,IACR,KAAM,IACR,CAAC,CACH,CAAC,CAAC,EAEFJ,EAAO,OAAO,OAAQE,EAAa,MAAOC,EAAcC,IAAkB,CACxE,MAAME,EAAe,cAAcH,EAAI,OAAO,EAAE,EAChDC,EAAI,KAAK,CACP,OAAQ,IACR,KAAM,IACR,CAAC,CACH,CAAC,CAAC,EAEFJ,EAAO,IAAI,cAAeE,EAAa,MAAOC,EAAcC,IAAkB,CAC5E,IAAMM,EAAS,MAAMJ,EAAe,aAAaH,EAAI,OAAO,GAAIA,EAAI,IAAI,EACxEC,EAAI,KAAK,CACP,OAAQ,IACR,KAAMM,CACR,CAAC,CACH,CAAC,CAAC,EAEFV,EAAO,IAAI,gBAAiBE,EAAa,MAAOC,EAAcC,IAAkB,CAC9E,GAAM,CAAE,GAAAO,CAAG,EAAIR,EAAI,OACbS,EAAW,MAAMN,EAAe,iBAAiBK,CAAE,EAGzDP,EAAI,UAAU,eAAgB,YAAY,EAC1CA,EAAI,UAAU,sBAAuB,iCAAiCO,CAAE,IAAI,EAC5EP,EAAI,KAAKQ,CAAQ,CACnB,CAAC,CAAC,EAEFZ,EAAO,KAAK,eAAgBE,EAAa,MAAOC,EAAcC,IAAkB,CAC9E,GAAM,CAAE,GAAAO,CAAG,EAAIR,EAAI,OACbI,EAAUJ,EAAI,OAAO,QACrBM,EAAWN,EAAI,KAErB,GAAI,CAACI,EACH,MAAM,IAAI,SAAS,IAAK,4CAAS,EAGnC,IAAMG,EAAS,MAAMJ,EAAe,eAAeK,EAAIJ,EAASE,CAAQ,EACxEL,EAAI,KAAK,CACP,OAAQ,IACR,KAAMM,CACR,CAAC,CACH,CAAC,CAAC,EAGFV,EAAO,KAAK,SAAUE,EAAa,MAAOC,EAAcC,IAAkB,CACxE,GAAI,CAACD,EAAI,OAAO,MACd,MAAM,IAAI,MAAM,eAAe,EAGjC,IAAMU,EAAQV,EAAI,MAAM,MAClB,CAAE,KAAAW,EAAM,SAAAC,EAAU,MAAAC,CAAM,EAAIb,EAAI,KAGhCc,EAAWpB,EAAK,KAAK,QAAQ,IAAI,EAAG,UAAW,SAAUiB,CAAI,EACnE,MAAMhB,EAAW,MAAMmB,EAAU,CAAE,UAAW,EAAK,CAAC,EACpD,MAAMJ,EAAM,GAAGhB,EAAK,KAAKoB,EAAUD,CAAK,CAAC,EAEzCZ,EAAI,KAAK,CAAE,OAAQ,GAAI,CAAC,CAC1B,CAAC,CAAC,EAGFJ,EAAO,KAAK,SAAUE,EAAa,MAAOC,EAAcC,IAAkB,CACxE,GAAI,CAACD,EAAI,OAAO,OACd,MAAM,IAAI,MAAM,qBAAqB,EAGvC,IAAMe,EAAaf,EAAI,MAAM,OACvB,CAAE,SAAAY,EAAU,KAAAD,EAAM,KAAAK,EAAM,YAAAC,EAAa,cAAAC,CAAc,EAAIlB,EAAI,KAG7DmB,EACJ,GAAI,CACF,IAAMC,EAAYL,EAAW,KAAK,SAAS,OAAO,EAClDI,EAAa,KAAK,MAAMC,CAAS,CACnC,OAASC,EAAO,CACd,cAAQ,MAAM,0BAA2BA,CAAK,EACxC,IAAI,SAAS,IAAK,kDAAU,CACpC,CAGA,IAAMP,EAAWpB,EAAK,KAAK,QAAQ,IAAI,EAAG,UAAW,SAAUiB,CAAI,EAC7DW,EAAW5B,EAAK,KAAK,QAAQ,IAAI,EAAG,UAAWkB,CAAQ,EAE7D,GAAI,CAEF,IAAMW,EAAc3B,GAAkB0B,CAAQ,EAE9C,QAASE,EAAI,EAAGA,EAAIR,EAAMQ,IAAK,CAC7B,IAAMC,EAAY/B,EAAK,KAAKoB,EAAUU,EAAE,SAAS,CAAC,EAC5CE,EAAY,MAAM/B,EAAW,SAAS8B,CAAS,EACrDF,EAAY,MAAMG,CAAS,CAC7B,CAGA,MAAM,IAAI,QAAQ,CAACC,EAASC,IAAW,CACrCL,EAAY,GAAG,SAAUI,CAAO,EAChCJ,EAAY,GAAG,QAASK,CAAM,EAC9BL,EAAY,IAAI,CAClB,CAAC,EAGD,GAAI,CACF,MAAM5B,EAAW,GAAGmB,EAAU,CAAE,UAAW,GAAM,MAAO,EAAK,CAAC,CAChE,MAAgB,CACd,QAAQ,IAAI,oCAAoC,CAClD,CAGA,IAAMP,EAAS,MAAMJ,EAAe,cAAcS,EAAUU,EAAU,CACpE,YAAAL,EACA,cAAAC,EACA,OAAQC,CACV,CAAC,EAEDlB,EAAI,KAAK,CAAE,OAAQ,IAAK,KAAMM,CAAO,CAAC,CACxC,OAASc,EAAO,CAEd,GAAI,CACF,MAAM1B,EAAW,OAAO2B,CAAQ,EAC5B,MAAM3B,EAAW,OAAOmB,CAAQ,EAAE,KAAK,IAAM,EAAI,EAAE,MAAM,IAAM,EAAK,GACtE,MAAMnB,EAAW,GAAGmB,EAAU,CAAE,UAAW,GAAM,MAAO,EAAK,CAAC,CAElE,OAASe,EAAc,CACrB,QAAQ,MAAM,iBAAkBA,CAAY,CAC9C,CACA,MAAMR,CACR,CACF,CAAC,CAAC,EAGFxB,EAAO,IAAI,eAAgBE,EAAa,MAAOC,EAAcC,IAAkB,CAC7E,GAAM,CAAE,GAAAO,CAAG,EAAIR,EAAI,OACb8B,EAAgB,MAAM3B,EAAe,oBAAoBK,CAAE,EAEjEP,EAAI,UAAU,eAAgB,iBAAiB,EAC/CA,EAAI,UAAU,sBAAuB,0CAA0C,EAC/EA,EAAI,KAAK6B,CAAa,CACxB,CAAC,CAAC,EAEF,IAAOC,GAAQlC,EChMf,OAAS,UAAAmC,OAAiC,UCM1C,IAAMC,EACJ,OAAO,aAAgB,UACvB,aACA,OAAO,YAAY,KAAQ,WACvB,YACA,KAEAC,GAAS,IAAI,IAMbC,GACJ,OAAO,SAAY,UAAc,QAAU,QAAU,CAAA,EAIjDC,GAAc,CAClBC,EACAC,EACAC,EACAC,IACE,CACF,OAAOL,GAAQ,aAAgB,WAC3BA,GAAQ,YAAYE,EAAKC,EAAMC,EAAMC,CAAE,EACvC,QAAQ,MAAM,IAAID,CAAI,KAAKD,CAAI,KAAKD,CAAG,EAAE,CAC/C,EAEII,EAAK,WAAW,gBAChBC,GAAK,WAAW,YAGpB,GAAI,OAAOD,EAAO,IAAa,CAE7BC,GAAK,KAAiB,CACpB,QACA,SAAqC,CAAA,EACrC,OACA,QAAmB,GACnB,iBAAiBC,EAAWH,EAAwB,CAClD,KAAK,SAAS,KAAKA,CAAE,CACvB,GAGFC,EAAK,KAAqB,CACxB,aAAA,CACEG,EAAc,CAChB,CACA,OAAS,IAAIF,GACb,MAAMG,EAAW,CACf,GAAI,MAAK,OAAO,QAEhB,MAAK,OAAO,OAASA,EAErB,KAAK,OAAO,QAAU,GAEtB,QAAWL,KAAM,KAAK,OAAO,SAC3BA,EAAGK,CAAM,EAEX,KAAK,OAAO,UAAUA,CAAM,EAC9B,GAEF,IAAIC,EACFX,GAAQ,KAAK,8BAAgC,IACzCS,EAAiB,IAAK,CACrBE,IACLA,EAAyB,GACzBV,GACE,maAOA,sBACA,UACAQ,CAAc,EAElB,EAIF,IAAMG,GAAcR,GAAiB,CAACL,GAAO,IAAIK,CAAI,EAE/CS,GAAO,OAAO,MAAM,EAIpBC,EAAYC,GAChBA,GAAKA,IAAM,KAAK,MAAMA,CAAC,GAAKA,EAAI,GAAK,SAASA,CAAC,EAc3CC,GAAgBC,GACnBH,EAASG,CAAG,EAETA,GAAO,KAAK,IAAI,EAAG,CAAC,EACpB,WACAA,GAAO,KAAK,IAAI,EAAG,EAAE,EACrB,YACAA,GAAO,KAAK,IAAI,EAAG,EAAE,EACrB,YACAA,GAAO,OAAO,iBACdC,EACA,KATA,KAYAA,EAAN,cAAwB,KAAa,CACnC,YAAYC,EAAY,CACtB,MAAMA,CAAI,EACV,KAAK,KAAK,CAAC,CACb,GAMIC,GAAN,MAAMC,CAAK,CACT,KACA,OAEA,MAAOC,GAAyB,GAChC,OAAO,OAAOL,EAAW,CACvB,IAAMM,EAAUP,GAAaC,CAAG,EAChC,GAAI,CAACM,EAAS,MAAO,CAAA,EACrBF,EAAMC,GAAgB,GACtB,IAAM,EAAI,IAAID,EAAMJ,EAAKM,CAAO,EAChC,OAAAF,EAAMC,GAAgB,GACf,CACT,CACA,YACEL,EACAM,EAAyC,CAGzC,GAAI,CAACF,EAAMC,GACT,MAAM,IAAI,UAAU,yCAAyC,EAG/D,KAAK,KAAO,IAAIC,EAAQN,CAAG,EAC3B,KAAK,OAAS,CAChB,CACA,KAAKF,EAAQ,CACX,KAAK,KAAK,KAAK,QAAQ,EAAIA,CAC7B,CACA,KAAG,CACD,OAAO,KAAK,KAAK,EAAE,KAAK,MAAM,CAChC,GAu7BWS,EAAP,MAAOC,CAAQ,CAIVC,GACAC,GACAC,GACAC,GACAC,GACAC,GAKT,IAKA,cAIA,aAIA,eAIA,eAIA,WAKA,eAIA,YAIA,aAIA,gBAIA,yBAIA,mBAIA,uBAIA,2BAIA,iBAGAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GAEAC,GACAC,GACAC,GAWA,OAAO,sBAILC,EAAqB,CACrB,MAAO,CAEL,OAAQA,EAAEL,GACV,KAAMK,EAAEJ,GACR,MAAOI,EAAEN,GACT,OAAQM,EAAEf,GACV,QAASe,EAAEd,GACX,QAASc,EAAEb,GACX,KAAMa,EAAEZ,GACR,KAAMY,EAAEX,GACR,IAAI,MAAI,CACN,OAAOW,EAAEV,EACX,EACA,IAAI,MAAI,CACN,OAAOU,EAAET,EACX,EACA,KAAMS,EAAER,GAER,kBAAoBS,GAAWD,EAAEE,GAAmBD,CAAC,EACrD,gBAAiB,CACfE,EACAC,EACAC,EACAC,IAEAN,EAAEO,GACAJ,EACAC,EACAC,EACAC,CAAO,EAEX,WAAaF,GACXJ,EAAEQ,GAAYJ,CAAc,EAC9B,QAAUC,GACRL,EAAES,GAASJ,CAAO,EACpB,SAAWA,GACTL,EAAEU,GAAUL,CAAO,EACrB,QAAUD,GACRJ,EAAEW,GAASP,CAAc,EAE/B,CAOA,IAAI,KAAG,CACL,OAAO,KAAK3B,EACd,CAIA,IAAI,SAAO,CACT,OAAO,KAAKC,EACd,CAIA,IAAI,gBAAc,CAChB,OAAO,KAAKM,EACd,CAIA,IAAI,MAAI,CACN,OAAO,KAAKD,EACd,CAIA,IAAI,aAAW,CACb,OAAO,KAAKF,EACd,CACA,IAAI,YAAU,CACZ,OAAO,KAAKC,EACd,CAIA,IAAI,SAAO,CACT,OAAO,KAAKH,EACd,CAIA,IAAI,cAAY,CACd,OAAO,KAAKC,EACd,CAEA,YACEyB,EAAwD,CAExD,GAAM,CACJ,IAAArC,EAAM,EACN,IAAA4C,EACA,cAAAC,EAAgB,EAChB,aAAAC,EACA,eAAAC,EACA,eAAAC,EACA,WAAAC,EACA,QAAAC,EACA,aAAAC,EACA,eAAAC,EACA,YAAAC,EACA,QAAAC,EAAU,EACV,aAAAC,EAAe,EACf,gBAAAC,EACA,YAAAC,EACA,WAAAC,EACA,yBAAAC,EACA,mBAAAC,EACA,2BAAAC,EACA,uBAAAC,EACA,iBAAAC,CAAgB,EACd1B,EAEJ,GAAIrC,IAAQ,GAAK,CAACH,EAASG,CAAG,EAC5B,MAAM,IAAI,UAAU,0CAA0C,EAGhE,IAAMgE,EAAYhE,EAAMD,GAAaC,CAAG,EAAI,MAC5C,GAAI,CAACgE,EACH,MAAM,IAAI,MAAM,sBAAwBhE,CAAG,EAO7C,GAJA,KAAKS,GAAOT,EACZ,KAAKU,GAAW4C,EAChB,KAAK,aAAeC,GAAgB,KAAK7C,GACzC,KAAK,gBAAkB8C,EACnB,KAAK,gBAAiB,CACxB,GAAI,CAAC,KAAK9C,IAAY,CAAC,KAAK,aAC1B,MAAM,IAAI,UACR,oEAAoE,EAGxE,GAAI,OAAO,KAAK,iBAAoB,WAClC,MAAM,IAAI,UAAU,qCAAqC,EAI7D,GACEgD,IAAe,QACf,OAAOA,GAAe,WAEtB,MAAM,IAAI,UAAU,0CAA0C,EAIhE,GAFA,KAAK5C,GAAc4C,EAGjBD,IAAgB,QAChB,OAAOA,GAAgB,WAEvB,MAAM,IAAI,UACR,6CAA6C,EAsCjD,GAnCA,KAAK5C,GAAe4C,EACpB,KAAK3B,GAAkB,CAAC,CAAC2B,EAEzB,KAAKxC,GAAU,IAAI,IACnB,KAAKC,GAAW,IAAI,MAAMlB,CAAG,EAAE,KAAK,MAAS,EAC7C,KAAKmB,GAAW,IAAI,MAAMnB,CAAG,EAAE,KAAK,MAAS,EAC7C,KAAKoB,GAAQ,IAAI4C,EAAUhE,CAAG,EAC9B,KAAKqB,GAAQ,IAAI2C,EAAUhE,CAAG,EAC9B,KAAKsB,GAAQ,EACb,KAAKC,GAAQ,EACb,KAAKC,GAAQrB,GAAM,OAAOH,CAAG,EAC7B,KAAKe,GAAQ,EACb,KAAKC,GAAkB,EAEnB,OAAOkC,GAAY,aACrB,KAAKvC,GAAWuC,GAEd,OAAOC,GAAiB,YAC1B,KAAKvC,GAAgBuC,EACrB,KAAK1B,GAAY,CAAA,IAEjB,KAAKb,GAAgB,OACrB,KAAKa,GAAY,QAEnB,KAAKI,GAAc,CAAC,CAAC,KAAKlB,GAC1B,KAAKoB,GAAmB,CAAC,CAAC,KAAKnB,GAE/B,KAAK,eAAiB,CAAC,CAACwC,EACxB,KAAK,YAAc,CAAC,CAACC,EACrB,KAAK,yBAA2B,CAAC,CAACM,EAClC,KAAK,2BAA6B,CAAC,CAACE,EACpC,KAAK,uBAAyB,CAAC,CAACC,EAChC,KAAK,iBAAmB,CAAC,CAACC,EAGtB,KAAK,eAAiB,EAAG,CAC3B,GAAI,KAAKrD,KAAa,GAChB,CAACb,EAAS,KAAKa,EAAQ,EACzB,MAAM,IAAI,UACR,iDAAiD,EAIvD,GAAI,CAACb,EAAS,KAAK,YAAY,EAC7B,MAAM,IAAI,UACR,sDAAsD,EAG1D,KAAKoE,GAAuB,EAa9B,GAVA,KAAK,WAAa,CAAC,CAAChB,EACpB,KAAK,mBAAqB,CAAC,CAACW,EAC5B,KAAK,eAAiB,CAAC,CAACb,EACxB,KAAK,eAAiB,CAAC,CAACC,EACxB,KAAK,cACHnD,EAASgD,CAAa,GAAKA,IAAkB,EACzCA,EACA,EACN,KAAK,aAAe,CAAC,CAACC,EACtB,KAAK,IAAMF,GAAO,EACd,KAAK,IAAK,CACZ,GAAI,CAAC/C,EAAS,KAAK,GAAG,EACpB,MAAM,IAAI,UACR,6CAA6C,EAGjD,KAAKqE,GAAsB,EAI7B,GAAI,KAAKzD,KAAS,GAAK,KAAK,MAAQ,GAAK,KAAKC,KAAa,EACzD,MAAM,IAAI,UACR,kDAAkD,EAGtD,GAAI,CAAC,KAAK,cAAgB,CAAC,KAAKD,IAAQ,CAAC,KAAKC,GAAU,CACtD,IAAMvB,EAAO,sBACTQ,GAAWR,CAAI,IACjBL,GAAO,IAAIK,CAAI,EAIfH,GAFE,gGAEe,wBAAyBG,EAAMqB,CAAQ,GAG9D,CAMA,gBAAgB2D,EAAM,CACpB,OAAO,KAAKlD,GAAQ,IAAIkD,CAAG,EAAI,IAAW,CAC5C,CAEAD,IAAsB,CACpB,IAAME,EAAO,IAAInE,EAAU,KAAKQ,EAAI,EAC9B4D,EAAS,IAAIpE,EAAU,KAAKQ,EAAI,EACtC,KAAKmB,GAAQwC,EACb,KAAKzC,GAAU0C,EAEf,KAAKC,GAAc,CAAClC,EAAOQ,EAAK2B,EAAQ1F,EAAK,IAAG,IAAM,CAGpD,GAFAwF,EAAOjC,CAAK,EAAIQ,IAAQ,EAAI2B,EAAQ,EACpCH,EAAKhC,CAAK,EAAIQ,EACVA,IAAQ,GAAK,KAAK,aAAc,CAClC,IAAM4B,EAAI,WAAW,IAAK,CACpB,KAAK7B,GAASP,CAAK,GACrB,KAAKqC,GAAQ,KAAKvD,GAASkB,CAAK,EAAQ,QAAQ,CAEpD,EAAGQ,EAAM,CAAC,EAGN4B,EAAE,OACJA,EAAE,MAAK,EAIb,EAEA,KAAKE,GAAiBtC,GAAQ,CAC5BiC,EAAOjC,CAAK,EAAIgC,EAAKhC,CAAK,IAAM,EAAIvD,EAAK,IAAG,EAAK,CACnD,EAEA,KAAK8F,GAAa,CAACC,EAAQxC,IAAS,CAClC,GAAIgC,EAAKhC,CAAK,EAAG,CACf,IAAMQ,EAAMwB,EAAKhC,CAAK,EAChBmC,EAAQF,EAAOjC,CAAK,EAE1B,GAAI,CAACQ,GAAO,CAAC2B,EAAO,OACpBK,EAAO,IAAMhC,EACbgC,EAAO,MAAQL,EACfK,EAAO,IAAMC,GAAaC,EAAM,EAChC,IAAMC,EAAMH,EAAO,IAAML,EACzBK,EAAO,aAAehC,EAAMmC,EAEhC,EAIA,IAAIF,EAAY,EACVC,EAAS,IAAK,CAClB,IAAM,EAAIjG,EAAK,IAAG,EAClB,GAAI,KAAK,cAAgB,EAAG,CAC1BgG,EAAY,EACZ,IAAML,EAAI,WACR,IAAOK,EAAY,EACnB,KAAK,aAAa,EAIhBL,EAAE,OACJA,EAAE,MAAK,EAIX,OAAO,CACT,EAEA,KAAK,gBAAkBL,GAAM,CAC3B,IAAM/B,EAAQ,KAAKnB,GAAQ,IAAIkD,CAAG,EAClC,GAAI/B,IAAU,OACZ,MAAO,GAET,IAAMQ,EAAMwB,EAAKhC,CAAK,EAChBmC,EAAQF,EAAOjC,CAAK,EAC1B,GAAI,CAACQ,GAAO,CAAC2B,EACX,MAAO,KAET,IAAMQ,GAAOF,GAAaC,EAAM,GAAMP,EACtC,OAAO3B,EAAMmC,CACf,EAEA,KAAKpC,GAAWP,GAAQ,CACtB,IAAM4C,EAAIX,EAAOjC,CAAK,EAChBoC,EAAIJ,EAAKhC,CAAK,EACpB,MAAO,CAAC,CAACoC,GAAK,CAAC,CAACQ,IAAMH,GAAaC,EAAM,GAAME,EAAIR,CACrD,CACF,CAGAE,GAAyC,IAAK,CAAE,EAChDC,GACE,IAAK,CAAE,EACTL,GAMY,IAAK,CAAE,EAGnB3B,GAAsC,IAAM,GAE5CsB,IAAuB,CACrB,IAAMgB,EAAQ,IAAIhF,EAAU,KAAKQ,EAAI,EACrC,KAAKO,GAAkB,EACvB,KAAKU,GAASuD,EACd,KAAKC,GAAkB9C,GAAQ,CAC7B,KAAKpB,IAAmBiE,EAAM7C,CAAK,EACnC6C,EAAM7C,CAAK,EAAI,CACjB,EACA,KAAK+C,GAAe,CAAChD,EAAGiD,EAAGlF,EAAMsD,IAAmB,CAGlD,GAAI,KAAKtB,GAAmBkD,CAAC,EAC3B,MAAO,GAET,GAAI,CAACvF,EAASK,CAAI,EAChB,GAAIsD,EAAiB,CACnB,GAAI,OAAOA,GAAoB,WAC7B,MAAM,IAAI,UAAU,oCAAoC,EAG1D,GADAtD,EAAOsD,EAAgB4B,EAAGjD,CAAC,EACvB,CAACtC,EAASK,CAAI,EAChB,MAAM,IAAI,UACR,0DAA0D,MAI9D,OAAM,IAAI,UACR,2HAEwB,EAI9B,OAAOA,CACT,EACA,KAAKmF,GAAe,CAClBjD,EACAlC,EACA0E,IACE,CAEF,GADAK,EAAM7C,CAAK,EAAIlC,EACX,KAAKQ,GAAU,CACjB,IAAM4C,EAAU,KAAK5C,GAAYuE,EAAM7C,CAAK,EAC5C,KAAO,KAAKpB,GAAkBsC,GAC5B,KAAKgC,GAAO,EAAI,EAGpB,KAAKtE,IAAmBiE,EAAM7C,CAAK,EAC/BwC,IACFA,EAAO,UAAY1E,EACnB0E,EAAO,oBAAsB,KAAK5D,GAEtC,CACF,CAEAkE,GAA0CK,GAAK,CAAE,EACjDF,GAIY,CAACE,EAAIC,EAAIC,IAAO,CAAE,EAC9BN,GAKqB,CACnBO,EACAC,EACAzF,EACAsD,IACE,CACF,GAAItD,GAAQsD,EACV,MAAM,IAAI,UACR,kEAAkE,EAGtE,MAAO,EACT,EAEA,CAACf,GAAS,CAAE,WAAAQ,EAAa,KAAK,UAAU,EAAK,CAAA,EAAE,CAC7C,GAAI,KAAKlC,GACP,QAAS6E,EAAI,KAAKrE,GACZ,GAAC,KAAKsE,GAAcD,CAAC,KAGrB3C,GAAc,CAAC,KAAKN,GAASiD,CAAC,KAChC,MAAMA,GAEJA,IAAM,KAAKtE,MAGbsE,EAAI,KAAKvE,GAAMuE,CAAC,CAIxB,CAEA,CAAClD,GAAU,CAAE,WAAAO,EAAa,KAAK,UAAU,EAAK,CAAA,EAAE,CAC9C,GAAI,KAAKlC,GACP,QAAS6E,EAAI,KAAKtE,GACZ,GAAC,KAAKuE,GAAcD,CAAC,KAGrB3C,GAAc,CAAC,KAAKN,GAASiD,CAAC,KAChC,MAAMA,GAEJA,IAAM,KAAKrE,MAGbqE,EAAI,KAAKxE,GAAMwE,CAAC,CAIxB,CAEAC,GAAczD,EAAY,CACxB,OACEA,IAAU,QACV,KAAKnB,GAAQ,IAAI,KAAKC,GAASkB,CAAK,CAAM,IAAMA,CAEpD,CAMA,CAAC,SAAO,CACN,QAAWwD,KAAK,KAAKnD,GAAQ,EAEzB,KAAKtB,GAASyE,CAAC,IAAM,QACrB,KAAK1E,GAAS0E,CAAC,IAAM,QACrB,CAAC,KAAK1D,GAAmB,KAAKf,GAASyE,CAAC,CAAC,IAEzC,KAAM,CAAC,KAAK1E,GAAS0E,CAAC,EAAG,KAAKzE,GAASyE,CAAC,CAAC,EAG/C,CAQA,CAAC,UAAQ,CACP,QAAWA,KAAK,KAAKlD,GAAS,EAE1B,KAAKvB,GAASyE,CAAC,IAAM,QACrB,KAAK1E,GAAS0E,CAAC,IAAM,QACrB,CAAC,KAAK1D,GAAmB,KAAKf,GAASyE,CAAC,CAAC,IAEzC,KAAM,CAAC,KAAK1E,GAAS0E,CAAC,EAAG,KAAKzE,GAASyE,CAAC,CAAC,EAG/C,CAMA,CAAC,MAAI,CACH,QAAWA,KAAK,KAAKnD,GAAQ,EAAI,CAC/B,IAAMN,EAAI,KAAKjB,GAAS0E,CAAC,EAEvBzD,IAAM,QACN,CAAC,KAAKD,GAAmB,KAAKf,GAASyE,CAAC,CAAC,IAEzC,MAAMzD,GAGZ,CAQA,CAAC,OAAK,CACJ,QAAWyD,KAAK,KAAKlD,GAAS,EAAI,CAChC,IAAMP,EAAI,KAAKjB,GAAS0E,CAAC,EAEvBzD,IAAM,QACN,CAAC,KAAKD,GAAmB,KAAKf,GAASyE,CAAC,CAAC,IAEzC,MAAMzD,GAGZ,CAMA,CAAC,QAAM,CACL,QAAWyD,KAAK,KAAKnD,GAAQ,EACjB,KAAKtB,GAASyE,CAAC,IAEjB,QACN,CAAC,KAAK1D,GAAmB,KAAKf,GAASyE,CAAC,CAAC,IAEzC,MAAM,KAAKzE,GAASyE,CAAC,EAG3B,CAQA,CAAC,SAAO,CACN,QAAWA,KAAK,KAAKlD,GAAS,EAClB,KAAKvB,GAASyE,CAAC,IAEjB,QACN,CAAC,KAAK1D,GAAmB,KAAKf,GAASyE,CAAC,CAAC,IAEzC,MAAM,KAAKzE,GAASyE,CAAC,EAG3B,CAMA,CAAC,OAAO,QAAQ,GAAC,CACf,OAAO,KAAK,QAAO,CACrB,CAOA,CAAC,OAAO,WAAW,EAAI,WAMvB,KACExG,EACA0G,EAA4C,CAAA,EAAE,CAE9C,QAAWF,KAAK,KAAKnD,GAAQ,EAAI,CAC/B,IAAM2C,EAAI,KAAKjE,GAASyE,CAAC,EACnBG,EAAQ,KAAK7D,GAAmBkD,CAAC,EACnCA,EAAE,qBACFA,EACJ,GAAIW,IAAU,QACV3G,EAAG2G,EAAO,KAAK7E,GAAS0E,CAAC,EAAQ,IAAI,EACvC,OAAO,KAAK,IAAI,KAAK1E,GAAS0E,CAAC,EAAQE,CAAU,EAGvD,CAaA,QACE1G,EACA4G,EAAa,KAAI,CAEjB,QAAWJ,KAAK,KAAKnD,GAAQ,EAAI,CAC/B,IAAM2C,EAAI,KAAKjE,GAASyE,CAAC,EACnBG,EAAQ,KAAK7D,GAAmBkD,CAAC,EACnCA,EAAE,qBACFA,EACAW,IAAU,QACd3G,EAAG,KAAK4G,EAAOD,EAAO,KAAK7E,GAAS0E,CAAC,EAAQ,IAAI,EAErD,CAMA,SACExG,EACA4G,EAAa,KAAI,CAEjB,QAAWJ,KAAK,KAAKlD,GAAS,EAAI,CAChC,IAAM0C,EAAI,KAAKjE,GAASyE,CAAC,EACnBG,EAAQ,KAAK7D,GAAmBkD,CAAC,EACnCA,EAAE,qBACFA,EACAW,IAAU,QACd3G,EAAG,KAAK4G,EAAOD,EAAO,KAAK7E,GAAS0E,CAAC,EAAQ,IAAI,EAErD,CAMA,YAAU,CACR,IAAIK,EAAU,GACd,QAAWL,KAAK,KAAKlD,GAAU,CAAE,WAAY,EAAI,CAAE,EAC7C,KAAKC,GAASiD,CAAC,IACjB,KAAKnB,GAAQ,KAAKvD,GAAS0E,CAAC,EAAQ,QAAQ,EAC5CK,EAAU,IAGd,OAAOA,CACT,CAcA,KAAK9B,EAAM,CACT,IAAMyB,EAAI,KAAK3E,GAAQ,IAAIkD,CAAG,EAC9B,GAAIyB,IAAM,OAAW,OACrB,IAAMR,EAAI,KAAKjE,GAASyE,CAAC,EACnBG,EAAuB,KAAK7D,GAAmBkD,CAAC,EAClDA,EAAE,qBACFA,EACJ,GAAIW,IAAU,OAAW,OACzB,IAAMG,EAA2B,CAAE,MAAAH,CAAK,EACxC,GAAI,KAAKnE,IAAS,KAAKD,GAAS,CAC9B,IAAMiB,EAAM,KAAKhB,GAAMgE,CAAC,EAClBrB,EAAQ,KAAK5C,GAAQiE,CAAC,EAC5B,GAAIhD,GAAO2B,EAAO,CAChB,IAAM4B,EAASvD,GAAO/D,EAAK,IAAG,EAAK0F,GACnC2B,EAAM,IAAMC,EACZD,EAAM,MAAQ,KAAK,IAAG,GAG1B,OAAI,KAAKxE,KACPwE,EAAM,KAAO,KAAKxE,GAAOkE,CAAC,GAErBM,CACT,CAeA,MAAI,CACF,IAAME,EAAgC,CAAA,EACtC,QAAWR,KAAK,KAAKnD,GAAS,CAAE,WAAY,EAAI,CAAE,EAAG,CACnD,IAAM0B,EAAM,KAAKjD,GAAS0E,CAAC,EACrBR,EAAI,KAAKjE,GAASyE,CAAC,EACnBG,EAAuB,KAAK7D,GAAmBkD,CAAC,EAClDA,EAAE,qBACFA,EACJ,GAAIW,IAAU,QAAa5B,IAAQ,OAAW,SAC9C,IAAM+B,EAA2B,CAAE,MAAAH,CAAK,EACxC,GAAI,KAAKnE,IAAS,KAAKD,GAAS,CAC9BuE,EAAM,IAAM,KAAKtE,GAAMgE,CAAC,EAGxB,IAAMb,EAAMlG,EAAK,IAAG,EAAM,KAAK8C,GAAQiE,CAAC,EACxCM,EAAM,MAAQ,KAAK,MAAM,KAAK,IAAG,EAAKnB,CAAG,EAEvC,KAAKrD,KACPwE,EAAM,KAAO,KAAKxE,GAAOkE,CAAC,GAE5BQ,EAAI,QAAQ,CAACjC,EAAK+B,CAAK,CAAC,EAE1B,OAAOE,CACT,CAWA,KAAKA,EAA6B,CAChC,KAAK,MAAK,EACV,OAAW,CAACjC,EAAK+B,CAAK,IAAKE,EAAK,CAC9B,GAAIF,EAAM,MAAO,CAOf,IAAMnB,EAAM,KAAK,IAAG,EAAKmB,EAAM,MAC/BA,EAAM,MAAQrH,EAAK,IAAG,EAAKkG,EAE7B,KAAK,IAAIZ,EAAK+B,EAAM,MAAOA,CAAK,EAEpC,CAgCA,IACE/D,EACAiD,EACAiB,EAA4C,CAAA,EAAE,CAE9C,GAAIjB,IAAM,OACR,YAAK,OAAOjD,CAAC,EACN,KAET,GAAM,CACJ,IAAAS,EAAM,KAAK,IACX,MAAA2B,EACA,eAAAnB,EAAiB,KAAK,eACtB,gBAAAI,EAAkB,KAAK,gBACvB,OAAAoB,CAAM,EACJyB,EACA,CAAE,YAAAhD,EAAc,KAAK,WAAW,EAAKgD,EAEnCnG,EAAO,KAAKiF,GAChBhD,EACAiD,EACAiB,EAAW,MAAQ,EACnB7C,CAAe,EAIjB,GAAI,KAAK,cAAgBtD,EAAO,KAAK,aACnC,OAAI0E,IACFA,EAAO,IAAM,OACbA,EAAO,qBAAuB,IAGhC,KAAKH,GAAQtC,EAAG,KAAK,EACd,KAET,IAAIC,EAAQ,KAAKrB,KAAU,EAAI,OAAY,KAAKE,GAAQ,IAAIkB,CAAC,EAC7D,GAAIC,IAAU,OAEZA,EACE,KAAKrB,KAAU,EACX,KAAKQ,GACL,KAAKC,GAAM,SAAW,EACtB,KAAKA,GAAM,IAAG,EACd,KAAKT,KAAU,KAAKN,GACpB,KAAK6E,GAAO,EAAK,EACjB,KAAKvE,GAEX,KAAKG,GAASkB,CAAK,EAAID,EACvB,KAAKhB,GAASiB,CAAK,EAAIgD,EACvB,KAAKnE,GAAQ,IAAIkB,EAAGC,CAAK,EACzB,KAAKhB,GAAM,KAAKG,EAAK,EAAIa,EACzB,KAAKf,GAAMe,CAAK,EAAI,KAAKb,GACzB,KAAKA,GAAQa,EACb,KAAKrB,KACL,KAAKsE,GAAajD,EAAOlC,EAAM0E,CAAM,EACjCA,IAAQA,EAAO,IAAM,OACzBvB,EAAc,OACT,CAEL,KAAKb,GAAYJ,CAAK,EACtB,IAAMkE,EAAS,KAAKnF,GAASiB,CAAK,EAClC,GAAIgD,IAAMkB,EAAQ,CAChB,GAAI,KAAKxE,IAAmB,KAAKI,GAAmBoE,CAAM,EAAG,CAC3DA,EAAO,kBAAkB,MAAM,IAAI,MAAM,UAAU,CAAC,EACpD,GAAM,CAAE,qBAAsBtB,CAAC,EAAKsB,EAChCtB,IAAM,QAAa,CAAC5B,IAClB,KAAKvB,IACP,KAAKlB,KAAWqE,EAAQ7C,EAAG,KAAK,EAE9B,KAAKJ,IACP,KAAKN,IAAW,KAAK,CAACuD,EAAQ7C,EAAG,KAAK,CAAC,QAGjCiB,IACN,KAAKvB,IACP,KAAKlB,KAAW2F,EAAanE,EAAG,KAAK,EAEnC,KAAKJ,IACP,KAAKN,IAAW,KAAK,CAAC6E,EAAanE,EAAG,KAAK,CAAC,GAMhD,GAHA,KAAK+C,GAAgB9C,CAAK,EAC1B,KAAKiD,GAAajD,EAAOlC,EAAM0E,CAAM,EACrC,KAAKzD,GAASiB,CAAK,EAAIgD,EACnBR,EAAQ,CACVA,EAAO,IAAM,UACb,IAAM2B,EACJD,GAAU,KAAKpE,GAAmBoE,CAAM,EACpCA,EAAO,qBACPA,EACFC,IAAa,SAAW3B,EAAO,SAAW2B,SAEvC3B,IACTA,EAAO,IAAM,UAYjB,GATIhC,IAAQ,GAAK,CAAC,KAAKhB,IACrB,KAAKsC,GAAsB,EAEzB,KAAKtC,KACFyB,GACH,KAAKiB,GAAYlC,EAAOQ,EAAK2B,CAAK,EAEhCK,GAAQ,KAAKD,GAAWC,EAAQxC,CAAK,GAEvC,CAACgB,GAAkB,KAAKrB,IAAoB,KAAKN,GAAW,CAC9D,IAAM+E,EAAK,KAAK/E,GACZgF,EACJ,KAAQA,EAAOD,GAAI,MAAK,GACtB,KAAK5F,KAAgB,GAAG6F,CAAI,EAGhC,OAAO,IACT,CAMA,KAAG,CACD,GAAI,CACF,KAAO,KAAK1F,IAAO,CACjB,IAAM2F,EAAM,KAAKvF,GAAS,KAAKG,EAAK,EAEpC,GADA,KAAKgE,GAAO,EAAI,EACZ,KAAKpD,GAAmBwE,CAAG,GAC7B,GAAIA,EAAI,qBACN,OAAOA,EAAI,6BAEJA,IAAQ,OACjB,OAAOA,WAIX,GAAI,KAAK3E,IAAoB,KAAKN,GAAW,CAC3C,IAAM+E,EAAK,KAAK/E,GACZgF,EACJ,KAAQA,EAAOD,GAAI,MAAK,GACtB,KAAK5F,KAAgB,GAAG6F,CAAI,GAIpC,CAEAnB,GAAOqB,EAAa,CAClB,IAAMC,EAAO,KAAKtF,GACZa,EAAI,KAAKjB,GAAS0F,CAAI,EACtBxB,EAAI,KAAKjE,GAASyF,CAAI,EAC5B,OAAI,KAAK9E,IAAmB,KAAKI,GAAmBkD,CAAC,EACnDA,EAAE,kBAAkB,MAAM,IAAI,MAAM,SAAS,CAAC,GACrC,KAAKvD,IAAe,KAAKE,MAC9B,KAAKF,IACP,KAAKlB,KAAWyE,EAAGjD,EAAG,OAAO,EAE3B,KAAKJ,IACP,KAAKN,IAAW,KAAK,CAAC2D,EAAGjD,EAAG,OAAO,CAAC,GAGxC,KAAK+C,GAAgB0B,CAAI,EAErBD,IACF,KAAKzF,GAAS0F,CAAI,EAAI,OACtB,KAAKzF,GAASyF,CAAI,EAAI,OACtB,KAAKpF,GAAM,KAAKoF,CAAI,GAElB,KAAK7F,KAAU,GACjB,KAAKO,GAAQ,KAAKC,GAAQ,EAC1B,KAAKC,GAAM,OAAS,GAEpB,KAAKF,GAAQ,KAAKF,GAAMwF,CAAI,EAE9B,KAAK3F,GAAQ,OAAOkB,CAAC,EACrB,KAAKpB,KACE6F,CACT,CAkBA,IAAIzE,EAAM0E,EAA4C,CAAA,EAAE,CACtD,GAAM,CAAE,eAAA7D,EAAiB,KAAK,eAAgB,OAAA4B,CAAM,EAClDiC,EACIzE,EAAQ,KAAKnB,GAAQ,IAAIkB,CAAC,EAChC,GAAIC,IAAU,OAAW,CACvB,IAAMgD,EAAI,KAAKjE,GAASiB,CAAK,EAC7B,GACE,KAAKF,GAAmBkD,CAAC,GACzBA,EAAE,uBAAyB,OAE3B,MAAO,GAET,GAAK,KAAKzC,GAASP,CAAK,EASbwC,IACTA,EAAO,IAAM,QACb,KAAKD,GAAWC,EAAQxC,CAAK,OAV7B,QAAIY,GACF,KAAK0B,GAAetC,CAAK,EAEvBwC,IACFA,EAAO,IAAM,MACb,KAAKD,GAAWC,EAAQxC,CAAK,GAExB,QAKAwC,IACTA,EAAO,IAAM,QAEf,MAAO,EACT,CASA,KAAKzC,EAAM2E,EAA8C,CAAA,EAAE,CACzD,GAAM,CAAE,WAAA7D,EAAa,KAAK,UAAU,EAAK6D,EACnC1E,EAAQ,KAAKnB,GAAQ,IAAIkB,CAAC,EAChC,GACEC,IAAU,QACT,CAACa,GAAc,KAAKN,GAASP,CAAK,EAEnC,OAEF,IAAMgD,EAAI,KAAKjE,GAASiB,CAAK,EAE7B,OAAO,KAAKF,GAAmBkD,CAAC,EAAIA,EAAE,qBAAuBA,CAC/D,CAEA7C,GACEJ,EACAC,EACAC,EACAC,EAAY,CAEZ,IAAM8C,EAAIhD,IAAU,OAAY,OAAY,KAAKjB,GAASiB,CAAK,EAC/D,GAAI,KAAKF,GAAmBkD,CAAC,EAC3B,OAAOA,EAGT,IAAM2B,EAAK,IAAI1H,EACT,CAAE,OAAA2H,CAAM,EAAK3E,EAEnB2E,GAAQ,iBAAiB,QAAS,IAAMD,EAAG,MAAMC,EAAO,MAAM,EAAG,CAC/D,OAAQD,EAAG,OACZ,EAED,IAAME,EAAY,CAChB,OAAQF,EAAG,OACX,QAAA1E,EACA,QAAAC,GAGI4E,EAAK,CACT9B,EACA+B,EAAc,KACG,CACjB,GAAM,CAAE,QAAAC,CAAO,EAAKL,EAAG,OACjBM,EAAchF,EAAQ,kBAAoB+C,IAAM,OAUtD,GATI/C,EAAQ,SACN+E,GAAW,CAACD,GACd9E,EAAQ,OAAO,aAAe,GAC9BA,EAAQ,OAAO,WAAa0E,EAAG,OAAO,OAClCM,IAAahF,EAAQ,OAAO,kBAAoB,KAEpDA,EAAQ,OAAO,cAAgB,IAG/B+E,GAAW,CAACC,GAAe,CAACF,EAC9B,OAAOG,EAAUP,EAAG,OAAO,MAAM,EAGnC,IAAMQ,EAAKtF,EACX,OAAI,KAAKd,GAASiB,CAAc,IAAMH,IAChCmD,IAAM,OACJmC,EAAG,qBACL,KAAKpG,GAASiB,CAAc,EAAImF,EAAG,qBAEnC,KAAK9C,GAAQtC,EAAG,OAAO,GAGrBE,EAAQ,SAAQA,EAAQ,OAAO,aAAe,IAClD,KAAK,IAAIF,EAAGiD,EAAG6B,EAAU,OAAO,IAG7B7B,CACT,EAEMoC,EAAMC,IACNpF,EAAQ,SACVA,EAAQ,OAAO,cAAgB,GAC/BA,EAAQ,OAAO,WAAaoF,GAEvBH,EAAUG,CAAE,GAGfH,EAAaG,GAA0B,CAC3C,GAAM,CAAE,QAAAL,CAAO,EAAKL,EAAG,OACjBW,EACJN,GAAW/E,EAAQ,uBACfY,EACJyE,GAAqBrF,EAAQ,2BACzBsF,EAAW1E,GAAcZ,EAAQ,yBACjCkF,EAAKtF,EAeX,GAdI,KAAKd,GAASiB,CAAc,IAAMH,IAGxB,CAAC0F,GAAYJ,EAAG,uBAAyB,OAEnD,KAAK9C,GAAQtC,EAAG,OAAO,EACbuF,IAKV,KAAKvG,GAASiB,CAAc,EAAImF,EAAG,uBAGnCtE,EACF,OAAIZ,EAAQ,QAAUkF,EAAG,uBAAyB,SAChDlF,EAAQ,OAAO,cAAgB,IAE1BkF,EAAG,qBACL,GAAIA,EAAG,aAAeA,EAC3B,MAAME,CAEV,EAEMG,EAAQ,CACZC,EACAC,IACE,CACF,IAAMC,EAAM,KAAKlH,KAAesB,EAAGiD,EAAG6B,CAAS,EAC3Cc,GAAOA,aAAe,SACxBA,EAAI,KAAK3C,GAAKyC,EAAIzC,IAAM,OAAY,OAAYA,CAAC,EAAG0C,CAAG,EAKzDf,EAAG,OAAO,iBAAiB,QAAS,IAAK,EAErC,CAAC1E,EAAQ,kBACTA,EAAQ,0BAERwF,EAAI,MAAS,EAETxF,EAAQ,yBACVwF,EAAMzC,GAAK8B,EAAG9B,EAAG,EAAI,GAG3B,CAAC,CACH,EAEI/C,EAAQ,SAAQA,EAAQ,OAAO,gBAAkB,IACrD,IAAMJ,EAAI,IAAI,QAAQ2F,CAAK,EAAE,KAAKV,EAAIM,CAAE,EAClCD,EAAyB,OAAO,OAAOtF,EAAG,CAC9C,kBAAmB8E,EACnB,qBAAsB3B,EACtB,WAAY,OACb,EAED,OAAIhD,IAAU,QAEZ,KAAK,IAAID,EAAGoF,EAAI,CAAE,GAAGN,EAAU,QAAS,OAAQ,MAAS,CAAE,EAC3D7E,EAAQ,KAAKnB,GAAQ,IAAIkB,CAAC,GAE1B,KAAKhB,GAASiB,CAAK,EAAImF,EAElBA,CACT,CAEArF,GAAmBD,EAAM,CACvB,GAAI,CAAC,KAAKH,GAAiB,MAAO,GAClC,IAAMkG,EAAI/F,EACV,MACE,CAAC,CAAC+F,GACFA,aAAa,SACbA,EAAE,eAAe,sBAAsB,GACvCA,EAAE,6BAA6B3I,CAEnC,CA+GA,MAAM,MACJ8C,EACA8F,EAAgD,CAAA,EAAE,CAElD,GAAM,CAEJ,WAAAhF,EAAa,KAAK,WAClB,eAAAF,EAAiB,KAAK,eACtB,mBAAAa,EAAqB,KAAK,mBAE1B,IAAAhB,EAAM,KAAK,IACX,eAAAQ,EAAiB,KAAK,eACtB,KAAAlD,EAAO,EACP,gBAAAsD,EAAkB,KAAK,gBACvB,YAAAH,EAAc,KAAK,YAEnB,yBAAAM,EAA2B,KAAK,yBAChC,2BAAAE,EAA6B,KAAK,2BAClC,iBAAAE,EAAmB,KAAK,iBACxB,uBAAAD,EAAyB,KAAK,uBAC9B,QAAAxB,EACA,aAAA4F,EAAe,GACf,OAAAtD,EACA,OAAAoC,CAAM,EACJiB,EAEJ,GAAI,CAAC,KAAKnG,GACR,OAAI8C,IAAQA,EAAO,MAAQ,OACpB,KAAK,IAAIzC,EAAG,CACjB,WAAAc,EACA,eAAAF,EACA,mBAAAa,EACA,OAAAgB,EACD,EAGH,IAAMvC,EAAU,CACd,WAAAY,EACA,eAAAF,EACA,mBAAAa,EACA,IAAAhB,EACA,eAAAQ,EACA,KAAAlD,EACA,gBAAAsD,EACA,YAAAH,EACA,yBAAAM,EACA,2BAAAE,EACA,uBAAAC,EACA,iBAAAC,EACA,OAAAa,EACA,OAAAoC,GAGE5E,EAAQ,KAAKnB,GAAQ,IAAIkB,CAAC,EAC9B,GAAIC,IAAU,OAAW,CACnBwC,IAAQA,EAAO,MAAQ,QAC3B,IAAM3C,EAAI,KAAKM,GAAiBJ,EAAGC,EAAOC,EAASC,CAAO,EAC1D,OAAQL,EAAE,WAAaA,MAClB,CAEL,IAAMmD,EAAI,KAAKjE,GAASiB,CAAK,EAC7B,GAAI,KAAKF,GAAmBkD,CAAC,EAAG,CAC9B,IAAM+C,GACJlF,GAAcmC,EAAE,uBAAyB,OAC3C,OAAIR,IACFA,EAAO,MAAQ,WACXuD,KAAOvD,EAAO,cAAgB,KAE7BuD,GAAQ/C,EAAE,qBAAwBA,EAAE,WAAaA,EAK1D,IAAMgD,EAAU,KAAKzF,GAASP,CAAK,EACnC,GAAI,CAAC8F,GAAgB,CAACE,EACpB,OAAIxD,IAAQA,EAAO,MAAQ,OAC3B,KAAKpC,GAAYJ,CAAK,EAClBW,GACF,KAAK2B,GAAetC,CAAK,EAEvBwC,GAAQ,KAAKD,GAAWC,EAAQxC,CAAK,EAClCgD,EAKT,IAAMnD,EAAI,KAAKM,GAAiBJ,EAAGC,EAAOC,EAASC,CAAO,EAEpD+F,GADWpG,EAAE,uBAAyB,QACfgB,EAC7B,OAAI2B,IACFA,EAAO,MAAQwD,EAAU,QAAU,UAC/BC,IAAYD,IAASxD,EAAO,cAAgB,KAE3CyD,GAAWpG,EAAE,qBAAwBA,EAAE,WAAaA,EAE/D,CAoCA,MAAM,WACJE,EACA8F,EAAgD,CAAA,EAAE,CAElD,IAAM7C,EAAI,MAAM,KAAK,MACnBjD,EACA8F,CAI8C,EAEhD,GAAI7C,IAAM,OAAW,MAAM,IAAI,MAAM,4BAA4B,EACjE,OAAOA,CACT,CAqCA,KAAKjD,EAAMmG,EAA8C,CAAA,EAAE,CACzD,IAAM5E,EAAa,KAAK5C,GACxB,GAAI,CAAC4C,EACH,MAAM,IAAI,MAAM,uCAAuC,EAEzD,GAAM,CAAE,QAAApB,EAAS,aAAA4F,EAAc,GAAG7F,CAAO,EAAKiG,EACxClD,EAAI,KAAK,IAAIjD,EAAGE,CAAO,EAC7B,GAAI,CAAC6F,GAAgB9C,IAAM,OAAW,OAAOA,EAC7C,IAAMmD,EAAK7E,EAAWvB,EAAGiD,EAAG,CAC1B,QAAA/C,EACA,QAAAC,EACqC,EACvC,YAAK,IAAIH,EAAGoG,EAAIlG,CAAO,EAChBkG,CACT,CAQA,IAAIpG,EAAM2D,EAA4C,CAAA,EAAE,CACtD,GAAM,CACJ,WAAA7C,EAAa,KAAK,WAClB,eAAAF,EAAiB,KAAK,eACtB,mBAAAa,EAAqB,KAAK,mBAC1B,OAAAgB,CAAM,EACJkB,EACE1D,EAAQ,KAAKnB,GAAQ,IAAIkB,CAAC,EAChC,GAAIC,IAAU,OAAW,CACvB,IAAM2D,EAAQ,KAAK5E,GAASiB,CAAK,EAC3BoG,EAAW,KAAKtG,GAAmB6D,CAAK,EAE9C,OADInB,GAAQ,KAAKD,GAAWC,EAAQxC,CAAK,EACrC,KAAKO,GAASP,CAAK,GACjBwC,IAAQA,EAAO,IAAM,SAEpB4D,GAQD5D,GACA3B,GACA8C,EAAM,uBAAyB,SAE/BnB,EAAO,cAAgB,IAElB3B,EAAa8C,EAAM,qBAAuB,SAb5CnC,GACH,KAAKa,GAAQtC,EAAG,QAAQ,EAEtByC,GAAU3B,IAAY2B,EAAO,cAAgB,IAC1C3B,EAAa8C,EAAQ,UAY1BnB,IAAQA,EAAO,IAAM,OAMrB4D,EACKzC,EAAM,sBAEf,KAAKvD,GAAYJ,CAAK,EAClBW,GACF,KAAK2B,GAAetC,CAAK,EAEpB2D,SAEAnB,IACTA,EAAO,IAAM,OAEjB,CAEA6D,GAASxG,EAAUnC,EAAQ,CACzB,KAAKuB,GAAMvB,CAAC,EAAImC,EAChB,KAAKb,GAAMa,CAAC,EAAInC,CAClB,CAEA0C,GAAYJ,EAAY,CASlBA,IAAU,KAAKb,KACba,IAAU,KAAKd,GACjB,KAAKA,GAAQ,KAAKF,GAAMgB,CAAK,EAE7B,KAAKqG,GACH,KAAKpH,GAAMe,CAAK,EAChB,KAAKhB,GAAMgB,CAAK,CAAU,EAG9B,KAAKqG,GAAS,KAAKlH,GAAOa,CAAK,EAC/B,KAAKb,GAAQa,EAEjB,CAOA,OAAOD,EAAI,CACT,OAAO,KAAKsC,GAAQtC,EAAG,QAAQ,CACjC,CAEAsC,GAAQtC,EAAM1C,EAA8B,CAC1C,IAAIwG,EAAU,GACd,GAAI,KAAKlF,KAAU,EAAG,CACpB,IAAMqB,EAAQ,KAAKnB,GAAQ,IAAIkB,CAAC,EAChC,GAAIC,IAAU,OAEZ,GADA6D,EAAU,GACN,KAAKlF,KAAU,EACjB,KAAK2H,GAAOjJ,CAAM,MACb,CACL,KAAKyF,GAAgB9C,CAAK,EAC1B,IAAMgD,EAAI,KAAKjE,GAASiB,CAAK,EAc7B,GAbI,KAAKF,GAAmBkD,CAAC,EAC3BA,EAAE,kBAAkB,MAAM,IAAI,MAAM,SAAS,CAAC,GACrC,KAAKvD,IAAe,KAAKE,MAC9B,KAAKF,IACP,KAAKlB,KAAWyE,EAAQjD,EAAG1C,CAAM,EAE/B,KAAKsC,IACP,KAAKN,IAAW,KAAK,CAAC2D,EAAQjD,EAAG1C,CAAM,CAAC,GAG5C,KAAKwB,GAAQ,OAAOkB,CAAC,EACrB,KAAKjB,GAASkB,CAAK,EAAI,OACvB,KAAKjB,GAASiB,CAAK,EAAI,OACnBA,IAAU,KAAKb,GACjB,KAAKA,GAAQ,KAAKF,GAAMe,CAAK,UACpBA,IAAU,KAAKd,GACxB,KAAKA,GAAQ,KAAKF,GAAMgB,CAAK,MACxB,CACL,IAAMuG,EAAK,KAAKtH,GAAMe,CAAK,EAC3B,KAAKhB,GAAMuH,CAAE,EAAI,KAAKvH,GAAMgB,CAAK,EACjC,IAAMwG,EAAK,KAAKxH,GAAMgB,CAAK,EAC3B,KAAKf,GAAMuH,CAAE,EAAI,KAAKvH,GAAMe,CAAK,EAEnC,KAAKrB,KACL,KAAKS,GAAM,KAAKY,CAAK,GAI3B,GAAI,KAAKL,IAAoB,KAAKN,IAAW,OAAQ,CACnD,IAAM+E,EAAK,KAAK/E,GACZgF,EACJ,KAAQA,EAAOD,GAAI,MAAK,GACtB,KAAK5F,KAAgB,GAAG6F,CAAI,EAGhC,OAAOR,CACT,CAKA,OAAK,CACH,OAAO,KAAKyC,GAAO,QAAQ,CAC7B,CACAA,GAAOjJ,EAA8B,CACnC,QAAW2C,KAAS,KAAKM,GAAU,CAAE,WAAY,EAAI,CAAE,EAAG,CACxD,IAAM0C,EAAI,KAAKjE,GAASiB,CAAK,EAC7B,GAAI,KAAKF,GAAmBkD,CAAC,EAC3BA,EAAE,kBAAkB,MAAM,IAAI,MAAM,SAAS,CAAC,MACzC,CACL,IAAMjD,EAAI,KAAKjB,GAASkB,CAAK,EACzB,KAAKP,IACP,KAAKlB,KAAWyE,EAAQjD,EAAQ1C,CAAM,EAEpC,KAAKsC,IACP,KAAKN,IAAW,KAAK,CAAC2D,EAAQjD,EAAQ1C,CAAM,CAAC,GAoBnD,GAfA,KAAKwB,GAAQ,MAAK,EAClB,KAAKE,GAAS,KAAK,MAAS,EAC5B,KAAKD,GAAS,KAAK,MAAS,EACxB,KAAKU,IAAS,KAAKD,KACrB,KAAKC,GAAM,KAAK,CAAC,EACjB,KAAKD,GAAQ,KAAK,CAAC,GAEjB,KAAKD,IACP,KAAKA,GAAO,KAAK,CAAC,EAEpB,KAAKJ,GAAQ,EACb,KAAKC,GAAQ,EACb,KAAKC,GAAM,OAAS,EACpB,KAAKR,GAAkB,EACvB,KAAKD,GAAQ,EACT,KAAKgB,IAAoB,KAAKN,GAAW,CAC3C,IAAM+E,EAAK,KAAK/E,GACZgF,EACJ,KAAQA,EAAOD,GAAI,MAAK,GACtB,KAAK5F,KAAgB,GAAG6F,CAAI,EAGlC,GCl2FF,OAAS,QAAAoC,OAAY,gBACrB,OAAS,aAAAC,OAAiB,OAE1B,OAAOC,OAAQ,cACf,OAAOC,OAAU,OAGjB,IAAMC,EAAYC,GAAUC,EAAI,EA+F1BC,GAAe,CACnB,IAAK,IACL,IAAK,IACL,eAAgB,GAChB,WAAY,EACd,EAGMC,GAAc,IAAIC,EAASF,EAAY,EACvCG,GAAe,IAAID,EAASF,EAAY,EACxCI,GAAc,IAAIF,EAASF,EAAY,EAEhCK,EAAN,MAAMC,CAAgB,CAC3B,aAAa,WAAY,CACvB,GAAI,CAEF,IAAMC,EAAeN,GAAY,IAAI,QAAQ,EAC7C,GAAIM,EACF,OAAOA,EAGT,GAAM,CAAE,OAAAC,CAAO,EAAI,MAAMX,EAAU,oBAAoB,EAEjDY,EAAUD,EAAO,OAAS,GAAK,CAACA,EAAO,SAAS,8BAA8B,EAEpF,GAAI,CAACC,EAAS,CACZ,IAAMC,EAAS,CACb,QAAS,GACT,QAAS,CAAC,EACV,OAAQ,CAAC,EACT,WAAY,EACZ,eAAgB,IAAI,KAAK,EAAE,YAAY,CACzC,EACA,OAAAT,GAAY,IAAI,SAAUS,CAAM,EACzBA,CACT,CAEA,IAAMC,EAAU,MAAM,KAAK,WAAW,EAChCC,EAAS,MAAM,KAAK,UAAU,EAE9BF,EAAS,CACb,QAAAD,EACA,QAAAE,EACA,OAAAC,EACA,WAAYD,EAAQ,OAAO,CAACE,EAAKC,IAAMD,EAAMC,EAAE,WAAY,CAAC,EAC5D,eAAgB,IAAI,KAAK,EAAE,YAAY,CACzC,EAGA,OAAAb,GAAY,IAAI,SAAUS,CAAM,EACzBA,CACT,OAASK,EAAgB,CAEvB,GADYA,EACJ,SAAS,SAAS,2BAA2B,EACnD,MAAO,CACL,QAAS,GACT,QAAS,CAAC,EACV,OAAQ,CAAC,EACT,WAAY,EACZ,eAAgB,IAAI,KAAK,EAAE,YAAY,CACzC,EAEF,cAAQ,MAAM,iCAAkCA,CAAK,EAC/C,IAAIC,EAAS,IAAK,gDAAkB,CAC5C,CACF,CAEA,aAAa,YAAgC,CAC3C,GAAI,CAEF,IAAMC,EAAgBd,GAAa,IAAI,SAAS,EAChD,GAAIc,EACF,OAAOA,EAGT,GAAM,CAAE,OAAAT,CAAO,EAAI,MAAMX,EAAU,oBAAoB,EACjDc,EAAoB,CAAC,EACvBO,EAAiC,CAAC,EAClCC,EAAgB,GAGdC,EAAa,MAAM,KAAK,qBAAqB,EAE7CC,EAAQb,EAAO,MAAM;AAAA,CAAI,EAC/B,QAAWc,KAAQD,EACjB,GAAIC,EAAK,WAAW,QAAQ,EACtB,OAAO,KAAKJ,CAAa,EAAE,OAAS,GACtCP,EAAQ,KAAKO,CAAuB,EAEtCA,EAAgB,CACd,MAAO,SAASI,EAAK,MAAM,aAAa,IAAI,CAAC,GAAK,GAAG,EACrD,MAAO,GACP,OAAQ,GACR,WAAY,EACZ,IAAK,GACL,UAAW,GACX,KAAM,GACN,SAAU,EACV,SAAU,EACV,QAAS,EACT,QAAS,EACT,WAAY,EACZ,YAAa,CAAC,EAAG,EAAG,CAAC,EACrB,OAAQ,CAAC,EAAG,EAAG,CAAC,EAChB,YAAa,CAAC,EAAG,EAAG,CAAC,EACrB,OAAQ,CAAC,EAAG,EAAG,CAAC,EAChB,SAAU,CAAC,EAAG,EAAG,CAAC,EAClB,UAAW,CAAC,EAAG,EAAG,CAAC,CACrB,UACSA,EAAK,KAAK,EAAE,WAAW,QAAQ,EACxCJ,EAAc,MAAQI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,UACrCA,EAAK,KAAK,EAAE,WAAW,SAAS,EACzCJ,EAAc,OAASI,EAAK,SAAS,KAAK,UACjCA,EAAK,KAAK,EAAE,WAAW,SAAS,EACzCJ,EAAc,WAAa,SAASI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,UACpDA,EAAK,SAAS,OAAO,EAAG,CAEjC,IAAMC,EAAWD,EAAK,MAAM,6FAA6F,EACzH,GAAIC,EAAU,CACZ,IAAMC,EAAMD,EAAS,CAAC,EAAE,YAAY,EACpCL,EAAc,IAAMM,EACpBN,EAAc,UAAYE,EAAW,IAAII,CAAG,GAAK,EACnD,CACF,MAAWF,EAAK,KAAK,EAAE,WAAW,OAAO,EACvCJ,EAAc,KAAOI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,EACpCA,EAAK,SAAS,SAAS,EAChCH,EAAgB,GACPA,IACLG,EAAK,SAAS,YAAY,EAC5BJ,EAAc,SAAW,SAASI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EAClDA,EAAK,SAAS,YAAY,EACnCJ,EAAc,SAAW,SAASI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EAClDA,EAAK,SAAS,WAAW,EAClCJ,EAAc,QAAU,SAASI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EACjDA,EAAK,SAAS,WAAW,EAClCJ,EAAc,QAAU,SAASI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EACjDA,EAAK,SAAS,cAAc,EACrCJ,EAAc,WAAa,SAASI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EACpDA,EAAK,SAAS,sBAAsB,EAC7CJ,EAAc,YAAcI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,EAAE,IAAI,MAAM,EACpEA,EAAK,SAAS,oBAAoB,EAC3CJ,EAAc,OAASI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,EAAE,IAAI,MAAM,EAC/DA,EAAK,SAAS,sBAAsB,EAC7CJ,EAAc,YAAcI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,EAAE,IAAI,MAAM,EACpEA,EAAK,SAAS,oBAAoB,EAC3CJ,EAAc,OAASI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,EAAE,IAAI,MAAM,EAC/DA,EAAK,SAAS,kBAAkB,EACzCJ,EAAc,SAAWI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,EAAE,IAAI,MAAM,EACjEA,EAAK,SAAS,iBAAiB,IACxCJ,EAAc,UAAYI,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,EAAE,IAAI,MAAM,IAKjF,OAAI,OAAO,KAAKJ,CAAa,EAAE,OAAS,GACtCP,EAAQ,KAAKO,CAAuB,EAItCf,GAAa,IAAI,UAAWQ,CAAO,EAC5BA,CACT,OAASI,EAAO,CACd,eAAQ,MAAM,yBAA0BA,CAAK,EACtC,CAAC,CACV,CACF,CAEA,aAAa,WAA8B,CACzC,GAAI,CAEF,IAAMU,EAAerB,GAAY,IAAI,QAAQ,EAC7C,GAAIqB,EACF,OAAOA,EAGT,GAAM,CAAE,OAAAjB,CAAO,EAAI,MAAMX,EAAU,oBAAoB,EACjDe,EAAkB,CAAC,EACrBc,EAA+B,CAAC,EAChCR,EAAgB,EAChBS,EAAU,GACVC,EAAsB,CAAC,EAErBP,EAAQb,EAAO,MAAM;AAAA,CAAI,EAC/B,QAAWc,KAAQD,EACjB,GAAIC,EAAK,WAAW,YAAY,EAAG,CAC7B,OAAO,KAAKI,CAAY,EAAE,OAAS,IACrCA,EAAa,MAAQE,EACrBhB,EAAO,KAAKc,CAAqB,GAEnCA,EAAe,CAAC,EAChBE,EAAe,CAAC,EAChBD,EAAU,GACV,IAAME,EAAUP,EAAK,MAAM,2BAA2B,EAClDO,IACFX,EAAgB,SAASW,EAAQ,CAAC,EAAG,EAAE,EACvCH,EAAa,OAASR,EACtBQ,EAAa,MAAQ,SAASG,EAAQ,CAAC,EAAG,EAAE,EAEhD,SAAWP,EAAK,WAAW,SAAS,EAClCI,EAAa,KAAOJ,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,UACnCA,EAAK,WAAW,QAAQ,EACjCI,EAAa,MAAQJ,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,UACpCA,EAAK,SAAS,YAAY,EACnCI,EAAa,SAAWJ,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,UACvCA,EAAK,SAAS,eAAe,EACtCI,EAAa,YAAcJ,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,UAC1CA,EAAK,SAAS,kBAAkB,EACzCI,EAAa,SAAWJ,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,UACvCA,EAAK,SAAS,gBAAgB,EACvCI,EAAa,OAASJ,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,UACrCA,EAAK,SAAS,QAAQ,EAC/BI,EAAa,MAAQJ,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,UACpCA,EAAK,SAAS,eAAe,EACtCI,EAAa,YAAcJ,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,UAC1CA,EAAK,SAAS,cAAc,EACrCI,EAAa,WAAaJ,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,UACzCA,EAAK,SAAS,sBAAsB,EAC7CI,EAAa,mBAAqB,SAASJ,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,UAC3DA,EAAK,SAAS,qBAAqB,EAC5CI,EAAa,iBAAmBJ,EAAK,SAAS,KAAK,UAC1CA,EAAK,SAAS,qBAAqB,EAC5CI,EAAa,kBAAoBJ,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,UAChDA,EAAK,SAAS,oCAAoC,EAC3DI,EAAa,kBAAoB,SAASJ,EAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,UAC1DA,EAAK,KAAK,EAAE,WAAW,MAAM,EACtCK,EAAU,WACDA,GAAWL,EAAK,KAAK,EAAE,MAAM,KAAK,EAAG,CAC9C,GAAM,CAACQ,EAAOC,EAAMC,EAAMC,EAAMC,EAAQC,EAAWC,EAAQC,EAAMC,CAAM,EACrEhB,EAAK,KAAK,EAAE,MAAM,KAAK,EACzBM,EAAa,KAAK,CAChB,MAAO,SAASE,CAAK,EACrB,KAAAC,EACA,KAAAC,EACA,KAAAC,EACA,OAAAC,EACA,UAAAC,EACA,OAAQC,IAAW,IAAM,EAAI,SAASA,CAAM,EAC5C,KAAMC,IAAS,IAAM,EAAI,SAASA,CAAI,EACtC,OAAQC,IAAW,IAAM,EAAI,SAASA,CAAM,CAC9C,CAAC,CACH,CAGE,OAAO,KAAKZ,CAAY,EAAE,OAAS,IACrCA,EAAa,MAAQE,EACrBhB,EAAO,KAAKc,CAAqB,GAInC,QAAWa,KAAS3B,EAClB,GAAI,CACF,GAAM,CAAE,OAAQ4B,CAAO,EAAI,MAAM3C,EAAU,oBAAoB0C,EAAM,MAAM,OAAOA,EAAM,KAAK,EAAE,EACzFE,EAA8B,CAAC,EACjCC,EAAkC,CAAC,EACnCC,EAA2B,CAAC,EAE1BtB,EAAQmB,EAAO,MAAM;AAAA,CAAI,EAC/B,QAAWlB,KAAQD,EACjB,GAAIC,EAAK,WAAW,IAAI,EAAG,CAErB,OAAO,KAAKqB,CAAU,EAAE,OAAS,IACnCD,EAAU,MAAM,KAAKC,CAAiB,EACtCA,EAAa,CAAC,GAGZ,OAAO,KAAKD,CAAS,EAAE,OAAS,GAClCD,EAAa,KAAKC,CAAwB,EAE5C,IAAMb,EAAUP,EAAK,MAAM,sFAAsF,EAC7GO,IACFa,EAAY,CACV,MAAO,SAASb,EAAQ,CAAC,CAAC,EAC1B,SAAUA,EAAQ,CAAC,EACnB,YAAa,SAASA,EAAQ,CAAC,CAAC,EAChC,gBAAiBA,EAAQ,CAAC,EAC1B,OAAQA,EAAQ,CAAC,IAAM,IACvB,KAAM,CAAC,CACT,EAEJ,SAAWP,EAAK,KAAK,EAAE,WAAW,OAAO,GAAKA,EAAK,KAAK,EAAE,WAAW,OAAO,EAAG,CAEzE,OAAO,KAAKqB,CAAU,EAAE,OAAS,GACnCD,EAAU,MAAM,KAAKC,CAAiB,EAExC,IAAMd,EAAUP,EAAK,MAAM,2BAA2B,EAClDO,IACFc,EAAa,CACX,KAAM,GAAGd,EAAQ,CAAC,CAAC,MACnB,MAAOA,EAAQ,CAAC,EAChB,KAAMA,EAAQ,CAAC,EACf,QAAS,CAAC,CACZ,EAEJ,SAAWP,EAAK,KAAK,EAAE,WAAW,WAAW,EAAG,CAC9C,IAAMO,EAAUP,EAAK,MAAM,8CAA8C,EACzE,GAAIO,GAAWc,EAAW,QAAS,CACjC,IAAMC,EAAkB,CACtB,MAAOf,EAAQ,CAAC,EAChB,SAAUA,EAAQ,CAAC,EACnB,KAAM,SAASA,EAAQ,CAAC,CAAC,EACzB,KAAMA,EAAQ,CAAC,CACjB,EACAc,EAAW,QAAQ,KAAKC,CAAK,CAC/B,CACF,CAIE,OAAO,KAAKD,CAAU,EAAE,OAAS,GACnCD,EAAU,MAAM,KAAKC,CAAiB,EAEpC,OAAO,KAAKD,CAAS,EAAE,OAAS,GAClCD,EAAa,KAAKC,CAAwB,EAG5CH,EAAM,aAAeE,CACvB,OAAS1B,EAAO,CACd,QAAQ,MAAM,gCAAgCwB,EAAM,KAAK,IAAKxB,CAAK,CACrE,CAIF,OAAAX,GAAY,IAAI,SAAUQ,CAAM,EACzBA,CACT,OAASG,EAAO,CACd,eAAQ,MAAM,wBAAyBA,CAAK,EACrC,CAAC,CACV,CACF,CAEA,aAAa,cAA8B,CACzC,GAAI,CACF,MAAMlB,EAAU,mBAAmB,EACnC,MAAM,IAAI,QAAQgD,GAAW,WAAWA,EAAS,GAAI,CAAC,CACxD,OAAS9B,EAAO,CACd,cAAQ,MAAM,2BAA4BA,CAAK,EACzC,IAAIC,EAAS,IAAK,sCAAQ,CAClC,CACF,CAEA,aAAa,aAA6B,CACxC,GAAI,CACF,MAAMnB,EAAU,kBAAkB,EAClC,MAAM,IAAI,QAAQgD,GAAW,WAAWA,EAAS,GAAI,CAAC,CACxD,OAAS9B,EAAO,CACd,cAAQ,MAAM,0BAA2BA,CAAK,EACxC,IAAIC,EAAS,IAAK,sCAAQ,CAClC,CACF,CAEA,aAAa,YAAY8B,EAAgBP,EAAgC,CACvE,GAAI,CACF,GAAM,CAAE,OAAA/B,CAAO,EAAI,MAAMX,EAAU,mBAAmBiD,CAAM,OAAOP,CAAK,EAAE,EAC1E,OAAO/B,CACT,OAASO,EAAO,CACd,cAAQ,MAAM,2BAA4BA,CAAK,EACzC,IAAIC,EAAS,IAAK,uDAAe,CACzC,CACF,CAEA,aAAa,iBAAiB8B,EAAiC,CAC7D,GAAI,CACF,GAAM,CAAE,OAAAtC,CAAO,EAAI,MAAMX,EAAU,qBAAqBiD,CAAM,EAAE,EAChE,OAAOtC,CACT,OAASO,EAAO,CACd,cAAQ,MAAM,+BAAgCA,CAAK,EAC7C,IAAIC,EAAS,IAAK,4CAAS,CACnC,CACF,CAEA,aAAa,qBAAyC,CACpD,GAAI,CACF,GAAM,CAAE,OAAAR,CAAO,EAAI,MAAMX,EAAU,iBAAiB,EAC9Cc,EAAoB,CAAC,EAGrBU,EAAQb,EAAO,MAAM;AAAA,CAAI,EAC/B,QAAWc,KAAQD,EAAO,CACxB,IAAM0B,EAAQzB,EAAK,MAAM,cAAc,EACnCyB,GACFpC,EAAQ,KAAK,SAASoC,EAAM,CAAC,CAAC,CAAC,CAEnC,CAEA,OAAOpC,CACT,OAASI,EAAO,CACd,eAAQ,MAAM,mCAAoCA,CAAK,EAChD,CAAC,CACV,CACF,CAGA,OAAO,YAAa,CAClBd,GAAY,MAAM,EAClBE,GAAa,MAAM,EACnBC,GAAY,MAAM,CACpB,CAEA,aAAa,sBAA0C,CAErD,MAAO,CAAC,OAAQ,MAAM,CACxB,CAGA,aAAa,sBAAqD,CAChE,GAAI,CACF,GAAM,CAAE,OAAAI,CAAO,EAAI,MAAMX,EAAU,6CAA6C,EAC1EwB,EAAQb,EAAO,MAAM;AAAA,CAAI,EACzBY,EAAa,IAAI,IAEnB4B,EAAe,GACnB,QAAW1B,KAAQD,EACjB,GAAIC,EAAK,SAAS,OAAO,GAAKA,EAAK,SAAS,OAAO,EACjD0B,EAAe1B,EAAK,MAAM,IAAI,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,UACtCA,EAAK,SAAS,YAAY,GAAK0B,EAAc,CACtD,IAAMxB,EAAMF,EAAK,MAAM,aAAa,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,YAAY,EACnEF,EAAW,IAAII,EAAKwB,CAAY,CAClC,CAGF,OAAO5B,CACT,OAASL,EAAO,CACd,eAAQ,MAAM,sCAAuCA,CAAK,EACnD,IAAI,GACb,CACF,CAEA,aAAa,WAAqC,CAChD,GAAI,CAEF,IAAMM,GADU,MAAM1B,GAAG,SAAS,qBAAsB,OAAO,GACzC,MAAM;AAAA,CAAI,EAE5BsD,EAAgB,GAChBC,EAA+B,KAEnC,QAAW5B,KAAQD,EAAO,CAExB,IAAM8B,EAAc7B,EAAK,KAAK,EAE9B,GAAI6B,EAAY,WAAW,GAAG,GAAK,CAACA,EAAa,SAGjD,IAAMC,EAAeD,EAAY,MAAM,oCAAoC,EACvEC,IACFH,EAAgBG,EAAa,CAAC,GAIhC,IAAMC,EAAeF,EAAY,MAAM,oCAAoC,EACvEE,IACFH,EAAgBG,EAAa,CAAC,EAElC,CAGA,GAAI,CAACJ,EACH,MAAM,IAAIjC,EAAS,IAAK,iEAAyB,EAGnD,MAAO,CACL,cAAAiC,EACA,cAAAC,CACF,CACF,OAASnC,EAAO,CAEd,MADA,QAAQ,MAAM,yBAA0BA,CAAK,EACzCA,aAAiBC,EACbD,EAEF,IAAIC,EAAS,IAAK,sCAAQ,CAClC,CACF,CAEA,aAAa,aAAasC,EAAuC,CAC/D,GAAI,CACF,GAAI,CAACA,EAAO,cACV,MAAM,IAAItC,EAAS,IAAK,yCAAqB,EAQ/C,IAAMuC,GAJkB,MAAM5D,GAAG,SAAS,qBAAsB,OAAO,GACzC,MAAM;AAAA,CAAI,EAGb,OAAO2B,GAAQ,CACxC,IAAM6B,EAAc7B,EAAK,KAAK,EAC9B,OAAO6B,GACA,CAACA,EAAY,WAAW,GAAG,GAC3B,CAACA,EAAY,WAAW,iBAAiB,GACzC,CAACA,EAAY,WAAW,iBAAiB,CAClD,CAAC,EAGKK,EAAW,CAEf,mBAAmBF,EAAO,aAAa,IACvCA,EAAO,cAAgB,mBAAmBA,EAAO,aAAa,IAAM,GAGpE,GAAGC,CACL,EAAE,OAAOjC,GAAQA,CAAI,EAGrB,MAAM3B,GAAG,UAAU,qBAAsB6D,EAAS,KAAK;AAAA,CAAI,EAAI;AAAA,EAAM,OAAO,CAC9E,OAASzC,EAAO,CACd,cAAQ,MAAM,2BAA4BA,CAAK,EACzC,IAAIC,EAAS,IAAK,gCAAO,CACjC,CACF,CAEA,aAAa,gBAAgC,CAC3C,GAAI,CAEF,MAAMnB,EAAU,qBAAqB,EAGrC,MAAM,IAAI,QAAQgD,GAAW,WAAWA,EAAS,GAAI,CAAC,EAGtD,GAAM,CAAE,OAAArC,CAAO,EAAI,MAAMX,EAAU,oBAAoB,EAGvD,GAAI,EAFYW,EAAO,OAAS,GAAK,CAACA,EAAO,SAAS,8BAA8B,GAGlF,MAAM,IAAI,MAAM,yBAAyB,EAI3C,KAAK,WAAW,CAClB,OAASO,EAAO,CACd,cAAQ,MAAM,6BAA8BA,CAAK,EAC3C,IAAIC,EAAS,IAAK,sFAAgB,CAC1C,CACF,CAEA,aAAa,kBAAqC,CAChD,GAAI,CACF,GAAM,CAAE,OAAAR,CAAO,EAAI,MAAMX,EAAU,oBAAoB,EACvD,OAAOW,EAAO,SAAS,SAAS,CAClC,OAASO,EAAO,CACd,eAAQ,MAAM,gCAAiCA,CAAK,EAC7C,EACT,CACF,CAEA,aAAa,gBAA+B,CAC1C,GAAI,CACF,IAAMH,EAAS,MAAM,KAAK,UAAU,EAC9B0C,EAAS,CACb,OAAQ,MAAM,QAAQ,IAAI1C,EAAO,IAAI,MAAO2B,GAAiB,CAC3D,GAAI,CACF,GAAM,CAAE,OAAQkB,CAAW,EAAI,MAAM5D,EAAU,mBAAmB0C,EAAM,MAAM,OAAOA,EAAM,KAAK,EAAE,EAG5FmB,EAAkBD,EAAW,MAAM,2CAA2C,EAC9EE,EAAaD,EAAkBA,EAAgB,CAAC,EAAE,KAAK,EAAInB,EAAM,KAGjEqB,EAAaH,EAAW,MAAM,sDAAsD,EACpFI,EAAaD,EAAaA,EAAW,CAAC,EAAI,OAG1CE,EAAaL,EAAW,MAAM,sDAAsD,EACpFM,EAAaD,EAAaA,EAAW,CAAC,EAAI,OAG1CE,EAAS,CAAC,EACVC,EAAeR,EAAW,MAAM,yBAAyB,EAC/D,GAAIQ,EAAc,CAChB,IAAMC,EAAUD,EAAa,CAAC,EAAE,SAAS,uMAAuM,EAChP,QAAWrB,KAASsB,EAClBF,EAAO,KAAK,CACV,KAAMpB,EAAM,CAAC,EAAE,KAAK,GAAK,YAAYA,EAAM,CAAC,CAAC,GAC7C,MAAO,KAAKA,EAAM,CAAC,CAAC,GACpB,SAAUA,EAAM,CAAC,EACjB,KAAMtC,EAAgB,oBAAoBsC,EAAM,CAAC,EAAG,SAASA,EAAM,CAAC,CAAC,CAAC,EACtE,OAAQ,SAASA,EAAM,CAAC,CAAC,CAC3B,CAAC,CAEL,CAGA,IAAMuB,EAAS,CAAC,EACVC,EAAeX,EAAW,MAAM,yBAAyB,EAC/D,GAAIW,EAAc,CAChB,IAAMF,EAAUE,EAAa,CAAC,EAAE,SAAS,uMAAuM,EAChP,QAAWxB,KAASsB,EAClBC,EAAO,KAAK,CACV,KAAMvB,EAAM,CAAC,EAAE,KAAK,GAAK,YAAYA,EAAM,CAAC,CAAC,GAC7C,MAAO,KAAKA,EAAM,CAAC,CAAC,GACpB,SAAUA,EAAM,CAAC,EACjB,KAAMtC,EAAgB,oBAAoBsC,EAAM,CAAC,EAAG,SAASA,EAAM,CAAC,CAAC,CAAC,EACtE,OAAQ,SAASA,EAAM,CAAC,CAAC,CAC3B,CAAC,CAEL,CAEA,MAAO,CACL,MAAOL,EAAM,MAAM,SAAS,EAC5B,KAAMoB,EACN,IAAKpB,EAAM,SACX,IAAKA,EAAM,YACX,OAAQ,KAAKsB,CAAU,GACvB,OAAQ,KAAKE,CAAU,GACvB,QAASC,EACT,QAASG,CACX,CACF,OAASpD,EAAO,CACd,eAAQ,MAAM,kCAAkCwB,EAAM,MAAM,IAAIA,EAAM,KAAK,IAAKxB,CAAK,EAC9E,IACT,CACF,CAAC,CAAC,CACJ,EAGA,OAAAuC,EAAO,OAASA,EAAO,OAAO,OAAOf,GAASA,IAAU,IAAI,EAErDe,CACT,OAASvC,EAAO,CACd,cAAQ,MAAM,8BAA+BA,CAAK,EAC5C,IAAIC,EAAS,IAAK,kDAAU,CACpC,CACF,CAEA,OAAe,oBAAoBqD,EAAkBC,EAAwB,CAC3E,OAAQD,EAAS,YAAY,EAAG,CAC9B,IAAK,OACH,MAAO,OACT,IAAK,OACL,IAAK,MACL,IAAK,OACH,MAAO,OACT,IAAK,OACL,IAAK,QACH,MAAO,QACT,IAAK,SACL,IAAK,OACH,MAAO,SACT,IAAK,QACH,MAAO,QACT,IAAK,SACL,IAAK,QACH,MAAO,SACT,IAAK,QACH,MAAO,QACT,IAAK,SACH,MAAO,SACT,IAAK,QACH,MAAO,QACT,IAAK,QACH,MAAO,SACT,QACE,OAAOC,GAAU,EAAI,QACdA,GAAU,GAAK,SACfA,GAAU,GAAK,QACfA,GAAU,GAAK,QAAU,QACpC,CACF,CAEA,aAAa,mBAAkC,CAC7C,GAAI,CACF,IAAMC,EAAe3E,GAAK,KAAK,QAAQ,IAAI,EAAG,WAAY,qBAAqB,EACzE4E,EAAkB,MAAM7E,GAAG,SAAS4E,EAAc,OAAO,EAC/D,OAAO,KAAK,MAAMC,CAAe,CACnC,OAASzD,EAAO,CACd,cAAQ,MAAM,gCAAiCA,CAAK,EAC9C,IAAIC,EAAS,IAAK,kDAAU,CACpC,CACF,CAEA,aAAa,gBAA0D,CACrE,GAAI,CAEF,GAAM,CAAE,OAAQyD,CAAW,EAAI,MAAM5E,EAAU,qBAAqB,EAG9D6E,EAAQ,MAAM,IAAI,QAAgB,CAAC7B,EAAS8B,IAAW,CAC3D,IAAMC,EAAM,MAAM,MAAO,CAAC,OAAO,CAAC,EAC5BC,EAAmB,CAAC,EAE1BD,EAAI,OAAO,GAAG,OAASE,GAAUD,EAAO,KAAKC,CAAK,CAAC,EACnDF,EAAI,OAAO,GAAG,OAASG,GAAS,QAAQ,MAAM,cAAcA,CAAI,EAAE,CAAC,EACnEH,EAAI,GAAG,QAAUI,GAAS,CACpBA,IAAS,EACXnC,EAAQ,OAAO,OAAOgC,CAAM,CAAC,EAE7BF,EAAO,IAAI,MAAM,gCAAgCK,CAAI,EAAE,CAAC,CAE5D,CAAC,EAEDJ,EAAI,MAAM,MAAMH,CAAU,EAC1BG,EAAI,MAAM,IAAI,CAChB,CAAC,EAED,MAAO,CAAE,IAAKH,EAAY,MAAAC,CAAM,CAClC,OAAS3D,EAAO,CACd,cAAQ,MAAM,8BAA+BA,CAAK,EAC5C,IAAIC,EAAS,IAAK,wDAAW,CACrC,CACF,CACF,EF9xBA,OAAS,QAAAiE,OAAY,gBACrB,OAAS,aAAAC,OAAiB,OAC1B,IAAMC,GAAYD,GAAUD,EAAI,EAE1BG,EAASC,GAAO,EAEtBD,EAAO,IAAI,UAAWE,EAAa,MAAOC,EAAcC,IAAkB,CACxE,GAAI,CACF,IAAMC,EAAS,MAAMC,EAAgB,UAAU,EAE/CF,EAAI,KAAK,CACP,OAAQ,IACR,KAAMC,CACR,CAAC,CACH,OAASE,EAAO,CACd,cAAQ,MAAM,iCAAkCA,CAAK,EAC/CA,CACR,CACF,CAAC,CAAC,EAEFP,EAAO,IAAI,UAAWE,EAAa,MAAOC,EAAcC,IAAkB,CACxE,IAAMI,EAAS,MAAMF,EAAgB,UAAU,EAC/CF,EAAI,KAAK,CACP,OAAQ,IACR,KAAMI,CACR,CAAC,CACH,CAAC,CAAC,EAEFR,EAAO,KAAK,SAAUE,EAAa,MAAOC,EAAcC,IAAkB,CACxE,MAAME,EAAgB,aAAa,EACnCF,EAAI,KAAK,CACP,OAAQ,IACR,KAAM,CAAE,QAAS,EAAK,CACxB,CAAC,CACH,CAAC,CAAC,EAEFJ,EAAO,KAAK,QAASE,EAAa,MAAOC,EAAcC,IAAkB,CACvE,MAAME,EAAgB,YAAY,EAClCF,EAAI,KAAK,CACP,OAAQ,IACR,KAAM,CAAE,QAAS,EAAK,CACxB,CAAC,CACH,CAAC,CAAC,EAEFJ,EAAO,IAAI,sBAAuBE,EAAa,MAAOC,EAAcC,IAAkB,CACpF,IAAMK,EAAS,SAASN,EAAI,OAAO,MAAM,EACnCO,EAAQ,SAASP,EAAI,OAAO,KAAK,EACjCQ,EAAM,MAAML,EAAgB,YAAYG,EAAQC,CAAK,EAC3DN,EAAI,KAAK,CACP,OAAQ,IACR,KAAMO,CACR,CAAC,CACH,CAAC,CAAC,EAEFX,EAAO,IAAI,oBAAqBE,EAAa,MAAOC,EAAcC,IAAkB,CAClF,IAAMK,EAAS,SAASN,EAAI,OAAO,MAAM,EACnCS,EAAM,MAAMN,EAAgB,iBAAiBG,CAAM,EACzDL,EAAI,UAAU,eAAgB,eAAe,EAC7CA,EAAI,KAAKQ,CAAG,CACd,CAAC,CAAC,EAEFZ,EAAO,IAAI,WAAYE,EAAa,MAAOC,EAAcC,IAAkB,CACzE,IAAMS,EAAU,MAAMP,EAAgB,oBAAoB,EAC1DF,EAAI,KAAK,CACP,OAAQ,IACR,KAAMS,CACR,CAAC,CACH,CAAC,CAAC,EAEFb,EAAO,IAAI,sBAAuBE,EAAa,MAAOC,EAAcC,IAAkB,CACpF,GAAM,CAAE,OAAAU,CAAO,EAAI,MAAMf,GAAU,mEAAmE,EAChGgB,EAAaD,EAAO,KAAK,EAAE,MAAM;AAAA,CAAI,EAC3CV,EAAI,KAAK,CACP,OAAQ,IACR,KAAMW,CACR,CAAC,CACH,CAAC,CAAC,EAEFf,EAAO,IAAI,UAAWE,EAAa,MAAOC,EAAcC,IAAkB,CACxE,IAAMY,EAAS,MAAMV,EAAgB,UAAU,EAC/CF,EAAI,KAAK,CACP,OAAQ,IACR,KAAMY,CACR,CAAC,CACH,CAAC,CAAC,EAEFhB,EAAO,KAAK,UAAWE,EAAa,MAAOC,EAAcC,IAAkB,CACzE,MAAME,EAAgB,aAAaH,EAAI,IAAI,EAC3CC,EAAI,KAAK,CACP,OAAQ,IACR,QAAS,gCACX,CAAC,CACH,CAAC,CAAC,EAEFJ,EAAO,KAAK,WAAYE,EAAa,MAAOC,EAAcC,IAAkB,CAC1E,MAAME,EAAgB,eAAe,EACrCF,EAAI,KAAK,CACP,OAAQ,IACR,QAAS,gCACX,CAAC,CACH,CAAC,CAAC,EAEFJ,EAAO,IAAI,gBAAiBE,EAAa,MAAOC,EAAcC,IAAkB,CAC9E,IAAMY,EAAS,MAAMV,EAAgB,eAAe,EACpDF,EAAI,KAAK,CACP,OAAQ,IACR,KAAMY,CACR,CAAC,CACH,CAAC,CAAC,EAEFhB,EAAO,IAAI,mBAAoBE,EAAa,MAAOC,EAAcC,IAAkB,CACjF,IAAMY,EAAS,MAAMV,EAAgB,kBAAkB,EACvDF,EAAI,KAAK,CACP,OAAQ,IACR,KAAMY,CACR,CAAC,CACH,CAAC,CAAC,EAEFhB,EAAO,IAAI,gBAAiBE,EAAa,MAAOC,EAAcC,IAAkB,CAC9E,IAAMa,EAASd,EAAI,MAAM,OACnBe,EAAW,MAAMZ,EAAgB,eAAe,EAElDW,IAAW,OACbb,EAAI,UAAU,eAAgB,WAAW,EACzCA,EAAI,UAAU,sBAAuB,qCAAqC,EAC1EA,EAAI,KAAKc,EAAS,KAAK,GAEvBd,EAAI,KAAK,CACP,OAAQ,IACR,KAAM,CACJ,IAAKc,EAAS,IACd,YAAaA,EAAS,MAAM,SAAS,QAAQ,CAC/C,CACF,CAAC,CAEL,CAAC,CAAC,EAEF,IAAOC,GAAQnB,EG5If,OAAS,UAAAoB,OAAc,UCEvB,OAAS,uBAAAC,OAA2B,aACpC,OAAOC,OAAQ,cACf,OAAS,QAAAC,OAAY,gBACrB,OAAS,aAAAC,OAAiB,OAC1B,IAAMC,GAAYD,GAAUD,EAAI,EAUnBG,EAAN,KAAsB,CAC3B,aAAa,iBAAyC,CACpD,GAAI,CACF,IAAMC,EAAK,MAAMC,EAAkB,WAAW,EAE9C,GAAI,CAEF,OADe,MAAMD,EAAG,WAAW,UAAU,EAAE,iBAAiB,aAAa,GAC/D,QAChB,OAASE,EAAO,CACd,GAAIA,aAAiBR,IAAuBQ,EAAM,SAAW,IAAK,CAChE,IAAMC,EAAgC,CACpC,UAAW,GACX,SAAU,GACV,SAAU,GACV,MAAO,GACP,SAAU,EACZ,EAEA,aAAM,KAAK,mBAAmBA,CAAe,EACtCA,CACT,CACA,MAAMD,CACR,CACF,OAASA,EAAO,CACd,cAAQ,MAAM,+BAAgCA,CAAK,EAC7C,IAAIE,EAAS,IAAK,sCAAQ,CAClC,CACF,CAEA,aAAa,mBAAmBC,EAAuC,CACrE,GAAI,CACF,IAAML,EAAK,MAAMC,EAAkB,WAAW,EAE9C,GAAI,CACF,IAAMK,EAAS,MAAMN,EAAG,WAAW,UAAU,EAAE,iBAAiB,aAAa,EAC7E,MAAMA,EAAG,WAAW,UAAU,EAAE,OAAOM,EAAO,GAAI,CAChD,SAAUD,CACZ,CAAC,CACH,OAASH,EAAO,CACd,GAAIA,aAAiBR,IAAuBQ,EAAM,SAAW,IAE3D,MAAMF,EAAG,WAAW,UAAU,EAAE,OAAO,CACrC,KAAM,OACN,SAAUK,CACZ,CAAC,MAED,OAAMH,CAEV,CACF,OAASA,EAAO,CACd,cAAQ,MAAM,kCAAmCA,CAAK,EAChD,IAAIE,EAAS,IAAK,sCAAQ,CAClC,CACF,CAEA,aAAa,kBAAiC,CAC5C,GAAI,CAEF,MAAO,CACL,QAFc,MAAMT,GAAG,SAAS,0BAA2B,OAAO,CAGpE,CACF,OAASO,EAAO,CACd,cAAQ,MAAM,iCAAkCA,CAAK,EAC/C,IAAIE,EAAS,IAAK,kDAAU,CACpC,CACF,CAEA,aAAa,oBAAoBG,EAA4C,CAC3E,GAAI,CAEF,MAAMZ,GAAG,UAAU,0BAA2BY,EAAO,QAAS,OAAO,EAGrET,GAAU,2BAA2B,EAAE,MAAMI,GAAS,CACpD,QAAQ,MAAM,uCAAwCA,CAAK,CAC7D,CAAC,EAGD,MACF,OAASA,EAAO,CACd,cAAQ,MAAM,mCAAoCA,CAAK,EACjD,IAAIE,EAAS,IAAK,kDAAU,CACpC,CACF,CAEA,OAAe,oBAAoBI,EAAsB,CACvD,IAAMC,EAAQD,EAAQ,MAAM;AAAA,CAAI,EAC1BE,EAAkB,CAAC,EACrBC,EAAe,GAEnB,QAAWC,KAAQH,EAAO,CACxB,IAAMI,EAAcD,EAAK,KAAK,EAC9B,GAAI,EAAAC,EAAY,WAAW,GAAG,GAAK,CAACA,GAEpC,GAAIA,EAAY,WAAW,OAAO,EAAG,CACnC,IAAMC,EAAQD,EAAY,MAAM,GAAG,EACnCF,EAAeG,EAAM,CAAC,EACtBJ,EAAWC,CAAY,EAAI,CACzB,UAAWA,EACX,KAAMG,EAAM,CAAC,IAAM,OAAS,OAAS,SACrC,QAAS,GACT,QAAS,GACT,QAAS,GACT,IAAK,EACP,CACF,MAAWH,GAAgBD,EAAWC,CAAY,IAC5CE,EAAY,WAAW,SAAS,EAClCH,EAAWC,CAAY,EAAE,QAAUE,EAAY,MAAM,GAAG,EAAE,CAAC,EAClDA,EAAY,WAAW,SAAS,EACzCH,EAAWC,CAAY,EAAE,QAAUE,EAAY,MAAM,GAAG,EAAE,CAAC,EAClDA,EAAY,WAAW,SAAS,EACzCH,EAAWC,CAAY,EAAE,QAAUE,EAAY,MAAM,GAAG,EAAE,CAAC,EAClDA,EAAY,WAAW,gBAAgB,IAChDH,EAAWC,CAAY,EAAE,IAAME,EAAY,MAAM,GAAG,EAAE,CAAC,GAG7D,CAGA,OAAOH,EAAW,MAAW,CAC3B,UAAW,OACX,KAAM,OACN,QAAS,GACT,QAAS,GACT,QAAS,GACT,IAAK,EACP,CACF,CAEA,OAAe,uBAAuBH,EAAqB,CACzD,IAAME,EAAQ,CACZ,UACA,yBACA,GACA,YACA,qBACA,mBAAmBF,EAAO,IAAI,EAChC,EAEA,OAAIA,EAAO,OAAS,WAClBE,EAAM,KAAK,WAAWF,EAAO,OAAO,EAAE,EACtCE,EAAM,KAAK,WAAWF,EAAO,OAAO,EAAE,EAClCA,EAAO,SACTE,EAAM,KAAK,WAAWF,EAAO,OAAO,EAAE,EAEpCA,EAAO,KACTE,EAAM,KAAK,kBAAkBF,EAAO,GAAG,EAAE,GAK7CE,EAAM,KAAK,EAAE,EACbA,EAAM,KAAK,WAAW,EACtBA,EAAM,KAAK,oBAAoB,EAC/BA,EAAM,KAAK,wBAAwB,EACnCA,EAAM,KAAK,yBAAyB,EACpCA,EAAM,KAAK,uBAAuB,EAE3BA,EAAM,KAAK;AAAA,CAAI,EAAI;AAAA,CAC5B,CACF,ED3KA,IAAMM,EAASC,GAAO,EAEtBD,EAAO,IAAI,QAASE,EAAa,MAAOC,EAAcC,IAAkB,CACtE,IAAMC,EAAW,MAAMC,EAAgB,gBAAgB,EACvDF,EAAI,KAAK,CACP,OAAQ,IACR,KAAMC,CACR,CAAC,CACH,CAAC,CAAC,EAEFL,EAAO,KAAK,QAASE,EAAa,MAAOC,EAAcC,IAAkB,CACvE,MAAME,EAAgB,mBAAmBH,EAAI,IAAI,EACjDC,EAAI,KAAK,CACP,OAAQ,IACR,QAAS,gCACX,CAAC,CACH,CAAC,CAAC,EAEFJ,EAAO,IAAI,WAAYE,EAAa,MAAOC,EAAcC,IAAkB,CACzE,IAAMG,EAAS,MAAMD,EAAgB,iBAAiB,EACtDF,EAAI,KAAK,CACP,OAAQ,IACR,KAAMG,CACR,CAAC,CACH,CAAC,CAAC,EAEFP,EAAO,KAAK,WAAYE,EAAa,MAAOC,EAAcC,IAAkB,CAC1E,MAAME,EAAgB,oBAAoBH,EAAI,IAAI,EAClDC,EAAI,KAAK,CACP,OAAQ,IACR,QAAS,4CACX,CAAC,CACH,CAAC,CAAC,EAEF,IAAOI,GAAQR,EEvCf,OAAS,UAAAS,OAAc,UCAvB,OAAOC,OAAQ,cACf,OAAOC,OAAU,OAGV,IAAMC,GAAN,KAAuB,CAC5B,aAAa,aAAaC,EAAaC,EAAmC,CACxE,GAAI,CACF,OAAQA,EAAU,CAChB,IAAK,KACH,OAAO,MAAM,KAAK,mBAAmBD,CAAM,EAE7C,QACE,MAAM,IAAIE,EAAS,IAAK,kDAAU,CACtC,CACF,OAASC,EAAO,CACd,cAAQ,MAAM,2BAA4BA,CAAK,EACzC,IAAID,EAAS,IAAK,sCAAQ,CAClC,CACF,CAEA,aAAqB,mBAAmBF,EAA8B,CACpE,GAAI,CAEF,IAAMI,EAAeC,GAAK,KAAK,QAAQ,IAAI,EAAG,WAAY,qBAAqB,EAC3EC,EAAW,MAAMC,GAAG,SAASH,EAAc,OAAO,EAGhDI,EAAgB,KAAK,qBAAqBR,CAAM,EAGtD,OAAAM,EAAWA,EAAS,QAClB,iDACA;AAAA;AAAA,EAA+CE,CAAa;AAAA,MAC9D,EAEOF,CACT,OAASH,EAAO,CACd,cAAQ,MAAM,8BAA+BA,CAAK,EAC5C,IAAID,EAAS,IAAK,wCAAU,CACpC,CACF,CAEA,OAAe,qBAAqBF,EAAqB,CACvD,IAAMS,EAAkB,CAAC,EAEzB,OAAIT,EAAO,QAAUA,EAAO,OAAO,OAAS,GAE1CA,EAAO,OAAO,QAAQ,CAACU,EAAYC,IAAkB,CAE/CD,EAAM,SACRA,EAAM,QAAQ,QAASE,GAAa,CAClC,IAAMC,EAAU,KAAK,qBAAqBD,EAAKD,EAAO,IAAI,EACpDG,EAAU,KAAK,gBAAgBF,CAAG,EACxCH,EAAM,KAAK,sBAAsBI,CAAO,IAAIC,CAAO,EAAE,CACvD,CAAC,EAICJ,EAAM,SACRA,EAAM,QAAQ,QAASE,GAAa,CAClC,IAAMC,EAAU,KAAK,qBAAqBD,EAAKD,EAAO,IAAI,EACpDG,EAAU,KAAK,gBAAgBF,CAAG,EACxCH,EAAM,KAAK,sBAAsBI,CAAO,IAAIC,CAAO,EAAE,CACvD,CAAC,CAEL,CAAC,EAGIL,EAAM,KAAK;AAAA,CAAI,CACxB,CAEA,OAAe,qBAAqBG,EAAUG,EAAoBC,EAAgC,CAGhG,MADiB,YAAYD,CAAU,IAAIC,CAAS,IAAIJ,EAAI,MAAM,YAAY,CAAC,IAAIA,EAAI,KAAK,YAAY,CAAC,GACzF,QAAQ,cAAe,GAAG,CAC5C,CAEA,OAAe,gBAAgBA,EAAkB,CAE/C,OAAOA,EAAI,QAAU,OAAOA,EAAI,OAAO,GAAK,EAC9C,CACF,ED5EA,IAAMK,GAASC,GAAO,EAEtBD,GAAO,KAAK,YAAaE,EAAa,MAAOC,EAAcC,IAAkB,CAC3E,GAAM,CAAE,OAAAC,EAAQ,SAAAC,CAAS,EAAIH,EAAI,KAC3BI,EAAO,MAAMC,GAAiB,aAAaH,EAAQC,CAAQ,EACjEF,EAAI,KAAK,CACP,OAAQ,IACR,KAAMG,CACR,CAAC,CACH,CAAC,CAAC,EAEF,IAAOE,GAAQT,GbsCf,IAAMU,EAAMC,GAAQ,EAGdC,GAAU,QAAQ,IAAI,EAG5BF,EAAI,IAAIG,GAAK,CAAC,EACdH,EAAI,IAAIC,GAAQ,KAAK,CAAC,EACtBD,EAAI,IAAII,GAAW,CACjB,iBAAkB,GAClB,OAAQ,CACN,SAAU,IAAO,KAAO,IAC1B,CACF,CAAC,CAAC,EAGFJ,EAAI,IAAIC,GAAQ,OAAOI,GAAK,KAAKH,GAAS,MAAM,CAAC,CAAC,EAGlDF,EAAI,IAAI,YAAaM,EAAU,EAC/BN,EAAI,IAAI,gBAAiBO,EAAa,EACtCP,EAAI,IAAI,gBAAiBQ,EAAc,EACvCR,EAAI,IAAI,gBAAiBS,EAAc,EACvCT,EAAI,IAAI,iBAAkBU,EAAe,EAGzCV,EAAI,IAAI,IAAK,CAACW,EAAKC,IAAQ,CACzBA,EAAI,SAASP,GAAK,KAAKH,GAAS,OAAQ,YAAY,CAAC,CACvD,CAAC,EAGDF,EAAI,IAAIa,EAAY,EAGpB,IAAMC,GAAOC,EAAO,KACpBf,EAAI,OAAOc,GAAM,IAAM,CACrB,QAAQ,IAAI,6BAA6BA,EAAI,EAAE,CACjD,CAAC,EAED,IAAOE,GAAQhB", "names": ["require_package", "__commonJSMin", "exports", "module", "require_main", "__commonJSMin", "exports", "module", "fs", "path", "os", "crypto", "packageJson", "version", "LINE", "parse", "src", "obj", "lines", "match", "key", "value", "maybeQuote", "_parseVault", "options", "vaultPath", "_vaultPath", "result", "DotenvModule", "err", "keys", "_dotenv<PERSON>ey", "length", "decrypted", "attrs", "_instructions", "error", "_log", "message", "_warn", "_debug", "dotenvKey", "uri", "environment", "environmentKey", "ciphertext", "<PERSON><PERSON>ault<PERSON><PERSON>", "filepath", "_resolveHome", "envPath", "_config<PERSON>ault", "parsed", "processEnv", "configDotenv", "dotenvPath", "encoding", "debug", "optionPaths", "lastError", "parsedAll", "e", "config", "decrypt", "encrypted", "keyStr", "nonce", "authTag", "aesgcm", "isRange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decryptionFailed", "populate", "override", "express", "cors", "fileUpload", "path", "import_dotenv", "path", "rootDir", "dotenv", "config", "ApiError", "statusCode", "message", "<PERSON><PERSON><PERSON><PERSON>", "err", "req", "res", "next", "Router", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "req", "res", "next", "error", "ApiError", "PocketBase", "PocketBase", "fs", "path", "exec", "promisify", "execAsync", "TemplateService", "config", "syncInfos", "slave", "index", "stdout", "modifiedSyncInfo", "error", "template", "ApiError", "struct", "pdo", "varName", "comment", "slaveIndex", "direction", "conf", "mapping", "rxCount", "txCount", "syncInfo", "si", "type", "lines", "readFunc", "writeFunc", "exec", "promisify", "archiver", "existsSync", "decompress", "execAsync", "PocketBaseManager", "PocketBase", "config", "error", "pb", "token", "ProgramManager", "ApiError", "program", "formData", "uploadDir", "path", "fs", "programName", "archivePath", "programDir", "extractedDir", "file", "extractedPath", "configData", "configStr", "mainProgram", "filePath", "mainProgramPath", "id", "shortId", "shmFile", "template", "tempDir", "templatePath", "outputPath", "middleLayerCmd", "resolve", "middleLayerPid", "userCmd", "userProgramPid", "userLog", "logError", "cleanupError", "execWithLog", "cmd", "stdout", "stderr", "initialCheck", "userCheck", "final<PERSON><PERSON>ck", "record", "slave", "TemplateService", "newProgram", "currentProgram", "programPath", "filename", "programId", "archive", "reject", "chunks", "chunk", "config<PERSON><PERSON>", "router", "Router", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "req", "res", "username", "password", "pb", "PocketBase", "config", "authData", "PocketBaseManager", "error", "id", "oldPassword", "passwordConfirm", "auth_routes_default", "Router", "path", "fsPromises", "createWriteStream", "router", "Router", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "req", "res", "programs", "ProgramManager", "program", "config", "formData", "result", "id", "template", "chunk", "hash", "filename", "index", "chunkDir", "configFile", "size", "masterIndex", "taskFrequency", "configData", "configStr", "error", "filePath", "writeStream", "i", "chunkPath", "chunkData", "resolve", "reject", "cleanupError", "packageBuffer", "program_routes_default", "Router", "perf", "warned", "PROCESS", "emitWarning", "msg", "type", "code", "fn", "AC", "AS", "_", "warnACPolyfill", "reason", "printACPolyfillWarning", "<PERSON><PERSON><PERSON><PERSON>", "TYPE", "isPosInt", "n", "getUintArray", "max", "ZeroArray", "size", "<PERSON><PERSON>", "_<PERSON>ack", "#constructing", "HeapCls", "L<PERSON><PERSON><PERSON>", "_LR<PERSON>ache", "#max", "#maxSize", "#dispose", "#disposeAfter", "#fetchMethod", "#memoMethod", "#size", "#calculatedSize", "#keyMap", "#keyList", "#valList", "#next", "#prev", "#head", "#tail", "#free", "#disposed", "#sizes", "#starts", "#ttls", "#hasDispose", "#hasFetchMethod", "#hasDisposeAfter", "c", "p", "#isBackgroundFetch", "k", "index", "options", "context", "#backgroundFetch", "#moveToTail", "#indexes", "#rindexes", "#isStale", "ttl", "ttlResolution", "ttlAutopurge", "updateAgeOnGet", "updateAgeOnHas", "allowStale", "dispose", "disposeAfter", "noDisposeOnSet", "noUpdateTTL", "maxSize", "maxEntrySize", "sizeCalculation", "fetch<PERSON><PERSON><PERSON>", "memoMethod", "noDeleteOnFetchRejection", "noDeleteOnStaleGet", "allowStaleOnFetchRejection", "allowStaleOnFetchAbort", "ignoreFetchAbort", "UintArray", "#initializeSizeTracking", "#initializeTTLTracking", "key", "ttls", "starts", "#setItemTTL", "start", "t", "#delete", "#updateItemAge", "#statusTTL", "status", "cachedNow", "getNow", "age", "s", "sizes", "#removeItemSize", "#requireSize", "v", "#addItemSize", "#evict", "_i", "_s", "_st", "_k", "_v", "i", "#isValidIndex", "getOptions", "value", "thisp", "deleted", "entry", "remain", "arr", "setOptions", "oldVal", "oldValue", "dt", "task", "val", "free", "head", "hasOptions", "peekOptions", "ac", "signal", "fetchOpts", "cb", "updateCache", "aborted", "ignoreAbort", "fetchFail", "bf", "eb", "er", "allowStaleAborted", "noDelete", "pcall", "res", "rej", "fmp", "b", "fetchOptions", "forceRefresh", "stale", "isStale", "staleVal", "memoOptions", "vv", "fetching", "#connect", "#clear", "pi", "ni", "exec", "promisify", "fs", "path", "execAsync", "promisify", "exec", "cacheOptions", "statusCache", "L<PERSON><PERSON><PERSON>", "mastersCache", "slavesCache", "EthercatService", "_EthercatService", "cachedStatus", "stdout", "running", "status", "masters", "slaves", "sum", "m", "error", "ApiError", "cachedMasters", "currentMaster", "inCommonStats", "macToIface", "lines", "line", "macMatch", "mac", "cachedSlaves", "currentSlave", "inPorts", "currentPorts", "matches", "index", "type", "link", "loop", "signal", "nextSlave", "rxTime", "diff", "nextDc", "slave", "pdoOut", "syncManagers", "currentSM", "currentPDO", "entry", "resolve", "master", "match", "currentIface", "master0Device", "master1Device", "trimmedLine", "master0Match", "master1Match", "config", "otherConfigs", "newLines", "xmlContent", "deviceNameMatch", "deviceName", "rxPdoMatch", "rxPdoIndex", "txPdoMatch", "txPdoIndex", "rxPdos", "rxPdoSection", "entries", "txPdos", "txPdoSection", "dataType", "bitLen", "templatePath", "templateContent", "dotContent", "image", "reject", "dot", "chunks", "chunk", "data", "code", "exec", "promisify", "execAsync", "router", "Router", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "req", "res", "status", "EthercatService", "error", "slaves", "master", "slave", "xml", "svg", "masters", "stdout", "interfaces", "config", "format", "topology", "ethercat_routes_default", "Router", "ClientResponseError", "fs", "exec", "promisify", "execAsync", "SettingsService", "pb", "PocketBaseManager", "error", "defaultSettings", "ApiError", "settings", "record", "config", "content", "lines", "interfaces", "currentIface", "line", "trimmedLine", "parts", "router", "Router", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "req", "res", "settings", "SettingsService", "config", "settings_routes_default", "Router", "fs", "path", "GeneratorService", "config", "language", "ApiError", "error", "templatePath", "path", "template", "fs", "structContent", "lines", "slave", "index", "pdo", "varName", "comment", "slaveIndex", "direction", "router", "Router", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "req", "res", "config", "language", "code", "GeneratorService", "generator_routes_default", "app", "express", "rootDir", "cors", "fileUpload", "path", "auth_routes_default", "program_routes_default", "ethercat_routes_default", "settings_routes_default", "generator_routes_default", "req", "res", "<PERSON><PERSON><PERSON><PERSON>", "PORT", "config", "server_default"]}