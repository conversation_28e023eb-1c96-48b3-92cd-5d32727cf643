{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "node",
    "outDir": "./distBackend",
    "rootDir": "./src",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "noEmitOnError": false, // 允许有错误时仍然输出文件
    "resolveJsonModule": true,
    "noImplicitAny": true, // 忽略索引隐式 any 类型错误
    "strict": false ,        // 关闭所有严格模式检查
    "allowJs": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "ts-node": {
    "esm": true,
    "experimentalSpecifiers": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules"]
} 