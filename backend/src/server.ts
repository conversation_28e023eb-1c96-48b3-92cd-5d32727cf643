// import express from 'express';
// import cors from 'cors';
// import fileUpload from 'express-fileupload';
// import { config } from './config/index.js';
// import { errorHandler } from './utils/errors.js';
// import authRoutes from './routes/auth.routes.js';
// import programRoutes from './routes/program.routes.js';
// import ethercatRoutes from './routes/ethercat.routes.js';
// import settingsRoutes from './routes/settings.routes.js';
// import generatorRoutes from './routes/generator.routes.js';

// const app = express();

// // 中间件
// app.use(cors());
// app.use(express.json());
// app.use(fileUpload({
//   createParentPath: true,
//   limits: { 
//     fileSize: 1000 * 1024 * 1024 // 1000MB max-file-size
//   },
// }));

// // 路由
// app.use('/api/auth', authRoutes);
// app.use('/api/programs', programRoutes);
// app.use('/api/ethercat', ethercatRoutes);
// app.use('/api/settings', settingsRoutes);
// app.use('/api/generator', generatorRoutes);

// // 错误处理中间件
// app.use(errorHandler);

// // 启动服务器
// const PORT = config.port;
// app.listen(PORT, () => {
//   console.log(`Server is running on port ${PORT}`);
// });

// export default app;


import express from 'express';
import cors from 'cors';
import fileUpload from 'express-fileupload';
import path from 'path';
import { fileURLToPath } from 'url';
import { config } from './config/index.js';
import { errorHandler } from './utils/errors.js';
import authRoutes from './routes/auth.routes.js';
import programRoutes from './routes/program.routes.js';
import ethercatRoutes from './routes/ethercat.routes.js';
import settingsRoutes from './routes/settings.routes.js';
import generatorRoutes from './routes/generator.routes.js';
import systemRoutes from './routes/system.routes.js';
import { ProgramManager } from './services/program.manager.js';
import { LoggerService } from './services/logger.service.js';

const app = express();

// 使用 import.meta.url 定义 __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 中间件
app.use(cors());
app.use(express.json());
app.use(fileUpload({
  createParentPath: true,
  limits: { 
    fileSize: 1000 * 1024 * 1024, // 1000MB max-file-size
  },
  uploadTimeout: 300000,  // 5分钟
}));

// 静态文件托管
app.use(express.static(path.join(__dirname, '../dist')));

// API 路由
app.use('/api/auth', authRoutes);
app.use('/api/programs', programRoutes);
app.use('/api/ethercat', ethercatRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/generator', generatorRoutes);
app.use('/api/system', systemRoutes);

// 通配路由，用于 SPA 的前端路由
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist', 'index.html'));
});

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
const PORT = config.port;

// 初始化日志服务
const logger = LoggerService.getInstance();
console.log("日志服务初始化完成");

// 记录应用启动日志
logger.info('Application', 'Starting EtherCAT Web UI application...');

app.listen(PORT, async () => {
  console.log(`Server is running on port ${PORT}`);
  
  // 执行断电恢复
  console.log('Starting power outage recovery...');
  await ProgramManager.handlePowerOutageRecovery();
  console.log('Power outage recovery completed');
});

export default app;
