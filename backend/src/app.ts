import express from 'express';
import fileUpload from 'express-fileupload';
import programRoutes from './routes/program.routes.js';
import settingsRoutes from './routes/settings.routes.js';
import generatorRoutes from './routes/generator.routes.js';
import systemRoutes from './routes/system.routes.js';
// import { LoggerService } from './services/logger.service.js';


const app = express();

// // 初始化日志服务
// const logger = LoggerService.getInstance();
// console.log("日志服务初始化完成");

// // 记录应用启动日志
// logger.info('Application', 'Starting EtherCAT Web UI application...');


// 配置文件上传中间件
app.use(fileUpload({
  limits: { 
    fileSize: 100 * 1024 * 1024,  // 100MB
    files: 2
  },
  useTempFiles: true,
  tempFileDir: '/tmp/',
  abortOnLimit: true,
  uploadTimeout: 300000,  // 5分钟
  debug: true
}));

// 注册路由
app.use('/api/programs', programRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/generator', generatorRoutes);
app.use('/api/system', systemRoutes);

export default app;