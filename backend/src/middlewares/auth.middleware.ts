import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service.js';
import { ApiError } from '../utils/errors.js';

export async function authMiddleware(req: Request, res: Response, next: NextFunction) {
  const token = req.headers.authorization;

  if (!token) {
    throw new ApiError(401, '未提供认证令牌');
  }

  const isValid = await AuthService.verifyToken(token);
  if (!isValid) {
    throw new ApiError(401, '认证令牌无效或已过期');
  }

  next();
} 