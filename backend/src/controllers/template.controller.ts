import { Request, Response } from 'express';
import { TemplateService } from '../services/template.service.js';
import { ApiError } from '../utils/errors.js';

export class TemplateController {
  static async generateTemplate(req: Request, res: Response) {
    try {
      const config = req.body;
      
      // 验证配置
      if (!config.slaves || !Array.isArray(config.slaves) || config.slaves.length === 0) {
        throw new ApiError(400, '无效的从站配置');
      }

      // 生成模板
      const template = await TemplateService.generateTemplate(config);
      
      res.json({
        status: 200,
        data: template
      });
    } catch (error) {
      console.error('Failed to generate template:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '生成模板失败');
    }
  }

  static async validateConfig(req: Request, res: Response) {
    try {
      const config = req.body;
      
      // 验证配置
      await TemplateService.validateConfig(config);
      
      res.json({
        status: 200,
        message: '配置有效'
      });
    } catch (error) {
      console.error('Config validation failed:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(400, '配置验证失败');
    }
  }
} 