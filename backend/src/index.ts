import app from './app.js';
import path from 'path';
import express from 'express';
import { ProgramManager } from './services/program.manager.js';

const port = process.env.PORT || 3000;



// 静态文件托管，将前端编译生成的 dist 目录作为静态文件目录
app.use(express.static(path.join(__dirname, 'dist')));

// 处理 SPA 路由，将所有未匹配的请求重定向到 index.html
app.get('*', (req: any, res: any) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

app.listen(port, async () => {
  console.log(`Server is running on port ${port}`);
  
  // 打印所有注册的路由
  app._router.stack.forEach((r: any) => {
    if (r.route && r.route.path) {
      console.log(`Route registered: ${r.route.stack[0].method.toUpperCase()} ${r.route.path}`);
    }
  });

  // 系统启动时尝试恢复断电前运行的程序
  console.log('Attempting to recover programs after system startup...');
  try {
    await ProgramManager.handlePowerOutageRecovery();
    console.log('Power outage recovery process completed');
  } catch (error) {
    console.error('Failed to recover programs:', error);
  }
});