import express, { Request, Response } from 'express';
import { ProgramManager } from '../services/program.manager.js';
import { asyncHandler } from '../utils/async-handler.js';
import { authMiddleware } from '../middleware/auth.middleware.js';
import path from 'path';
import { promises as fs } from 'fs';
import { execAsync } from '../utils/exec-async.js';
import { ApiError } from '../utils/api-error.js';
import type { UploadedFile } from 'express-fileupload';

const router = express.Router();

// Get platform logs
router.get('/platform/logs', authMiddleware, asyncHandler(async (req, res) => {
  const logs = await ProgramManager.getPlatformLogs();
  res.json({
    success: true,
    logs
  });
}));

// Restart platform
router.post('/platform/restart', authMiddleware, asyncHandler(async (req, res) => {
  await ProgramManager.restartPlatform();
  res.json({
    success: true
  });
}));

// Update platform
router.post('/platform/update', asyncHandler(async (req: Request, res: Response) => {
  try {
    if (!req.files?.package) {
      throw new ApiError(422, '缺少更新包文件');
    }

    const updatePackage = req.files.package as UploadedFile;
    const tempDir = path.join(process.cwd(), 'temp');
    
    // 创建带时间戳的更新目录
    const timestamp = new Date().toISOString().replace(/[-:]/g, '').slice(0, 12);
    const updateDir = path.join(tempDir, `update_${timestamp}`);
    const packagePath = path.join(updateDir, updatePackage.name);

    // 创建临时目录
    await fs.mkdir(updateDir, { recursive: true });

    // 移动上传的文件到临时目录
    await updatePackage.mv(packagePath);

    // 解压文件到更新目录
    await execAsync(`tar -zxpf ${packagePath} -C ${updateDir}`);

    // 递归查找 install.sh
    async function findInstallScript(dir: string): Promise<string | null> {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        if (entry.isDirectory()) {
          const found = await findInstallScript(fullPath);
          if (found) return found;
        } else if (entry.name === 'install.sh') {
          return fullPath;
        }
      }
      return null;
    }

    // 查找并执行安装脚本
    const installScript = await findInstallScript(updateDir);
    if (!installScript) {
      throw new ApiError(422, '更新包中未找到安装脚本');
    }

    // 添加执行权限并运行安装脚本
    await fs.chmod(installScript, 0o755);
    const scriptDir = path.dirname(installScript);
    await execAsync(`cd ${scriptDir} && bash ./install.sh`);

    // 清理临时文件
    await fs.rm(updateDir, { recursive: true });

    res.json({
      status: 200,
      message: '更新成功'
    });
  } catch (error: any) {
    console.error('Update failed:', error);
    res.status(500).json({
      status: 500,
      message: error.message || '更新失败'
    });
  }
}));

export default router;