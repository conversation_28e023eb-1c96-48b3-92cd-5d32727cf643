import { Router, Request, Response } from 'express';
import { asyncHandler } from '../utils/async-handler.js';
import { TemplateService } from '../services/template.service.js';

const router = Router();

router.post('/generate', asyncHandler(async (req: Request, res: Response) => {
  const config = req.body;
  const template = await TemplateService.generateCTemplate(config);
  res.json({
    status: 200,
    data: template
  });
}));

export default router; 