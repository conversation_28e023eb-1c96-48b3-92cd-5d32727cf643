import { Router } from 'express';
import { GeneratorService } from '../services/generator.service.js';
import { asyncHandler } from '../utils/async-handler.js';
import type { Request, Response } from 'express';

const router = Router();

router.post('/generate', asyncHandler(async (req: Request, res: Response) => {
  const { config, language } = req.body;
  const code = await GeneratorService.generateCode(config, language);
  res.json({
    status: 200,
    data: code
  });
}));

export default router; 