import { Router, Request, Response } from 'express';
import { asyncHandler } from '../utils/async-handler.js';
import { UserService } from '../services/user.service.js';
import { authMiddleware } from '../middlewares/auth.middleware.js';

const router = Router();

router.use(authMiddleware); // 所有用户管理接口都需要认证

router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const users = await UserService.getUsers();
  res.json({
    status: 200,
    data: users
  });
}));

router.post('/', asyncHandler(async (req: Request, res: Response) => {
  const user = await UserService.createUser(req.body);
  res.json({
    status: 200,
    data: user
  });
}));

router.put('/:id', asyncHandler(async (req: Request, res: Response) => {
  const user = await UserService.updateUser(req.params.id, req.body);
  res.json({
    status: 200,
    data: user
  });
}));

router.delete('/:id', asyncHandler(async (req: Request, res: Response) => {
  await UserService.deleteUser(req.params.id);
  res.json({
    status: 200,
    data: null
  });
}));

router.post('/:id/reset-password', asyncHandler(async (req: Request, res: Response) => {
  await UserService.resetPassword(req.params.id, req.body.password);
  res.json({
    status: 200,
    data: null
  });
}));

export default router; 