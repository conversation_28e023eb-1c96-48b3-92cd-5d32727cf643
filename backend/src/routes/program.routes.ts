import { Router } from 'express';
import { ProgramManager } from '../services/program.manager.js';
import { asyncHandler } from '../utils/async-handler.js';
import { ApiError } from '../utils/errors.js';
import { ExternalApiLogger } from '../utils/external-api-logger.js';
import type { NextFunction, Request, Response } from 'express';
import type { UploadedFile } from 'express-fileupload';
import path from 'path';
import fs from 'fs';
import { promises as fsPromises } from 'fs';
import { createWriteStream } from 'fs';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';

const router = Router();

// 获取程序列表
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  try {
    const programs = await ProgramManager.getPrograms();
    res.json({
      status: 200,
      data: programs
    });
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Get program list error:', error);
    res.status(500).json({
      status: 500,
      message: '获取程序列表失败'
    });
  }
}));

// 上传程序
router.post('/', asyncHandler(async (req: Request, res: Response) => {
  try {
    if (!req.files || !req.files.program || !req.files.config) {
      throw new ApiError(422, '缺少必需的文件');
    }

    const program = req.files.program as UploadedFile;
    const config = req.files.config as UploadedFile;
    const formData = req.body;

    const result = await ProgramManager.uploadProgram(program, config, formData);
    res.json({
      status: 200,
      data: result
    });
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Upload program error:', error);
    res.status(500).json({
      status: 500,
      message: '上传程序失败'
    });
  }
}));

router.post('/:id/start', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await ProgramManager.startProgram(id);
    res.json({
      status: 200,
      message: '启动成功'
    });
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码和消息
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    // 其他错误作为内部错误处理
    console.error('[Program Routes] Start program error:', error);
    res.status(500).json({
      status: 500,
      message: '启动失败'
    });
  }
}));

router.post('/:id/stop', asyncHandler(async (req: Request, res: Response) => {
  try {
    await ProgramManager.stopProgram(req.params.id);
    res.json({
      status: 200,
      data: null
    });
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Stop program error:', error);
    res.status(500).json({
      status: 500,
      message: '停止程序失败'
    });
  }
}));

router.delete('/:id', asyncHandler(async (req: Request, res: Response) => {
  try {
    await ProgramManager.deleteProgram(req.params.id);
    res.json({
      status: 200,
      data: null
    });
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Delete program error:', error);
    res.status(500).json({
      status: 500,
      message: '删除程序失败'
    });
  }
}));

router.put('/:id/config', asyncHandler(async (req: Request, res: Response) => {
  try {
    const result = await ProgramManager.updateConfig(req.params.id, req.body);
    res.json({
      status: 200,
      data: result
    });
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Update config error:', error);
    res.status(500).json({
      status: 500,
      message: '更新配置失败'
    });
  }
}));

router.get('/:id/template', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const template = await ProgramManager.generateTemplate(id);
  
    // 直接发送模板内容，不要包装在对象中
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Content-Disposition', `attachment; filename=ethercat_${id}.c`);
    res.send(template);
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Get template error:', error);
    res.status(500).json({
      status: 500,
      message: '获取模板失败'
    });
  }
}));

router.post('/:id/replace', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const program = req.files?.program as UploadedFile;

    if (!program) {
      throw new ApiError(422, '未找到程序文件');
    }

    const result = await ProgramManager.replaceProgram(id, program);
    res.json({
      status: 200,
      data: result
    });
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Replace program error:', error);
    res.status(500).json({
      status: 500,
      message: '替换程序失败'
    });
  }
}));

// 处理分片上传（仅用于程序文件）
router.post('/chunk', asyncHandler(async (req: Request, res: Response) => {
  try {
    if (!req.files?.chunk) {
      throw new ApiError(422, '缺少分片文件');
    }

    const chunk = req.files.chunk as UploadedFile;
    const { hash, filename, index } = req.body;

    if (!hash || !filename || index === undefined) {
      throw new ApiError(422, '缺少必需的参数');
    }

    // 保存分片
    const chunkDir = path.join(process.cwd(), 'uploads', 'chunks', hash);
    await fsPromises.mkdir(chunkDir, { recursive: true });
    await chunk.mv(path.join(chunkDir, index));

    res.json({ status: 200 });
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Upload chunk error:', error);
    res.status(500).json({
      status: 500,
      message: '上传分片失败'
    });
  }
}));

// 合并分片并完成上传
router.post('/merge', asyncHandler(async (req: Request, res: Response) => {
  try {
    if (!req.files?.config) {
      throw new ApiError(422, '缺少配置文件');
    }

    const configFile = req.files.config as UploadedFile;
    const { filename, hash, size, masterIndex, taskFrequency } = req.body;

    if (!filename || !hash || !size || masterIndex === undefined || taskFrequency === undefined) {
      throw new ApiError(422, '缺少必需的参数');
    }
  
    // 读取配置文件内容
    let configData;
    try {
      const configStr = configFile.data.toString('utf-8');
      configData = JSON.parse(configStr);
    } catch (error) {
      console.error('Failed to parse config:', error);
      throw new ApiError(422, '配置文件格式错误');
    }

    // 检查分片目录是否存在
    const chunkDir = path.join(process.cwd(), 'uploads', 'chunks', hash);
    if (!await fsPromises.access(chunkDir).then(() => true).catch(() => false)) {
      throw new ApiError(422, '未找到上传的分片文件');
    }

    const filePath = path.join(process.cwd(), 'uploads', filename);
  
    try {
      // 按顺序合并分片
      const writeStream = createWriteStream(filePath);
      
      for (let i = 0; i < size; i++) {
        const chunkPath = path.join(chunkDir, i.toString());
        if (!await fsPromises.access(chunkPath).then(() => true).catch(() => false)) {
          throw new ApiError(422, `缺少分片文件: ${i}`);
        }
        const chunkData = await fsPromises.readFile(chunkPath);
        writeStream.write(chunkData);
      }
      
      // 等待写入完成
      await new Promise((resolve, reject) => {
        writeStream.on('finish', resolve);
        writeStream.on('error', reject);
        writeStream.end();
      });

      // 清理分片文件
      try {
        await fsPromises.rm(chunkDir, { recursive: true, force: true });
      } catch (error) {
        console.log('Chunk directory already cleaned up');
      }

      // 创建程序记录
      const result = await ProgramManager.createProgram(filename, filePath, {
        masterIndex,
        taskFrequency,
        config: configData
      });
      
      res.json({ status: 200, data: result });
    } catch (error: any) {
      // 清理临时文件
      try {
        await fsPromises.unlink(filePath);
        if (await fsPromises.access(chunkDir).then(() => true).catch(() => false)) {
          await fsPromises.rm(chunkDir, { recursive: true, force: true });
        }
      } catch (cleanupError) {
        console.error('Cleanup error:', cleanupError);
      }
      // 如果是ApiError，保持原始状态码
      if (error instanceof ApiError) {
        res.status(error.statusCode).json({
          status: error.statusCode,
          message: error.message
        });
        return;
      }
      console.error('[Program Routes] Merge chunks error:', error);
      res.status(500).json({
        status: 500,
        message: '合并分片失败'
      });
    }
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Merge chunks error:', error);
    res.status(500).json({
      status: 500,
      message: '合并分片失败'
    });
  }
}));

// 添加程序包下载路由
router.get('/:id/package', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const packageBuffer = await ProgramManager.downloadFullPackage(id);
  
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename=program_package.zip`);
    res.send(packageBuffer);
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Download package error:', error);
    res.status(500).json({
      status: 500,
      message: '下载程序包失败'
    });
  }
}));

// 获取程序日志
router.get('/:id/logs', asyncHandler(async (req: Request, res: Response) => {
  try {
    const logs = await ProgramManager.getProgramLogs(req.params.id);
    res.json({
      status: 200,
      data: logs
    });
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Get program logs error:', error);
    res.status(500).json({
      status: 500,
      message: '获取程序日志失败'
    });
  }
}));

// 下载中间层代码
router.get('/:id/middleware-code', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const fileContent = await ProgramManager.downloadMiddlewareCode(id);
    
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Content-Disposition', `attachment; filename=middleware.c`);
    res.send(fileContent);
  } catch (error: any) {
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Download middleware code error:', error);
    res.status(500).json({
      status: 500,
      message: '下载中间层代码失败'
    });
  }
}));

// 给第三方程序使用的路由
router.post('/start-middleware', checkLocalhost, asyncHandler(async (req: Request, res: Response) => {
  console.log('=== 外部调用：启动中间层 ===');
  console.log('请求参数:', {
    programName: req.body.programName,
    sdk: req.body.sdk
  });
  try {
    const { programName, sdk = false } = req.body;
    
    if (!programName) {
      throw new ApiError(422, '缺少程序名称');
    }

    // 先获取程序列表
    const programs = await ProgramManager.getProgramsForMiddleware();
    
    // 查找匹配的程序
    const program = programs.find(p => p.name === programName);
    
    if (!program) {
      throw new ApiError(404, `未找到名为 ${programName} 的程序`, {
        availablePrograms: programs.map(p => ({
          name: p.name,
          id: p.id
        }))
      });
    }

    // 传递type参数给startMiddleware
    const shmFile = await ProgramManager.startMiddleware(program.id, sdk);
    
    // 成功返回前打印
    const successResponse = {
      code: 0,
      msg: "中间层启动成功",
      data: {
        shmFile,
        sdk // 在响应中也包含使用的类型
      }
    };
    console.log("成功返回数据：\n", JSON.stringify(successResponse, null, 2));
    
    // 记录访问日志
    await ExternalApiLogger.log('外部接口启动中间层', successResponse);
    
    res.json(successResponse);
    
  } catch (error: any) {
    // 错误情况下返回JSON格式，包含可用程序列表
    let errorResponse;
    if (error instanceof ApiError) {
      errorResponse = {
        code: error.statusCode,
        msg: `中间层启动失败：${error.message}`,
        data: error.data? {errorData: error.data} : {} // 如果有额外数据（如 availablePrograms）
      };
    } else {
      errorResponse = {
        code: 500,
        msg: `中间层启动失败：${error.message}`,
        data: {}
      };
    }

    // 失败返回前打印
    console.error("失败返回数据：\n", JSON.stringify(errorResponse, null, 2));
    
    // 记录错误日志
    await ExternalApiLogger.log('start-middleware', errorResponse);
    
    res.status(errorResponse.code).json(errorResponse);
  }
}));

// 给前端使用的路由
router.post('/:id/start-middleware', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    console.log("=====  startMiddleware internal =====", id);
    const shmFile = await ProgramManager.startMiddleware(id);
    
    // 直接返回共享内存文件路径字符串
    res.setHeader('Content-Type', 'text/plain');
    res.send(shmFile);
  } catch (error: any) {
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Start middleware error:', error);
    res.status(500).json({
      status: 500,
      message: '启动中间层程序失败'
    });
  }
}));

// 停止中间层
router.post('/:id/stop-middleware', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await ProgramManager.stopMiddleware(id);
    res.json({ status: 200, message: '停止成功' });
  } catch (error: any) {
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    res.status(500).json({
      status: 500,
      message: '停止中间层失败'
    });
  }
}));

// 给第三方程序使用的停止中间层路由
router.post('/stop-middleware', checkLocalhost, asyncHandler(async (req: Request, res: Response) => {
  console.log('=== 外部调用：停止中间层 ===');
  console.log('请求参数:', {
    programName: req.body.programName
  });
  try {
    const { programName } = req.body;
    console.log('Stop middleware request received for:', programName);
    if (!programName) {
      throw new ApiError(422, '缺少程序名称');
    }

    // 先获取程序列表
    const programs = await ProgramManager.getProgramsForMiddleware();
    
    // 查找匹配的程序
    const program = programs.find(p => p.name === programName);
    
    if (!program) {
      throw new ApiError(404, `未找到名为 ${programName} 的程序`, {
        availablePrograms: programs.map(p => ({
          name: p.name,
          id: p.id
        }))
      });
    }

    // 停止中间层服务
    await ProgramManager.stopMiddleware(program.id);
    
    const successResponse = {
      code: 0,
      msg: '中间层服务成功停止',
      data: {}
    };

    // 记录访问日志
    await ExternalApiLogger.log('stop-middleware', successResponse);
    
    res.json(successResponse);

  } catch (error: any) {
    // 错误情况下返回JSON格式，包含可用程序列表
    let errorResponse;
    if (error instanceof ApiError) {
      errorResponse = {
        code: error.statusCode,
        msg: error.message,
        data: {}
      };
    } else {
      errorResponse = {
        code: 500,
        msg: error.message,
        data: {}
      };
    }

    // 记录错误日志
    await ExternalApiLogger.log('stop-middleware', errorResponse);

    res.status(errorResponse.code).json(errorResponse);
  }
}));

// 外部删除程序接口
router.post('/external-delete', checkLocalhost, asyncHandler(async (req: Request, res: Response) => {
  console.log('=== 外部调用：删除程序 ===');
  console.log('请求参数:', {
    programName: req.body.programName
  })
  try {
    const { programName } = req.body;
    if (!programName) {
      throw new ApiError(422, '缺少程序名称');
    }
    await ProgramManager.deleteProgram_external(programName);
    const successResponse = {
      code: 0,
      msg: '程序删除成功',
      data: {}
    };

    // 记录访问日志
    await ExternalApiLogger.log('external-delete', successResponse);

    res.json(successResponse);
  } catch (error: any) {
    const errorResponse = {
      code: 500,
      msg: '程序删除失败',
      data: {}
    };

    // 记录错误日志
    await ExternalApiLogger.log('external-delete', errorResponse);

    res.status(500).json(errorResponse);
  }
}));

// 检查本地主机的中间件
function checkLocalhost(req: Request, res: Response, next: NextFunction) {
  return next();
  // const allowedHosts = ['127.0.0.1', 'localhost'];
  // const requestHost = req.get('Host');
  // const requestOrigin = req.get('Origin');

  // if (requestHost && allowedHosts.includes(requestHost.split(':')[0])) {
  //   return next();
  // }

  // if (requestOrigin && allowedHosts.includes(new URL(requestOrigin).hostname)) {
  //   return next();
  // }

  // return res.status(403).json({
  //   status: 403,
  //   message: 'Forbidden: Access is allowed only from localhost'
  // });
}

// 更新程序自启动状态
router.put('/:id/startup', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { startup, masterIndex } = req.body;

    if (startup !== '0' && startup !== '1') {
      throw new ApiError(422, '无效的自启动状态值');
    }

    if (!masterIndex) {
      throw new ApiError(422, '缺少主站索引');
    }

    const result = await ProgramManager.updateStartup(id, startup, masterIndex);
    res.json({
      status: 200,
      data: result
    });
  } catch (error: any) {
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    res.status(500).json({
      status: 500,
      message: '更新自启动状态失败'
    });
  }
}));

// 添加外部上传程序的路由
router.post('/external-upload', checkLocalhost, asyncHandler(async (req: Request, res: Response) => {
  console.log('=== 外部调用：上传程序 ===');
  console.log('请求参数:', {
    name: req.body.name,
    masterIndex: req.body.masterIndex,
    force: req.body.force,
    files: {
      program: req.files?.program ? (req.files.program as UploadedFile).name : undefined,
      config: req.files?.config ? (req.files.config as UploadedFile).name : undefined
    }
  });
  try {
    if (!req.files?.program || !req.files?.config) {
      throw new ApiError(422, '缺少必需的文件');
    }

    const program = req.files.program as UploadedFile;
    const config = req.files.config as UploadedFile;
    const formData = req.body;

    // 验证必需的段
    if (!formData.name || !formData.masterIndex) {
      throw new ApiError(422, '缺少必需的参数 (name, masterIndex)');
    }

    // force 参数，默认为 0
    const force = formData.force === '1' || formData.force === 1;

    const result = await ProgramManager.uploadProgram_external(program, config, formData, force);
    const successResponse = {
      code: 0,
      msg: "程序上传成功",
      data: {
        record: result
      }
    };

    // 记录访问日志
    await ExternalApiLogger.log('external-upload', successResponse);

    res.json(successResponse);
  } catch (error: any) {
    let errorResponse;
    if (error instanceof ApiError) {
      errorResponse = {
        code: error.statusCode,
        msg: `程序上传失败: ${error.message}`,
        data: {}
      };
    } else {
      errorResponse = {
        code: 500,
        msg: `程序上传失败: ${error.message}`,
        data: {}
      };
    }

    // 记录错误日志
    await ExternalApiLogger.log('external-upload', errorResponse);

    res.status(errorResponse.code).json(errorResponse);
  }
}));

// 给第三方程序使用的启动程序路由
router.post('/start-program', checkLocalhost, asyncHandler(async (req: Request, res: Response) => {
  console.log('=== 外部调用：启动程序 ===');
  console.log('请求参数:', {
    programName: req.body.programName
  });
  try {
    const { programName } = req.body;
    
    if (!programName) {
      throw new ApiError(422, '缺少程序名称');
    }

    // 先获取程序列表
    const programs = await ProgramManager.getProgramsForMiddleware();
    
    // 查找匹配的程序
    const program = programs.find(p => p.name === programName);
    
    if (!program) {
      throw new ApiError(404, `未找到名为 ${programName} 的程序`, {
        availablePrograms: programs.map(p => ({
          name: p.name,
          id: p.id
        }))
      });
    }

    await ProgramManager.startProgram_external(program.id);
    
    const successResponse = {
      code: 0,
      msg: '程序启动成功',
      data: {}
    };

    // 记录访问日志
    await ExternalApiLogger.log('start-program', successResponse);

    res.json(successResponse);

  } catch (error: any) {
    let errorResponse;
    if (error instanceof ApiError) {
      errorResponse = {
        code: error.statusCode,
        msg: `程序启动失败：${error.message}`,
        data: {}
      };
    } else {
      errorResponse = {
        code: 500,
        msg: `程序启动失败：${error.message}`,
        data: {}
      };
    }

    // 记录错误日志
    await ExternalApiLogger.log('start-program', errorResponse);

    res.status(errorResponse.code).json(errorResponse);
  }
}));

// 给第三方程序使用的停止程序路由
router.post('/stop-program', checkLocalhost, asyncHandler(async (req: Request, res: Response) => {
  console.log('=== 外部调用：停止程序 ===');
  console.log('请求参数:', {
    programName: req.body.programName
  });
  try {
    const { programName } = req.body;
    console.log('Stop program request received for:', programName);
    
    if (!programName) {
      throw new ApiError(422, '缺少程序名称');
    }

    // 先获取程序列表
    const programs = await ProgramManager.getProgramsForMiddleware();
    
    // 查找匹配的程序
    const program = programs.find(p => p.name === programName);
    
    if (!program) {
      throw new ApiError(404, `未找到名为 ${programName} 的程序`, {
        availablePrograms: programs.map(p => ({
          name: p.name,
          id: p.id
        }))
      });
    }

    await ProgramManager.stopProgram_external(program.id);
    
    const successResponse = {
      code: 0,
      msg: '程序成功停止',
      data: {}
    };

    // 记录访问日志
    await ExternalApiLogger.log('stop-program', successResponse);

    res.json(successResponse);

  } catch (error: any) {
    let errorResponse;
    if (error instanceof ApiError) {
      errorResponse = {
        code: error.statusCode,
        msg: `程序停止失败：${error.message}`,
        data: {}
      };
    } else {
      errorResponse = {
        code: 500,
        msg: `程序停止失败：${error.message}`,
        data: {}
      };
    }

    // 记录错误日志
    await ExternalApiLogger.log('stop-program', errorResponse);

    res.status(errorResponse.code).json(errorResponse);
  }
}));

// 添加外部替换JSON配置的路由
router.post('/replace-config', checkLocalhost, asyncHandler(async (req: Request, res: Response) => {
  console.log('=== 外部调用：替换配置 ===');
  console.log('请求参数:', {
    programName: req.body.programName,
    config: req.files?.config ? (req.files.config as UploadedFile).name : undefined
  });
  try {
    if (!req.files?.config) {
      throw new ApiError(422, '缺少配置文件');
    }

    const { programName } = req.body;
    if (!programName) {
      throw new ApiError(422, '缺少程序名称');
    }

    const config = req.files.config as UploadedFile;

    // 先获取程序列表
    const programs = await ProgramManager.getProgramsForMiddleware();
    
    // 查找匹配的程序
    const program = programs.find(p => p.name === programName);
    
    if (!program) {
      throw new ApiError(404, `未找到名为 ${programName} 的程序`, {
        availablePrograms: programs.map(p => ({
          name: p.name,
          id: p.id
        }))
      });
    }

    await ProgramManager.replaceConfig_external(program.id, config);
    
    res.json({
      code: 0,
      msg: '配置更新成功',
      data: {}
    });

  } catch (error: any) {
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        code: error.statusCode,
        msg: `配置更新失败：${error.message}`,
        data: {}
      });
      return;
    }
    console.error('[Program Routes] Replace config error:', error);
    res.status(500).json({
      code: 500,
      msg: `配置更新失败：${error.message}`,
      data: {}
    });
  }
}));


router.put('/:id/cpu-binding', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { programCpu, middlewareCpu } = req.body;
    
    await ProgramManager.updateCpuBinding(id, programCpu, middlewareCpu);
    
    res.json({
      status: 200,
      message: 'CPU绑定更新成功'
    });
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Update CPU binding error:', error);
    res.status(500).json({
      status: 500,
      message: '更新CPU绑定失败'
    });
  }
}));

// 添加新的路由
router.get('/system-info', asyncHandler(async (req: Request, res: Response) => {
  try {
    const systemInfo = await ProgramManager.getSystemInfo();
    res.json({
      status: 200,
      data: systemInfo
    });
  } catch (error: any) {
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Get system info error:', error);
    res.status(500).json({
      status: 500,
      message: '获取系统信息失败'
    });
  }
}));

// 获取程序的 CPU 绑定信息
router.get('/:id/cpu-binding', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const cpuBinding = await ProgramManager.getCpuBinding(id);
    res.json({
      status: 200,
      data: cpuBinding
    });
  } catch (error: any) {
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Program Routes] Get CPU binding error:', error);
    res.status(500).json({
      status: 500,
      message: '获取CPU绑定信息失败'
    });
  }
}));

// 获取调试模式状态
router.get('/debug-mode', asyncHandler(async (req: Request, res: Response) => {
  try {
    const debug = await ProgramManager.getDebugMode();
    res.json({
      status: 200,
      data: { debug }
    });
  } catch (error: any) {
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message,
        data: { debug: false }
      });
      return;
    }
    console.error('[Program Routes] Get debug mode error:', error);
    res.status(500).json({
      status: 500,
      message: '获取调试模式状态失败',
      data: { debug: false }
    });
  }
}));

// 更新调试模式状态
router.post('/debug-mode', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { debug } = req.body;
    
    if (debug === undefined) {
      throw new ApiError(422, '缺少debug参数');
    }
    
    const success = await ProgramManager.updateDebugMode(!!debug);
    
    if (!success) {
      throw new ApiError(500, '更新调试模式失败');
    }
    
    res.json({
      status: 200,
      message: `调试模式已${debug ? '启用' : '禁用'}`,
      data: { debug }
    });
  } catch (error: any) {
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message,
        data: { debug: false }
      });
      return;
    }
    console.error('[Program Routes] Update debug mode error:', error);
    res.status(500).json({
      status: 500,
      message: '更新调试模式失败',
      data: { debug: false }
    });
  }
}));

// 获取程序，中间层运行状态
router.get('/program-status', asyncHandler(async (req: Request, res: Response) => {
  console.log('=== 外部调用：获取程序，中间层运行状态 ===');
  console.log('请求参数：', {
    programName: req.query.programName
  });
  try {
    const { programName } = req.query;

    if (!programName || typeof programName !== 'string') {
      throw new ApiError(422, '缺少程序名称')
    }

    const { program, middleware} = await ProgramManager.getProgramStatus(programName as string);
    const successResponse = {
      code: 0,
      msg: '获取程序状态成功',
      data: {
        programStatus: program,
        middlewareStatus: middleware
      }
    }
    return res.json(successResponse);
  } catch (error: any) {
    let errorResponse;
    if (error instanceof ApiError) {
      errorResponse = {
        code: error.statusCode,
        msg: `获取程序状态失败: ${error.message}`,
        data: {}
      }
    } else {
      errorResponse = {
        code: 500,
        msg: `获取程序状态失败:${error.message}`,
        data: {}
      }
    }
    return res.status(errorResponse.code).json(errorResponse);
  }
}))

// 获取程序主站信息
router.get('/master-info', checkLocalhost, asyncHandler(async (req: Request, res: Response) => {
  console.log('=== 外部调用：获取程序主站信息 ===');
  console.log('请求参数:', {
    programName: req.query.programName
  });
  try {
    const { programName } = req.query;
    
    if (!programName || typeof programName !== 'string') {
      throw new ApiError(422, '缺少程序名称');
    }

    // 获取主站信息
    const masterIndex = await ProgramManager.getMasterInfo(programName);
    
    const successResponse = {
      code: 0,
      msg: '获取主站信息成功',
      data: {
        masterIndex
      }
    };

    // 记录访问日志
    await ExternalApiLogger.log('master-info', successResponse);

    res.json(successResponse);

  } catch (error: any) {
    let errorResponse;
    if (error instanceof ApiError) {
      errorResponse = {
        code: error.statusCode,
        msg: `获取主站信息失败：${error.message}`,
        data: {}
      };
    } else {
      errorResponse = {
        code: 500,
        msg: `获取主站信息失败：${error.message}`,
        data: {}
      };
    }

    // 记录错误日志
    await ExternalApiLogger.log('master-info', errorResponse);

    res.status(errorResponse.code).json(errorResponse);
  }
}));

export default router;