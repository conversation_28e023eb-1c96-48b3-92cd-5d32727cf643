import { Router, Request, Response } from 'express';
import { asyncHand<PERSON> } from '../utils/async-handler.js';
import { EthercatService } from '../services/ethercat.service.js';
import { exec } from 'child_process';
import { promisify } from 'util';
import { spawn } from 'child_process';
import { UploadedFile } from 'express-fileupload';
import { ExternalApiLogger } from '../utils/external-api-logger.js';
import { ApiError } from '../utils/errors.js';
import type { NextFunction } from 'express';

const execAsync = promisify(exec);

const router = Router();

// 添加类型定义
interface EniConfig {
  slaveIndex: number;
  rxPdo: string[];
  txPdo: string[];
  sdos: string[];
  xmlContent: string;
  slaves?: {
    slaveIndex: number;
    rxPdo: string[];
    txPdo: string[];
    sdos: string[];
  }[];
}

router.get('/status', asyncHandler(async (req: Request, res: Response) => {
  try {
    const status = await EthercatService.getStatus();
   // console.log('Sending EtherCAT status:', status);
    res.json({
      status: 200,
      data: status
    });
  } catch (error) {
    console.error('Error getting EtherCAT status:', error);
    throw error;
  }
}));

router.get('/slaves', asyncHandler(async (req: Request, res: Response) => {
  const slaves = await EthercatService.getSlaves();
  res.json({
    status: 200,
    data: slaves
  });
}));

/**
 * 获取所有从站的状态
 * 路径: /all-slaves-status
 * 方法: GET
 * 参数: detail - 可选，默认为0，当为0时仅返回统计数据，非0时返回详细信息
 */
router.get('/all-slaves-status', asyncHandler(async (req: Request, res: Response) => {
  try {
    // 获取detail参数，默认为0
    const detail = parseInt(req.query.detail as string) || 0;
    const masterIndex = parseInt(req.query.masterIndex as string) || -1;


    console.log(`外部调用：获取所有从站信息，detail：${detail}`)
    
    // 调用更新后的服务方法，传入detail参数
    const slavesStatus = await EthercatService.getAllSlavesStatus(detail, masterIndex);
    
    const successResponse = {
      code: 0,
      msg: '获取所有从站状态成功',
      data: slavesStatus
    }
    
    // 记录访问日志
    // await ExternalApiLogger.log('all-slaves-status', successResponse);
    

    // 返回所有从站状态
    return res.json(successResponse);
  } catch (error: any) {
    // 使用统一的返回格式处理错误
    const statusCode = error.statusCode || 500;
    return res.status(statusCode).json({
      code: statusCode,
      msg: `获取从站状态失败：${error.message}`,
      data: {}
    });
  }
}));

/**
 * 获取特定从站的状态
 * 路径: /slave-status/:master/:slave
 * 方法: GET
 */
router.get('/slave-status/:master/:slave', asyncHandler(async (req: Request, res: Response) => {
  const master = parseInt(req.params.master);
  const slave = parseInt(req.params.slave);
  
  // 参数验证
  if (isNaN(master) || isNaN(slave)) {
    return res.status(400).json({
      status: 400,
      message: '主站和从站参数必须是有效的数字'
    });
  }
  
  try {
    const slaveStatus = await EthercatService.getSlaveStatus(master, slave);
    
    // 返回从站状态
    return res.json({
      code: 0,  // 使用统一的返回格式
      msg: '获取从站状态成功',
      data: slaveStatus
    });
  } catch (error: any) {
    // 使用统一的返回格式处理错误
    const statusCode = error.statusCode || 500;
    return res.status(statusCode).json({
      code: statusCode,
      msg: `获取指定从站状态失败：${error.message}`,
      data: {}
    });
  }
}));

router.post('/start', asyncHandler(async (req: Request, res: Response) => {
  await EthercatService.startService();
  res.json({
    status: 200,
    data: { success: true }
  });
}));

router.post('/stop', asyncHandler(async (req: Request, res: Response) => {
  await EthercatService.stopService();
  res.json({
    status: 200,
    data: { success: true }
  });
}));

router.get('/xml/:master/:slave', asyncHandler(async (req: Request, res: Response) => {
  const master = parseInt(req.params.master);
  const slave = parseInt(req.params.slave);
  const xml = await EthercatService.getSlaveXml(master, slave);
  res.json({
    status: 200,
    data: xml
  });
}));

router.get('/topology/:master', asyncHandler(async (req: Request, res: Response) => {
  const master = parseInt(req.params.master);
  const svg = await EthercatService.generateTopology(master);
  res.setHeader('Content-Type', 'image/svg+xml');
  res.send(svg);
}));

router.get('/masters', asyncHandler(async (req: Request, res: Response) => {
  const masters = await EthercatService.getAvailableMasters();
  res.json({
    status: 200,
    data: masters
  });
}));

router.get('/network-interfaces', asyncHandler(async (req: Request, res: Response) => {
  const { stdout } = await execAsync(
    "find /sys/class/net -type l -not -lname '*virtual*' -printf '%f\\n' | grep -E '^(eth|eno|enp|end)' | cut -d@ -f1 | sort"
  );
  const interfaces = stdout.trim().split('\n').filter(line => line.trim());
  res.json({
    status: 200,
    data: interfaces
  });
}));

router.get('/config', asyncHandler(async (req: Request, res: Response) => {
  const config = await EthercatService.getConfig();
  res.json({
    status: 200,
    data: config
  });
}));

router.post('/config', asyncHandler(async (req: Request, res: Response) => {
  await EthercatService.updateConfig(req.body);
  res.json({
    status: 200,
    message: '配置已更新'
  });
}));

router.post('/restart', asyncHandler(async (req: Request, res: Response) => {
  await EthercatService.restartService();
  res.json({
    status: 200,
    message: '服务已重启'
  });
}));

router.get('/slave-config', asyncHandler(async (req: Request, res: Response) => {
  const config = await EthercatService.getSlaveConfig();
  res.json({
    status: 200,
    data: config
  });
}));

router.get('/template-config', asyncHandler(async (req: Request, res: Response) => {
  const config = await EthercatService.getTemplateConfig();
  res.json({
    status: 200,
    data: config
  });
}));

router.get('/bus-topology', asyncHandler(async (req: Request, res: Response) => {
  const format = req.query.format as string;
  const topology = await EthercatService.getBusTopology();
  
  if (format === 'png') {
    res.setHeader('Content-Type', 'image/png');
    res.setHeader('Content-Disposition', 'attachment; filename="topology.png"');
    res.send(topology.image);
  } else {
    res.json({
      status: 200,
      data: {
        dot: topology.dot,
        imageBase64: topology.image.toString('base64')
      }
    });
  }
}));

router.post('/render-topology', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { dot } = req.body;
    
    if (!dot) {
      return res.status(400).json({
        status: 400,
        message: '缺少 DOT 内容'
      });
    }
    
    // 使用 graphviz 将 DOT 转换为 PNG
    const image = await new Promise<Buffer>((resolve, reject) => {
      const dotProcess = spawn('dot', ['-Tpng']);
      const chunks: Buffer[] = [];

      dotProcess.stdout.on('data', (chunk) => chunks.push(Buffer.from(chunk)));
      dotProcess.stderr.on('data', (data) => console.error(`dot error: ${data}`));
      
      dotProcess.on('close', (code) => {
        if (code === 0) {
          resolve(Buffer.concat(chunks));
        } else {
          reject(new Error(`dot process exited with code ${code}`));
        }
      });

      dotProcess.stdin.write(dot);
      dotProcess.stdin.end();
    });

    // 设置响应头并发送图像
    res.setHeader('Content-Type', 'image/png');
    res.send(image);
  } catch (error) {
    console.error('Failed to render topology:', error);
    res.status(500).json({
      status: 500,
      message: '渲染拓扑图失败'
    });
  }
}));

router.post('/upload-eni', asyncHandler(async (req: Request, res: Response) => {
  if (!req.files || !req.files.eni) {
    return res.status(400).json({
      status: 400,
      message: '请上传 ENI 文件'
    });
  }

  const eniFile = req.files.eni as UploadedFile;
  
  // 验证文件类型
  if (!eniFile.name.toLowerCase().endsWith('.xml') && !eniFile.name.toLowerCase().endsWith('.eni')) {
    return res.status(400).json({
      status: 400,
      message: '请上传 XML 或 ENI 格式的文件'
    });
  }

  // 验证文件大小（例如限制为 10MB）
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (eniFile.size > maxSize) {
    return res.status(400).json({
      status: 400,
      message: '文件大小不能超过 10MB'
    });
  }
  
  try {
    const jsonConfig = await EthercatService.parseENIFile(eniFile);
    res.json({
      status: 200,
      data: jsonConfig
    });
  } catch (error) {
    console.error('解析 ENI 文件失败:', error);
    res.status(500).json({
      status: 500,
      message: '解析 ENI 文件失败'
    });
  }
}));

router.post('/parse-eni', asyncHandler(async (req: Request, res: Response) => {
  if (!req.body.xml) {
    return res.status(400).json({
      status: 400,
      message: '请提供 XML 内容'
    });
  }

  try {
    const xmlContent = req.body.xml;
    const config = await EthercatService.parseENI(xmlContent);
    res.json({
      status: 200,
      data: config
    });
  } catch (error) {
    console.error('解析 ENI 文件失败:', error);
    res.status(500).json({
      status: 500,
      message: '解析 ENI 文件失败'
    });
  }
}));

// 添加新的路由处理
router.post('/generate-config', asyncHandler(async (req: Request, res: Response) => {
  const config: EniConfig = req.body;
  
  if (!config || !config.xmlContent) {
    return res.status(400).json({
      status: 400,
      message: '请提供有效的配置数据'
    });
  }

  try {
    // 打印接收到的配置数据
    console.log('收到的前端配置数据:\n', JSON.stringify({
      slaves: config.slaves?.map(slave => ({
        slaveIndex: slave.slaveIndex,
        rxPdo: slave.rxPdo,
        txPdo: slave.txPdo,
        sdos: slave.sdos
      })),
      xmlContentLength: config.xmlContent.length
    }, null, 2));

    // 调用 service 处理配置
    const finalJson = await EthercatService.generateSlaveConfig(config);

    res.json({
      status: 200,
      data: finalJson
    });
  } catch (error) {
    console.error('生成从站配置失败:', error);
    res.status(500).json({
      status: 500,
      message: '生成从站配置失败'
    });
  }
}));

// 检查本地主机的中间件
function checkLocalhost(req: Request, res: Response, next: NextFunction) {
  return next();
}

// 外部启动 EtherCAT 服务
router.post('/external-ethercat-start', checkLocalhost, asyncHandler(async (req: Request, res: Response) => {
  console.log('=== 外部调用：启动 EtherCAT 服务 ===');
  try {
    await EthercatService.startService();
    
    const successResponse = {
      code: 0,
      msg: 'EtherCAT 服务启动成功',
      data: {}
    };

    // 记录访问日志
    await ExternalApiLogger.log('external-start-ethercat', successResponse);

    res.json(successResponse);
  } catch (error: any) {
    const errorResponse = {
      code: 500,
      msg: `启动 EtherCAT 服务失败：${error.message}`,
      data: {}
    };

    // 记录错误日志
    await ExternalApiLogger.log('external-start-ethercat', errorResponse);

    res.status(500).json(errorResponse);
  }
}));

// 外部停止 EtherCAT 服务
router.post('/external-ethercat-stop', checkLocalhost, asyncHandler(async (req: Request, res: Response) => {
  console.log('=== 外部调用：停止 EtherCAT 服务 ===');
  try {
    await EthercatService.stopService();
    
    const successResponse = {
      code: 0,
      msg: 'EtherCAT 服务停止成功',
      data: {}
    };

    // 记录访问日志
    await ExternalApiLogger.log('external-stop-ethercat', successResponse);

    res.json(successResponse);
  } catch (error: any) {
    let errorResponse;
    if (error instanceof ApiError) {
      errorResponse = {
        code: error.statusCode,
        msg: error.message,
        data: {}
      };
    } else {
      errorResponse = {
        code: 500,
        msg: `停止 EtherCAT 服务失败：${error.message}`,
        data: {}
      };
    }

    // 记录错误日志
    await ExternalApiLogger.log('external-stop-ethercat', errorResponse);

    res.status(200).json(errorResponse); // 注意：这里返回 200 状态码，错误信息在 code 字段中
  }
}));

export default router; 