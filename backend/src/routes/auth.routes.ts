import { Router, Request, Response } from 'express';
import { async<PERSON>and<PERSON> } from '../utils/async-handler.js';
import PocketBase from 'pocketbase';
import { config } from '../config/index.js';
import { PocketBaseManager } from '../services/program.manager.js';
import { ProgramManager } from '../services/program.manager.js';
import { AuthService } from '../services/auth.service.js';
import { ApiError } from '../utils/errors.js';

const router = Router();

router.post('/login', asyncHandler(async (req: Request, res: Response) => {
  const { username, password } = req.body;
  const pb = new PocketBase(config.pocketbaseUrl);
  
  try {
    const authData = await pb.collection('users').authWithPassword(username, password);
    
    // 保存认证 token
    PocketBaseManager.setAuthToken(authData.token);

    // 如果是管理员登录，尝试恢复程序
    if (authData.record.role === 'admin') {
      console.log('Admin user logged in, attempting power outage recovery...');
      // 异步执行恢复，不阻塞登录响应
      ProgramManager.handlePowerOutageRecovery().catch(error => {
        console.error('Power outage recovery failed:', error);
      });
    }
    
    res.json({
      status: 200,
      data: {
        token: authData.token,
        user: authData.record
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(401).json({
      status: 401,
      message: 'Login failed',
      error: error.response?.data || error.message
    });
  }
}));

router.post('/logout', asyncHandler(async (req: Request, res: Response) => {
  PocketBaseManager.clearAuth();
  
  res.json({
    status: 200,
    data: { success: true }
  });
}));

router.post('/change-password/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { oldPassword, password, passwordConfirm } = req.body;

  try {
    const pb = await PocketBaseManager.ensureAuth();
    
    await pb.collection('users').update(id, {
      oldPassword,
      password,
      passwordConfirm
    });

    res.json({
      status: 200,
      message: '密码修改成功'
    });
  } catch (error) {
    console.error('Failed to change password:', error);
    res.status(400).json({
      status: 400,
      message: '密码修改失败',
      error: error.response?.data || error.message
    });
  }
}));

router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  const token = req.headers.authorization;
  if (!token) {
    throw new ApiError(401, '未提供认证令牌');
  }

  const result = await AuthService.refreshToken(token);
  res.json({
    status: 200,
    data: result
  });
}));

export default router;