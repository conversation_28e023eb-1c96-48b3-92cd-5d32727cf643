import { Router } from 'express';
import { DashboardService } from '../services/dashboard.service.js';
import { asyncHandler } from '../utils/async-handler.js';

const router = Router();

// 获取变量值
router.get('/variables/:programId', asyncHandler(async (req, res) => {
  const { programId } = req.params;
  const variables = await DashboardService.getVariableValues(programId);
  res.json({
    status: 200,
    data: variables
  });
}));

// 获取 EtherCAT 日志
router.get('/logs', asyncHandler(async (req, res) => {
  const logs = await DashboardService.getEtherCATLogs();
  res.json({
    status: 200,
    data: logs
  });
}));

export default router; 