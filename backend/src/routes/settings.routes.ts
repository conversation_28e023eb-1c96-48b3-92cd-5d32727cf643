import { Router } from 'express';
import { SettingsService } from '../services/settings.service.js';
import { asyncHandler } from '../utils/async-handler.js';
import { ApiError } from '../utils/errors.js';
import type { Request, Response } from 'express';

const router = Router();

// 获取菜单设置
router.get('/menu', asyncHandler(async (req: Request, res: Response) => {
  try {
    const settings = await SettingsService.getMenuSettings();
    res.json({
      status: 200,
      data: settings
    });
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Settings Routes] Get menu settings error:', error);
    res.status(500).json({
      status: 500,
      message: '获取菜单设置失败'
    });
  }
}));

// 更新菜单设置
router.put('/menu', asyncHandler(async (req: Request, res: Response) => {
  try {
    await SettingsService.updateMenuSettings(req.body);
    res.json({
      status: 200,
      data: null
    });
  } catch (error: any) {
    // 如果是ApiError，保持原始状态码
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
      return;
    }
    console.error('[Settings Routes] Update menu settings error:', error);
    res.status(500).json({
      status: 500,
      message: '更新菜单设置失败'
    });
  }
}));

// 获取网络配置
router.get('/network', asyncHandler(async (req: Request, res: Response) => {
  const config = await SettingsService.getNetworkConfig();
  res.json({
    status: 200,
    data: config
  });
}));

// 更新网络配置
router.post('/network', asyncHandler(async (req: Request, res: Response) => {
  await SettingsService.updateNetworkConfig(req.body);
  res.json({
    status: 200,
    message: '网络设置已更新'
  });
}));

export default router;