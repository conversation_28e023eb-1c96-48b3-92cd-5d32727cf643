import { Request, Response, NextFunction } from 'express';
import { ApiError } from '../utils/errors.js';

export const authMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new ApiError(401, 'No auth token provided');
  }

  const token = authHeader.split(' ')[1];
  if (!token) {
    throw new ApiError(401, 'Invalid auth token');
  }

  // 将 token 添加到请求对象中
  (req as any).authToken = token;
  next();
}; 