import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import fs from 'fs';
import moment from 'moment-timezone';

export class LoggerService {
    private static instance: LoggerService;
    private logger: winston.Logger;
    private readonly LOG_DIR = '/home/<USER>/logs/commons';
    private readonly TIMEZONE = 'Asia/Shanghai';
    private readonly TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
    private readonly FILE_TIME_FORMAT = 'YYYYMMDDHH';

    private constructor() {
        this.initializeLogger();
    }

    private getCurrentTime(): string {
        return moment().tz(this.TIMEZONE).format(this.TIME_FORMAT);
    }

    private getFileNameTime(): string {
        return moment().tz(this.TIMEZONE).format(this.FILE_TIME_FORMAT);
    }


    private initializeLogger() {
        if (!fs.existsSync(this.LOG_DIR)) {
            fs.mkdirSync(this.LOG_DIR, { recursive: true });
        }

        this.logger = winston.createLogger({
            level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
            format: winston.format.combine(
                winston.format.timestamp({
                    format: () => this.getCurrentTime()
                }),
                winston.format.json()
            ),
            transports: [
                new DailyRotateFile({
                    dirname: this.LOG_DIR,
                    filename: '%DATE%.log',
                    datePattern: 'YYYYMMDDHH',  // 每小时生成新日志
                    createSymlink: false,
                    frequency: '1h',
                    maxSize: '20m',
                    maxFiles: '14d',
                    auditFile: null  // 禁用审计文件
                })
            ]
        });

        if (process.env.NODE_ENV !== 'production') {
            this.logger.add(new winston.transports.Console({
                format: winston.format.combine(
                    winston.format.colorize(),
                    winston.format.simple()
                )
            }));
        }
    }

    public static getInstance(): LoggerService {
        if (!LoggerService.instance) {
            LoggerService.instance = new LoggerService();
        }
        return LoggerService.instance;
    }

    public error(service: string, message: string, error?: any): void {
        this.logger.error({
            service,
            message,
            error: error?.message || error,
            stack: error?.stack
        });
    }

    public warn(service: string, message: string, meta?: any): void {
        this.logger.warn({
            service,
            message,
            ...meta
        });
    }

    public info(service: string, message: string, meta?: any): void {
        this.logger.info({
            service,
            message,
            ...meta
        });
    }

    public debug(service: string, message: string, meta?: any): void {
        this.logger.debug({
            service,
            message,
            ...meta
        });
    }
}

export const logger = LoggerService.getInstance();