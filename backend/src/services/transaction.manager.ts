import PocketBase from 'pocketbase';

export class Transaction {
  private isActive: boolean = false;
  private operations: Array<() => Promise<void>> = [];

  async begin(): Promise<void> {
    if (this.isActive) {
      throw new Error('Transaction already started');
    }
    this.isActive = true;
    this.operations = [];
    }

  async commit(): Promise<void> {
    if (!this.isActive) {
      throw new Error('No active transaction');
    }
        try {
            for (const operation of this.operations) {
        await operation();
      }
      this.isActive = false;
      this.operations = [];
                } catch (error) {
                    await this.rollback();
            throw error;
        }
    }

  async rollback(): Promise<void> {
    this.isActive = false;
    this.operations = [];
  }

  addOperation(operation: () => Promise<void>): void {
    if (!this.isActive) {
      throw new Error('No active transaction');
    }
    this.operations.push(operation);
    }
}

export default Transaction; 