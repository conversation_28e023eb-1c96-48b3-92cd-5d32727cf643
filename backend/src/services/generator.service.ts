import fs from 'fs/promises';
import path from 'path';
import { ApiError } from '../utils/errors.js';

export class GeneratorService {
  static async generateCode(config: any, language: string): Promise<string> {
    try {
      switch (language) {
        case 'cs':
          return await this.generateCSharpCode(config);
        // ... 其他语言的情况
        default:
          throw new ApiError(400, '不支持的语言类型');
      }
    } catch (error) {
      console.error('Failed to generate code:', error);
      throw new ApiError(500, '代码生成失败');
    }
  }

  private static async generateCSharpCode(config: any): Promise<string> {
    try {
      let template = `using System;
using System.IO.MemoryMappedFiles;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;

namespace EtherCATControl
{
    public class EtherCATController
    {
        private readonly MemoryMappedFile _memoryMappedFile;
        private readonly MemoryMappedViewAccessor _viewAccessor;
        private EtherCATSharedMemory _sharedMemory;

        public EtherCATController(string EtherCATSharedMemoryFilePath)
        {
            _memoryMappedFile = MemoryMappedFile.CreateFromFile(EtherCATSharedMemoryFilePath, FileMode.Open);
            _viewAccessor = _memoryMappedFile.CreateViewAccessor();
            _sharedMemory = new EtherCATSharedMemory();
        }

        private void UpdateSharedMemory()
        {
            _viewAccessor.Read(0, out _sharedMemory);
        }

        private void WriteSharedMemory()
        {
            _viewAccessor.Write(0, ref _sharedMemory);
        }

        //---------------伺服状态检查---------------------------
        // private bool CheckStatusWord(ushort expectedStatus)
        // {
        //     UpdateSharedMemory();
        //     return (_sharedMemory.shm_slave0_tx_0x6041_status_word状态字 & 0x0FFF) == expectedStatus;
        // }

        // private bool WaitForStatus(ushort expectedStatus, int timeoutMs = 1000)
        // {
        //     int elapsed = 0;
        //     while (elapsed < timeoutMs)
        //     {
        //         if (CheckStatusWord(expectedStatus))
        //         {
        //             return true;
        //         }
        //         Thread.Sleep(10);
        //         elapsed += 10;
        //     }
        //     return false;
        // }

        // public bool WaitForServoReady(CancellationToken cancellationToken = default)
        // {
        //     while (!cancellationToken.IsCancellationRequested)
        //     {
        //         UpdateSharedMemory();
        //         // 检查状态字是否为0x0231（伺服准备好）
        //         if (CheckStatusWord(0x0231))
        //         {
        //             Console.WriteLine("Servo is ready");
        //             return true;
        //         }
        //         Thread.Sleep(100);
        //     }
        //     return false;
        // }

        //----------------伺服使能 DS402逻辑-------------------------
        // public bool EnableServo()
        // {
        //     Console.WriteLine("Enabling servo...");
        // 
        //     // 步骤2：伺服无故障→伺服准备好 (0x0231)
        //     _sharedMemory.shm_slave0_rx_0x6040_control_word控制字 = 0x06;
        //     WriteSharedMemory();
        //     if (!WaitForStatus(0x0231))
        //     {
        //         Console.WriteLine("Failed to reach ready state");
        //         return false;
        //     }
        // 
        //     // 步骤3：伺服准备好→等待打开伺服使能 (0x0233)
        //     _sharedMemory.shm_slave0_rx_0x6040_control_word控制字 = 0x07;
        //     WriteSharedMemory();
        //     if (!WaitForStatus(0x0233))
        //     {
        //         Console.WriteLine("Failed to reach wait-for-enable state");
        //         return false;
        //     }
        // 
        //     // 步骤4：等待打开伺服使能→伺服运行 (0x0237)
        //     _sharedMemory.shm_slave0_rx_0x6040_control_word控制字 = 0x0F;
        //     WriteSharedMemory();
        //     if (!WaitForStatus(0x0237))
        //     {
        //         Console.WriteLine("Failed to reach operation enabled state");
        //         return false;
        //     }
        // 
        //     // 设置运行模式（CSV模式）
        //     _sharedMemory.shm_slave0_rx_0x6060_modes_of_operation运行模式 = 0x09;
        //     WriteSharedMemory();
        //     Thread.Sleep(100);
        // 
        //     Console.WriteLine("Servo enabled successfully");
        //     return true;
        // }

        //----------------伺服停止使能-------------------------
        // public bool DisableServo()
        // {
        //     Console.WriteLine("Disabling servo...");
        // 
        //     // 步骤11：伺服运行→快速停机 (0x0217)
        //     _sharedMemory.shm_slave0_rx_0x6040_control_word控制字 = 0x02;
        //     WriteSharedMemory();
        //     if (!WaitForStatus(0x0217))
        //     {
        //         Console.WriteLine("Failed to reach quick stop state");
        //         return false;
        //     }
        // 
        //     // 步骤12：快速停机→无故障 (0x0250)
        //     // 等待快速停机完成
        //     if (!WaitForStatus(0x0250))
        //     {
        //         Console.WriteLine("Failed to reach fault-free state after quick stop");
        //         return false;
        //     }
        // 
        //     // 最终设置控制字为0
        //     _sharedMemory.shm_slave0_rx_0x6040_control_word控制字 = 0x00;
        //     WriteSharedMemory();
        //     Console.WriteLine("Servo disabled successfully");
        //     return true;
        // }

        //----------------伺服设置速度-------------------------
        // public void SetVelocity(int velocity)
        // {
        //     _sharedMemory.shm_slave0_rx_0x60ff_target_velocity实际速度 = velocity;
        //     WriteSharedMemory();
        //     Console.WriteLine($"Set velocity to: {velocity}");
        // }

        public void UpdateStatus()
        {
            UpdateSharedMemory();
            Console.WriteLine(_sharedMemory.shm_slave0_online_status);
        }

        public void EnterRealtimeMode()
        {
            Thread.CurrentThread.Priority = ThreadPriority.Highest;

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var schedParam = new SchedParam
                {
                    sched_priority = sched_get_priority_max((int)SchedPolicy.SCHED_FIFO)
                };
                if (sched_setscheduler(0, (int)SchedPolicy.SCHED_FIFO, ref schedParam) != 0)
                {
                    Console.WriteLine("Warning: Failed to set SCHED_FIFO scheduling policy");
                }
            }
        }

        public async Task RunControlSequence(CancellationToken cancellationToken = default)
        {
            try
            {
                bool servoEnabled = false;
                bool wasOnline = false;

                while (!cancellationToken.IsCancellationRequested)
                {
                    UpdateSharedMemory();
                    bool isOnline = _sharedMemory.shm_slave0_online_status == 1;
                    bool isOperational = _sharedMemory.shm_slave0_operational_status == 1;

                    // 检测从站状态变化
                    if (isOnline != wasOnline)
                    {
                        if (isOnline)
                        {
                            Console.WriteLine("Slave is online, checking operational status...");
                            
                            // 等待从站进入OP状态
                            while (!isOperational && !cancellationToken.IsCancellationRequested)
                            {
                                Console.WriteLine("Waiting for slave to reach OP state...");
                                await Task.Delay(100, cancellationToken);
                                UpdateSharedMemory();
                                isOperational = _sharedMemory.shm_slave0_operational_status == 1;
                            }

                            if (isOperational)
                            {
                                Console.WriteLine("Slave is operational, enabling servo...");
                                if (EnableServo())
                                {
                                    servoEnabled = true;
                                    Console.WriteLine("Servo enabled successfully");
                                }
                                else
                                {
                                    Console.WriteLine("Failed to enable servo");
                                    servoEnabled = false;
                                }
                            }
                        }
                        else
                        {
                            Console.WriteLine("Slave went offline, disabling servo...");
                            DisableServo();
                            servoEnabled = false;
                        }
                        wasOnline = isOnline;
                    }

                    // 只有在从站在线且伺服使能成功的情况下才写入速度
                    if (isOnline && servoEnabled)
                    {
                        SetVelocity(10000);
                    }
                    else
                    {
                        SetVelocity(0);
                    }

                    await Task.Delay(1000, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in control sequence: {ex.Message}");
            }
            finally
            {
                if (!DisableServo())
                {
                    Console.WriteLine("Warning: Failed to properly disable servo");
                }
                Console.WriteLine("Process completed.");
            }
        }

        //---------Linux-RT实时性，勿删-----------
        private enum SchedPolicy
        {
            SCHED_FIFO = 1
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct SchedParam
        {
            public int sched_priority;
        }

        [DllImport("libc", EntryPoint = "sched_setscheduler")]
        private static extern int sched_setscheduler(int pid, int policy, ref SchedParam param);

        [DllImport("libc", EntryPoint = "sched_get_priority_max")]
        private static extern int sched_get_priority_max(int policy);
        //--------------------------------------------------------------------

        //共享内存结构体，控制的变量就在这，顺序保持一致
        [StructLayout(LayoutKind.Sequential)]
        private struct EtherCATSharedMemory
        {
${this.generateCSharpStruct(config)}
        }
    }

    class Program
    {
        static async Task Main(string[] args)
        {
            if (args.Length == 0)
            {
                Console.WriteLine($"Usage: ./{Path.GetFileName(Process.GetCurrentProcess().MainModule.FileName)} <shared_memory_file_path>");
                return;
            }

            string sharedMemoryFilePath = args[0];
            Console.WriteLine($"Starting EtherCAT Control Program with shared memory file: {sharedMemoryFilePath}...");

            var controller = new EtherCATController(sharedMemoryFilePath);

            //进入实时，必须保留
            controller.EnterRealtimeMode();

            //处理退出程序情况逻辑
            var cts = new CancellationTokenSource();
            Console.CancelKeyPress += (s, e) => {
                e.Cancel = true;
                cts.Cancel();
            };

            try
            {
                await controller.RunControlSequence(cts.Token);
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Operation cancelled by user.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }
    }
}`;

    return template;
  } catch (error) {
    console.error('Failed to generate C# code:', error);
    throw new ApiError(500, '生成C#代码失败');
  }
}

  private static generateCSharpStruct(config: any): string {
    const lines: string[] = [];
    
    if (config.slaves && config.slaves.length > 0) {
        // 首先为每个从站添加状态变量
        config.slaves.forEach((slave: any, index: number) => {
            // 添加从站状态变量
            lines.push(`        public int shm_slave${index}_online_status; // 从站${index}在线状态`);
            lines.push(`        public int shm_slave${index}_operational_status; // 从站${index}运行状态`);
            lines.push(`        public int shm_slave${index}_al_state; // 从站${index}AL状态`);
            lines.push(''); // 添加空行分隔

            // 处理 RxPDOs
            if (slave.rx_pdos) {
                slave.rx_pdos.forEach((pdo: any) => {
                    const varName = this.sanitizeVariableName(pdo, index, 'rx');
                    const comment = this.generateComment(pdo);
                    lines.push(`        public int ${varName};${comment}`);
                });
            }

            // 处理 TxPDOs
            if (slave.tx_pdos) {
                slave.tx_pdos.forEach((pdo: any) => {
                    const varName = this.sanitizeVariableName(pdo, index, 'tx');
                    const comment = this.generateComment(pdo);
                    lines.push(`        public int ${varName};${comment}`);
                });
            }

            // 在每个从站的变量组之后添加空行
            lines.push('');
        });
    }

    return lines.join('\n');
  }

  private static sanitizeVariableName(pdo: any, slaveIndex: number, direction: 'rx' | 'tx'): string {
    // 构造变量名: shm_slave{index}_rx/tx_index_name
    const baseName = `shm_slave${slaveIndex}_${direction}_${pdo.index.toLowerCase()}_${pdo.name.toLowerCase()}`;
    return baseName.replace(/[^a-z0-9_]/g, '_');
  }

  private static generateComment(pdo: any): string {
    // 只使用 comment 作为注释
    return pdo.comment ? ` // ${pdo.comment}` : '';
  }
} 