import { config } from '../config/index.js';
import PocketBase, { ClientResponseError } from 'pocketbase';
import { PocketBaseManager } from './program.manager.js';
import { ApiError } from '../utils/errors.js';
import fs from 'fs/promises';
import { exec } from 'child_process';
import { promisify } from 'util';
import { logger } from './logger.service.js';
import { ProgramManager } from './program.manager.js';
const execAsync = promisify(exec);

interface MenuSettings {
  dashboard: boolean;
  ethercat: boolean;
  programs: boolean;
  users: boolean;
  settings: boolean;
}

export class SettingsService {
  static async getMenuSettings(): Promise<any> {
    try {
      // 先检查认证状态
      const pb = await PocketBaseManager.ensureAuth();
      
      try {
        const record = await pb.collection('settings').getFirstListItem('type="menu"');
        return record.settings;
      } catch (error) {
        // 如果是认证相关的错误，转换为400错误
        if (error.status === 401 || error.message?.includes('auth') || error.message?.includes('token')) {
          throw new ApiError(400, '认证已过期，请重新登录');
        }
        // 其他错误保持500
        console.error('[Settings Service] Get menu settings error:', error);
        throw new ApiError(500, '获取菜单设置失败');
      }
    } catch (error) {
      // 如果是ApiError（包括认证错误），直接往上传递
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('[Settings Service] Get menu settings error:', error);
      throw new ApiError(500, '获取菜单设置失败');
    }
  }

  static async updateMenuSettings(settings: any): Promise<void> {
    try {
      // 先检查认证状态
      const pb = await PocketBaseManager.ensureAuth();
      
      try {
        const record = await pb.collection('settings').getFirstListItem('type="menu"');
        await pb.collection('settings').update(record.id, {
          settings: settings
        });
      } catch (error) {
        // 如果是认证相关的错误，转换为400错误
        if (error.status === 401 || error.message?.includes('auth') || error.message?.includes('token')) {
          throw new ApiError(400, '认证已过期，请重新登录');
        }
        // 其他错误保持500
        console.error('[Settings Service] Update menu settings error:', error);
        throw new ApiError(500, '更新菜单设置失败');
      }
    } catch (error) {
      // 如果是ApiError（包括认证错误），直接往上传递
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('[Settings Service] Update menu settings error:', error);
      throw new ApiError(500, '更新菜单设置失败');
    }
  }

  static async getNetworkConfig(): Promise<any> {
    try {
      const content = await fs.readFile('/etc/network/interfaces', 'utf-8');
      return {
        content
      };
    } catch (error) {
      console.error('Failed to read network config:', error);
      throw new ApiError(500, '获取网络配置失败');
    }
  }

  static async updateNetworkConfig(config: { content: string }): Promise<void> {

    const configPath = "/etc/network/interfaces";
    const reloadCommand = "service networking reload";
    const checkServiceCommand = "systemctl is-active networking.service";

    try {
      // 1.获取所有程序名
      const pb = await PocketBaseManager.ensureAuth();
      const programs = await pb.collection('programs').getList(1, 50);

      // 2.判断所有程序/中间层状态
      for (const program of programs.items) {
        const programService = `ethercat_program@${program.name}.service`;
        const middlwareService = `ethercat_middleware@${program.name}.service`;

        const programStatus  = await ProgramManager.getServiceStatus(programService);
        const middlewareStatus = await ProgramManager.getServiceStatus(middlwareService);
        if (programStatus === 'active' || middlewareStatus === 'active') {
          logger.info('settings更新网络配置', `${program.name}程序服务：${programStatus}, 中间层服务：${middlewareStatus}`);
          throw new ApiError(500, `程序/中间层 ${program.name} 正在运行，无法更新网络配置`);
        }
      }

      // 3.验证配置语法
      this.validateNetworkConfig(config.content);
      // 4.写入配置文件
      await fs.writeFile(configPath, config.content, 'utf-8');
      // 5.执行网络重启命令
      const { stdout, stderr } = await execAsync(reloadCommand);
      // 6.获取命令输出，判断是否包含done
      const lastChars = stdout.trim().slice(-10);
      if (!lastChars.includes('done')) {
        console.log("重启网络错误输出：", stderr);
        console.log("重启网络输出：", stdout);
        throw new ApiError(500, "网络配置应用失败，请检查配置文件内容");
      }
      // 7.检查网络服务是否正常运行
      try {
        const { stdout: status } = await execAsync(checkServiceCommand);
        if (status.trim() !== 'active') {
          throw new ApiError(500, "网络服务未能正常启动，当前状态为：", status.trim());
        }
      } catch (error) {
        logger.error("settings更新网络配置", "检查网络服务状态失败：", error);
        throw new ApiError(500, "网络服务状态检测失败");
      }

      return;
    } catch (error) {
      logger.error("settings更新网络配置", "更新网络配置失败：", error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, error.message || "更新网络配置失败");
    }

  }

  private static parseInterfacesFile(content: string): any {
    const lines = content.split('\n');
    const interfaces: any = {};
    let currentIface = '';

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('#') || !trimmedLine) continue;

      if (trimmedLine.startsWith('iface')) {
        const parts = trimmedLine.split(' ');
        currentIface = parts[1];
        interfaces[currentIface] = {
          interface: currentIface,
          type: parts[3] === 'dhcp' ? 'dhcp' : 'static',
          address: '',
          netmask: '',
          gateway: '',
          dns: ''
        };
      } else if (currentIface && interfaces[currentIface]) {
        if (trimmedLine.startsWith('address')) {
          interfaces[currentIface].address = trimmedLine.split(' ')[1];
        } else if (trimmedLine.startsWith('netmask')) {
          interfaces[currentIface].netmask = trimmedLine.split(' ')[1];
        } else if (trimmedLine.startsWith('gateway')) {
          interfaces[currentIface].gateway = trimmedLine.split(' ')[1];
        } else if (trimmedLine.startsWith('dns-nameserver')) {
          interfaces[currentIface].dns = trimmedLine.split(' ')[1];
        }
      }
    }

    // 返回 eth0 的配置
    return interfaces['eth0'] || {
      interface: 'eth0',
      type: 'dhcp',
      address: '',
      netmask: '',
      gateway: '',
      dns: ''
    };
  }

  private static generateInterfacesFile(config: any): string {
    const lines = [
      'auto lo',
      'iface lo inet loopback',
      '',
      'auto eth0',
      'allow-hotplug eth0',
      `iface eth0 inet ${config.type}`
    ];

    if (config.type === 'static') {
      lines.push(`address ${config.address}`);
      lines.push(`netmask ${config.netmask}`);
      if (config.gateway) {
        lines.push(`gateway ${config.gateway}`);
      }
      if (config.dns) {
        lines.push(`dns-nameserver ${config.dns}`);
      }
    }

    // 保持 eth1 的配置不变
    lines.push('');
    lines.push('auto eth1');
    lines.push('allow-hotplug eth1');
    lines.push('iface eth1 inet static');
    lines.push('address ***************');
    lines.push('netmask *************');

    return lines.join('\n') + '\n';
  }

  private static validateNetworkConfig(content: string): void {
    const lines = content.split('\n');
    const requiredInterfaces = ['lo', 'eth0'];
    const foundInterfaces = new Set<string>();
    let currentInterface: string | null = null;

    for (const [index, line] of lines.entries()) {
      const trimmedLine = line.trim();
      if (!trimmedLine || trimmedLine.startsWith('#')) continue;

      const parts = trimmedLine.split(' ').filter(Boolean);
      
      if (parts[0] === 'iface') {
        if (parts.length < 4) {
          throw new ApiError(400, `第 ${index + 1} 行: iface 配置格式错误，正确格式: iface <interface> inet <type>`);
        }
        currentInterface = parts[1];
        foundInterfaces.add(currentInterface);
        
        // 特殊处理 lo 接口的 loopback 类型
        if (currentInterface === 'lo' && parts[3] !== 'loopback') {
          throw new ApiError(400, `第 ${index + 1} 行: lo 接口必须使用 loopback 类型`);
        } else if (currentInterface !== 'lo' && !['static', 'dhcp'].includes(parts[3])) {
          throw new ApiError(400, `第 ${index + 1} 行: 网络类型必须是 static 或 dhcp`);
        }
      } else if (currentInterface && parts[0] === 'address') {
        if (parts.length < 2 || !this.isValidIpAddress(parts[1])) {
          throw new ApiError(400, `第 ${index + 1} 行: IP 地址格式无效`);
        }
      } else if (currentInterface && parts[0] === 'netmask') {
        if (parts.length < 2 || !this.isValidIpAddress(parts[1])) {
          throw new ApiError(400, `第 ${index + 1} 行: 子网掩码格式无效`);
        }
      } else if (currentInterface && parts[0] === 'gateway') {
        if (parts.length < 2 || !this.isValidIpAddress(parts[1])) {
          throw new ApiError(400, `第 ${index + 1} 行: 网关地址格式无效`);
        }
      }
    }

    for (const iface of requiredInterfaces) {
      if (!foundInterfaces.has(iface)) {
        throw new ApiError(400, `缺少必需的网络接口配置: ${iface}`);
      }
    }
  }

  private static isValidIpAddress(ip: string): boolean {
    const parts = ip.split('.');
    if (parts.length !== 4) return false;
    
    return parts.every(part => {
      const num = parseInt(part, 10);
      return !isNaN(num) && num >= 0 && num <= 255;
    });
  }
}