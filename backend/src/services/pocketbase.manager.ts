import PocketBase from 'pocketbase';
import { config } from '../config/index.js';

export class PocketBaseManager {
  private static instance: PocketBase | null = null;
  private static authToken: string | null = null;

  static getInstance(): PocketBase {
    if (!this.instance) {
      this.instance = new PocketBase(config.pocketbaseUrl);
      if (this.authToken) {
        try {
          this.instance.authStore.save(this.authToken, null);
        } catch (error) {
          console.error('Failed to restore auth token:', error);
          this.authToken = null;
        }
      }
    }
    return this.instance;
  }

  static async ensureAuth(): Promise<PocketBase> {
    const pb = this.getInstance();
    if (!pb.authStore.isValid) {
      try {
        console.log('PocketBaseManager: Attempting authentication with:', {
          email: config.pocketbase.adminEmail,
          password: config.pocketbase.adminPassword
        });
        // 使用管理员认证端点
        await pb.admins.authWithPassword(
          config.pocketbase.adminEmail,
          config.pocketbase.adminPassword
        );
      } catch (error) {
        console.error('Auth failed:', error);
        throw new Error('Authentication failed');
      }
    }
    return pb;
  }

  static setAuthToken(token: string) {
    this.authToken = token;
    if (this.instance) {
      try {
        this.instance.authStore.save(token, null);
      } catch (error) {
        console.error('Failed to save auth token:', error);
      }
    }
  }

  static clearAuth() {
    this.authToken = null;
    if (this.instance) {
      this.instance.authStore.clear();
    }
  }
}