import { LRUCache } from 'lru-cache';
import { exec } from 'child_process';
import { promisify } from 'util';
import { ApiError } from '../utils/errors.js';
import fs from 'fs/promises';
import path from 'path';
import { execAsync } from '../utils/exec.js';
import { UploadedFile } from 'express-fileupload';
import { parseENI, processEniConfig } from '../utils/eni2json.js';
import { PocketBaseManager } from './program.manager.js'
import { ProgramManager } from './program.manager.js'
import { LoggerService } from './logger.service.js';

const execAsync = promisify(exec);
const logger = LoggerService.getInstance();

interface Master {
  index: number;
  phase: string;
  active: boolean;
  slaveCount: number;
  mac: string;
  interface: string;
  link: string;
  txFrames: number;
  rxFrames: number;
  txBytes: number;
  rxBytes: number;
  lostFrames: number;
  txFrameRate: number[];
  txRate: number[];
  rxFrameRate: number[];
  rxRate: number[];
  lossRate: number[];
  frameLoss: number[];
}

interface PDOEntry {
  index: string;
  subIndex: string;
  bits: number;
  name: string;
}

interface PDO {
  index: string;
  name: string;
  entries: PDOEntry[];
  type: 'RxPDO' | 'TxPDO';
}

interface SyncManager {
  index: number;
  physAddr: string;
  defaultSize: number;
  controlRegister: string;
  enable: boolean;
  pdos: PDO[];
}

interface Slave {
  master: number;
  position: string;
  index: number;
  name: string;
  state: string;
  vendorId: string;
  productCode: string;
  revision: string;
  serial: string;
  group: string;
  orderNumber: string;
  deviceName: string;
  currentConsumption: number;
  fmmuBitOperation: boolean;
  distributedClocks: string;
  dcSystemTimeDelay: number;
  ports: {
    index: number;
    type: string;
    link: string;
    loop: string;
    signal: string;
    nextSlave: string;
    rxTime: number;
    diff: number;
    nextDc: number;
  }[];
  syncManagers?: SyncManager[];
}

interface SlaveGroup {
  master: number;
  slaves: Slave[];
}

interface EtherCATConfig {
  masterDevices: { [key: number]: string | null };  // 改为使用对象存储多个主站设备
}

interface SlaveConfig {
  position: string;
  name: string;
  vendorId: number;
  productCode: number;
}

interface EniConfig {
  slaveIndex: number;
  rxPdo: string[];
  txPdo: string[];
  sdos: string[];
  xmlContent: string;
  slaves?: {
    slaveIndex: number;
    rxPdo: string[];
    txPdo: string[];
    sdos: string[];
  }[];
}

// 配置缓存选项
const cacheOptions = {
  max: 100,                    // 最大缓存数量
  ttl: 1000,                   // 缓存有效期 1 秒
  updateAgeOnGet: true,        // 访问时更新时间
  allowStale: true,            // 允许返回过期数据（在更新时）
};

// 创建缓存实例
const statusCache = new LRUCache(cacheOptions);
const mastersCache = new LRUCache(cacheOptions);
const slavesCache = new LRUCache(cacheOptions);

export class EthercatService {
  static async getStatus() {
    try {
      // 尝试从缓存获取
      // const cachedStatus = statusCache.get('status');
      // if (cachedStatus) {
      //   return cachedStatus;
      // }
      console.log('开始获取EtherCAT状态，执行 ethercat master -v')
      const { stdout } = await execAsync('ethercat master -v');
      
      const running = stdout.length > 0 && !stdout.includes('Failed to open master device');
      
      if (!running) {
        const status = {
          running: false,
          masters: [],
          slaves: [],
          slaveCount: 0,
          lastUpdateTime: new Date().toISOString()
        };
        statusCache.set('status', status);
        return status;
      }
      
      const masters = await this.getMasters();
      const slaves = await this.getSlaves();
      
      const status = {
        running,
        masters,
        slaves,
        slaveCount: masters.reduce((sum, m) => sum + m.slaveCount, 0),
        lastUpdateTime: new Date().toISOString()
      };

      // 保存到缓存
      statusCache.set('status', status);
      return status;
    } catch (error: unknown) {
      const err = error as Error;
      if (err.message?.includes('No such file or directory')) {
        return {
          running: false,
          masters: [],
          slaves: [],
          slaveCount: 0,
          lastUpdateTime: new Date().toISOString()
        };
      }
      console.error('Failed to get EtherCAT status:', error);
      throw new ApiError(500, '获取 EtherCAT 状态失败');
    }
  }

  static async getMasters(): Promise<Master[]> {
    try {
      // 尝试从缓存获取
      const cachedMasters = mastersCache.get('masters');
      if (cachedMasters) {
        return cachedMasters;
      }

      const { stdout } = await execAsync('ethercat master -v');
      const masters: Master[] = [];
      let currentMaster: Partial<Master> = {};
      let inCommonStats = false;
      
      // 获取 MAC 地址网口的映射
      const macToIface = await this.getMacToInterfaceMap();
      
      const lines = stdout.split('\n');
      for (const line of lines) {
        if (line.startsWith('Master')) {
          if (Object.keys(currentMaster).length > 0) {
            masters.push(currentMaster as Master);
          }
          currentMaster = {
            index: parseInt(line.match(/Master(\d+)/)?.[1] || '0'),
            phase: '',
            active: false,
            slaveCount: 0,
            mac: '',
            interface: '', // 添加网口字段
            link: '',
            txFrames: 0,
            rxFrames: 0,
            txBytes: 0,
            rxBytes: 0,
            lostFrames: 0,
            txFrameRate: [0, 0, 0],
            txRate: [0, 0, 0],
            rxFrameRate: [0, 0, 0],
            rxRate: [0, 0, 0],
            lossRate: [0, 0, 0],
            frameLoss: [0, 0, 0]
          };
        } else if (line.trim().startsWith('Phase:')) {
          currentMaster.phase = line.split(':')[1].trim();
        } else if (line.trim().startsWith('Active:')) {
          currentMaster.active = line.includes('yes');
        } else if (line.trim().startsWith('Slaves:')) {
          currentMaster.slaveCount = parseInt(line.split(':')[1].trim());
        } else if (line.includes('Main:')) {
          // 提取 MAC 地址并查找对应的网口
          const macMatch = line.match(/([0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2})/);
          if (macMatch) {
            const mac = macMatch[1].toLowerCase();
            currentMaster.mac = mac;
            currentMaster.interface = macToIface.get(mac) || ''; // 设置对应的网口
          }
        } else if (line.trim().startsWith('Link:')) {
          currentMaster.link = line.split(':')[1].trim();
        } else if (line.includes('Common:')) {
          inCommonStats = true;
        } else if (inCommonStats) {
          if (line.includes('Tx frames:')) {
            currentMaster.txFrames = parseInt(line.split(':')[1].trim());
          } else if (line.includes('Rx frames:')) {
            currentMaster.rxFrames = parseInt(line.split(':')[1].trim());
          } else if (line.includes('Tx bytes:')) {
            currentMaster.txBytes = parseInt(line.split(':')[1].trim());
          } else if (line.includes('Rx bytes:')) {
            currentMaster.rxBytes = parseInt(line.split(':')[1].trim());
          } else if (line.includes('Lost frames:')) {
            currentMaster.lostFrames = parseInt(line.split(':')[1].trim());
          } else if (line.includes('Tx frame rate [1/s]:')) {
            currentMaster.txFrameRate = line.split(':')[1].trim().split(/\s+/).map(Number);
          } else if (line.includes('Tx rate [KByte/s]:')) {
            currentMaster.txRate = line.split(':')[1].trim().split(/\s+/).map(Number);
          } else if (line.includes('Rx frame rate [1/s]:')) {
            currentMaster.rxFrameRate = line.split(':')[1].trim().split(/\s+/).map(Number);
          } else if (line.includes('Rx rate [KByte/s]:')) {
            currentMaster.rxRate = line.split(':')[1].trim().split(/\s+/).map(Number);
          } else if (line.includes('Loss rate [1/s]:')) {
            currentMaster.lossRate = line.split(':')[1].trim().split(/\s+/).map(Number);
          } else if (line.includes('Frame loss [%]:')) {
            currentMaster.frameLoss = line.split(':')[1].trim().split(/\s+/).map(Number);
          }
        }
      }
      
      if (Object.keys(currentMaster).length > 0) {
        masters.push(currentMaster as Master);
      }
      
      // 保存到缓存
      mastersCache.set('masters', masters);
      return masters;
    } catch (error) {
      console.error('Failed to get masters:', error);
      return [];
    }
  }

  static async getSlaves(): Promise<Slave[]> {
    try {
      // 尝试从缓存获取
      // const cachedSlaves = slavesCache.get('slaves');
      // if (cachedSlaves) {
      //   console.log('=== 从缓存获取从站信息 ===');
      //   return cachedSlaves;
      // }

      console.log("======== 执行获取从站信息命令 ethercat slaves -v =========")
      const { stdout } = await execAsync('ethercat slaves -v');
      const slaves: Slave[] = [];
      let currentSlave: Partial<Slave> = {};
      let currentMaster = 0;
      let inPorts = false;
      let currentPorts: any[] = [];
      
      const lines = stdout.split('\n');
      for (const line of lines) {
        if (line.startsWith('=== Master')) {
          if (Object.keys(currentSlave).length > 0) {
            currentSlave.ports = currentPorts;
            slaves.push(currentSlave as Slave);
          }
          currentSlave = {};
          currentPorts = [];
          inPorts = false;
          const matches = line.match(/Master (\d+), Slave (\d+)/);
          if (matches) {
            currentMaster = parseInt(matches[1], 10);
            currentSlave.master = currentMaster;
            currentSlave.index = parseInt(matches[2], 10);
          }
        } else if (line.startsWith('Device:')) {
          currentSlave.name = line.split(':')[1].trim();
        } else if (line.startsWith('State:')) {
          currentSlave.state = line.split(':')[1].trim();
        } else if (line.includes('Vendor Id:')) {
          currentSlave.vendorId = line.split(':')[1].trim();
        } else if (line.includes('Product code:')) {
          currentSlave.productCode = line.split(':')[1].trim();
        } else if (line.includes('Revision number:')) {
          currentSlave.revision = line.split(':')[1].trim();
        } else if (line.includes('Serial number:')) {
          currentSlave.serial = line.split(':')[1].trim();
        } else if (line.includes('Group:')) {
          currentSlave.group = line.split(':')[1].trim();
        } else if (line.includes('Order number:')) {
          currentSlave.orderNumber = line.split(':')[1].trim();
        } else if (line.includes('Device name:')) {
          currentSlave.deviceName = line.split(':')[1].trim();
        } else if (line.includes('Current consumption:')) {
          currentSlave.currentConsumption = parseInt(line.split(':')[1].trim());
        } else if (line.includes('FMMU bit operation:')) {
          currentSlave.fmmuBitOperation = line.includes('yes');
        } else if (line.includes('Distributed clocks:')) {
          currentSlave.distributedClocks = line.split(':')[1].trim();
        } else if (line.includes('DC system time transmission delay:')) {
          currentSlave.dcSystemTimeDelay = parseInt(line.split(':')[1].trim());
        } else if (line.trim().startsWith('Port')) {
          inPorts = true;
        } else if (inPorts && line.trim().match(/^\d/)) {
          const [index, type, link, loop, signal, nextSlave, rxTime, diff, nextDc] = 
            line.trim().split(/\s+/);
          currentPorts.push({
            index: parseInt(index),
            type,
            link,
            loop,
            signal,
            nextSlave,
            rxTime: rxTime === '-' ? 0 : parseInt(rxTime),
            diff: diff === '-' ? 0 : parseInt(diff),
            nextDc: nextDc === '-' ? 0 : parseInt(nextDc)
          });
        }
      }
      
      if (Object.keys(currentSlave).length > 0) {
        currentSlave.ports = currentPorts;
        slaves.push(currentSlave as Slave);
      }
      
      // 获取每个从站的 PDO 信息
      for (const slave of slaves) {
        try {
          const { stdout: pdoOut } = await execAsync(`ethercat pdos -m ${slave.master} -p ${slave.index}`);
          const syncManagers: SyncManager[] = [];
          let currentSM: Partial<SyncManager> = {};
          let currentPDO: Partial<PDO> = {};
          
          const lines = pdoOut.split('\n');
          for (const line of lines) {
            if (line.startsWith('SM')) {
              // 保存前一个 SM 的最一个 PDO
              if (Object.keys(currentPDO).length > 0) {
                currentSM.pdos?.push(currentPDO as PDO);
                currentPDO = {};
              }
              // 保存前一个 SM
              if (Object.keys(currentSM).length > 0) {
                syncManagers.push(currentSM as SyncManager);
              }
              const matches = line.match(/SM(\d+): PhysAddr (0x\w+), DefaultSize\s+(\d+), ControlRegister (0x\w+), Enable (\d)/);
              if (matches) {
                currentSM = {
                  index: parseInt(matches[1]),
                  physAddr: matches[2],
                  defaultSize: parseInt(matches[3]),
                  controlRegister: matches[4],
                  enable: matches[5] === '1',
                  pdos: []
                };
              }
            } else if (line.trim().startsWith('RxPDO') || line.trim().startsWith('TxPDO')) {
              // 保存前一个 PDO
              if (Object.keys(currentPDO).length > 0) {
                currentSM.pdos?.push(currentPDO as PDO);
              }
              const matches = line.match(/(Rx|Tx)PDO (0x\w+) "(.*)"/);
              if (matches) {
                currentPDO = {
                  type: `${matches[1]}PDO` as 'RxPDO' | 'TxPDO',
                  index: matches[2],
                  name: matches[3],
                  entries: []
                };
              }
            } else if (line.trim().startsWith('PDO entry')) {
              const matches = line.match(/PDO entry (0x\w+):(\w+),\s+(\d+) bit, "(.*)"/);
              if (matches && currentPDO.entries) {
                const entry: PDOEntry = {
                  index: matches[1],
                  subIndex: matches[2],
                  bits: parseInt(matches[3]),
                  name: matches[4]
                };
                currentPDO.entries.push(entry);
              }
            }
          }
          
          // 保存最后一个 PDO 和 SM
          if (Object.keys(currentPDO).length > 0) {
            currentSM.pdos?.push(currentPDO as PDO);
          }
          if (Object.keys(currentSM).length > 0) {
            syncManagers.push(currentSM as SyncManager);
          }
          
          slave.syncManagers = syncManagers;
        } catch (error) {
          console.error(`Failed to get PDOs for slave ${slave.index}:`, error);
        }
      }

      // 存到缓存
      slavesCache.set('slaves', slaves);
      return slaves;
    } catch (error) {
      console.error('Failed to get slaves:', error);
      return [];
    }
  }

  static async startService(): Promise<void> {
    try {
      await execAsync('ethercatctl start');
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error('Failed to start service:', error);
      throw new ApiError(500, '启动服务失败');
    }
  }
  

  static async stopService(): Promise<void> {
    try {
      // 获取当前状态，检查是否有程序在运行
      const status = await this.getStatus();
      if (!status.running) {
        return; // 如果服务未运行，直接返回
      }

      // 查询程序列表并检查运行状态
      const programs = await ProgramManager.getPrograms();
      
      // 检查每个程序的运行状态
      for (const program of programs) {
        const serviceName = `ethercat_program@${program.name}.service`;
        try {
          const { stdout } = await execAsync(`systemctl is-active ${serviceName}`);
          if (stdout.trim() === 'active') {
            throw new ApiError(500, `程序 ${program.name} 正在运行，请先停止所有程序`);
          }
        } catch (error: any) {
          // 如果命令执行失败或返回非 active（如 inactive、failed 等），继续检查下一个
          if (error instanceof ApiError) {
            throw error;
          }
          // 如果是 systemctl 命令执行失败，检查错误输出
          if (error.stderr && error.stderr.includes('Failed to connect to bus')) {
            throw new ApiError(500, 'systemd 服务连接失败');
          }
          continue;
        }
      }

      // 执行停止命令
      await execAsync('ethercatctl stop');
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Failed to stop service:', error);
      if (error instanceof ApiError) {
        throw error; // 如果是 ApiError，直接抛出
      }
      throw new ApiError(500, '停止服务失败');
    }
  }

  static async getSlaveXml(master: number, slave: number): Promise<string> {
    try {
      const { stdout } = await execAsync(`ethercat xml -m ${master} -p ${slave}`);
      return stdout;
    } catch (error) {
      console.error('Failed to get slave XML:', error);
      throw new ApiError(500, '获取从站 XML 配置失败');
    }
  }

  static async generateTopology(master: number): Promise<string> {
    try {
      const { stdout } = await execAsync(`ethercat graph -m ${master}`);
      return stdout;
    } catch (error) {
      console.error('Failed to generate topology:', error);
      throw new ApiError(500, '生成拓扑图失败');
    }
  }

  static async getAvailableMasters(): Promise<number[]> {
    try {
      const { stdout } = await execAsync('ethercat master');
      const masters: number[] = [];
      
      // 解析输出，提取主站索引
      const lines = stdout.split('\n');
      for (const line of lines) {
        const match = line.match(/^Master(\d+)/);
        if (match) {
          masters.push(parseInt(match[1]));
        }
      }
      
      // 确保返回的是排序后的主站索引数组
      return masters.sort((a, b) => a - b);
    } catch (error) {
      console.error('Failed to get available masters:', error);
      return [];
    }
  }

  // 添加清除缓存方法
  static clearCache() {
    statusCache.clear();
    mastersCache.clear();
    slavesCache.clear();
  }

  static async getNetworkInterfaces(): Promise<string[]> {
    try {
      // 使用 find 命令获取所有物理网口
      const { stdout } = await execAsync(
        "find /sys/class/net -type l -not -lname '*virtual*' -printf '%f\\n' | grep -E '^(eth|eno|enp|end)' | cut -d@ -f1 | sort"
      );
      
      // 将输出按行分割并过滤空行
      return stdout.split('\n').filter(line => line.trim());
    } catch (error) {
      console.error('Failed to get network interfaces:', error);
      return [];
    }
  }

  // 添加 MAC 地址反查网口的方法
  static async getMacToInterfaceMap(): Promise<Map<string, string>> {
    try {
      const { stdout } = await execAsync("ip link | grep -E '^[0-9]+: (eth|end)' -A 1");
      const lines = stdout.split('\n');
      const macToIface = new Map<string, string>();
      
      let currentIface = '';
      for (const line of lines) {
        if (line.includes(': eth') || line.includes(': end')) {
          currentIface = line.split(': ')[1].split('@')[0];
        } else if (line.includes('link/ether') && currentIface) {
          const mac = line.split('link/ether ')[1].split(' ')[0].toLowerCase();
          macToIface.set(mac, currentIface);
        }
      }
      
      return macToIface;
    } catch (error) {
      console.error('Failed to get MAC to interface map:', error);
      return new Map();
    }
  }

  static async getEthercatConfigPath(): Promise<string> {
    try {
      // 首先获取 ethercatctl 的实际路径
      const { stdout: ethercatctlPath } = await execAsync('which ethercatctl');
      
      // // 读取 ethercatctl 脚本内容
      const content = await fs.readFile(ethercatctlPath.trim(), 'utf-8');
      
      // // 查找 ETHERCAT_CONFIG 的定义
      // const configMatch = content.match(/ETHERCAT_CONFIG=["']?([^"'\n]+)["']?/);
      
      // if (!configMatch) {
      //   throw new Error('Cannot find ETHERCAT_CONFIG in ethercatctl');
      // }
      
      // 匹配形如 ETHERCAT_CONFIG = "/some/path/to.conf" 或 ETHERCAT_CONFIG=/etc/ethercat.conf
    const pathRegex = /ETHERCAT_CONFIG\s*=\s*["']?(\/[^\s"'#;]+)["']?/g;

    const matches = [...content.matchAll(pathRegex)]
      .map(m => m[1]);   // 提取所有符合条件的路径

    if (matches.length === 0) {
      throw new Error('未找到任何有效的 ETHERCAT_CONFIG 路径');
    }

    // 如果你想要最后一条（脚本中生效的覆盖逻辑通常是后面的）
    const configPath = matches[matches.length - 1];
    console.log('最终使用的配置文件：', configPath);

      return configPath;
    } catch (error) {
      console.error('Failed to get ethercat config path:', error);
      // 如果无法获取,则返回默认路径
      return '/etc/ethercat.conf';
    }
  }

  static async getConfig(): Promise<EtherCATConfig> {
    try {
      // 动态获取配置文件路径
      const configPath = await this.getEthercatConfigPath();
      const content = await fs.readFile(configPath, 'utf-8');
      const lines = content.split('\n');
      
      const masterDevices: { [key: number]: string | null } = {};

      for (const line of lines) {
        const trimmedLine = line.trim();
        // 跳过注释和空行
        if (trimmedLine.startsWith('#') || !trimmedLine) continue;

        // 匹配任意主站号的设备配置
        const masterMatch = trimmedLine.match(/^MASTER(\d+)_DEVICE=["']?([^"']+)["']?/);
        if (masterMatch) {
          const masterIndex = parseInt(masterMatch[1]);
          const device = masterMatch[2];
          masterDevices[masterIndex] = device;
        }
      }

      // 确保至少有一个主站配置
      if (Object.keys(masterDevices).length === 0) {
        throw new ApiError(500, '配置文件中未找到任何主站设备配置');
      }

      return {
        masterDevices
      };
    } catch (error) {
      console.error('Failed to read config:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '读取配置失败');
    }
  }

  static async updateConfig(config: EtherCATConfig): Promise<void> {
    // 保存原始配置内容，用于回滚
    let originalContent: string | null = null;
    let configPath: string;

    try {
      if (Object.keys(config.masterDevices).length === 0) {
        throw new ApiError(400, '至少需要配置一个主站设备');
      }

      console.log('更新主站配置：config', config);

      console.log("更新主站配置：获取程序列表，检查是否有程序在运行")
      // 获取程序列表
      const programs = await ProgramManager.getPrograms();
      // 判断是否有程序或中间层服务状态为active，如果有禁止修改
     await Promise.all(programs.map(async (program) => {
      const middlewareService = `ethercat_middleware@${program.name}`;
      const programService = `ethercat_program@${program.name}`;

      const [middlewareStatus, programStatus] = await Promise.all([
        ProgramManager.getServiceStatus(middlewareService),
        ProgramManager.getServiceStatus(programService)
      ]);
      if (middlewareStatus == "active" || programStatus == "active") {
        const runningServices = [];
        if (middlewareStatus) runningServices.push(`中间层服务(${program.name})`);
        if (programStatus) runningServices.push(`程序(${program.name})`);
        logger.error('EthercatService', `有程序或中间层服务在运行，禁止修改配置: ${middlewareService} ${programService}`);
        throw new ApiError(500, `请先停止正在运行的${runningServices.join('和')}后再修改配置`);
      }
     }))

      // 获取配置文件路径
      configPath = await this.getEthercatConfigPath();
      
      // 读取原配置文件内容
      try {
        originalContent = await fs.readFile(configPath, 'utf-8');
      } catch (error) {
        throw new ApiError(500, '无法读取配置文件，请检查文件权限或是否存在');
      }
      
      const lines = originalContent.split('\n');
      
      // 保存所有非 MASTER 设备的配置行
      const otherConfigs = lines.filter(line => {
        const trimmedLine = line.trim();
        return trimmedLine && 
               !trimmedLine.startsWith('#') && 
               !trimmedLine.match(/^MASTER\d+_DEVICE=/);
      });

      // 构建新的配置内容
      const newLines = [
        // 添加所有主站设备配置
        ...Object.entries(config.masterDevices)
          .map(([index, device]) => device ? `MASTER${index}_DEVICE="${device}"` : '')
          .filter(line => line),
        
        // 添加其他配置
        ...otherConfigs
      ];

      // 写入配置文件
      try {
        await fs.writeFile(configPath, newLines.join('\n') + '\n', 'utf-8');
      } catch (error) {
        // 如果写入失败，尝试回滚到原始配置
        if (originalContent) {
          try {
            await fs.writeFile(configPath, originalContent, 'utf-8');
            throw new ApiError(500, '配置更新失败，已回滚到原始配置。错误原因：无法写入配置文件，请检查文件权限');
          } catch (rollbackError) {
            logger.error('EthercatService', '配置回滚失败:', rollbackError);
            throw new ApiError(500, '配置更新失败且回滚失败，系统可能处于不一致状态，请手动检查配置文件');
          }
        }
        throw new ApiError(500, '无法写入配置文件，请检查文件权限');
      }

      // 重启服务
      await this.restartService();
    } catch (error) {
      // 如果是其他类型的错误（非写入错误）且我们有原始配置，也尝试回滚
      if (!(error instanceof ApiError) && originalContent && configPath) {
        try {
          await fs.writeFile(configPath, originalContent, 'utf-8');
          logger.info('EthercatService', '发生错误，已回滚到原始配置');
        } catch (rollbackError) {
          logger.error('EthercatService', '配置回滚失败:', rollbackError);
          throw new ApiError(500, '配置更新失败且回滚失败，系统可能处于不一致状态，请手动检查配置文件');
        }
      }

      if (error instanceof ApiError) {
        throw error;
      }
      console.error('Failed to update config:', error);
      throw new ApiError(500, '更新配置失败：' + (error.message || '未知错误'));
    }
  }

  static async restartService(): Promise<void> {
    try {
      // 执行重启命令
      await execAsync('ethercatctl restart');
      
      // 等待服务重启
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 使用 ethercat master 检查服务状态
      const { stdout } = await execAsync('ethercat master -v');
      const running = stdout.length > 0 && !stdout.includes('Failed to open master device');
      
      if (!running) {
        throw new Error('Service failed to start');
      }

      // 清除缓存，确保获取最新状态
      this.clearCache();
    } catch (error) {
      console.error('Failed to restart service:', error);
      throw new ApiError(500, '重启服务失败，请检查网口配置');
    }
  }

  static async getServiceStatus(): Promise<boolean> {
    try {
      const { stdout } = await execAsync('ethercatctl status');
      return stdout.includes('running');
    } catch (error) {
      console.error('Failed to get service status:', error);
      return false;
    }
  }

  static async getSlaveConfig(): Promise<any> {
    try {
      const slaves = await this.getSlaves();
      const config = {
        slaves: await Promise.all(slaves.map(async (slave: Slave) => {
          try {
            const { stdout: xmlContent } = await execAsync(`ethercat xml -m ${slave.master} -p ${slave.index}`);
            
            // 提取设备名称
            const deviceNameMatch = xmlContent.match(/<Name>\s*<!\[CDATA\[(.*?)\]\]>\s*<\/Name>/);
            const deviceName = deviceNameMatch ? deviceNameMatch[1].trim() : slave.name;

            // 提取 RxPdo 的 Index
            const rxPdoMatch = xmlContent.match(/<RxPdo[^>]*>[\s\S]*?<Index>#x([0-9a-fA-F]+)<\/Index>/);
            const rxPdoIndex = rxPdoMatch ? rxPdoMatch[1] : '1600';
            
            // 提取 TxPdo 的 Index
            const txPdoMatch = xmlContent.match(/<TxPdo[^>]*>[\s\S]*?<Index>#x([0-9a-fA-F]+)<\/Index>/);
            const txPdoIndex = txPdoMatch ? txPdoMatch[1] : '1a00';

            // 提取所有 RxPdo entries
            const rxPdos = [];
            const rxPdoSection = xmlContent.match(/<RxPdo[\s\S]*?<\/RxPdo>/);
            if (rxPdoSection) {
              const entries = rxPdoSection[0].matchAll(/<Entry>[\s\S]*?<Index>#x([0-9a-fA-F]+)<\/Index>[\s\S]*?<SubIndex>(\d+)<\/SubIndex>[\s\S]*?<BitLen>(\d+)<\/BitLen>[\s\S]*?<Name>([^<]*)<\/Name>[\s\S]*?<DataType>([^<]*)<\/DataType>[\s\S]*?<\/Entry>/g);
              for (const entry of entries) {
                rxPdos.push({
                  name: entry[4].trim() || `SubIndex ${entry[2]}`,
                  index: `0x${entry[1]}`,
                  subindex: entry[2],
                  type: EthercatService.getTypeFromDataType(entry[5], parseInt(entry[3])),
                  bitlen: parseInt(entry[3])
                });
              }
            }

            // 提取所有 TxPdo entries
            const txPdos = [];
            const txPdoSection = xmlContent.match(/<TxPdo[\s\S]*?<\/TxPdo>/);
            if (txPdoSection) {
              const entries = txPdoSection[0].matchAll(/<Entry>[\s\S]*?<Index>#x([0-9a-fA-F]+)<\/Index>[\s\S]*?<SubIndex>(\d+)<\/SubIndex>[\s\S]*?<BitLen>(\d+)<\/BitLen>[\s\S]*?<Name>([^<]*)<\/Name>[\s\S]*?<DataType>([^<]*)<\/DataType>[\s\S]*?<\/Entry>/g);
              for (const entry of entries) {
                txPdos.push({
                  name: entry[4].trim() || `SubIndex ${entry[2]}`,
                  index: `0x${entry[1]}`,
                  subindex: entry[2],
                  type: EthercatService.getTypeFromDataType(entry[5], parseInt(entry[3])),
                  bitlen: parseInt(entry[3])
                });
              }
            }

            return {
              index: slave.index.toString(),
              name: deviceName,
              vid: slave.vendorId,
              pid: slave.productCode,
              rx_pdo: `0x${rxPdoIndex}`,
              tx_pdo: `0x${txPdoIndex}`,
              rx_pdos: rxPdos,
              tx_pdos: txPdos
            };
          } catch (error) {
            console.error(`Failed to get config for slave ${slave.master}:${slave.index}:`, error);
            return null;
          }
        }))
      };

      // 过滤掉获取失败的从站配置
      config.slaves = config.slaves.filter(slave => slave !== null);

      return config;
    } catch (error) {
      console.error('Failed to get slave config:', error);
      throw new ApiError(500, '获取从站配置失败');
    }
  }

  private static getTypeFromDataType(dataType: string, bitLen: number): string {
    switch (dataType.toUpperCase()) {
      case 'BOOL':
        return 'bool';
      case 'INT8':
      case 'INT':
      case 'SINT':
        return 'int8';
      case 'BYTE':
      case 'USINT':
        return 'uint8';
      case 'UINT16':
      case 'WORD':
        return 'uint16';
      case 'INT16':
        return 'int16';
      case 'UINT32':
      case 'DWORD':
        return 'uint32';
      case 'INT32':
        return 'int32';
      case 'UINT64':
        return 'uint64';
      case 'INT64':
        return 'int64';
      case 'LREAL':
        return 'double';
      default:
        return bitLen <= 8 ? 'uint8' : 
               bitLen <= 16 ? 'uint16' : 
               bitLen <= 32 ? 'int32' :
               bitLen <= 64 ? 'int64' : 'double';
    }
  }

  static async getTemplateConfig(): Promise<any> {
    try {
      const templatePath = path.join(process.cwd(), 'template', 'slave_template.json');
      const templateContent = await fs.readFile(templatePath, 'utf-8');
      return JSON.parse(templateContent);
    } catch (error) {
      console.error('Failed to read template file:', error);
      throw new ApiError(500, '获取模板配置失败');
    }
  }

  static async getBusTopology(): Promise<{ dot: string; image: Buffer }> {
    try {
      // 获取 dot 格式的拓扑图
      const { stdout: dotContent } = await execAsync('ethercat graph -m 0');
      
      // 使用 graphviz 将 dot 转换为 PNG
      const image = await new Promise<Buffer>((resolve, reject) => {
        const dot = spawn('dot', ['-Tpng']);
        const chunks: Buffer[] = [];

        dot.stdout.on('data', (chunk) => chunks.push(chunk));
        dot.stderr.on('data', (data) => console.error(`dot error: ${data}`));
        dot.on('close', (code) => {
          if (code === 0) {
            resolve(Buffer.concat(chunks));
          } else {
            reject(new Error(`dot process exited with code ${code}`));
          }
        });

        dot.stdin.write(dotContent);
        dot.stdin.end();
      });

      return { dot: dotContent, image };
    } catch (error) {
      console.error('Failed to get bus topology:', error);
      throw new ApiError(500, '获取总线拓扑图失败');
    }
  }

  static async parseENIFile(eniFile: UploadedFile) {
    console.log('外部接口访问eni2json')
    try {
      const xmlContent = eniFile.data.toString('utf-8');
      const jsonConfig = await parseENI(xmlContent);
      return jsonConfig;
    } catch (error) {
      console.error('解析 ENI 文件失败:', error);
      throw new ApiError(500, '解析 ENI 文件失败');
    }
  }

  static async parseENI(xmlContent: string) {
    try {
      return parseENI(xmlContent);
    } catch (error) {
      console.error('解析 ENI 内容失败:', error);
      throw new ApiError(500, '解析 ENI 内容失败');
    }
  }

  static async generateSlaveConfig(config: EniConfig): Promise<any> {
    try {
      console.log('EthercatService: 开始生成从站配置...');
      
      // 调用 eni2json.ts 中的 processEniConfig 方法
      const finalJson = await processEniConfig(config.xmlContent, config);
      
      console.log('EthercatService: 从站配置生成完成');
      return finalJson;
    } catch (error) {
      console.error('EthercatService: 生成从站配置失败:', error);
      throw new ApiError(500, '生成从站配置失败');
    }
  }

  

  /**
   * 获取所有从站的状态
   * @param detail 是否返回详细信息，0表示只返回统计数据，非0返回详细信息
   * @returns 从站状态统计信息和可选的详细状态
   */
  static async getAllSlavesStatus(detail: number = 0, masterIndex: number = -1): Promise<any> {
    try {
      // 首先检查EtherCAT服务是否运行
      const status = await this.getStatus();
      if (!status.running) {
        throw new ApiError(400, 'EtherCAT服务未运行');
      }
      console.log('EtherCAT服务运行中')
      // 获取从站列表
      const slaves = await this.getSlaves();
      console.log('获取从站列表成功')

      // 如果指定了masterIndex，过滤出该主站下的从站
      const filteredSlaves = masterIndex >= 0 ? slaves.filter(slave => slave.master === masterIndex) : slaves;
      
      // 提取所有从站的状态信息
      const slavesStatus = await Promise.all(filteredSlaves.map(async slave => {
        // 获取错误码和实际速度
        let errorCode = null;
        let speed = null;
        
        return {
          index: slave.index,
          master: slave.master,
          name: slave.name || slave.deviceName || '未知设备',
          state: slave.state,
          group: slave.group,
          online: /^(PREOP|OP|SAFEOP)$/i.test(slave.state),
          operationalStatus: /^OP$/i.test(slave.state) ? 1 : 0,
          alState: this.getALStateCode(slave.state),
          vendorId: slave.vendorId,
          productCode: slave.productCode,
          position: slave.position || `${slave.master}:${slave.index}`,
          // errorCode,
          // speed
        };
      }));
      
      // 计算各状态从站数量
      const totalCount = slavesStatus.length;
      const opCount = slavesStatus.filter(s => /^OP$/i.test(s.state)).length;
      const preopCount = slavesStatus.filter(s => /^PREOP$/i.test(s.state)).length;
      const otherCount = Math.max(0, totalCount - opCount - preopCount);
      
      // 根据detail参数决定返回内容
      const response = {
        totalCount,
        opCount,
        preopCount,
        otherCount,
        masterIndex: masterIndex >= 0 ? masterIndex : undefined
      };

      // 如果detail不为0，添加从站详细信息
      if (detail !== 0) {
        return {
          ...response,
          slaves: slavesStatus
        };
      }

      return response;
    } catch (error) {
      console.error('Failed to get all slaves status:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '获取所有从站状态失败');
    }
  }

  /**
   * 获取指定从站的详细状态
   * @param master 主站索引
   * @param slave 从站索引
   * @returns 从站状态信息
   */
  static async getSlaveStatus(master: number, slave: number): Promise<any> {
    try {
      // 首先检查EtherCAT服务是否运行
      const status = await this.getStatus();
      if (!status.running) {
        // 尝试启动服务，启动失败，执行 reboot命令
        // logger.warn('EthercatManager', 'EtherCAT服务未运行，尝试启动服务');
        // await this.startService();
        // if (!status.running) {
        //   // console.log('EtherCAT服务启动失败，执行重启命令')
        //   logger.error('EthercatManager', 'EtherCAT服务启动失败，执行重启命令');
        //   await execAsync('reboot');
        // }
        throw new ApiError(400, 'EtherCAT服务未运行');
      }
      
      // 获取从站列表
      const slaves = await this.getSlaves();
      
      // 查找指定的从站
      const targetSlave = slaves.find(s => s.master === master && s.index === slave);
      
      if (!targetSlave) {
        throw new ApiError(404, `未找到主站${master}上的从站${slave}`);
      }
      
      // 提取所需的状态信息
      const slaveStatus = {
        index: targetSlave.index,
        master: targetSlave.master,
        name: targetSlave.name || targetSlave.deviceName || '未知设备',
        state: targetSlave.state,
        online: targetSlave.state.includes('OP'),
        operationalStatus: targetSlave.state.includes('OP') ? 1 : 0,
        alState: this.getALStateCode(targetSlave.state),
        vendorId: targetSlave.vendorId,
        productCode: targetSlave.productCode,
        revision: targetSlave.revision,
        serial: targetSlave.serial,
        ports: targetSlave.ports
      };
      
      return slaveStatus;
    } catch (error) {
      console.error('Failed to get slave status:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '获取从站状态失败');
    }
  }
  
  /**
   * 获取AL状态码
   * @param stateStr 状态字符串
   * @returns AL状态码
   */
  private static getALStateCode(stateStr: string): number {
    if (stateStr.includes('INIT')) return 1;
    if (stateStr.includes('PREOP')) return 2;
    if (stateStr.includes('SAFEOP')) return 4;
    if (stateStr.includes('OP')) return 8;
    return 0; // 未知状态
  }

  /**
   * 读取从站的SDO数据
   * @param master 主站索引
   * @param slave 从站索引
   * @param index SDO索引
   * @param subindex SDO子索引
   * @returns SDO数据
   * 暂未使用
   */
  static async readSDO(master: number, slave: number, index: number, subindex: number): Promise<any> {
    try {
      // 根据不同的SDO索引使用不同的数据类型
      let dataType = 'uint32'; // 默认类型
      
      // 根据SDO索引设置对应的数据类型
      switch(index) {
        case 0x6041: // 状态字
        case 0x6040: // 控制字
        case 0x603f: // 错误码
          dataType = 'uint16';
          break;
        case 0x606c: // 实际速度
          dataType = 'int32';
          break;
        case 0x6061: // 运行模式
          dataType = 'uint8';
          break;
      }

      const command = `ethercat upload --master=${master} --position=${slave} --type=${dataType} ${index} ${subindex}`;
      const { stdout } = await execAsync(command);
      
      // 解析输出获取值
      const match = stdout.match(/0x[0-9A-Fa-f]+\s+(\d+)/);
      if (match) {
        return {
          value: parseInt(match[1], 10)
        };
      }
      return null;
    } catch (error) {
      console.error(`Failed to read SDO ${index}:${subindex} from slave ${slave}:`, error);
      throw new ApiError(500, `读取SDO数据失败: ${error.message}`);
    }
  }
} 