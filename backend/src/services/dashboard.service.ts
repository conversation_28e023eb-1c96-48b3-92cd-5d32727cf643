import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import { ApiError } from '../utils/errors.js';
import { EthercatService } from './ethercat.service.js';
import { ProgramManager } from './program.manager.js';

const execAsync = promisify(exec);

interface VariableValue {
  name: string;
  value: any;
  type: 'int' | 'float' | 'bool' | 'string';
}

interface EtherCATLog {
  time: string;
  level: 'ERROR' | 'WARNING';
  message: string;
}

export class DashboardService {
  static async getSystemStatus() {
    try {
      // 获取 EtherCAT 状态
      const masters = await EthercatService.getMasters();
      const slaves = await EthercatService.getSlaves();
      
      // 获取运行中的程序
      const programs = await ProgramManager.getPrograms();
      const runningProgram = programs.find(p => p.status === 'running');

      // 统计从站状态
      const slaveStatusCount = {
        running: slaves.filter(s => s.state === 'OP').length,
        preop: slaves.filter(s => s.state === 'PREOP').length,
        init: slaves.filter(s => s.state === 'INIT').length,
        error: slaves.filter(s => !['OP', 'PREOP', 'INIT'].includes(s.state)).length
      };

      // 获取系统指标
      const { stdout: cpuInfo } = await execAsync("top -bn1 | grep 'Cpu(s)' | awk '{print $2}'");
      const { stdout: memInfo } = await execAsync("free | grep Mem | awk '{print $3/$2 * 100.0}'");
      const { stdout: netInfo } = await execAsync("cat /proc/net/dev | grep eth0 | awk '{print $2,$10}'");

      const [netRx, netTx] = netInfo.trim().split(' ').map(Number);

      const metrics = {
        timestamp: new Date().toISOString(),
        cpu_usage: parseFloat(cpuInfo),
        memory_usage: parseFloat(memInfo),
        network_rx: netRx,
        network_tx: netTx
      };

      return {
        ethercatStatus: masters.length > 0 && masters.some(m => m.phase === 'OPERATION'),
        slaveCount: slaves.length,
        currentProgram: runningProgram?.name || null,
        programUptime: runningProgram ? await this.getProgramUptime(runningProgram.id) : null,
        slaveStatusCount,
        metrics
      };
    } catch (error) {
      console.error('Failed to get system status:', error);
      throw error;
    }
  }

  private static async getProgramUptime(programId: string): Promise<string> {
    try {
      const { stdout } = await execAsync(`ps -o etimes= -p $(pgrep -f "ethercat_${programId.substring(0, 6)}")`);
      const uptime = parseInt(stdout.trim());
      const hours = Math.floor(uptime / 3600);
      const minutes = Math.floor((uptime % 3600) / 60);
      const seconds = uptime % 60;
      return `${hours}h ${minutes}m ${seconds}s`;
    } catch {
      return '0h 0m 0s';
    }
  }

  // 获取共享内存中的变量值
  static async getVariableValues(programId: string): Promise<VariableValue[]> {
    try {
      const program = await pb.collection('programs').getOne(programId);
      if (!program) {
        throw new ApiError(404, '程序不存在');
      }

      const variables: VariableValue[] = [];
      const ethercatDir = program.ethercatDir || '/dev/shm/ethercat';

      // 读取目录下所有文件
      const files = await fs.readdir(ethercatDir);
      
      for (const file of files) {
        try {
          // 读取文件内容
          const content = await fs.readFile(`${ethercatDir}/${file}`, 'utf-8');
          const value = content.trim();

          // 判断值的类型
          let type: 'int' | 'float' | 'bool' | 'string';
          let parsedValue: any;

          if (value === '0' || value === '1') {
            type = 'bool';
            parsedValue = value === '1';
          } else if (!isNaN(parseInt(value))) {
            type = 'int';
            parsedValue = parseInt(value);
          } else if (!isNaN(parseFloat(value))) {
            type = 'float';
            parsedValue = parseFloat(value);
          } else {
            type = 'string';
            parsedValue = value;
          }

          variables.push({
            name: file,
            value: parsedValue,
            type
          });
        } catch (error) {
          console.error(`Failed to read variable file ${file}:`, error);
        }
      }

      return variables;
    } catch (error) {
      console.error('Failed to get variable values:', error);
      throw error;
    }
  }

  // 获取 EtherCAT 相关的 dmesg 日志
  static async getEtherCATLogs(): Promise<EtherCATLog[]> {
    try {
      // 使用 dmesg 获取日志，并过滤 EtherCAT 相关的错误和警告
      const { stdout } = await execAsync(
        "dmesg | grep -E 'EtherCAT (ERROR|WARNING)' | tail -n 100"
      );

      const logs: EtherCATLog[] = [];
      const lines = stdout.split('\n');

      for (const line of lines) {
        if (!line.trim()) continue;

        // 解析日志级别
        let level: 'ERROR' | 'WARNING';
        if (line.includes('ERROR')) {
          level = 'ERROR';
        } else if (line.includes('WARNING')) {
          level = 'WARNING';
        } else {
          continue;
        }

        // 提取时间戳和消息
        const match = line.match(/\[(.*?)\]/);
        const time = match ? match[1] : new Date().toISOString();
        const message = line.replace(/\[.*?\]/, '').trim();

        logs.push({
          time,
          level,
          message
        });
      }

      return logs.reverse(); // 最新的日志在前
    } catch (error) {
      console.error('Failed to get EtherCAT logs:', error);
      return [];
    }
  }
} 