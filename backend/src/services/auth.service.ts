import PocketBase from 'pocketbase';
import { ApiError } from '../utils/errors';
import { config } from '../config';

const pb = new PocketBase(config.pocketbaseUrl);

export class AuthService {
  static async login(username: string, password: string) {
    console.log('[Auth Service] Login attempt for user:', username);
    try {
      const authData = await pb.collection('users').authWithPassword(
        username,
        password
      );
      
      console.log('[Auth Service] Login successful for user:', username);
      return {
        token: authData.token,
        user: {
          id: authData.record.id,
          username: authData.record.username,
          email: authData.record.email,
          role: authData.record.role
        }
      };
    } catch (error) {
      console.error('[Auth Service] Login failed:', error);
      throw new ApiError(401, '用户名或密码错误');
    }
  }

  static async logout(token: string) {
    console.log('[Auth Service] Logout attempt');
    try {
      // 验证token
      const isValid = await this.verifyToken(token);
      if (!isValid) {
        throw new ApiError(401, '认证令牌无效');
      }

      await pb.authStore.clear();
      console.log('[Auth Service] Logout successful');
      return true;
    } catch (error) {
      console.error('[Auth Service] Logout failed:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '退出登录失败');
    }
  }

  static async verifyToken(token: string) {
    console.log('[Auth Service] Token verification attempt');
    try {
      // 检查token格式
      if (!token || typeof token !== 'string') {
        return false;
      }

      // 移除Bearer前缀
      const actualToken = token.replace('Bearer ', '');
      
      // 保存token到PocketBase
      pb.authStore.save(actualToken, null);
      
      // 验证token有效性
      const isValid = await pb.authStore.isValid;
      console.log('[Auth Service] Token verification result:', isValid);
      
      // 如果token无效，清除认证状态
      if (!isValid) {
        pb.authStore.clear();
      }
      
      return isValid;
    } catch (error) {
      console.error('[Auth Service] Token verification failed:', error);
      return false;
    }
  }

  static async refreshToken(token: string) {
    console.log('[Auth Service] Token refresh attempt');
    try {
      // 验证当前token
      const isValid = await this.verifyToken(token);
      if (!isValid) {
        throw new ApiError(401, '认证令牌无效');
      }

      // 尝试刷新token
      const authData = await pb.collection('users').authRefresh();
      console.log('[Auth Service] Token refresh successful');
      
      return {
        token: authData.token,
        user: {
          id: authData.record.id,
          username: authData.record.username,
          email: authData.record.email,
          role: authData.record.role
        }
      };
    } catch (error) {
      console.error('[Auth Service] Token refresh failed:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(401, '刷新认证令牌失败');
    }
  }
}