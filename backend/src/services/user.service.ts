import PocketBase from 'pocketbase';
import { config } from '../config/index.js';
import { ApiError } from '../utils/errors.js';

const pb = new PocketBase(config.pocketbaseUrl);

export class UserService {
  static async getUsers() {
    try {
      const records = await pb.collection('users').getList(1, 50);
      return records.items.map(record => ({
        id: record.id,
        username: record.username,
        email: record.email,
        role: record.role,
        status: record.status,
        created: record.created
      }));
    } catch (error) {
      throw new ApiError(500, '获取用户列表失败');
    }
  }

  static async createUser(userData: any) {
    try {
      const record = await pb.collection('users').create(userData);
      return {
        id: record.id,
        username: record.username,
        email: record.email,
        role: record.role,
        status: record.status,
        created: record.created
      };
    } catch (error) {
      throw new ApiError(500, '创建用户失败');
    }
  }

  static async updateUser(id: string, userData: any) {
    try {
      const record = await pb.collection('users').update(id, userData);
      return {
        id: record.id,
        username: record.username,
        email: record.email,
        role: record.role,
        status: record.status,
        created: record.created
      };
    } catch (error) {
      throw new ApiError(500, '更新用户失败');
    }
  }

  static async deleteUser(id: string) {
    try {
      await pb.collection('users').delete(id);
    } catch (error) {
      throw new ApiError(500, '删除用户失败');
    }
  }

  static async resetPassword(id: string, password: string) {
    try {
      await pb.collection('users').update(id, {
        password,
        passwordConfirm: password
      });
    } catch (error) {
      throw new ApiError(500, '重置密码失败');
    }
  }
} 