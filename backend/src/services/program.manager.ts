import { ApiError } from '../utils/errors.js';
import { config } from '../config/index.js';
import PocketBase, { RecordModel } from 'pocketbase';
import { UploadedFile } from 'express-fileupload';
import fs from 'fs/promises';
import path from 'path';
import { TemplateService } from './template.service.js';
import { exec as execCallback } from 'child_process';
import { promisify } from 'util';
import { ProgramConfig } from '../types/index.js';
import archiver from 'archiver';
import { Readable } from 'stream';
import { spawn } from 'child_process';
import { existsSync } from 'fs';
import decompress from 'decompress';
import { EthercatService } from './ethercat.service.js';
import { ErrorCode } from '../utils/error-codes.js';
import os from 'os';
import Transaction from './transaction.manager.js';
import PocketBaseManager from './pocketbase.manager';
import { LoggerService } from './logger.service.js';
import { SDKGeneratorService } from '../utils/sdk-generator.service.js';
import { start } from 'repl';



const exec = promisify(execCallback);
const execAsync = promisify(execCallback);
const logger = LoggerService.getInstance();

interface ProgramConfig {
  masterIndex: string;
  taskFrequency: string;
  config: any;
  debug?: string;
}

interface ActualSlave {
  vid: string;
  pid: string;
  [key: string]: any;
}

// 使用 PocketBaseManager 来管理 PocketBase 实例
export class PocketBaseManager {
  private static instance: PocketBase | null = null;
  private static authToken: string | null = null;

  static getInstance(): PocketBase {
    if (!this.instance) {
      this.instance = new PocketBase(config.pocketbaseUrl);
      if (this.authToken) {
        try {
          this.instance.authStore.save(this.authToken, null);
        } catch (error) {
          console.error('Failed to restore auth token:', error);
          this.authToken = null;
        }
      }
    }
    return this.instance;
  }

  static async ensureAuth(): Promise<PocketBase> {
    const pb = this.getInstance();
    try {
      if (!pb.authStore.isValid) {
        throw new ApiError(400, '认证已过期，请重新登录');
      }
    } catch (error) {
      console.error('[PocketBase] Authentication error:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(400, '认证验证失败，请重新登录');
    }
    return pb;
  }

  static setAuthToken(token: string) {
    this.authToken = token;
    if (this.instance) {
      try {
        this.instance.authStore.save(token, null);
      } catch (error) {
        console.error('Failed to save auth token:', error);
      }
    }
  }

  static clearAuth() {
    this.authToken = null;
    if (this.instance) {
      this.instance.authStore.clear();
    }
  }
}

interface ProgramRecord {
  id: string;
  name: string;
  config: ProgramConfig;
  filePath: string;
  middlePath?: string;
  systemdName?: string;
  status: string;
  masterIndex: string;
  startup?: string;
  debug?: string;
  taskFrequency?: string;
}

export interface SystemInfo {
  cpuCount: number;          // CPU核心数
  cpuModel: string;          // CPU型号
  totalMemory: number;       // 总内存(GB)
  freeMemory: number;        // 可用内存(GB)
  platform: string;          // 操作系统平台
  arch: string;             // CPU架构
  hostname: string;         // 主机名
  cpuDetails?: {            // CPU详细信息（仅Linux）
    processors: Array<{
      processor: number;
      model: string;
      mhz: number;
      cacheSize: string;
    }>;
  };
}

export class ProgramManager {

  static async getProgramsForMiddleware(): Promise<any[]> {
    try {
      // 直接使 PocketBase，不需要认证
      const pb = new PocketBase(config.pocketbaseUrl);
      const records = await pb.collection('programs').getList(1, 100, {
        sort: '-uploadTime',
        expand: 'config'
      });
      return records.items;
    } catch (error) {
      console.error('[Program Manager] Get programs error:', error);
      throw new ApiError(500, '获取程序列表失败');
    }
  }

  static async getPrograms(): Promise<any[]> {
    try {
      const pb = await PocketBaseManager.ensureAuth();
      const records = await pb.collection('programs').getList(1, 100, {
        sort: '-uploadTime',
        expand: 'config'
      });

      // 获取每个程序的实际状态
      const programsWithStatus = await Promise.all(records.items.map(async (program) => {
        const middlewareService = `ethercat_middleware@${program.name}`;
        const programService = `ethercat_program@${program.name}`;
        // const middlewareService = `ethercat_middleware_${this.sanitizeServiceName(program.name)}-${program.id.substring(0, 6)}`;
        // const programService = `ethercat_program_${this.sanitizeServiceName(program.name)}-${program.id.substring(0, 6)}`;

        const [middlewareStatus, programStatus] = await Promise.all([
          this.getServiceStatus(middlewareService),
          this.getServiceStatus(programService)
        ]);

        return {
          ...program,
          middlewareStatus,
          programStatus,
          middlewareService,
          programService
        };
      }));

      return programsWithStatus;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, '获取程序列表失败');
    }
  }

  static async getAvailableMasters(): Promise<number[]> {
    try {
      // 使用 EthercatService 获取实际的主站列表
      const masters = await EthercatService.getAvailableMasters();
      return masters;
    } catch (error) {
      console.error('Failed to get available masters:', error);
      return [0]; // 如果获取失败，至少返回主站0
    }
  }

  // 外部上传程序
  static async uploadProgram_external(
    program: UploadedFile,
    uploadConfig: UploadedFile,
    formData: any,
    force = true
  ) {
    try {
      const pb = new PocketBase(config.pocketbaseUrl);

      // 验证主站索引是否有效
      const availableMasters = await this.getAvailableMasters();
      const masterIndex = parseInt(formData.masterIndex);
      if (!availableMasters.includes(masterIndex)) {
        throw new ApiError(500, `上传失败：无效的主站索引 ${masterIndex}`);
      }

      const uploadDir = path.join(process.cwd(), 'uploads');
      await fs.mkdir(uploadDir, { recursive: true });

      const programName = formData.name;
      const programDir = path.join(uploadDir, programName);

      // 检查程序是否已存在于数据库
      const existingPrograms = await pb.collection('programs').getList(1, 1, {
        filter: `name = "${programName}"`
      });

      if (existingPrograms.items.length > 0) {
        console.log('=== Existing Programs ===');
        console.log(existingPrograms.items);
        if (!force) {
          throw new ApiError(500, `上传失败：程序 ${programName} 已存在于数据库中`);
        }
        // 如果 force 为 true，删除已存在的程序
        const existingProgram = existingPrograms.items[0];
        await this.deleteProgram_external(programName);
        console.log(`已删除已存在的程序: ${programName}`);
      }

      // 检查文件系统中是否存在
      if (existsSync(programDir)) {
        if (!force) {
          throw new ApiError(500, `上传失败：程序 ${programName} 已存在于文件系统中`);
        }
        // 如果 force 为 true，删除已存在的目录
        await fs.rm(programDir, { recursive: true, force: true });
        console.log(`已删除已存在的程序目录: ${programDir}`);
      }

      const archivePath = path.join(uploadDir, `${programName}_temp.zip`);

      try {
        // 1. 移动上传的压缩包到临时位置
        await program.mv(archivePath);

        // 2. 创建程序目录
        await fs.mkdir(programDir, { recursive: true });

        // 3. 解压文件到程序目录
        await decompress(archivePath, programDir);

        // 4. 删除临时压缩包
        await fs.unlink(archivePath);

        // 5. 递归查找主程序文件
        const mainProgramPath = await this.findMainProgram(programDir, programName);

        if (!mainProgramPath) {
          // 如果找不到主程序，清理创建的目录
          await fs.rm(programDir, { recursive: true, force: true });
          throw new ApiError(500, `上传失败：压缩包中不包含名为 ${programName} 的主程序`);
        }

        // 检查程序架构类型
        try {
          // 2. 获取系统架构
          const { stdout: systemArch } = await execAsync('uname -m');
          const { stdout: fileInfo } = await execAsync(`file "${mainProgramPath}"`);
          
          console.log('系统架构信息:', systemArch);
          console.log('文件信息:', fileInfo);

          const cleanSystemArch = systemArch.trim().toLowerCase();
          const fileInfoLower = fileInfo.toLowerCase();

          // 架构映射表
          const archMapping: { [key: string]: string[] } = {
            // x86 架构
            'x86_64': ['x86-64', 'x86_64', 'amd64'],
            'i386': ['x86', 'i386', 'i686'],
            // ARM 架构
            'aarch64': ['arm64', 'aarch64', 'arm aarch64'],
            'armv7l': ['arm', 'armv7', 'arm32'],
            'armv6l': ['armv6', 'arm32'],
            // MIPS 架构
            'mips64': ['mips64'],
            'mips': ['mips'],
            // PowerPC 架构
            'ppc64le': ['powerpc64le', 'ppc64le'],
            'ppc64': ['powerpc64', 'ppc64']
          };

          // 3. 检测程序架构
          let programArch = '';
          const detectedArchs: string[] = [];

          // 遍历映射表检查文件信息中包含的架构标识
          for (const [arch, patterns] of Object.entries(archMapping)) {
            if (patterns.some(pattern => fileInfoLower.includes(pattern))) {
              detectedArchs.push(arch);
            }
          }

          // 如果检测到多个架构，记录警告
          if (detectedArchs.length > 1) {
            console.warn(`警告：检测到多个可能的架构: ${detectedArchs.join(', ')}`);
            programArch = detectedArchs[0]; // 使用第一个检测到的架构
          } else if (detectedArchs.length === 1) {
            programArch = detectedArchs[0];
          }

          // 4. 检查是否为动态链接的可执行文件
          if (fileInfoLower.includes('statically linked')) {
            console.log('检测到静态链接的可执行文件');
          } else if (fileInfoLower.includes('dynamically linked')) {
            console.log('检测到动态链接的可执行文件');
          }

          // 5. 架构兼容性检查
          let isCompatible = false;
          
          // 检查系统架构是否在映射表中
          for (const [arch, patterns] of Object.entries(archMapping)) {
            if (patterns.some(pattern => cleanSystemArch.includes(pattern))) {
              isCompatible = (arch === programArch);
              break;
            }
          }

          if (!programArch) {
            throw new ApiError(400, `无法识别程序的架构类型，文件信息：${fileInfo}`);
          }

          if (!isCompatible) {
            const error = new Error(`程序架构(${programArch})与系统架构(${cleanSystemArch})不匹配`);
            (error as any).code = ErrorCode.ARCHITECTURE_MISMATCH;
            throw error;
          }

          console.log(`架构检查通过：程序架构 ${programArch} 与系统架构 ${cleanSystemArch} 兼容`);

        } catch (error) {
          // 清理程序目录
          await fs.rm(programDir, { recursive: true, force: true });
          
          if (error instanceof ApiError) {
            throw error;
          }
          console.error('架构检查失败:', error);
          if (error.message.includes('command not found')) {
            throw new ApiError(500, '系统缺少必要的检查工具(file/uname)，请联系管理员');
          }
          throw new ApiError(500, `程序架构检查失败: ${error.message}`);
        }

        // 给主程序添加执行权限
        await fs.chmod(mainProgramPath, 0o755);

        // 6. 解析配置文件
        let configData;
        try {
          const configStr = uploadConfig.data.toString('utf-8');
          configData = JSON.parse(configStr);
        } catch (error) {
          // 如果配置解析失败，清理创建的目录
          await fs.rm(programDir, { recursive: true, force: true });
          throw new ApiError(400, '配置文件格式错误');
        }

        // 7. 创建数据库记录
        await pb.collection('programs').create({
          name: programName,
          status: 'stopped',
          uploadTime: new Date().toISOString(),
          masterIndex: formData.masterIndex?.toString() || '0',
          taskFrequency: formData.taskFrequency?.toString() || '1000',
          config: configData,
          filePath: mainProgramPath,
          middlePath: '',
          systemdName: '',
          startup: '0',
          debug: '0'
        });

        // 8. 创建旧版中间件
        const programRecord = await pb.collection('programs').getFirstListItem(`name = "${programName}"`);
        console.log('===== 接口上传程序，创建旧版中间层 =====');
        try {
          await this.generateOldMiddleware(programRecord);
        } catch (error) {
          logger.error('ProgramManager', `外部替换JSON,创建旧版中间层失败: ${error.message}`);
          // 删除中间层
          const middlewarePath = path.join(path.dirname(programRecord.filePath), 'middleware');
          if (existsSync(middlewarePath)) {
            await fs.rm(middlewarePath, { recursive: true, force: true });
            console.log('旧版中间层删除成功');
          }
        }
        return programRecord;

      } catch (error) {
        // 清理临
        if (existsSync(archivePath)) {
          await fs.unlink(archivePath);
        }
        // 如果是ApiError，继续往上抛，否则清理目录
        if (!(error instanceof ApiError) && existsSync(programDir)) {
          await fs.rm(programDir, { recursive: true, force: true });
        }
        throw error;
      }

    } catch (error) {
      console.error('Failed to upload program:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '程序上传失败');
    }
  }




  static async uploadProgram(program: UploadedFile, config: UploadedFile, formData: any) {
    try {
      const pb = await PocketBaseManager.ensureAuth();

      // 验证主站索引是否有效
      const availableMasters = await this.getAvailableMasters();
      const masterIndex = parseInt(formData.masterIndex);
      if (!availableMasters.includes(masterIndex)) {
        throw new ApiError(500, `上传失败：无效的主站索引 ${masterIndex}`);
      }

      const uploadDir = path.join(process.cwd(), 'uploads');
      await fs.mkdir(uploadDir, { recursive: true });

      const programName = formData.name;
      const programDir = path.join(uploadDir, programName);
      const archivePath = path.join(uploadDir, `${programName}_temp.zip`);

      // 检查程序是否已存在
      if (existsSync(programDir)) {
        throw new ApiError(500, `上传失败：程序 ${programName} 已存在`);
      }

      try {
        // 1. 移动上传的压缩包到临时位置
        await program.mv(archivePath);

        // 2. 创建程序目录
        await fs.mkdir(programDir, { recursive: true });

        // 3. 解压文件到程序目录
        await decompress(archivePath, programDir);

        // 4. 删除临时压缩包
        await fs.unlink(archivePath);

        // 5. 递归查找主程序文件
        const mainProgramPath = await this.findMainProgram(programDir, programName);

        if (!mainProgramPath) {
          // 如果找不到主程序，清理创建的目录
          await fs.rm(programDir, { recursive: true, force: true });
          throw new ApiError(500, `上传失败：压缩包中不包含名为 ${programName} 的主程序`);
        }

        // 检查程序架构类型
        try {
          // 1. 首先检查文件是否为可执行文件
          const { stdout: fileTypeInfo } = await execAsync(`file "${mainProgramPath}"`);
          if (!fileTypeInfo.toLowerCase().includes('executable')) {
            throw new ApiError(400, `文件 ${programName} 不是可执行文件`);
          }

          // 2. 获取系统架构
          const { stdout: systemArch } = await execAsync('uname -m');
          const { stdout: fileInfo } = await execAsync(`file "${mainProgramPath}"`);
          
          console.log('系统架构信息:', systemArch);
          console.log('文件信息:', fileInfo);

          const cleanSystemArch = systemArch.trim().toLowerCase();
          const fileInfoLower = fileInfo.toLowerCase();

          // 架构映射表
          const archMapping: { [key: string]: string[] } = {
            // x86 架构
            'x86_64': ['x86-64', 'x86_64', 'amd64'],
            'i386': ['x86', 'i386', 'i686'],
            // ARM 架构
            'aarch64': ['arm64', 'aarch64', 'arm aarch64'],
            'armv7l': ['arm', 'armv7', 'arm32'],
            'armv6l': ['armv6', 'arm32'],
            // MIPS 架构
            'mips64': ['mips64'],
            'mips': ['mips'],
            // PowerPC 架构
            'ppc64le': ['powerpc64le', 'ppc64le'],
            'ppc64': ['powerpc64', 'ppc64']
          };

          // 3. 检测程序架构
          let programArch = '';
          const detectedArchs: string[] = [];

          // 遍历映射表检查文件信息中包含的架构标识
          for (const [arch, patterns] of Object.entries(archMapping)) {
            if (patterns.some(pattern => fileInfoLower.includes(pattern))) {
              detectedArchs.push(arch);
            }
          }

          // 如果检测到多个架构，记录警告
          if (detectedArchs.length > 1) {
            console.warn(`警告：检测到多个可能的架构: ${detectedArchs.join(', ')}`);
            programArch = detectedArchs[0]; // 使用第一个检测到的架构
          } else if (detectedArchs.length === 1) {
            programArch = detectedArchs[0];
          }

          // 4. 检查是否为动态链接的可执行文件
          if (fileInfoLower.includes('statically linked')) {
            console.log('检测到静态链接的可执行文件');
          } else if (fileInfoLower.includes('dynamically linked')) {
            console.log('检测到动态链接的可执行文件');
          }

          // 5. 架构兼容性检查
          let isCompatible = false;
          
          // 检查系统架构是否在映射表中
          for (const [arch, patterns] of Object.entries(archMapping)) {
            if (patterns.some(pattern => cleanSystemArch.includes(pattern))) {
              isCompatible = (arch === programArch);
              break;
            }
          }

          if (!programArch) {
            throw new ApiError(400, `无法识别程序的架构类型，文件信息：${fileInfo}`);
          }

          if (!isCompatible) {
            const error = new Error(`程序架构(${programArch})与系统架构(${cleanSystemArch})不匹配`);
            (error as any).code = ErrorCode.ARCHITECTURE_MISMATCH;
            throw error;
          }

          console.log(`架构检查通过：程序架构 ${programArch} 与系统架构 ${cleanSystemArch} 兼容`);

        } catch (error) {
          // 清理程序目录
          await fs.rm(programDir, { recursive: true, force: true });
          
          if (error instanceof ApiError) {
            throw error;
          }
          console.error('架构检查失败:', error);
          if (error.message.includes('command not found')) {
            throw new ApiError(500, '系统缺少必要的检查工具(file/uname)，请联系管理员');
          }
          throw new ApiError(500, `程序架构检查失败: ${error.message}`);
        }

        // 给主程序添加执行权限
        await fs.chmod(mainProgramPath, 0o755);

        // 6. 解析配置文件
        let configData;
        try {
          const configStr = config.data.toString('utf-8');
          configData = JSON.parse(configStr);
        } catch (error) {
          // 如果配置解析失败，清理创建的目录
          await fs.rm(programDir, { recursive: true, force: true });
          throw new ApiError(400, '配置文件格式错误');
        }
        
        // 7. 创建数据库记录
        await pb.collection('programs').create({
          name: programName,
          status: 'stopped',
          uploadTime: new Date().toISOString(),
          masterIndex: formData.masterIndex?.toString() || '0',
          taskFrequency: formData.taskFrequency?.toString() || '1000',
          config: configData,
          filePath: mainProgramPath,
          middlePath: '',
          systemdName: '',
          startup: '0',
          programCpu: '2',
          middlewareCpu: '3',
          debug: '0'
        });

        // 8. 创建旧版中间件
        const programRecord = await pb.collection('programs').getFirstListItem(`name = "${programName}"`);
        console.log('===== 上传程序，创建旧版中间层 =====');
        try {
          await this.generateOldMiddleware(programRecord);
        } catch (error) {
          logger.error('ProgramManager', `外部替换JSON,创建旧版中间层失败: ${error.message}`);
          // 删除中间层
          const middlewarePath = path.join(path.dirname(programRecord.filePath), 'middleware');
          if (existsSync(middlewarePath)) {
            await fs.rm(middlewarePath, { recursive: true, force: true });
            console.log('旧版中间层删除成功');
          }
        }
        return programRecord;

      } catch (error) {
        // 清理临
        if (existsSync(archivePath)) {
          await fs.unlink(archivePath);
        }
        // 如果是ApiError，继续往上抛，否则清理目录
        if (!(error instanceof ApiError) && existsSync(programDir)) {
          await fs.rm(programDir, { recursive: true, force: true });
        }
        throw error;
      }

    } catch (error) {
      console.error('Failed to upload program:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '程序上传失败');
    }
  }

  private static async bindIrqToCpu(cpuId: string): Promise<void> {
    try {
      // 生成并执行中断绑定脚本
      console.log('bindIrqToCpu', cpuId);
      const bindIrqScript = TemplateService.generateBindIrqScript(parseInt(cpuId));
      const scriptPath = '/app/ethercat-web-ui/backend/bind_irq.sh';

      // 写入脚本文件
      await fs.writeFile(scriptPath, bindIrqScript);

      // 添加执行权限
      await fs.chmod(scriptPath, 0o755);

      // 执行脚本
      await execAsync(`sudo ${scriptPath}`);
      console.log('Successfully executed IRQ binding script');
    } catch (error) {
      console.error('Failed to bind IRQ to CPU:', error);
      // 不再抛出异常，只记录错误信息
      // 中断绑定失败不影响后续流程
    }
  }

  static async startProgram(id: string): Promise<void> {
    try {
      const pb = await PocketBaseManager.ensureAuth();
      const program = await pb.collection('programs').getOne(id);

      if (!program) {
        throw new ApiError(404, '程序不存在，开启程序失败');
      }

      logger.info('ProgramManager', `Starting program with UI ProgramName: ${program.name}`);

      // 获取实际从站配置
      const slaveConfig = await EthercatService.getSlaveConfig();
      const actualSlaves = slaveConfig.slaves || [];  // 获取实际从站数组
      // 1. 检查从站数量是否匹配
      const configuredSlaves = program.config.slaves || [];
      if (actualSlaves.length < configuredSlaves.length) {
        throw new ApiError(
          ErrorCode.SLAVE_COUNT_MISMATCH,
          `从站数量不匹配：配置数量 ${configuredSlaves.length}，实际数量 ${actualSlaves.length}`
        );
      }

      // 2. 检查每个从站的VID/PID是否匹配
      for (const configuredSlave of configuredSlaves) {
        // 在实际从站中查找匹配的VID/PID
        const matchingSlave = actualSlaves.find((actual: ActualSlave) => {
          const actualVid = actual.vid.toLowerCase().replace('0x', '');
          const actualPid = actual.pid.toLowerCase().replace('0x', '');
          const configVid = configuredSlave.vid.toLowerCase().replace('0x', '');
          const configPid = configuredSlave.pid.toLowerCase().replace('0x', '');
          return actualVid === configVid && actualPid === configPid;
        });

        if (!matchingSlave) {
          throw new ApiError(
            ErrorCode.SLAVE_VID_PID_MISMATCH,
            `从站VID/PID不匹配：未找到配置的从站(VID: ${configuredSlave.vid}, PID: ${configuredSlave.pid})`
          );
        }
      }

      // 修改数据库status字段
      await pb.collection('programs').update(id, {
        status: 'running'
      });

      const serviceName = `ethercat_program@${program.name}`;

      // 更新服务名
      if (!program.systemdName) {
        await pb.collection('programs').update(id, {
          systemdName: serviceName
        });
        console.log("update program systemd name")
      }

      console.log("program systemd name: " + serviceName)

      try {

        // 执行中断绑定
        if (program.middlewareCpu != '-1') {
          await this.bindIrqToCpu(program.middlewareCpu || '3');
        } else {
          console.log("跳过中间层中断绑定，program.middlewareCpu 为 -1");
        }

        // 先检查权限
        await this.checkSystemdPermissions();
        // 启动服务
        await this.enableServiceWithEnv(serviceName, 'program', program, true, false, '1');


        // 等待服务启动完成
        let retries = 5;
        while (retries > 0) {
          if (retries != 5) {
            console.log(`Service not active yet, retries left: ${retries}`);
          }
          try {
            const { stdout } = await execAsync(`systemctl is-active ${serviceName}`);
            if (stdout.trim() === 'active') {
              return;
            }
          } catch (error) {
            console.log(`Service not active yet, retries left: ${retries}`);
          }
          await new Promise(resolve => setTimeout(resolve, 1000));
          retries--;
        }

        // 如果服务没有成功启动，抛出错误
        const { stdout: status } = await execAsync(`systemctl status ${serviceName}`);
        throw new Error(`服务启动失败: ${status}`);

      } catch (error) {
        logger.error('ProgramManager', `Failed to start program service: ${error}`);

        // 修改状态
        await pb.collection('programs').update(id, {
          status: 'stopped'
        });

        logger.info('ProgramManager', `rollbak program status to stopped`);

        throw new ApiError(500, error.message || '启动程序服务失败');
      }

    } catch (error) {
      console.error('Failed to start program:', error);

      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '启动程序失败');
    }
  }

  /**
   * 修改env文件中指定变量的值
   * @param filePath env文件路径
   * @param variableName 变量名
   * @param newValue 新值
   * @returns 是否成功修改
   */
  static async updateEnvVariable(filePath: string, variableName: string, newValue: string): Promise<boolean> {
    try {
      // 检查文件是否存在
      try {
        await fs.access(filePath);
      } catch {
        throw new Error(`env文件不存在: ${filePath}`);
      }

      // 读取文件内容
      const content = await fs.readFile(filePath, 'utf-8');

      // 分割为行
      const lines = content.split('\n');

      // 标记是否找到并修改了变量
      let variableFound = false;

      // 处理每一行
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        // 跳过空行和注释
        if (!line || line.startsWith('#')) continue;

        // 检查是否是目标变量
        if (line.startsWith(`${variableName}=`) || line.match(new RegExp(`^${variableName}\\s*=`))) {
          lines[i] = `${variableName}=${newValue}`;
          variableFound = true;
          break;
        }
      }

      // 如果没有找到变量，添加到文件末尾
      if (!variableFound) {
        lines.push(`${variableName}=${newValue}`);
      }

      // 重新组合内容
      const newContent = lines.join('\n');

      // 写回文件
      await fs.writeFile(filePath, newContent, 'utf-8');

      return true;
    } catch (error) {
      console.error(`修改env文件失败: ${error.message}`);
      throw new ApiError(500, `修改env文件失败: ${error.message}`);
    }
  }

  static async stopProgram(id: string): Promise<void> {
    try {
      const pb = await PocketBaseManager.ensureAuth();
      const program = await pb.collection('programs').getOne(id);

      logger.info('ProgramManager', `Stopping program with UI ProgramName: ${program.name}`);

      if (!program) {
        throw new ApiError(404, '程序不存在，停止程序失败');
      }

      // const shortId = id.substring(0, 6);
      const serviceName = `ethercat_program@${program.name}`;

      try {
        await execAsync(`systemctl stop ${serviceName}`);
        // 修改 /app/ethercat-web-ui/backend/env/${program.name}.env 文件中的 AC_LOSS_RECOVER 为 0
        const envFilePath = `/app/ethercat-web-ui/backend/env/${program.name}.env`;
        // 文件不存在则创建
        try {
          await fs.access(envFilePath);
        } catch {
          await this.createEnvFile(program, '0', program.startup)
        }
        await this.updateEnvVariable(envFilePath, 'AC_LOSS_RECOVER', '0');

      } catch (error) {
        console.error('Failed to stop program service:', error);
        throw new ApiError(500, '停止程序服务失败');
      }

      // 停止成功后更新状态为 stopped
      await pb.collection('programs').update(id, {
        status: 'stopped'
      });

    } catch (error) {
      console.error('Failed to stop program:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '停止程序失败');
    }
  }

  // 停止service服务
  static async stopService(serviceName: string) {
    await execAsync(`systemctl disable ${serviceName} --now`);
  }

  // 删除env文件
  static async deleteEnvFile(programName: string) {
    const serviceName = `ethercat_program@${programName}`;
    await this.stopService(serviceName);

    const envFilePath = `/app/ethercat-web-ui/backend/env/${programName}.env`;
    if (existsSync(envFilePath)) {
      await fs.unlink(envFilePath);
    }
  }

  // 更新env文件内容，传入文件路径，key,value
  static async updateEnvFile(envPath: string, key: string, value: string): Promise<void> {
    try {
      // 检查文件是否存在
      if (!existsSync(envPath)) {
        throw new ApiError(404, `环境文件不存在: ${envPath}`);
      }

      // 读取文件内容
      const content = await fs.readFile(envPath, 'utf-8');
      const lines = content.split('\n');

      // 查找并更新指定的key
      let found = false;
      const updatedLines = lines.map(line => {
        if (line.startsWith(`${key}=`)) {
          found = true;
          return `${key}=${value}`;
        }
        return line;
      });

      // 如果没找到key，则添加到文件末尾
      if (!found) {
        updatedLines.push(`${key}=${value}`);
      }

      // 写回文件
      await fs.writeFile(envPath, updatedLines.join('\n'), 'utf-8');
      console.log(`已更新环境文件 ${envPath} 的 ${key}=${value}`);

    } catch (error: any) {
      console.error(`更新环境文件失败:`, error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, `更新环境文件失败: ${error.message}`);
    }
  }


  // 外部删除程序
  static async deleteProgram_external(programName: string) {
    // const pb = await PocketBaseManager.ensureAuth();
    const pb = new PocketBase(config.pocketbaseUrl);
    let program: ProgramRecord | null = null;

    try {
      const result = await pb.collection('programs').getList(1, 1, {
        filter: `name = "${programName}"`
      });
      
      if (result.items.length === 0) {
        throw new ApiError(404, '程序不存在，接口删除程序失败');
      }
      
      program = {
        id: result.items[0].id,
        name: result.items[0].name,
        config: result.items[0].config,
        filePath: result.items[0].filePath,
        middlePath: result.items[0].middlePath,
        systemdName: result.items[0].systemdName,
        status: result.items[0].status,
        masterIndex: result.items[0].masterIndex
      } as ProgramRecord;

      // const shortId = id.substring(0, 6);
      // const programServiceName = `ethercat_program_${this.sanitizeServiceName(program.name)}-${shortId}`;
      // const middlewareServiceName = `ethercat_middleware_${this.sanitizeServiceName(program.name)}-${shortId}`;

      // console.log('Checking services for deletion:');
      // console.log('programServiceName:', programServiceName);
      // console.log('middlewareServiceName:', middlewareServiceName);

      // 清理系统服务
      try {
        // 删除env文件
        console.log('deleteProgram_external: delete env file ', program.name);
        await this.deleteEnvFile(program.name);
        // // 检查并清理程序服务
        // const programServiceExists = await this.checkServiceFileExists(programServiceName);
        // console.log('programServiceExists:', programServiceExists);

        // if (programServiceExists) {
        //   // 禁用并停止服务
        //   await execAsync(`systemctl disable --now ${programServiceName}`).catch(console.error);
        //   console.log('Program service disabled');

        //   // 删除服务文件
        //   const programServiceFile = `/etc/systemd/system/${programServiceName}.service`;
        //   await fs.rm(programServiceFile, { force: true });
        //   console.log('Program service file deleted');
        // }

        // // 检查并清理中间层服务
        // const middlewareServiceExists = await this.checkServiceFileExists(middlewareServiceName);
        // console.log('middlewareServiceExists:', middlewareServiceExists);

        // if (middlewareServiceExists) {
        //   // 禁用并停止服务
        //   await execAsync(`systemctl disable --now ${middlewareServiceName}`).catch(console.error);
        //   console.log('Middleware service disabled');

        //   // 删除服务文件
        //   const middlewareServiceFile = `/etc/systemd/system/${middlewareServiceName}.service`;
        //   await fs.rm(middlewareServiceFile, { force: true });
        //   console.log('Middleware service file deleted');
        // }

        // // 重新加载 systemd 配置
        // if (programServiceExists || middlewareServiceExists) {
        //   await execAsync('systemctl daemon-reload').catch(console.error);
        //   console.log('Systemd configuration reloaded');
        // }

        // 删除 ALL_OP 文件（如果存在）
        const opFile = `/tmp/MASTER${program.masterIndex}_ALL_OP`;
        if (existsSync(opFile)) {
          await fs.rm(opFile, { force: true }).catch(console.error);
          console.log('ALL_OP file deleted');
        }

      } catch (error) {
        console.error('Failed to cleanup services:', error);
        // 继续执行，不要因为服务清理失败而中断删除操作
      }

      // 获取程序目录的路径
      const uploadDir = path.join(process.cwd(), 'uploads');
      const programDir = path.join(uploadDir, program.name);

      // 删除程序文件、目录和数据库记录
      await Promise.all([
        fs.rm(programDir, { recursive: true, force: true })
          .catch(error => {
            console.error('Failed to delete program directory:', error);
            throw new ApiError(500, '删除程序目录失败');
          }),
        pb.collection('programs').delete(program.id)
      ]);

      console.log('Program deletion completed successfully');

    } catch (error) {
      console.error('Failed to delete program:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '删除程序失败');
    }
  }


  static async deleteProgram(id: string) {
    const pb = await PocketBaseManager.ensureAuth();
    let program: ProgramRecord | null = null;

    try {
      program = await this.getProgram(id);
      if (!program) {
        throw new ApiError(404, '程序不存在，删除程序失败');
      }

      // const shortId = id.substring(0, 6);
      // const programServiceName = `ethercat_program_${this.sanitizeServiceName(program.name)}-${shortId}`;
      // const middlewareServiceName = `ethercat_middleware_${this.sanitizeServiceName(program.name)}-${shortId}`;

      // console.log('Checking services for deletion:');
      // console.log('programServiceName:', programServiceName);
      // console.log('middlewareServiceName:', middlewareServiceName);

      // 清理系统服务
      try {
        // 删除env文件
        console.log('deleteProgram: delete env file ', program.name);
        await this.deleteEnvFile(program.name);
        // // 检查并清理程序服务
        // const programServiceExists = await this.checkServiceFileExists(programServiceName);
        // console.log('programServiceExists:', programServiceExists);

        // if (programServiceExists) {
        //   // 禁用并停止服务
        //   await execAsync(`systemctl disable --now ${programServiceName}`).catch(console.error);
        //   console.log('Program service disabled');

        //   // 删除服务文件
        //   const programServiceFile = `/etc/systemd/system/${programServiceName}.service`;
        //   await fs.rm(programServiceFile, { force: true });
        //   console.log('Program service file deleted');
        // }

        // // 检查并清理中间层服务
        // const middlewareServiceExists = await this.checkServiceFileExists(middlewareServiceName);
        // console.log('middlewareServiceExists:', middlewareServiceExists);

        // if (middlewareServiceExists) {
        //   // 禁用并停止服务
        //   await execAsync(`systemctl disable --now ${middlewareServiceName}`).catch(console.error);
        //   console.log('Middleware service disabled');

        //   // 删除服务文件
        //   const middlewareServiceFile = `/etc/systemd/system/${middlewareServiceName}.service`;
        //   await fs.rm(middlewareServiceFile, { force: true });
        //   console.log('Middleware service file deleted');
        // }

        // // 重新加载 systemd 配置
        // if (programServiceExists || middlewareServiceExists) {
        //   await execAsync('systemctl daemon-reload').catch(console.error);
        //   console.log('Systemd configuration reloaded');
        // }

        // 删除 ALL_OP 文件（如果存在）
        const opFile = `/tmp/MASTER${program.masterIndex}_ALL_OP`;
        if (existsSync(opFile)) {
          await fs.rm(opFile, { force: true }).catch(console.error);
          console.log('ALL_OP file deleted');
        }

      } catch (error) {
        console.error('Failed to cleanup services:', error);
        // 继续执行，不要因为服务清理失败而中断删除操作
      }

      // 获取程序目录的路径
      const uploadDir = path.join(process.cwd(), 'uploads');
      const programDir = path.join(uploadDir, program.name);

      // 删除程序文件、目录和数据库记录
      await Promise.all([
        fs.rm(programDir, { recursive: true, force: true })
          .catch(error => {
            console.error('Failed to delete program directory:', error);
            throw new ApiError(500, '删除程序目录失败');
          }),
        pb.collection('programs').delete(id)
      ]);

      console.log('Program deletion completed successfully');

    } catch (error) {
      console.error('Failed to delete program:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '删除程序失败');
    }
  }

  private static async checkSystemdPermissions(): Promise<void> {
    try {
      // 检查是否有权限访问 systemd 服务目录
      await fs.access('/etc/systemd/system', fs.constants.W_OK);

      // 检查是否有权限执行 systemctl 命令
      await execAsync('systemctl --version');
    } catch (error) {
      console.error('Insufficient permissions for systemd operations:', error);
      throw new ApiError(500, '没有足够的权限来管理系统服务，请确保以root权限运行程序');
    }
  }

  private static sanitizeServiceName(name: string): string {
    // 1. 移除文件扩展名
    name = name.replace(/\.[^/.]+$/, '');

    // 2. 只保留、数字、下划线和连字符
    name = name.replace(/[^a-zA-Z0-9_-]/g, '_');

    // 3. 确保名称以字母开头
    name = name.replace(/^[^a-zA-Z]+/, '');

    // 4. 如果名称为空，使用默认名称
    if (!name) {
      name = 'program';
    }

    return name;
  }

  private static async createSystemdService(program: ProgramRecord, middleLayerPath: string): Promise<string> {
    // 首先检查限
    await ProgramManager.checkSystemdPermissions();

    // 生成安全的服务名称：程序名-记录ID
    const programName = this.sanitizeServiceName(program.name);
    const serviceName = `ethercat_${programName}-${program.id}`;

    // 确保路径是绝对路径并且是可执行文件
    const absoluteMiddleLayerPath = path.resolve(middleLayerPath);
    const absoluteProgramPath = path.resolve(program.filePath);
    const workingDir = path.dirname(absoluteProgramPath);

    // 生成共享内存文件路径
    const shmFileName = `/dev/shm/${program.id}_${program.name}_shm`;

    // 检查文件是否存在
    try {
      const [middleStats, programStats] = await Promise.all([
        fs.stat(absoluteMiddleLayerPath),
        fs.stat(absoluteProgramPath)
      ]);

      if (!middleStats.isFile() || !programStats.isFile()) {
        throw new Error('路径不是文件');
      }
    } catch (error) {
      console.error('Program files not found or invalid:', error);
      throw new ApiError(500, '程序文件不存在或无效');
    }

    // 确保文件有执行权限
    await Promise.all([
      fs.chmod(absoluteMiddleLayerPath, 0o755),
      fs.chmod(absoluteProgramPath, 0o755)
    ]);

    const serviceContent = `[Unit]
Description=${program.name} with middleware service
After=network.target

[Service]
Type=simple
WorkingDirectory=${workingDir}
ExecStart=/bin/bash -c '${absoluteMiddleLayerPath} & \\
while true; do \\
    if [ -f "/tmp/MASTER${program.masterIndex}_ALL_OP" ]; then \\
        echo "ALL OP detected, starting program..."; \\
        ${absoluteProgramPath} ${shmFileName}; \\
        break; \\
    fi; \\
done'
ExecStop=/bin/bash -c 'pkill -INT  "${path.basename(absoluteMiddleLayerPath)}"; pkill -INT  "${path.basename(absoluteProgramPath)}"'
Restart=always
#RestartSec=5

[Install]
WantedBy=multi-user.target
`;

    // 输出服务文件配置内容
    console.log('Service file content:');
    console.log('----------------------------------------');
    console.log('Program paths:');
    console.log(`Middleware: ${absoluteMiddleLayerPath}`);
    console.log(`Program: ${absoluteProgramPath}`);
    console.log(`Working Dir: ${workingDir}`);
    console.log(`Shared Memory File: ${shmFileName}`);
    console.log('\nService Name: ${serviceName}');
    console.log(`Service File Path: /etc/systemd/system/${serviceName}.service`);
    console.log('Service Content:');
    console.log(serviceContent);
    console.log('----------------------------------------');

    // 写入服务文件
    const serviceFilePath = `/etc/systemd/system/${serviceName}.service`;
    try {
      // 写入服务文件
      await fs.writeFile(serviceFilePath, serviceContent);
      await execAsync(`chmod 644 ${serviceFilePath}`);

      // 重新加载systemd配置
      await execAsync('systemctl daemon-reload');

      // 启用并启动服务
      await execAsync(`systemctl enable ${serviceName} --now`);
      //  await execAsync(`systemctl start ${serviceName}`);

      return serviceName;
    } catch (error) {
      console.error('Failed to create systemd service:', error);
      // 清理服务文件
      try {
        await fs.unlink(serviceFilePath);
        await execAsync('systemctl daemon-reload');
      } catch (cleanupError) {
        console.error('Failed to cleanup service file:', cleanupError);
      }
      throw new ApiError(500, '创建systemd服务失败');
    }
  }

  private static async stopSystemdService(serviceName: string): Promise<void> {
    try {
      // await execAsync(`systemctl stop ${serviceName}`);
      await execAsync(`systemctl disable ${serviceName} --now`);
      const serviceFilePath = `/etc/systemd/system/${serviceName}.service`;
      await fs.unlink(serviceFilePath);
      await execAsync('systemctl daemon-reload');
    } catch (error) {
      console.error('Failed to stop systemd service:', error);
      throw new ApiError(500, '停止systemd服务失败');
    }
  }

  static async updateConfig(id: string, config: ProgramConfig) {
    // console.log('updateConfig: config', config);
    try {
      const pb = await PocketBaseManager.ensureAuth();

      // 验证主站索引是否有效
      const availableMasters = await this.getAvailableMasters();
      const masterIndex = parseInt(config.masterIndex?.toString() || '0');
      if (!availableMasters.includes(masterIndex)) {
        throw new ApiError(500, `更新失败：无效的主站索引 ${masterIndex}`);
      }

      // 获取当前程序记录
      const program = await this.getProgram(id);
      if (!program) {
        throw new ApiError(404, '程序不存在，更新配置失败');
      }

      // 更新记录
      const programRecord = await pb.collection('programs').update(id, {
        masterIndex: config.masterIndex?.toString() || program.masterIndex,
        taskFrequency: config.taskFrequency?.toString() || program.taskFrequency,
        debug: config.debug || program.debug,
        config: config.config  // 使用传入的从站配置
      });

      // 创建旧版中间层
      console.log('===== 替换JSON配置，创建旧版中间层 =====');
      try {
        await this.generateOldMiddleware(programRecord);
      } catch (error) {
        logger.error('ProgramManager', `外部替换JSON,创建旧版中间层失败: ${error.message}`);
        // 删除中间层
        const middlewarePath = path.join(path.dirname(programRecord.filePath), 'middleware');
        if (existsSync(middlewarePath)) {
          await fs.rm(middlewarePath, { recursive: true, force: true });
          console.log('旧版中间层删除成功');
        }
      }
      return programRecord;

    } catch (error) {
      console.error('Failed to update config:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '更新配置失败');
    }
  }

  // 生成中间层程序源代码，返回路径（下载中间层源码）
  static async generateTemplate(id: string): Promise<string> {
    try {
      const pb = await PocketBaseManager.ensureAuth();
      const program = await pb.collection('programs').getOne(id);

      // 构造模板配置
      const config = {
        id: program.id,
        name: program.name,
        masterIndex: program.masterIndex,
        taskFrequency: program.taskFrequency,
        ethercatDir: program.ethercatDir,
        slaves: program.config.slaves.map((slave: any) => ({
          name: slave.name,
          index: slave.index,
          vid: slave.vid,
          pid: slave.pid,
          rx_pdo: slave.rx_pdo,
          tx_pdo: slave.tx_pdo,
          rx_pdos: slave.rx_pdos,
          tx_pdos: slave.tx_pdos,
          pdo_mapping: {
            rx_pdos: slave.pdo_mapping?.rx_pdos?.map((pdo: any) => ({
              index: pdo.index,
              entries: pdo.entries,
              entryOffset: pdo.entryOffset
            })) || [],
            tx_pdos: slave.pdo_mapping?.tx_pdos?.map((pdo: any) => ({
              index: pdo.index,
              entries: pdo.entries,
              entryOffset: pdo.entryOffset
            })) || []
          },
          sdos: slave.sdos,
          syncs: slave.syncs,
          dc_config: slave.dc_config
        }))
      };

      // 生成中间层程序源代码
      const sourceCode = await TemplateService.generateCTemplate(config);

      // 创源文件
      const programDir = path.dirname(program.filePath);
      const sourceFile = path.join(programDir, 'middleware.c');
      const outputFile = path.join(programDir, 'middleware');

      // 写入源代码
      await fs.writeFile(sourceFile, sourceCode);

      // 编译源代码
      await execAsync(`gcc -o "${outputFile}" "${sourceFile}" -lethercat -lrt -pthread`);

      // 设置执行权限
      await fs.chmod(outputFile, 0o755);

      // 返回编译后的可执行文件路径
      return outputFile;

    } catch (error) {
      console.error('Failed to generate template:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '生成模板失败');
    }
  }

  // 生成中间层程序源代码，返回代码（启动中间层）
  static async generateMiddlewareTemplate(id: string, pb: PocketBase): Promise<string> {
    try {
      console.log('正在创建模板');

      const calculateTimeoutDuration = (slaves: any[]): number => {
        console.log('slaves的类型:', typeof slaves);
        console.log('slaves的内容:', slaves);
        console.log('计算从站数量:', slaves.length);
        const length = slaves.length;
        
        if (length < 12) {
          return 5; // 少于12个从站，5秒超时
        } else if (length >= 12 && length < 24) {
          return 15; // 12-23个从站，15秒超时
        } else if (length >= 24 && length < 36) {
          return 20; // 24-35个从站，20秒超时
        } else {
          return length; // 36个或更多从站，以从站数量作为秒数
        }
      };

      // 根据id查询程序
      const program = await pb.collection('programs').getOne(id);
      const config = program.config;

      // 创建一个动态超时的Promise
      const createTimeoutPromise = (config: any) => {
        const timeoutSeconds = calculateTimeoutDuration(config.slaves);
        const timeoutMs = timeoutSeconds * 1000;
        
        return new Promise<string>((_, reject) => {
          setTimeout(() => {
            reject(new ApiError(500, `创建模板超时(${timeoutSeconds}s)`));
          }, timeoutMs);
        });
      };


      // 创建一个Promise，包含超时处理
      const templatePromise = new Promise<string>(async (resolve, reject) => {
        try {
          const program = await pb.collection('programs').getOne(id);

          // 构造模板配置
          const config = {
            id: program.id,
            name: program.name,
            masterIndex: program.masterIndex,
            taskFrequency: program.taskFrequency,
            ethercatDir: program.ethercatDir,
            slaves: program.config.slaves.map((slave: any) => ({
              name: slave.name,
              index: slave.index,
              vid: slave.vid,
              pid: slave.pid,
              rx_pdo: slave.rx_pdo,
              tx_pdo: slave.tx_pdo,
              rx_pdos: slave.rx_pdos,
              tx_pdos: slave.tx_pdos,
              pdo_mapping: {
                rx_pdos: slave.pdo_mapping?.rx_pdos?.map((pdo: any) => ({
                  index: pdo.index,
                  entries: pdo.entries,
                  entryOffset: pdo.entryOffset
                })) || [],
                tx_pdos: slave.pdo_mapping?.tx_pdos?.map((pdo: any) => ({
                  index: pdo.index,
                  entries: pdo.entries,
                  entryOffset: pdo.entryOffset
                })) || []
              },
              sdos: slave.sdos,
              syncs: slave.syncs,
              dc_config: slave.dc_config
            }))
          };

          // 生成模板
          const template = await TemplateService.generateCTemplate(config, program.name);
          console.log('在规定时间内，成功C语言获取模板,向上返回模板内容')
          resolve(template);
        } catch (error) {
          reject(error);
        }
      });

      // 创建动态超时Promise
      const timeoutPromise = createTimeoutPromise(config);

      // 使用 Promise.race 竞争，谁先完成就返回谁的结果
      return await Promise.race([templatePromise, timeoutPromise]);

    } catch (error) {
      console.error('Failed to generate template:', error);
      if (error instanceof ApiError) {
        throw error;  // 如果是ApiError，直接往上抛
      }
      throw new ApiError(500, '生成模板失败');
    }
  }

  static async replaceProgram(id: string, program: UploadedFile): Promise<void> {
    try {
      const pb = await PocketBaseManager.ensureAuth();
      const existingProgram = await pb.collection('programs').getOne(id);

      if (!existingProgram) {
        throw new ApiError(404, '程序不存在，替换程序失败');
      }

      const uploadDir = path.join(process.cwd(), 'uploads');
      const programName = existingProgram.name;
      const programDir = path.join(uploadDir, programName);
      const archivePath = path.join(uploadDir, `${programName}_temp.zip`);

      try {
        // 1. 移动上传的压缩包到临时位置
        await program.mv(archivePath);

        // 2. 备份原程目录
        const backupDir = `${programDir}_backup`;
        if (existsSync(programDir)) {
          // 如果备份目录已存在，先删除它
          if (existsSync(backupDir)) {
            await fs.rm(backupDir, { recursive: true, force: true });
            console.log('已删除旧的备份目录');
          }
          // 备份原程序目录
          await fs.rename(programDir, backupDir);
          console.log('已备份原程序目录');
        }

        // 3. 确保完全删除并重新创建程序目录
        try {
          // 强制删除程序目录及其所有内容
          await fs.rm(programDir, { recursive: true, force: true });
          console.log(`已删除程序目录及其所有内容: ${programDir}`);
        } catch (error) {
          console.log('删除程序目录时出错（目录可能不存在）:', error.message);
        }
        // 创建新的空程序目录
        await fs.mkdir(programDir);
        console.log('已创建新的空程序目录');

        try {
          // 4. 解压文件到程序目录
          await decompress(archivePath, programDir);

          // 5. 递归查找主程序文件
          const mainProgramPath = await this.findMainProgram(programDir, programName);

          if (!mainProgramPath) {
            // 如果找不到主程序，清理创建的目录
            await fs.rm(programDir, { recursive: true, force: true });
            throw new ApiError(500, `替换失败：压缩包中不包含名为 ${programName} 的主程序`);
          }
          console.log('找到主程序文件:', mainProgramPath);
          // 检查程序架构类型
          try {
            // 2. 获取系统架构
            const { stdout: systemArch } = await execAsync('uname -m');
            const { stdout: fileInfo } = await execAsync(`file "${mainProgramPath}"`);
            
            console.log('系统架构信息:', systemArch);
            console.log('文件信息:', fileInfo);

            const cleanSystemArch = systemArch.trim().toLowerCase();
            const fileInfoLower = fileInfo.toLowerCase();

            // 架构映射表
            const archMapping: { [key: string]: string[] } = {
              // x86 架构
              'x86_64': ['x86-64', 'x86_64', 'amd64'],
              'i386': ['x86', 'i386', 'i686'],
              // ARM 架构
              'aarch64': ['arm64', 'aarch64', 'arm aarch64'],
              'armv7l': ['arm', 'armv7', 'arm32'],
              'armv6l': ['armv6', 'arm32'],
              // MIPS 架构
              'mips64': ['mips64'],
              'mips': ['mips'],
              // PowerPC 架构
              'ppc64le': ['powerpc64le', 'ppc64le'],
              'ppc64': ['powerpc64', 'ppc64']
            };

            // 3. 检测程序架构
            let programArch = '';
            const detectedArchs: string[] = [];

            // 遍历映射表检查文件信息中包含的架构标识
            for (const [arch, patterns] of Object.entries(archMapping)) {
              if (patterns.some(pattern => fileInfoLower.includes(pattern))) {
                detectedArchs.push(arch);
              }
            }

            // 如果检测到多个架构，记录警告
            if (detectedArchs.length > 1) {
              console.warn(`警告：检测到多个可能的架构: ${detectedArchs.join(', ')}`);
              programArch = detectedArchs[0]; // 使用第一个检测到的架构
            } else if (detectedArchs.length === 1) {
              programArch = detectedArchs[0];
            }

            // 4. 检查是否为动态链接的可执行文件
            if (fileInfoLower.includes('statically linked')) {
              console.log('检测到静态链接的可执行文件');
            } else if (fileInfoLower.includes('dynamically linked')) {
              console.log('检测到动态链接的可执行文件');
            }

            // 5. 架构兼容性检查
            let isCompatible = false;
            
            // 检查系统架构是否在映射表中
            for (const [arch, patterns] of Object.entries(archMapping)) {
              if (patterns.some(pattern => cleanSystemArch.includes(pattern))) {
                isCompatible = (arch === programArch);
                break;
              }
            }

            if (!programArch) {
              throw new ApiError(400, `无法识别程序的架构类型，文件信息：${fileInfo}`);
            }

            if (!isCompatible) {
              const error = new Error(`程序架构(${programArch})与系统架构(${cleanSystemArch})不匹配`);
              (error as any).code = ErrorCode.ARCHITECTURE_MISMATCH;
              throw error;
            }

            console.log(`架构检查通过：程序架构 ${programArch} 与系统架构 ${cleanSystemArch} 兼容`);

          } catch (error) {
            // 清理程序目录
            await fs.rm(programDir, { recursive: true, force: true });
            
            if (error instanceof ApiError) {
              throw error;
            }
            console.error('架构检查失败:', error);
            if (error.message.includes('command not found')) {
              throw new ApiError(500, '系统缺少必要的检查工具(file/uname)，请联系管理员');
            }
            throw new ApiError(500, `程序架构检查失败: ${error.message}`);
          }

          // 给主程序添加执行权限
          await fs.chmod(mainProgramPath, 0o755);

          // 更新数据库中的文件路径
          await pb.collection('programs').update(id, {
            filePath: mainProgramPath
          });

          // 删除备份目录
          if (existsSync(backupDir)) {
            await fs.rm(backupDir, { recursive: true, force: true });
          }

          const programRecord = await pb.collection('programs').update(id, {
            uploadTime: new Date().toISOString(),  // 添加上传时间更新
            filePath: mainProgramPath  // 更新主程序路径
          });
          console.log('===== 替换程序，创建旧版中间层 =====');
          // 创建旧版中间层
          try {
            await this.generateOldMiddleware(programRecord);
          } catch (error) {
            logger.error('ProgramManager', `外部替换JSON,创建旧版中间层失败: ${error.message}`);
            // 删除中间层
            const middlewarePath = path.join(path.dirname(programRecord.filePath), 'middleware');
            if (existsSync(middlewarePath)) {
              await fs.rm(middlewarePath, { recursive: true, force: true });
              console.log('旧版中间层删除成功');
            }
          }

        } catch (error) {
          // 如果替换过程失败，恢复备份
          if (existsSync(backupDir)) {
            // 先删除失败的新目录
            if (existsSync(programDir)) {
              await fs.rm(programDir, { recursive: true, force: true });
            }
            // 恢复备份
            await fs.rename(backupDir, programDir);

          }
          throw error;
        }

        // 删除临时文件
        if (existsSync(archivePath)) {
          await fs.unlink(archivePath);
        }

      } catch (error) {
        // 清理临时文件
        if (existsSync(archivePath)) {
          await fs.unlink(archivePath);
        }
        throw error;
      }

    } catch (error) {
      console.error('Failed to replace program:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '程序替换失败');
    }
  }

  static async createProgram(filename: string, filePath: string, formData: any) {
    try {
      const pb = await PocketBaseManager.ensureAuth();
      console.log("创建数据库记录:", formData.config)

      // 创建记录
      return await pb.collection('programs').create({
        name: filename,
        status: 'stopped',
        uploadTime: new Date().toISOString(),
        masterIndex: formData.masterIndex?.toString() || '0',
        taskFrequency: formData.taskFrequency?.toString() || '1000',
        config: formData.config,  // 直接使用传入的配置对象
        filePath: filePath,
        middlePath: '',  // 初始化中层程序路径
        systemdName: '',  // 初始化systemd服务名称
        startup: '0',  // 添加默认的 startup 值
        programCpu: '2',  // 添加程序 CPU 绑定
        middlewareCpu: '3'  // 添加中间层 CPU 绑定
      });
    } catch (error) {
      console.error('Failed to create program:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '创建程序失败');
    }
  }

  static async getProgram(id: string): Promise<ProgramRecord | null> {
    try {
      const pb = await PocketBaseManager.ensureAuth();
      const record = await pb.collection('programs').getOne(id);
      return {
        id: record.id,
        name: record.name,
        config: record.config,
        filePath: record.filePath,
        middlePath: record.middlePath,
        systemdName: record.systemdName,
        status: record.status,
        masterIndex: record.masterIndex
      } as ProgramRecord;
    } catch (error) {
      console.error('Failed to get program:', error);
      return null;
    }
  }

  static async getProgram_External(id: string, pb: PocketBase): Promise<ProgramRecord | null> {
    try {

      const record = await pb.collection('programs').getOne(id);
      return {
        id: record.id,
        name: record.name,
        config: record.config,
        filePath: record.filePath,
        middlePath: record.middlePath,
        systemdName: record.systemdName,
        status: record.status,
        masterIndex: record.masterIndex
      } as ProgramRecord;
    } catch (error) {
      console.error('Failed to get program:', error);
      return null;
    }
  }

  static getProgramDir(): string {
    return path.join(process.cwd(), 'programs');
  }

  static async downloadFullPackage(programId: string): Promise<Buffer> {
    try {
      // 获取程序信息
      const pb = await PocketBaseManager.ensureAuth();
      const record = await pb.collection('programs').getOne(programId);
      if (!record) {
        throw new ApiError(404, '程序不存在，下载全包失败');
      }

      // 建压缩文件
      const archive = archiver('zip', {
        zlib: { level: 0 }
      });

      // 创建一个 Promise 来处理压缩完成
      const zipBuffer = await new Promise<Buffer>((resolve, reject) => {
        const chunks: Buffer[] = [];
        archive.on('data', (chunk: Buffer) => chunks.push(chunk));
        archive.on('end', () => resolve(Buffer.concat(chunks)));
        archive.on('error', reject);

        // 添加配文件
        const configJson = JSON.stringify(record.config, null, 2);
        archive.append(configJson, { name: 'slave_config.json' });

        // 获取程序目录
        const programPath = path.dirname(record.filePath);

        // 添加程序目录到压缩包
        archive.directory(programPath, false);

        // 加程序文件，使用 filePath
        // archive.file(record.filePath, { name: record.name });

        // 完成打包
        archive.finalize();
      });

      return zipBuffer;
    } catch (error) {
      console.error('Failed to create program package:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '程序包生成失败');
    }
  }

  static async downloadMiddlewareCode(id: string): Promise<Buffer> {
    try {
      const pb = await PocketBaseManager.ensureAuth();
      const program = await pb.collection('programs').getOne(id);

      // 构造模板配置
      const config = {
        id: program.id,
        name: program.name,
        masterIndex: program.masterIndex,
        taskFrequency: program.taskFrequency,
        ethercatDir: program.ethercatDir,
        slaves: program.config.slaves.map((slave: any) => ({
          name: slave.name,
          index: slave.index,
          vid: slave.vid,
          pid: slave.pid,
          rx_pdo: slave.rx_pdo,
          tx_pdo: slave.tx_pdo,
          rx_pdos: slave.rx_pdos,
          tx_pdos: slave.tx_pdos,
          pdo_mapping: {
            rx_pdos: slave.pdo_mapping?.rx_pdos?.map((pdo: any) => ({
              index: pdo.index,
              entries: pdo.entries,
              entryOffset: pdo.entryOffset
            })) || [],
            tx_pdos: slave.pdo_mapping?.tx_pdos?.map((pdo: any) => ({
              index: pdo.index,
              entries: pdo.entries,
              entryOffset: pdo.entryOffset
            })) || []
          },
          sdos: slave.sdos,
          syncs: slave.syncs,
          dc_config: slave.dc_config
        }))
      };

      // 直接生成模板代码，跳过从站数量校验
      const sourceCode = await TemplateService.generateCTemplateWithoutValidation(config);

      // 返回源代码的 Buffer
      return Buffer.from(sourceCode, 'utf-8');

    } catch (error) {
      console.error('Failed to generate middleware code:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '生成中间层代码失败');
    }
  }

  static async getProgramLogs(id: string): Promise<string[]> {
    try {
      const pb = await PocketBaseManager.ensureAuth();
      const program = await this.getProgram(id);

      if (!program || !program.systemdName) {
        console.log(`program: ${!program}, program.systemdName: ${!program.systemdName}`)
        throw new ApiError(404, '程序不存在，获取程序日志失败');
      }

      if(program.debug === '1'){
        throw new ApiError(508, '请先打开调试模式，再获取日志');
      }
      if (program.status !== 'running') {
        throw new ApiError(509, '程序未运行');
      }

      // 获取systemd务的日志
      // let preId = program.id.substring(0, 6)
      // let systemdName = "ethercat_program_" + program.name + "-" + preId + ".service"
      const systemdName = `ethercat_program@${program.name}.service`
      console.log("current systemdName:", systemdName)
      const { stdout } = await execAsync(`journalctl -u ${systemdName} -n 100 --no-pager`);
      // const { stdout } = await execAsync(`journalctl -u ${program.systemdName} -n 100 --no-pager`);

      // 将日志按行分割并返回
      return stdout.split('\n').filter(line => line.trim() !== '');
    } catch (error) {
      console.error('Failed to get program logs:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '取日志失败');
    }
  }

  static async getPlatformLogs(): Promise<string> {
    try {
      console.log('Fetching platform logs...');
      const { stdout } = await execAsync('journalctl -u ethercat-web-ui.service -n 100 --no-pager');
      console.log('Got platform logs, length:', stdout.length);
      return stdout;
    } catch (error) {
      console.error('Failed to get platform logs:', error);
      throw new ApiError(500, '获取平台日志失败');
    }
  }

  static async restartPlatform(): Promise<void> {
    try {
      console.log('Restarting platform...');
      await execAsync('systemctl restart ethercat-web-ui.service');
    } catch (error) {
      console.error('Failed to restart platform:', error);
      throw new ApiError(500, '重启平台失败');
    }
  }

  static async handlePowerOutageRecovery() {
    console.log('=== Power Outage Recovery ===');
    console.log('The current function is not yet enabled');
    
  }


  static async readFile(filePath: string): Promise<string> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      console.log('文件路径:', filePath);
      console.log('文件内容:');
      console.log(content);
      return content;
    } catch (error) {
      console.error(`读取文件失败: ${error.message}`);
      throw new ApiError(500, `读取文件失败: ${error.message}`);
    }
  }

  // 校验指定主站下从站是否全OP
  static async areAllSlavesOP(json: any, masterIndex: number): Promise<{ allOP: boolean, opCount: number, totalCount: number }> {
    // console.log('areAllSlavesOP json:', json);
    if (!json || !Array.isArray(json.slaves)) {
      return { allOP: false, opCount: 0, totalCount: 0 };
    }

    const slaves = json.slaves.filter(slave => slave.master === masterIndex);
    const totalCount = slaves.length;
    const opCount = slaves.filter(slave => slave.state === 'OP').length;
    const allOP = totalCount > 0 && opCount === totalCount;

    return { allOP, opCount, totalCount };
  }

  // 启动中间层程序
  static async startMiddleware(programId: string, sdk = false): Promise<string> {
    console.log('=====  startMiddleware sdk:', sdk);
    logger.info('ProgramManager', `startMiddleware sdk: ${sdk}`);
    try {
      const pb = new PocketBase(config.pocketbaseUrl);
      const program = await pb.collection('programs').getOne(programId);

      if (!program) {
        throw new ApiError(404, '程序不存在，启动中间层失败');
      }

      logger.info('ProgramManager', `Starting middleware ProgramName: ${program.name}`);

      // 检查中间层是否存在，存在则停止
      const middlewareServiceName = `ethercat_middleware_${this.sanitizeServiceName(program.name)}-${program.id.substring(0, 6)}`;
      const middlewareStatus = await this.getServiceStatus(middlewareServiceName);
      if (middlewareStatus === 'active') {
        console.log('中间层程序已存在，停止中间层程序')
        await this.stopMiddleware(programId);
      }

      console.log('中间层程序已停止, 正在启动中间层程序')

      if (!program) {
        throw new ApiError(404, '程序不存在，开启中间层失败');
      }

      // 获取从站数量
      const slaveCount = program.config.slaves?.length || 0;
      if (slaveCount === 0) {
        throw new ApiError(500, '从站配置为空');
      }

      console.log(`program.name: ${program.name}`)
      try {
        // 使用 TemplateService 的验证方法
        const validation = await TemplateService.validateSlaves({
          masterIndex: program.masterIndex,
          taskFrequency: program.taskFrequency,
          ethercatDir: program.ethercatDir,
          slaves: program.config.slaves
        }, program.name);

        if (!validation.isValid) {
          throw new ApiError(
            ErrorCode.SLAVE_VID_PID_MISMATCH,
            `从站配置验证失败:\n${validation.errors.join('\n')}`
          );
        }
      } catch (error) {
        if (error instanceof ApiError) {
          throw error;
        }
        throw new ApiError(500, '从站验证失败');
      }

      // 计算等待时间
      let maxWaitTime: number;
      if (slaveCount < 10) {
        maxWaitTime = 20000; 
      } else if (slaveCount < 20) {
        maxWaitTime = 40000; 
      } else if (slaveCount < 30) {
        maxWaitTime = 60000; 
      } else {
        maxWaitTime = slaveCount * 2000; 
        maxWaitTime += 10000;
      }
      
      console.log(`Calculated wait time for ${slaveCount} slaves: ${maxWaitTime}ms`);

      if(!sdk){
        // ================ 旧版创建c模板 =====================
        console.log('=====  进入旧版创建c模板分支 =====');
        // 判断中间层程序middleware是否存在，不存在则创建
        const programDir = path.dirname(program.filePath);
        const outputFile = path.join(programDir, 'middleware');
        console.log('=====  旧版中间层路径:', outputFile);
        if(!existsSync(outputFile)){
          console.log('=====  中间层程序不存在，创建旧版中间层 =====');
          await this.generateOldMiddleware(program);
        } else {
          console.log('=====  旧版中间层存在，跳过创建 =====');
        }
      } else {
        console.log('=====  进入sdk分支 =====');
        // 调用代码生成器生成 c 和 h 文件，存放到 当前目录下的sdk/${program.name}/下
        const sdkDir = path.join(process.cwd(), 'sdk', program.name);
        const templateDir = path.join(process.cwd(), 'template');
        const programDir = path.dirname(program.filePath);

        console.log('=====  sdkDir:', sdkDir);
        console.log('=====  templateDir:', templateDir);
        console.log('=====  programDir:', programDir);
        
        // 确保目录存在
        await fs.mkdir(sdkDir, { recursive: true });
        await fs.mkdir(templateDir, { recursive: true });

        // 生成SDK代码
        const sdkFiles = await SDKGeneratorService.generateSDKFiles({
          id: program.id,
          name: program.name,
          masterIndex: parseInt(program.masterIndex),
          taskFrequency: parseInt(program.taskFrequency),
          slaves: program.config.slaves
        });

        const sdkHPath = path.join(sdkDir, 'sdk.h');
        const sdkHLinkPath = path.join(sdkDir, `${program.id}.h`);
        // 写入生成的文件
        await fs.writeFile(path.join(sdkDir, 'sdk.c'), sdkFiles.cCode);
        await fs.writeFile(sdkHPath, sdkFiles.hCode);

        // 制作软链接，sdk.h -> programId.h
        // await this.checkAndRemoveFile(sdkHLinkPath, `sdk.h软链接：${sdkHLinkPath}`);
        // await fs.symlink(sdkHPath, sdkHLinkPath);

        // 定义文件路径
        const sdkLibPath = path.join(sdkDir, 'libsdk.so');
        const sdkMiddlewarePath = path.join(sdkDir, 'middleware');
        const programLibPath = path.join(programDir, 'libsdk.so');
        const programMiddlewarePath = path.join(programDir, 'middleware');

        try {
          // 检查并删除旧文件
          await this.checkAndRemoveFile(programLibPath, 'SDK库');
          await this.checkAndRemoveFile(programMiddlewarePath, '中间件程序');
          await this.checkAndRemoveFile(sdkLibPath, 'SDK库');
          await this.checkAndRemoveFile(sdkMiddlewarePath, '中间件程序');
        } catch (error) {
          console.error('删除旧文件失败:', error);
          throw new ApiError(500, '删除旧文件失败');
        }

        // 编译SDK库到SDK目录
        console.log('Compiling SDK library...');
        try {
          const sdkCPath = path.join(sdkDir, 'sdk.c');
          await execAsync(`gcc -fPIC -shared -fvisibility=hidden ${sdkCPath} -o ${sdkLibPath} -I${sdkDir} -lethercat -lpthread -lrt`);
          console.log(`SDK库编译完成: ${sdkLibPath}`);
        } catch (error) {
          console.error('SDK compilation failed:', error);
          throw new ApiError(500, 'SDK编译失败');
        }

        // 编译中间件程序到SDK目录
        const middlewareHostCPath = path.join(templateDir, 'middleware_host.c');
        console.log('Compiling middleware host...');
        try {
          await execAsync(`gcc ${middlewareHostCPath} -o ${sdkMiddlewarePath} -I${sdkDir} -L${sdkDir} -lsdk -lpthread`);
          console.log(`中间件程序编译完成: ${sdkMiddlewarePath}`);
          // 设置执行权限
          await fs.chmod(sdkMiddlewarePath, 0o755);
          console.log(`设置中间件程序执行权限成功`);
        } catch (error) {
          console.error('Middleware compilation failed:', error);
          throw new ApiError(500, '中间件程序编译失败');
        }

        // 创建软链接到程序目录
        try {
          await fs.symlink(sdkLibPath, programLibPath);
          console.log(`创建SDK库软链接成功: ${programLibPath}`);
          await fs.symlink(sdkMiddlewarePath, programMiddlewarePath);
          console.log(`创建中间件程序软链接成功: ${programMiddlewarePath}`);
        } catch (error) {
          console.error('创建软链接失败:', error);
          throw new ApiError(500, '创建软链接失败');
        }

        // 将当前程序的SDK库软链接到所有其他程序目录
        try {
          console.log('开始将SDK库软链接到其他程序目录...');

          // 获取所有程序列表
          const allPrograms = await pb.collection('programs').getList(1, 1000, {
            sort: '-uploadTime'
          });

          // 遍历所有程序，排除当前程序
          for (const otherProgram of allPrograms.items) {
            if (otherProgram.id === programId) {
              continue; // 跳过当前程序
            }

            // 获取其他程序的目录路径
            const otherProgramDir = path.dirname(otherProgram.filePath);
            const targetLinkPath = path.join(otherProgramDir, `lib${program.name}.so`);

            try {
              // 检查并删除已存在的软链接或文件
              await this.checkAndRemoveFile(targetLinkPath, `程序${otherProgram.name}目录下的lib${program.name}.so`);

              // 创建软链接
              await fs.symlink(sdkLibPath, targetLinkPath);
              console.log(`创建跨程序SDK库软链接成功: ${targetLinkPath} -> ${sdkLibPath}`);
            } catch (linkError) {
              console.error(`为程序 ${otherProgram.name} 创建软链接失败:`, linkError);
              // 不抛出错误，继续处理其他程序
            }
          }

          console.log('完成将SDK库软链接到其他程序目录');
        } catch (error) {
          console.error('将SDK库软链接到其他程序目录失败:', error);
          // 这里不抛出错误，因为这不应该影响当前程序的启动
        }
      }

      // 生成共享内存文件路径（与旧版保持一致）
      const shmFile = `/dev/shm/${programId}_${program.name}_shm`;

      // 6. 创并启动systemd服务
      try {
        console.log('创建并启动systemd服务')
        // 先检查权限
        await this.checkSystemdPermissions();

        // 生成安全的服务名称
        // const serviceName = `ethercat_middleware_${this.sanitizeServiceName(program.name)}-${shortId}`;
        const serviceName = `ethercat_middleware@${program.name}`;
        // 获取middlewareCpu
        const middlewareCpu = program.middlewareCpu || '3';
        console.log('middlewareCpu', middlewareCpu);

        // console.log("=====  startMiddleware =====", program.name);
        // 启动服务
        await this.enableServiceWithEnv(serviceName, 'middleware', program, true, false, '1');

        // 等待当前主站下所有从站进入OP
        const startTime = Date.now();
        const expectedSlaveCount = program.config.slaves?.length || 0;
        
        while(Date.now() - startTime < maxWaitTime) {
          const slaves = await EthercatService.getAllSlavesStatus(1);
          const result = (await this.areAllSlavesOP(slaves, program.masterIndex));
          
          // 只检查配置中指定数量的从站
          if (result.opCount >= expectedSlaveCount) {
            const elapsedTime = Date.now() - startTime;
            // console.log(`配置的${expectedSlaveCount}个从站已进入OP状态，用时: ${elapsedTime / 1000}s`);
            logger.info('ProgramManager', `配置的${expectedSlaveCount}个从站已进入OP状态，用时: ${elapsedTime / 1000}s`);
            return shmFile;
          } else {
            console.log(`当前程序：${program.name}，等待从站进入OP状态，期望从站数量: ${expectedSlaveCount}, 当前OP状态从站数量: ${result.opCount}`);
          }

          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // 如果超时，清理服务并抛出错误
        // await this.stopSystemdService(serviceName);
        await execAsync(`systemctl stop ${serviceName}`);
        // const elapsedTime = Date.now() - startTime;
        throw new Error(`当前程序：${program.name}，等待从站进入 OP 状态超时(${maxWaitTime / 1000}s)，请检查从站JSON配置`);

      } catch (error) {
        console.error('Failed to start middleware service:', error);
        throw new ApiError(500, error.message || '启动中间层服务失败');
      }


    } catch (error) {
      console.error('Failed to start middleware:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '启动中间层程序失败');
    }
  }

  static async getServiceStatus(serviceName: string): Promise<'active' | 'inactive' | 'failed'> {
    try {
      const { stdout } = await execAsync(`systemctl is-active ${serviceName}`);
      const status = stdout.trim();
      if (status === 'active') return 'active';
      if (status === 'failed') return 'failed';
      return 'inactive';
    } catch (error) {
      return 'inactive';
    }
  }

  static async stopMiddleware(id: string): Promise<void> {
    try {
      // 直接调用 PocketBase，不需要认
      const pb = new PocketBase(config.pocketbaseUrl);
      const program = await pb.collection('programs').getOne(id);

      if (!program) {
        throw new ApiError(404, '程序不存在，停止中间层失败');
      }

      logger.info('ProgramManager', `Stopping middleware ProgramName: ${program.name}`);

      // const shortId = id.substring(0, 6);
      // const serviceName = `ethercat_middleware_${this.sanitizeServiceName(program.name)}-${shortId}`;
      const serviceName = `ethercat_middleware@${program.name}`;
      try {
        // 先检查服务是否存在和运行
        const isActive = await this.getServiceStatus(serviceName);

        if (isActive !== 'inactive') {
          try {
            // 停止并禁用服务
            await execAsync(`systemctl disable ${serviceName} --now`);
          } catch (error) {
            // 忽略停止服务时的错误，因为服务可能已经停止
            console.log('Service stop command returned error (might be already stopped):', error);
          }
        }

        // // 删除服务文件（如果存在）
        // const serviceFilePath = `/etc/systemd/system/${serviceName}.service`;
        // try {
        //   await fs.unlink(serviceFilePath);
        // } catch (error) {
        //   // 忽略文件不存在的错误
        //   if (error.code !== 'ENOENT') {
        //     throw error;
        //   }
        // }

        // // 重新加载systemd配置
        // await execAsync('systemctl daemon-reload');

        // 除 ALL_OP 文件（如果存在）
        const opFile = `/tmp/MASTER${program.masterIndex}_ALL_OP`;
        try {
          if (existsSync(opFile)) {
            await fs.unlink(opFile);
            console.log('ALL_OP file deleted');
          }
        } catch (error) {
          // 忽略文件删除错误
          console.log('Failed to delete ALL_OP file:', error);
        }

      } catch (error) {
        console.error('Failed to stop middleware service:', error);
        // 只有在服务文件删除或重载 systemd 失败时才抛出错误
        if (error.code !== 'ENOENT' && !error.cmd?.includes('systemctl stop')) {
          throw new ApiError(500, '停止中间层服务失败');
        }
      }

    } catch (error) {
      console.error('Failed to stop middleware:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '停止中间层程序失败');
    }
  }

  static async getEnvVariables(program: RecordModel, filePath: string, variables: string[]): Promise<{ [key: string]: string }> {
    try {
      if (!existsSync(filePath)) {
        console.log(`env文件不存在: ${filePath}, 创建env文件`);
        await this.createEnvFile(program, '0', '0');
      }

      // 读取env文件内容
      const content = await fs.readFile(filePath, 'utf-8');

      // 创建结果对象
      const result: { [key: string]: string } = {};

      // 按行分割
      const lines = content.split('\n');

      // 遍历每一行
      for (const line of lines) {
        // 跳过空行和注释
        if (!line || line.startsWith('#')) continue;

        // 分割键值对
        const [key, value] = line.split('=').map(item => item.trim());

        // 如果是要查找的变量，添加到结果中
        if (variables.includes(key)) {
          result[key] = value;
        }
      }

      return result;
    } catch (error) {
      console.log(`读取env文件失败: ${error.message}`);
      // throw new ApiError(500, `读取env文件失败: ${error.message}`);
    }
  }

  static async updateStartup(id: string, startup: string, masterIndex: string): Promise<void> {
      const transaction = new Transaction();

    try {
      await transaction.begin();
      
      const pb = await PocketBaseManager.ensureAuth();

      // 获取当前程序记录
      const program = await pb.collection('programs').getOne(id);
      if (!program) {
        throw new ApiError(404, '未找到程序');
      }

      // 添加事务操作
      transaction.addOperation(async () => {
        // 检查是否已有其他程序在同一主站设置为自启动
        if (startup === '1') {
          console.log(`检查主站 ${masterIndex} 上是否已有其他程序自启动`);
          const existingStartup = await pb.collection('programs').getList(1, 1, {
            filter: `masterIndex = "${masterIndex}" && startup = "1" && id != "${id}"`
          });
          
          if (existingStartup.items.length > 0) {
            console.log(`主站 ${masterIndex} 上已存在其他程序自启动: ${existingStartup.items[0].name}`);
            // 如果找到了其他自启动程序，先取消它们的自启动
            await pb.collection('programs').update(existingStartup.items[0].id, {
                    startup: '0'
                  });
            const envFilePath = `/app/ethercat-web-ui/backend/env/${existingStartup.items[0].name}.env`;
            await this.updateEnvFile(envFilePath, 'FORCE_ENABLE', '0');
            console.log(`已更新主站 ${masterIndex} 上其他程序的环境文件`);
            // 删除原有程序的环境文件
            // await this.deleteEnvFile(existingStartup.items[0].name);
            // console.log(`已删除主站 ${masterIndex} 上其他程序的环境文件`);
          }
        }

        // 更新当前程序的自启动状态
        console.log(`更新${program.name}的自启动状态为${startup}`);
              await pb.collection('programs').update(id, {
          startup,
          masterIndex  // 同时更新主站索引
        });

        // 处理环境文件
        if (startup === '1') {
          console.log(`创建程序env文件 ${program.name}`);
          const newProgram = await pb.collection('programs').getOne(id);
          // 判断程序是否正在运行
          const isRunning = await this.getServiceStatus(`ethercat_program@${newProgram.name}`);
          if (isRunning === 'active') {
            await this.createEnvFile(newProgram, '1');
          }
          else{
            await this.createEnvFile(newProgram);
          }
        } else {
          // console.log(`删除程序env文件 ${program.name}`);
          // await this.deleteEnvFile(program.name);
          const envFilePath = `/app/ethercat-web-ui/backend/env/${program.name}.env`;
          await this.updateEnvFile(envFilePath, 'FORCE_ENABLE', '0');
          console.log(`已更新程序env文件 ${program.name}`);
        }
      });

      await transaction.commit();
      console.log(`Successfully updated startup status for program ${program.name}`);

    } catch (error: any) {
      console.error('Failed to update startup status:', error);
      await transaction.rollback();
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError(500, `更新自启动状态失败: ${error.message}`);
    }
  }

  // 添加检查服务是否存在的辅助方法
  private static async checkServiceExists(serviceName: string): Promise<boolean> {
    try {
      await execAsync(`systemctl status ${serviceName}`);
      return true;
    } catch (error) {
      return false;
    }
  }

  // 添加新的检测方法
  private static async checkServiceFileExists(serviceName: string): Promise<boolean> {
    try {
      const serviceFilePath = `/etc/systemd/system/${serviceName}.service`;
      await fs.access(serviceFilePath);
      console.log(`Service file exists for ${serviceName}`);
      return true;
    } catch (error) {
      if (error.code === 'ENOENT') {
        console.log(`Service file does not exist for ${serviceName}`);
      } else {
        console.error(`Error checking service file for ${serviceName}:`, error);
      }
      return false;
    }
  }

  static async startProgram_external(id: string): Promise<void> {
    try {
      const pb = new PocketBase(config.pocketbaseUrl);
      const program = await pb.collection('programs').getOne(id);

      if (!program) {
        throw new ApiError(404, '程序不存在，开启程序失败');
      }
      // const currentTime = new Date().toISOString();
      // logger.info('ProgramManager', `外部程序启动程序: ${program.name}, 时间：${currentTime}`);

      // 获取实际从站配置
      const slaveConfig = await EthercatService.getSlaveConfig();
      const actualSlaves = slaveConfig.slaves || [];  // 获取实际从站数组
      // 1. 检查从站数量是否匹配
      const configuredSlaves = program.config.slaves || [];
      if (actualSlaves.length < configuredSlaves.length) {
        throw new ApiError(
          ErrorCode.SLAVE_COUNT_MISMATCH,
          `从站数量不匹配：配置数量 ${configuredSlaves.length}，实际数量 ${actualSlaves.length}`
        );
      }

      // 2. 检查每个从站的VID/PID是否匹配
      for (const configuredSlave of configuredSlaves) {
        // 在实际从站中查找匹配的VID/PID
        const matchingSlave = actualSlaves.find((actual: ActualSlave) => {
          const actualVid = actual.vid.toLowerCase().replace('0x', '');
          const actualPid = actual.pid.toLowerCase().replace('0x', '');
          const configVid = configuredSlave.vid.toLowerCase().replace('0x', '');
          const configPid = configuredSlave.pid.toLowerCase().replace('0x', '');
          return actualVid === configVid && actualPid === configPid;
        });

        if (!matchingSlave) {
          throw new ApiError(
            ErrorCode.SLAVE_VID_PID_MISMATCH,
            `从站VID/PID不匹配：未找到配置的从站(VID: ${configuredSlave.vid}, PID: ${configuredSlave.pid})`
          );
        }
      }

      // 修改数据库status字段
      await pb.collection('programs').update(id, {
        status: 'running'
      });

      // const shortId = id.substring(0, 6);
      // const serviceName = `ethercat_program_${this.sanitizeServiceName(program.name)}-${shortId}`;
      const serviceName = `ethercat_program@${program.name}`;


      try {

        // 执行中断绑定
        if (program.middlewareCpu != '-1') {
          await this.bindIrqToCpu(program.middlewareCpu || '3');
        } else {
          console.log("跳过中间层中断绑定，program.middlewareCpu 为 -1");
        }

        // 先检查权限
        await this.checkSystemdPermissions();

        // const middlewareServiceName = `ethercat_middleware_${this.sanitizeServiceName(program.name)}-${shortId}`;
        // console.log('program middlewareServiceName', middlewareServiceName);

        // 获取programCpu
        // const programCpu = program.programCpu || '2';

        await this.enableServiceWithEnv(serviceName, 'program', program, true, false, '1');


        // 等待服务启动完成
        let retries = 5;
        while (retries > 0) {
          try {
            const { stdout } = await execAsync(`systemctl is-active ${serviceName}`);
            if (stdout.trim() === 'active') {
              return;
            }
          } catch (error) {
            console.log(`Service not active yet, retries left: ${retries}`);
          }
          await new Promise(resolve => setTimeout(resolve, 1000));
          retries--;
        }

        // 如果服务没有成功启动，抛出错误
        const { stdout: status } = await execAsync(`systemctl status ${serviceName}`);
        throw new Error(`服务启动失败: ${status}`);

      } catch (error) {
        console.error('Failed to start program service:', error);

        // 修改状态
        await pb.collection('programs').update(id, {
          status: 'stopped'
        });

        // 清理服务文件
        // const serviceFilePath = `/etc/systemd/system/${serviceName}.service`;
        // if (existsSync(serviceFilePath)) {
        //   await fs.unlink(serviceFilePath).catch(console.error);
        //   await execAsync('systemctl daemon-reload').catch(console.error);
        // }

        throw new ApiError(500, error.message || '启动程序服务失败');
      }

    } catch (error) {
      console.error('Failed to start program:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '启动程序失败');
    }
  }

  static async stopProgram_external(id: string): Promise<void> {
    try {
      const pb = new PocketBase(config.pocketbaseUrl);
      const program = await pb.collection('programs').getOne(id);

      if (!program) {
        throw new ApiError(404, '程序不存在，停止程序失败');
      }

      logger.info('ProgramManager', `Stopping program with API ProgramName: ${program.name}`);
      // const shortId = id.substring(0, 6);
      // const serviceName = `ethercat_program_${this.sanitizeServiceName(program.name)}-${shortId}`;
      const serviceName = `ethercat_program@${program.name}`;

      try {
        // 先检查服务是否存在和运行
        const isActive = await this.getServiceStatus(serviceName);

        if (isActive !== 'inactive') {
          try {
            // 停止并禁用服务
            await execAsync(`systemctl stop ${serviceName}`);
            // 修改 /app/ethercat-web-ui/backend/env/${program.name}.env 文件中的 AC_LOSS_RECOVER 为 0
            const envFilePath = `/app/ethercat-web-ui/backend/env/${program.name}.env`;
            // 文件不存在则创建
            try {
              await fs.access(envFilePath);
            } catch {
              await this.createEnvFile(program, '0', program.startup)
            }
            await this.updateEnvVariable(envFilePath, 'AC_LOSS_RECOVER', '0');
          } catch (error) {
            // 忽略停止服务时的错误，因为服务可能已经停止
            console.log('Service stop command returned error (might be already stopped):', error);
          }
        }

      } catch (error) {
        console.error('Failed to stop program service:', error);
        // 只有在服务文件删除或重载 systemd 失败时才抛出错误
        if (error.code !== 'ENOENT' && !error.cmd?.includes('systemctl stop')) {
          throw new ApiError(500, '停止程序服务失败');
        }
      }

      // 修改状态
      await pb.collection('programs').update(id, {
        status: 'stopped'
      });

    } catch (error) {
      console.error('Failed to stop program:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '停止程序失败');
    }
  }

  // 在 ProgramManager 类中添加新方法
  static async replaceConfig_external(id: string, configFile: UploadedFile): Promise<void> {
    try {
      const pb = new PocketBase(config.pocketbaseUrl);
      const program = await pb.collection('programs').getOne(id);

      if (!program) {
        throw new ApiError(404, '程序不存在，更新配置失败');
      }

      // 解析配置文件
      let configData;
      try {
        const configStr = configFile.data.toString('utf-8');
        configData = JSON.parse(configStr);
      } catch (error) {
        throw new ApiError(400, '配置文件格式错误');
      }

      // 验证主站索引是否有效
      const availableMasters = await this.getAvailableMasters();
      const masterIndex = parseInt(configData.masterIndex?.toString() || program.masterIndex);
      if (!availableMasters.includes(masterIndex)) {
        throw new ApiError(500, `更新失败：无效的主站索引 ${masterIndex}`);
      }

      // 检查程序是否正在运行
      const shortId = id.substring(0, 6);
      const middlewareServiceName = `ethercat_middleware_${this.sanitizeServiceName(program.name)}-${shortId}`;
      const programServiceName = `ethercat_program_${this.sanitizeServiceName(program.name)}-${shortId}`;

      const [middlewareStatus, programStatus] = await Promise.all([
        this.getServiceStatus(middlewareServiceName),
        this.getServiceStatus(programServiceName)
      ]);

      if (middlewareStatus === 'active' || programStatus === 'active') {
        throw new ApiError(400, '无法更新正在运行的程序配置，请先停止程序');
      }

      // 更新数据库记录
      const programRecord = await pb.collection('programs').update(id, {
        masterIndex: configData.masterIndex?.toString() || program.masterIndex,
        taskFrequency: configData.taskFrequency?.toString() || program.taskFrequency,
        config: configData.config || configData  // 支持完整配置对象或仅从站配置
      });

      // 创建旧版中间层
      console.log('===== 外部替换JSON配置，创建旧版中间层 =====');
      try {
        await this.generateOldMiddleware(programRecord);
      } catch (error) {
        logger.error('ProgramManager', `外部替换JSON,创建旧版中间层失败: ${error.message}`);
        // 删除中间层
        const middlewarePath = path.join(path.dirname(programRecord.filePath), 'middleware');
        if (existsSync(middlewarePath)) {
          await fs.rm(middlewarePath, { recursive: true, force: true });
          console.log('旧版中间层删除成功');
        }
      }
      

    } catch (error) {
      console.error('Failed to replace config:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '更新配置失败');
    }
  }

  static async updateCpuBinding(id: string, programCpu: string, middlewareCpu: string): Promise<void> {
    try {
      // 更新数据库记录
      console.log("更新CPU绑定:", programCpu, middlewareCpu);

      const pb = await PocketBaseManager.ensureAuth();
      const program = await pb.collection('programs').getOne(id);

      if (!program) {
        throw new ApiError(404, '程序不存在');
      }

      // 更新数据库中的 CPU 绑定字段
      await pb.collection('programs').update(id, {
        programCpu,
        middlewareCpu
      });

    } catch (error) {
      console.error('Failed to update CPU binding:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, '更新CPU绑定失败');
    }
  }

  static async getSystemInfo(): Promise<SystemInfo> {
    try {
      const platform = os.platform();
      const baseInfo = {
        cpuCount: os.cpus().length,
        cpuModel: os.cpus()[0]?.model || 'Unknown',
        totalMemory: Math.round(os.totalmem() / (1024 * 1024 * 1024)),
        freeMemory: Math.round(os.freemem() / (1024 * 1024 * 1024)),
        platform,
        arch: os.arch(),
        hostname: os.hostname()
      };

      // Linux系统获取更详细的CPU信息
      if (platform === 'linux') {
        try {
          // 使用 execAsync 而不是 exec
          const { stdout: cpuinfo } = await execAsync('cat /proc/cpuinfo');
          const processors: Array<{
            processor: number;
            model: string;
            mhz: number;
            cacheSize: string;
          }> = [];

          // 解析 cpuinfo
          const processorBlocks = cpuinfo.split('\n\n');
          for (const block of processorBlocks) {
            if (!block.trim()) continue;

            const lines = block.split('\n');
            const processor: any = {};

            for (const line of lines) {
              const [key, value] = line.split(':').map(s => s.trim());
              switch (key) {
                case 'processor':
                  processor.processor = parseInt(value);
                  break;
                case 'model name':
                  processor.model = value;
                  break;
                case 'cpu MHz':
                  processor.mhz = parseFloat(value);
                  break;
                case 'cache size':
                  processor.cacheSize = value;
                  break;
              }
            }

            if (Object.keys(processor).length > 0) {
              processors.push(processor);
            }
          }

          // 添加Linux特有的CPU详细信息
          return {
            ...baseInfo,
            cpuDetails: { processors }
          };
        } catch (error) {
          console.warn('Failed to get detailed CPU info:', error);
          return baseInfo;
        }
      }

      // Windows或其他系统返回基本信息
      return baseInfo;

    } catch (error) {
      console.error('Failed to get system info:', error);
      throw new ApiError(500, '获取系统信息失败');
    }
  }

  // 获取程序的 CPU 绑定信息
  static async getCpuBinding(id: string): Promise<{ programCpu: string; middlewareCpu: string }> {
    const pb = await PocketBaseManager.getInstance();
    const program = await pb.collection('programs').getOne(id);

    return {
      programCpu: program.programCpu || '2',
      middlewareCpu: program.middlewareCpu || '3'
    };
  }

  static async createEnvFile(program: RecordModel, recover = "0", forceEnable = "0"): Promise<void> {
    console.log("=====  create env file =====", program.name, recover, forceEnable);
    try {
      // 创建环境文件目录
      const envDir = '/app/ethercat-web-ui/backend/env';
      await fs.mkdir(envDir, { recursive: true });

      // 获取程序的 CPU 绑定信息
      const { programCpu, middlewareCpu } = await this.getCpuBinding(program.id);

      // 确保路径是绝对路径
      const absoluteProgramPath = path.resolve(program.filePath);
      const workingDir = path.dirname(absoluteProgramPath);

      const sdkPath = `/app/ethercat-web-ui/backend/sdk/${program.name}`;
      console.log(`数据库字段startup：${program.startup}`);
      // 创建环境文件内容
      const envContent = `FULL_PATH=${workingDir}
PROGRAM_NAME=${program.name}
MIDDLEWARECORE=${middlewareCpu}
AC_LOSS_RECOVER=${recover}
FORCE_ENABLE=${program.startup}
PROGRAMCORE=${programCpu}
LD_LIBRARY_PATH=${sdkPath}
DEBUG=${program.debug}`;

      // 写入环境文件
      const envFilePath = path.join(envDir, `${program.name}.env`);
      await fs.writeFile(envFilePath, envContent, 'utf-8');

      // 添加644权限
      await fs.chmod(envFilePath, 0o644);
      // 检查并创建 systemd 服务文件
      await this.ensureSystemdServices(program);


      console.log(`Environment file created at: ${envFilePath}`);
    } catch (error) {
      console.error('Failed to create environment file:', error);
      throw new ApiError(500, '创建环境文件失败');
    }
  }

  private static async ensureSystemdServices(program?: RecordModel): Promise<void> {
    try {
      // 检查并创建 program 服务文件
      const programServicePath = '/etc/systemd/system/ethercat_program@.service';
      if (!existsSync(programServicePath)) {
        const programServiceContent = TemplateService.generateProgramServiceScriptWithEnv();
        await fs.writeFile(programServicePath, programServiceContent, 'utf-8');
        // await fs.chmod(programServicePath, 0o644);
        console.log('Created program service template');
        // 重新加载 systemd 配置
        // await execAsync('systemctl daemon-reload');
        console.log('Systemd configuration reloaded');
      }

      // 检查并创建 middleware 服务文件
      const middlewareServicePath = '/etc/systemd/system/ethercat_middleware@.service';
      if (!existsSync(middlewareServicePath)) {
        const middlewareServiceContent = TemplateService.generateMiddlewareServiceScriptWithEnv();
        await fs.writeFile(middlewareServicePath, middlewareServiceContent, 'utf-8');
        // await fs.chmod(middlewareServicePath, 0o644);
        console.log('Created middleware service template');
        // 重新加载 systemd 配置
        // await execAsync('systemctl daemon-reload');
        console.log('Systemd configuration reloaded');
      }

    } catch (error) {
      console.error('Failed to ensure systemd services:', error);
      throw new ApiError(500, '创建 systemd 服务文件失败');
    }
  }

  private static async enableServiceWithEnv(
    serviceName: string,
    serviceType: 'program' | 'middleware',
    program: RecordModel,
    enable = false,
    setEnable = false,
    recover = '1'
  ): Promise<void> {
    try {

      // 如果type为middleware，判断对应程序服务是否启动如果未启动， recover为0
      if (serviceType === 'middleware') {
        const programServiceName = `ethercat_program@${program.name}`;
        const programStatus = await this.getServiceStatus(programServiceName);
        if (programStatus !== 'active') {
          recover = '0';
          console.log(`${programServiceName} 未启动， recover 设置为 0`);
        }
      }

      // 创建env文件
      await this.createEnvFile(program, recover);
  
      // 确定服务文件路径和生成方法
      const serviceFilePath = `/etc/systemd/system/ethercat_${serviceType}@.service`;
      const generateMethod = serviceType === 'program'
        ? TemplateService.generateProgramServiceScriptWithEnv
        : TemplateService.generateMiddlewareServiceScriptWithEnv;
  
      // 检查并创建服务文件
      if (!existsSync(serviceFilePath)) {
        const serviceContent = generateMethod();
        await fs.writeFile(serviceFilePath, serviceContent, 'utf-8');
        console.log(`Created ${serviceType} service template`);
      }
  
      // 启用并启动服务
      if (enable) {
        if (serviceType === 'middleware') {
          // 中间层只启动不启用
          await execAsync(`systemctl start ${serviceName}`);
          console.log(`${serviceType} service started (without enable)`);
        } else {
          // 主程序启动并启用
          await execAsync(`systemctl enable ${serviceName} --now`);
          console.log(`${serviceType} service enabled and started`);
        }
      }
      
      if (setEnable) {
        // setEnable 只对主程序有效
        if (serviceType === 'program') {
          await execAsync(`systemctl enable ${serviceName}`);
          console.log(`${serviceType} service enabled`);
        }
      }
  
    } catch (error) {
      console.error(`Failed to enable ${serviceType} service:`, error);
      throw new ApiError(500, `启用${serviceType === 'program' ? '程序' : '中间层'}服务失败`);
    }
  }


  /**
   * 更新调试模式
   * @param isDebug 是否启用调试模式
   * @returns 操作成功返回true，失败返回false
   */
  static async updateDebugMode(isDebug: boolean): Promise<boolean> {
    try {
      // 检查systemd权限
      await this.checkSystemdPermissions();
      
      // 服务文件路径
      const serviceFile = '/etc/systemd/system/ethercat_program@.service';

      // 检查服务文件是否存在，不存在创建
      await this.ensureSystemdServices();
      
      // 读取服务文件内容
      let content = await this.readFile(serviceFile);
      
      // 检查文件是否包含StandardOutput和StandardError行
      const hasStandardOutput = content.includes('StandardOutput=append:/dev/null');
      const hasStandardError = content.includes('StandardError=append:/dev/null');
      
      if (!hasStandardOutput || !hasStandardError) {
        console.error('Service file does not contain StandardOutput or StandardError lines');
        return false;
      }
      
      // 根据isDebug参数修改文件内容
      if (isDebug) {
        // 启动调试模式：添加注释
        content = content
        .replace('StandardOutput=append:/dev/null', '#StandardOutput=append:/dev/null')
        .replace('StandardError=append:/dev/null', '#StandardError=append:/dev/null');     
      } else {
        // 禁用调试模式：取消注释
        content = content
          .replace('#StandardOutput=append:/dev/null', 'StandardOutput=append:/dev/null')
          .replace('#StandardError=append:/dev/null', 'StandardError=append:/dev/null');
      }
      
      // 写入文件
      await fs.writeFile(serviceFile, content, 'utf8');
      
      // 重新加载systemd
      await execAsync('systemctl daemon-reload');
      
      console.log(`Debug mode has been ${isDebug ? 'enabled' : 'disabled'}`);
      return true;
    } catch (error) {
      console.error('Failed to update debug mode:', error);
      return false;
    }
  }
  
  /**
   * 获取当前调试模式状态
   * @returns 启用调试模式返回true，否则返回false
   */
  static async getDebugMode(): Promise<boolean> {
    try {
      // 服务文件路径
      const serviceFile = '/etc/systemd/system/ethercat_program@.service';

      // 检查服务文件是否存在，不存在创建
      await this.ensureSystemdServices();
      
      // 读取服务文件内容
      const content = await this.readFile(serviceFile);
      
      // 检查StandardOutput和StandardError行是否被注释
      const isStandardOutputCommented = content.includes('#StandardOutput=append:/dev/null');
      const isStandardErrorCommented = content.includes('#StandardError=append:/dev/null');
      
      // 如果被注释，则表示调试模式已禁用（返回false）
      // 如果未被注释，则表示调试模式已启用（返回true）
      return !isStandardOutputCommented && !isStandardErrorCommented;
    } catch (error) {
      console.error('Failed to get debug mode:', error);
      // 如果出错，默认返回false（禁用调试模式）
      return false;
    }
  }

  /**
   * 根据程序名称获取主站信息
   * @param programName 程序名称
   * @returns 主站索引
   */
  static async getMasterInfo(programName: string): Promise<number> {
    try {
      const pb = await PocketBaseManager.getInstance();
      
      // 查询数据库中匹配程序名的记录
      const result = await pb.collection('programs').getFirstListItem(`name="${programName}"`);
      
      if (!result) {
        throw new ApiError(404, `未找到名为 ${programName} 的程序`);
      }

      // 返回主站索引
      return parseInt(result.masterIndex);
    } catch (error: any) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('[Program Manager] Get master info error:', error);
      throw new ApiError(500, '获取主站信息失败');
    }
  }

  /**
   * 获取指定程序的状态
   * @param programName 程序名称
   * @returns 包含程序和中间层服务状态的对象
   */
  static async getProgramStatus(programName: string): Promise<{ program: string, middleware: string}> {
    console.log(`获取程序状态: ${programName}`);
    try{
      // 拼接程序服务名
      const programServiceName = `ethercat_program@${programName}`;
      // 拼接中间层服务名
      const middlewareServiceName = `ethercat_middleware@${programName}`;

      // systemctl is-active 判断服务是否启动, failed, inactive, active
      const programStatus = await this.getServiceStatus(programServiceName);
      const middlewareStatus = await this.getServiceStatus(middlewareServiceName);
      console.log(`程序服务状态: ${programStatus}, 中间层服务状态: ${middlewareStatus}`);

      return {
        program: programStatus,
        middleware: middlewareStatus
      };
    } catch (error) {
      console.error('获取程序状态失败:', error);
      // if (error instanceof ApiError) {
      //   throw error;
      // }
      // throw new ApiError(500, '获取程序状态失败');
    }
  }

  /**
   * 递归查找主程序文件
   * @param dir 要搜索的目录
   * @param programName 要查找的程序名
   * @returns 找到的程序文件路径，如果未找到则返回null
   */
  private static async findMainProgram(dir: string, programName: string): Promise<string | null> {
    const files = await fs.readdir(dir);

    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = await fs.stat(filePath);

      if (stat.isFile() && file === programName) {
        return filePath;
      }

      if (stat.isDirectory()) {
        const result = await this.findMainProgram(filePath, programName);
        if (result) {
          return result;
        }
      }
    }

    return null;
  }

  /**
   * 创建软链接，如果目标已存在则先删除
   * @param sourcePath 源文件路径
   * @param linkPath 软链接路径
   * @param chmod 是否设置执行权限
   */
  private static async createSymlink(sourcePath: string, linkPath: string, chmod = false): Promise<void> {
    try {
      // 检查目标路径是否存在
      try {
        const stats = await fs.lstat(linkPath);
        if (stats.isSymbolicLink()) {
          console.log(`发现已存在的软链接: ${linkPath}`);
        } else {
          console.log(`发现已存在的非软链接文件: ${linkPath}`);
        }
        // 无论是什么类型的文件，都删除
        await fs.rm(linkPath, { force: true });
        console.log(`已删除已存在的文件: ${linkPath}`);
      } catch (err: any) {
        if (err.code !== 'ENOENT') {
          // 如果错误不是"文件不存在"，则抛出
          throw err;
        }
        // 文件不存在，继续创建
      }

      // 创建新的软链接
      await fs.symlink(sourcePath, linkPath);
      console.log(`创建软链接成功: ${linkPath} -> ${sourcePath}`);

      // 如果需要，设置执行权限
      if (chmod) {
        await fs.chmod(sourcePath, 0o755);
        console.log(`设置执行权限成功: ${sourcePath}`);
      }
    } catch (error) {
      console.error(`创建软链接失败:`, error);
      throw new ApiError(500, `创建软链接失败: ${linkPath}`);
    }
  }

  /**
   * 检查文件是否存在并删除
   * @param filePath 文件路径
   * @param fileDesc 文件描述（用于日志）
   */
  private static async checkAndRemoveFile(filePath: string, fileDesc: string): Promise<void> {
    try {
      const stats = await fs.lstat(filePath);
      if (stats.isSymbolicLink()) {
        console.log(`发现已存在的${fileDesc}软链接: ${filePath}`);
      } else {
        console.log(`发现已存在的${fileDesc}文件: ${filePath}`);
      }
      await fs.rm(filePath, { force: true });
      console.log(`已删除旧的${fileDesc}: ${filePath}`);
    } catch (err: any) {
      if (err.code !== 'ENOENT') {
        console.error(`删除${fileDesc}失败:`, err);
        throw err;
      }
      // 文件不存在，静默处理
    }
  }

  private static async generateOldMiddleware(program: RecordModel): Promise<string> {
    const pb = new PocketBase(config.pocketbaseUrl);
    const programId = program.id;
    // 生成共享内存文件路径
    // const shmFile = `/dev/shm/${programId}_${program.name}_shm`;
    // const shortId = programId.substring(0, 6);

    // 1. 生成 C 语言模板
    const startGenerate = Date.now();
    const template = await this.generateMiddlewareTemplate(programId, pb);
    const endGenerate = Date.now();
    const timeSpend = endGenerate - startGenerate;
    logger.info('ProgramManager', `生成模板花费${timeSpend}ms`);

    // 2. 创建源文件和输出文件路径
    const programDir = path.dirname(program.filePath);
    const sourceFile = path.join(programDir, 'middleware.c');
    const outputFile = path.join(programDir, 'middleware');

    // 3. 写入源代码
    await fs.mkdir(programDir, { recursive: true });
    console.log('写入C语言源代码')
    await fs.writeFile(sourceFile, template);

    // 4. 编译 C 程序
    try {
      console.log('编译中间层程序')
      await execAsync(`gcc ${sourceFile} -o ${outputFile} -lethercat -lrt -pthread`);
    } catch (error) {
      console.error('Compilation failed:', error);
      throw new ApiError(500, '中间层程序编译失败');
    }

    // 5. 给程序添加执行权限
    try {
      console.log('设置执行权限')
      await fs.chmod(outputFile, 0o755);
    } catch (error) {
      console.error('Failed to set executable permission:', error);
      throw new ApiError(500, '设置执行权限失败');
    }

    return outputFile;
  }
}

// export { ProgramManager, PocketBaseManager };

