import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量，优先使用当前目录的.env
const envPath = path.resolve(process.cwd(), '.env');
console.log('Loading .env from:', envPath);
dotenv.config({ path: envPath });

export const config = {
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',
  pocketbaseUrl: process.env.POCKETBASE_URL || 'http://127.0.0.1:8090',
  ethercatDir: process.env.ETHERCAT_DIR || '/dev/shm/ethercat',
  taskFrequency: parseInt(process.env.TASK_FREQUENCY || '1000', 10),
  pocketbase: {
    adminEmail: process.env.POCKETBASE_ADMIN_EMAIL || '<EMAIL>',
    adminPassword: process.env.POCKETBASE_ADMIN_PASSWORD || 'your-password'
  }
}; 