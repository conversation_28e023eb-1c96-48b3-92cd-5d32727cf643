import fs from 'fs';
import path from 'path';
import { promises as fsPromises } from 'fs';

export class ExternalApiLogger {
    private static readonly LOG_DIR = '/home/<USER>/logs';
    
    private static async ensureLogDir(): Promise<void> {
        try {
            await fsPromises.access(this.LOG_DIR);
        } catch (error) {
            await fsPromises.mkdir(this.LOG_DIR, { recursive: true });
        }
    }

    private static getLogFileName(): string {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hour = String(now.getHours()).padStart(2, '0');
        return `${year}${month}${day}${hour}.log`;
    }

    static async log(apiName: string, responseData: any): Promise<void> {
        try {
            await this.ensureLogDir();
            
            const timestamp = new Date().toISOString();
            const logFileName = this.getLogFileName();
            const logFilePath = path.join(this.LOG_DIR, logFileName);
            
            const logEntry = {
                timestamp,
                apiName,
                responseData
            };
            
            const logLine = JSON.stringify(logEntry) + '\n';
            
            await fsPromises.appendFile(logFilePath, logLine, 'utf8');
        } catch (error) {
            console.error('Failed to write external API log:', error);
        }
    }
} 