export function generateCCode(config: any) {
  const template = `
    #include <errno.h>
    #include <signal.h>
    // ... (其他头文件)

    #define ETHERCAT_DIR "${config.ethercatDir || '/dev/shm/ethercat'}"
    #define TASK_FREQUENCY ${config.taskFrequency || 1000}

    // 根据配置生成从站定义
    ${generateSlaveDefinitions(config.slaves)}

    // 生成PDO映射
    ${generatePDOMapping(config.slaves)}

    // ... (其他代码生成逻辑)
  `;

  return template;
} 