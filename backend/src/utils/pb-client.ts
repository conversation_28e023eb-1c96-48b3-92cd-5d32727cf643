import PocketBase from 'pocketbase';
import { config } from '../config/index.js';

class PocketBaseClient {
  private static instance: PocketBase;
  private static adminAuthPromise: Promise<void> | null = null;

  static getInstance(): PocketBase {
    if (!this.instance) {
      this.instance = new PocketBase(config.pocketbaseUrl);
    }
    return this.instance;
  }

  static async ensureAdminAuth(): Promise<void> {
    if (!this.adminAuthPromise) {
      this.adminAuthPromise = this.getInstance()
        .admins.authWithPassword('<EMAIL>', 'qs2022qs2022')
        .then(() => {
          // 设置一个定时器，在认证过期前刷新认证状态
          setTimeout(() => { this.adminAuthPromise = null; }, 14 * 60 * 1000); // 14分钟后刷新
        });
    }
    return this.adminAuthPromise;
  }

  static isUserAuthenticated(): boolean {
    return this.getInstance().authStore.isValid;
  }

  static clearAuth(): void {
    this.getInstance().authStore.clear();
    this.adminAuthPromise = null;
  }
}

export default PocketBaseClient; 