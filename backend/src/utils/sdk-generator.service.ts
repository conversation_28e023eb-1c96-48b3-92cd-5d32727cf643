import * as fs from 'fs';
import * as nunjucks from 'nunjucks';
import * as path from 'path';
import * as util from 'util'
interface PDOConfig {
  index: string;
  subindex?: string;
  name: string;
  type: string;
  bitlen?: number;
  comment?: string;
}

interface SlaveConfig {
  index: string;
  name: string;
  vid?: string;
  pid: string;
  rx_pdo?: string;
  tx_pdo?: string;
  rx_pdos?: PDOConfig[];
  tx_pdos?: PDOConfig[];
  dc_config?: {
    assign_activate: string;
    sync0_cycle?: string;
    sync0_shift?: string;
    sync1_cycle?: string;
    sync1_shift?: string;
  };
  pdo_mapping?: {
    rx_pdos: any[];
    tx_pdos: any[];
  };
  sdos?: {
    index: string;
    subindex: string;
    name: string;
    type: string;
    value: string;
  }[];
}

interface SDKConfig {
  id?: string;
  name?: string;
  masterIndex?: number;
  taskFrequency?: number;
  coreAffinity?: number;
  slaves: SlaveConfig[];
}

export class SDKGeneratorService {
  private static getTemplatePath(): string {
    // 在打包后，模板文件会在当前目录的template文件夹下
    return path.join(process.cwd(), 'template');
  }

  static async generateSDKFiles(config: SDKConfig): Promise<{ cCode: string, hCode: string }> {
    try {
      // Pre-process the config to generate C code snippets and variable names
      const augmentedConfig = this.augmentConfig(config);
    
      // Configure Nunjucks with template path
      const templateDir = this.getTemplatePath();
      if (!fs.existsSync(templateDir)) {
        throw new Error(`Template directory not found: ${templateDir}`);
      }
      console.log("=====  templateDir =====", templateDir);
      const env = nunjucks.configure(templateDir, {
          autoescape: false,
          trimBlocks: true,
          lstripBlocks: true
      });
      
      // Generate C and H files from templates
      const cCode = env.render('sdk.c.njk', { config: augmentedConfig, id: augmentedConfig.id });
      const hCode = env.render('sdk.h.njk', { config: augmentedConfig });
      // console.log(util.inspect(augmentedConfig, { depth: null, colors: true }));
      return { cCode, hCode };
    } catch (error: any) {
      console.error(`Error generating SDK files: ${error.message}`);
      throw error;
    }
  }

  private static augmentConfig(config: SDKConfig): any {
    const periodNs = `(NSEC_PER_SEC / ${(config.taskFrequency || 4000)})`;

    config.slaves.forEach((slave, slaveIndex) => {
      const s = slave as any; // Allow adding new properties
      s.slave_index = slaveIndex;
      s.pos_define = `#define SLAVE${slaveIndex}_POS 0,${slaveIndex}`;
      s.vid_pid_define = `#define SLAVE${slaveIndex}_VID_PID ${slave.vid || '0x00000000'},${slave.pid}`;
      
      const activeRxPdos = this.getActivePdos(slave.rx_pdos);
      const activeTxPdos = this.getActivePdos(slave.tx_pdos);
      
      s.active_rx_pdos = activeRxPdos;
      s.active_tx_pdos = activeTxPdos;

      const allPdos = [...activeRxPdos, ...activeTxPdos];

      allPdos.forEach(pdo => {
        const p = pdo as any;
        const direction = activeRxPdos.includes(p) ? 'rx' : 'tx';
        const varName = this.sanitizeVariableName(p);
        const pdoBaseName = `pdo_slave${slaveIndex}_${direction}_${varName}`;
        const shmVarName = `shm_slave${slaveIndex}_${direction}_${varName}`;

        // Assign properties needed by other parts of the generator
        p.direction = direction;
        p.sanitized_var_name = varName;
        p.shm_var_name = shmVarName;
        p.subindex_val = p.subindex || 0;

        // Generate definitions needed for the C file header/top
        p.shm_var_definition = `int32_t ${shmVarName};${p.comment ? ` // ${p.comment}` : ''}`;

        if (p.type === 'bool') {
          p.offset_var_definitions = [
            `unsigned int ${pdoBaseName}_off;`,
            `unsigned int ${pdoBaseName}_bit;`
          ];
          p.registration_line = `{SLAVE${slaveIndex}_POS, SLAVE${slaveIndex}_VID_PID, ${p.index}, ${p.subindex || 0}, &g_pdo_offsets.${pdoBaseName}_off, &g_pdo_offsets.${pdoBaseName}_bit},`;
          p.cyclic_line = direction === 'rx'
            ? `EC_WRITE_BIT(g_domain1_pd + g_pdo_offsets.${pdoBaseName}_off, g_pdo_offsets.${pdoBaseName}_bit, g_sdk_shm_ptr->${shmVarName});`
            : `g_sdk_shm_ptr->${shmVarName} = EC_READ_BIT(g_domain1_pd + g_pdo_offsets.${pdoBaseName}_off, g_pdo_offsets.${pdoBaseName}_bit);`;

        } else {
          p.offset_var_definitions = [`unsigned int ${pdoBaseName};`];
          p.registration_line = `{SLAVE${slaveIndex}_POS, SLAVE${slaveIndex}_VID_PID, ${p.index}, ${p.subindex || 0}, &g_pdo_offsets.${pdoBaseName}},`;
          p.cyclic_line = direction === 'rx'
            ? `${this.getPDOWriteFunction(p.type)}(g_domain1_pd + g_pdo_offsets.${pdoBaseName}, g_sdk_shm_ptr->${shmVarName});`
            : `g_sdk_shm_ptr->${shmVarName} = ${this.getPDOReadFunction(p.type)}(g_domain1_pd + g_pdo_offsets.${pdoBaseName});`;
        }
      });
      
      // Generate larger C code blocks
      s.generated_c = {};

      const pdoEntryLines = [
          ...activeRxPdos.map(p => `{${p.index}, ${p.subindex || 0}, ${p.bitlen || this.getTypeBits(p)}},  /* ${p.name} */`),
          ...activeTxPdos.map(p => `{${p.index}, ${p.subindex || 0}, ${p.bitlen || this.getTypeBits(p)}},  /* ${p.name} */`)
      ];
      s.generated_c.pdo_entries = pdoEntryLines.join('\n');

      // 生成PDO信息
      const pdoInfoLines: string[] = [];
      let entryOffset = 0;

      // 检查是否使用新的PDO映射格式
      const hasPdoMapping = s.pdo_mapping && 
                          Array.isArray(s.pdo_mapping.rx_pdos) && 
                          Array.isArray(s.pdo_mapping.tx_pdos) &&
                          (s.pdo_mapping.rx_pdos.length > 0 || s.pdo_mapping.tx_pdos.length > 0);

      if (hasPdoMapping) {
        // 处理RxPDO
        if (s.pdo_mapping.rx_pdos.length > 0) {
          for (const pdo of s.pdo_mapping.rx_pdos) {
            if (pdo.index === '0x0000' || pdo.index === '0x00000000') continue;
            const pdoIndexHex = pdo.index.replace('0x', '');
            pdoInfoLines.push(`{0x${pdoIndexHex}, ${pdo.entries.length}, slave${slaveIndex}_pdo_entries + ${entryOffset}},  /* RxPDO */`);
            entryOffset += pdo.entries.length;
          }
        }

        // 处理TxPDO
        if (s.pdo_mapping.tx_pdos.length > 0) {
          for (const pdo of s.pdo_mapping.tx_pdos) {
            if (pdo.index === '0x0000' || pdo.index === '0x00000000') continue;
            const pdoIndexHex = pdo.index.replace('0x', '');
            pdoInfoLines.push(`{0x${pdoIndexHex}, ${pdo.entries.length}, slave${slaveIndex}_pdo_entries + ${entryOffset}},  /* TxPDO */`);
            entryOffset += pdo.entries.length;
          }
        }
      } else {
        // 使用旧的PDO格式
        const rxPdoCount = activeRxPdos.length;
        const txPdoCount = activeTxPdos.length;

        // 处理RxPDO
        if (s.rx_pdo) {
          const rxPdoIndices = s.rx_pdo.split(',').map(p => p.trim());
          for (const pdoIndex of rxPdoIndices) {
            if (pdoIndex === '0x0000' || pdoIndex === '0x00000000') continue;
            const pdoIndexHex = pdoIndex.replace('0x', '');
            pdoInfoLines.push(`{0x${pdoIndexHex}, ${rxPdoCount}, slave${slaveIndex}_pdo_entries + ${entryOffset}},  /* RxPDO */`);
          }
        }

        // 更新偏移量
        entryOffset = rxPdoCount;

        // 处理TxPDO
        if (s.tx_pdo) {
          const txPdoIndices = s.tx_pdo.split(',').map(p => p.trim());
          for (const pdoIndex of txPdoIndices) {
            if (pdoIndex === '0x0000' || pdoIndex === '0x00000000') continue;
            const pdoIndexHex = pdoIndex.replace('0x', '');
            pdoInfoLines.push(`{0x${pdoIndexHex}, ${txPdoCount}, slave${slaveIndex}_pdo_entries + ${entryOffset}},  /* TxPDO */`);
          }
        }
      }

      s.generated_c.pdo_info = pdoInfoLines.join('\n');

      // 生成同步管理器配置
      const syncInfoLines: string[] = [];
      let pdoOffset = 0;

      // 检查是否使用新的同步管理器格式
      const hasSyncs = s.syncs && Array.isArray(s.syncs) && s.syncs.length > 0;

      if (hasSyncs) {
        // 使用新的同步管理器格式
        for (const sync of s.syncs) {
          const direction = sync.direction === "OUTPUT" ? "EC_DIR_OUTPUT" : "EC_DIR_INPUT";
          const watchdog = sync.watchdog === "ENABLE" ? "EC_WD_ENABLE" : "EC_WD_DISABLE";
          const pdoCount = Array.isArray(sync.pdos) ? sync.pdos.length : 0;

          if (pdoCount > 0) {
            syncInfoLines.push(`{${sync.index}, ${direction}, ${pdoCount}, slave${slaveIndex}_pdos + ${pdoOffset}, ${watchdog}},`);
            pdoOffset += pdoCount;
          } else {
            syncInfoLines.push(`{${sync.index}, ${direction}, 0, NULL, ${watchdog}},`);
          }
        }
      } else {
        // 使用默认的同步管理器配置
        syncInfoLines.push(`{0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},`);
        syncInfoLines.push(`{1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},`);

        // 处理RxPDO (SM2)
        if (activeRxPdos.length > 0) {
          const rxPdoCount = s.rx_pdo ? s.rx_pdo.split(',').length : 0;
          syncInfoLines.push(`{2, EC_DIR_OUTPUT, ${rxPdoCount}, slave${slaveIndex}_pdos + ${pdoOffset}, EC_WD_ENABLE},`);
          pdoOffset += rxPdoCount;
        } else {
          syncInfoLines.push(`{2, EC_DIR_OUTPUT, 0, NULL, EC_WD_ENABLE},`);
        }

        // 处理TxPDO (SM3)
        if (activeTxPdos.length > 0) {
          const txPdoCount = s.tx_pdo ? s.tx_pdo.split(',').length : 0;
          syncInfoLines.push(`{3, EC_DIR_INPUT, ${txPdoCount}, slave${slaveIndex}_pdos + ${pdoOffset}, EC_WD_DISABLE},`);
        } else {
          syncInfoLines.push(`{3, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},`);
        }
      }

      // 添加终止标记
      syncInfoLines.push(`{0xff}`);
      s.generated_c.sync_info = syncInfoLines.join('\n');

      const groupPdosByIndex = (pdos: any[]) => {
          const grouped = new Map<string, any[]>();
          for (const pdo of pdos) {
              if (!grouped.has(pdo.index)) {
                  grouped.set(pdo.index, []);
              }
              grouped.get(pdo.index)!.push(pdo);
          }
          return grouped;
      };

      const generateSdkSwitchCases = (groupedPdos: Map<string, any[]>, accessType: 'read' | 'write') => {
          const allCases: string[] = [];
          for (const [index, pdosInGroup] of groupedPdos.entries()) {
              const caseBlock: string[] = [];
              caseBlock.push(`case ${index}:`);
              
              const ifElseChain: string[] = [];
              for (let i = 0; i < pdosInGroup.length; i++) {
                  const pdo = pdosInGroup[i];
                  
                  let core_logic = '';
                  if (accessType === 'write') {
                      core_logic = `g_sdk_shm_ptr->${pdo.shm_var_name} = value;`;
                  } else {
                      core_logic = `*value_ptr = g_sdk_shm_ptr->${pdo.shm_var_name};`;
                  }

                  const condition = `if (subindex == ${pdo.subindex_val})`;
                  if (i === 0) {
                      ifElseChain.push(`    ${condition} {`);
                  } else {
                      ifElseChain.push(`    } else ${condition} {`);
                  }
                  ifElseChain.push(`        ${core_logic}`);
              }
              ifElseChain.push(
`    } else {
        result = -3; // Invalid subindex
    }`);

              caseBlock.push(ifElseChain.join('\n'));
              caseBlock.push(`    break;`);

              allCases.push(caseBlock.join('\n'));
          }
          return allCases.join('\n');
      };

      const groupedRxPdos = groupPdosByIndex(activeRxPdos);
      s.generated_c.sdk_write_cases = generateSdkSwitchCases(groupedRxPdos, 'write');

      const groupedTxPdos = groupPdosByIndex(activeTxPdos);
      s.generated_c.sdk_read_cases = generateSdkSwitchCases(groupedTxPdos, 'read');

      if (s.sdos) {
        s.sdos.forEach((sdo: any) => {
          sdo.sdo_func_suffix = sdo.type.replace('uint', '').replace('int', '');
        });
      }

      if (s.dc_config) {
        s.dc_config.sync0_cycle = s.dc_config.sync0_cycle || periodNs;
        s.dc_config.sync0_shift = s.dc_config.sync0_shift || `${periodNs} / 3`;
        s.dc_config.sync1_cycle = s.dc_config.sync1_cycle || 0;
        s.dc_config.sync1_shift = s.dc_config.sync1_shift || 0;
      }
    });

    return config;
  }

  private static sanitizeVariableName(pdo: any): string {
    if (!pdo || !pdo.index || !pdo.name) {
      return 'invalid_pdo';
    }
    const baseName = `${pdo.index.toLowerCase()}_${pdo.name.toLowerCase()}`;
    return baseName.replace(/[^a-z0-9_]/g, '_');
  }

  private static getPDOReadFunction(type: string): string {
    switch (type) {
      case 'uint8':
        return 'EC_READ_U8';
      case 'uint16':
        return 'EC_READ_U16';
      case 'uint32':
        return 'EC_READ_U32';
      case 'int8':
        return 'EC_READ_S8';
      case 'int16':
        return 'EC_READ_S16';
      case 'int32':
        return 'EC_READ_S32';
      default:
        return 'EC_READ_U32'; // Default fallback
    }
  }

  private static getPDOWriteFunction(type: string): string {
    switch (type) {
      case 'uint8':
        return 'EC_WRITE_U8';
      case 'uint16':
        return 'EC_WRITE_U16';
      case 'uint32':
        return 'EC_WRITE_U32';
      case 'int8':
        return 'EC_WRITE_S8';
      case 'int16':
        return 'EC_WRITE_S16';
      case 'int32':
        return 'EC_WRITE_S32';
      default:
        return 'EC_WRITE_U32'; // Default fallback
    }
  }

  private static getCType(type: string): string {
    switch (type) {
      case 'uint8':
        return 'uint8_t';
      case 'uint16':
        return 'uint16_t';
      case 'uint32':
        return 'uint32_t';
      case 'int8':
        return 'int8_t';
      case 'int16':
        return 'int16_t';
      case 'int32':
        return 'int32_t';
      case 'bool':
        return 'uint8_t';
      default:
        return 'uint32_t'; // Default fallback
    }
  }

  private static getTypeBits(pdo: any): number {
    switch (pdo.type) {
      case 'uint8':
      case 'int8':
      case 'bool':
        return 8;
      case 'uint16':
      case 'int16':
        return 16;
      case 'uint32':
      case 'int32':
        return 32;
      default:
        return 32; // Default fallback
    }
  }

  private static getActivePdos(pdos?: PDOConfig[]): PDOConfig[] {
    if (!pdos) {
      return [];
    }
    return pdos.filter(pdo => {
      const pdoIndex = pdo.index.replace('0x', '');
      return pdoIndex !== '0000' && pdoIndex !== '0';
    });
  }
}