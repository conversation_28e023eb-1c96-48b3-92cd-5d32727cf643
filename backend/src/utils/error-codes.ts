/**
 * 错误码定义
 * 400: 认证相关错误
 * 500-599: 后端业务错误
 */
export enum ErrorCode {
  // 认证错误 (400)
  AUTH_ERROR = 400,

  // 后端错误 (500-599)
  INTERNAL_ERROR = 500,           // 内部错误
  TEMPLATE_ERROR = 501,           // 模板生成错误
  SLAVE_COUNT_MISMATCH = 502,     // 从站数量不匹配
  SLAVE_VID_PID_MISMATCH = 503,   // 从站VID/PID不匹配
  CONFIG_ERROR = 504,             // 配置错误
  NETWORK_ERROR = 505,            // 网络错误
  SERVICE_ERROR = 507,            // 服务错误
  MASTER_IN_USE = 506,            // 主站被占用
  ARCHITECTURE_MISMATCH = 508,    // 平台架构不匹配
}

export const ErrorMessages: Record<number, string> = {
  [ErrorCode.AUTH_ERROR]: '认证失败',
  [ErrorCode.INTERNAL_ERROR]: '内部错误',
  [ErrorCode.TEMPLATE_ERROR]: '生成模板失败',
  [ErrorCode.SLAVE_COUNT_MISMATCH]: '实际从站数量与配置数不一致',
  [ErrorCode.SLAVE_VID_PID_MISMATCH]: '从站VID/PID与实际不符',
  [ErrorCode.CONFIG_ERROR]: '配置错误',
  [ErrorCode.NETWORK_ERROR]: '网络错误',
  [ErrorCode.SERVICE_ERROR]: '服务错误',
  [ErrorCode.MASTER_IN_USE]: '主站已被其他程序占用',
  [ErrorCode.ARCHITECTURE_MISMATCH]: '程序架构与系统平台不匹配',
};
