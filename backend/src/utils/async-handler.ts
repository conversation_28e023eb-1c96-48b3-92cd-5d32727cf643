import { Request, Response, NextFunction } from 'express';
import { ApiError } from './errors.js';

export const asyncHandler = (fn: Function) => (req: Request, res: Response, next: NextFunction) => {
  Promise.resolve(fn(req, res, next)).catch((error) => {
    if (error instanceof ApiError) {
      res.status(error.statusCode).json({
        status: error.statusCode,
        message: error.message
      });
    } else {
      console.error('Unhandled error:', error);
      res.status(500).json({
        status: 500,
        message: 'Internal Server Error'
      });
    }
  });
}; 