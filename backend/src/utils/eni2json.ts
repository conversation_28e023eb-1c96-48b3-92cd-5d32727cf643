import { parseString } from 'xml2js';

export interface SDO {
  name: string;
  index: string;
  subindex: string;
  type: string;
  value: string;
}

export interface PDO {
  name: string;
  index: string;
  subindex: string;
  type: string;
}

export interface PDOEntry {
  index: string;
  subindex: string;
  name: string;
  type: string;
}

export interface PDOGroup {
  index: string;
  entries: PDOEntry[];
  entryOffset: number;
}

export interface PDOMapping {
  rx_pdos: PDOGroup[];
  tx_pdos: PDOGroup[];
}

export interface Sync {
  index: number;
  direction: string;
  pdos: string[];
  watchdog: string;
}

export interface Slave {
  index: string;
  name: string;
  vid: string;
  pid: string;
  rx_pdo: string;
  tx_pdo: string;
  sdos: SDO[];
  rx_pdos: PDO[];
  tx_pdos: PDO[];
  pdo_mapping: PDOMapping;
  syncs: Sync[];
  exclude: { [key: string]: string[] };
}

export interface SlaveConfig {
  slaves: Slave[];
}

function convertType(dataType: string): string {
  const typeMap: { [key: string]: string } = {
    'UINT': 'uint16',
    'UDINT': 'uint32',
    'USINT': 'uint8',
    'BOOL': 'bool',
    'BIT': 'bool'
  };
  return typeMap[dataType.toUpperCase()] || 'bool';
}

function formatHexValue(value: string | number | any): string {
  if (typeof value === 'object') {
    if (value._ !== undefined) {
      value = value._;
    } else {
      console.warn('无法解析的值格式:', value);
      return '0x00000000';
    }
  }

  if (value === undefined || value === null) {
    return '0x00000000';
  }

  if (typeof value === 'string') {
    if (value.startsWith('#x') || value.startsWith('0x')) {
      const hexValue = value.replace(/^#x|^0x/i, '').toLowerCase();
      return '0x' + hexValue.padStart(8, '0');
    }
    value = parseInt(value, 10);
  }

  if (typeof value === 'number') {
    return '0x' + value.toString(16).padStart(8, '0').toLowerCase();
  }

  return '0x00000000';
}

function convertSdoValue(value: string, type: string): string {
  let hexValue = value.replace(/^(0x|#x)/i, '');
  hexValue = hexValue.padStart(8, '0');
  
  switch(type) {
    case 'uint8':
      return '0x' + hexValue.slice(-2);
    case 'uint16':
      const bytes16 = hexValue.slice(-4).match(/.{2}/g) || [];
      return '0x' + bytes16.reverse().join('');
    case 'uint32':
      const bytes32 = hexValue.match(/.{2}/g) || [];
      return '0x' + bytes32.reverse().join('');
    default:
      return '0x' + hexValue;
  }
}

function extractPDOEntries(pdo: any): PDO[] {
  const entries: PDO[] = [];
  if (!pdo || !pdo.Entry) return entries;

  const entryArray = Array.isArray(pdo.Entry) ? pdo.Entry : [pdo.Entry];
  
  entryArray.forEach((entry: any) => {
    try {
      const index = entry.Index ? entry.Index[0] : '0';
      entries.push({
        name: entry.Name ? entry.Name[0] : 'Reserved',
        index: formatHexValue(index),
        subindex: entry.SubIndex ? entry.SubIndex[0] : '0',
        type: entry.DataType ? convertType(entry.DataType[0]) : 'bool'
      });
    } catch (error) {
      console.warn('处理PDO条目时出错:', error);
    }
  });

  return entries;
}

function extractSDOs(mailbox: any): SDO[] {
  const sdos: SDO[] = [];
  if (!mailbox || !mailbox.CoE || !mailbox.CoE[0].InitCmds) return sdos;

  const initCmds = mailbox.CoE[0].InitCmds[0].InitCmd;
  initCmds.forEach((cmd: any) => {
    const index = parseInt(cmd.Index[0], 10);
    const subIndex = parseInt(cmd.SubIndex[0], 10);
    const data = cmd.Data[0];
    
    let type = 'uint32';
    if (data.length <= 2) type = 'uint8';
    else if (data.length <= 4) type = 'uint16';
    
    const moduleHandle = cmd.ModuleHandle ? cmd.ModuleHandle[0] : '';
    const moduleName = moduleHandle === '0' ? 'Output' : 
                      moduleHandle === '1' ? 'Input' : 
                      `Module${moduleHandle}`;

    sdos.push({
      name: `${moduleName} Config ${subIndex}`,
      index: '0x' + index.toString(16).padStart(4, '0'),
      subindex: subIndex.toString(),
      type: type,
      value: convertSdoValue(data, type)
    });
  });

  return sdos;
}

function extractPDOGroup(pdo: any): PDOGroup {
  try {
    let indexValue = pdo.Index?.[0];
    if (typeof indexValue === 'object' && indexValue._) {
      indexValue = indexValue._;
    }

    const entries = pdo.Entry ? pdo.Entry.map((entry: any) => {
      let entryIndex = entry.Index?.[0];
      if (typeof entryIndex === 'object' && entryIndex._) {
        entryIndex = entryIndex._;
      }

      return {
        index: formatHexValue(entryIndex || 0),
        subindex: entry.SubIndex?.[0] || '0',
        name: entry.Name?.[0] || 'Reserved',
        type: entry.DataType?.[0] ? convertType(entry.DataType[0]) : 
              entry.BitLen?.[0] === '1' ? 'bool' : 'uint8'
      };
    }) : [];

    return {
      index: formatHexValue(indexValue || 0),
      entries,
      entryOffset: 0
    };
  } catch (error) {
    console.error('处理PDO组时出错:', error);
    return {
      index: '0x0000',
      entries: [],
      entryOffset: 0
    };
  }
}

function extractPDOMappings(processData: any): PDOMapping {
  const mapping: PDOMapping = {
    rx_pdos: [],
    tx_pdos: []
  };
  
  let currentOffset = 0;
  
  try {
    if (processData.RxPdo) {
      const rxPdos = Array.isArray(processData.RxPdo) ? 
        processData.RxPdo : [processData.RxPdo];

      mapping.rx_pdos = rxPdos.map((pdo: any) => {
        const group = extractPDOGroup(pdo);
        group.entryOffset = currentOffset;
        currentOffset += group.entries.length;
        return group;
      });
    }
    
    if (processData.TxPdo) {
      const txPdos = Array.isArray(processData.TxPdo) ? 
        processData.TxPdo : [processData.TxPdo];

      mapping.tx_pdos = txPdos.map((pdo: any) => {
        const group = extractPDOGroup(pdo);
        group.entryOffset = currentOffset;
        currentOffset += group.entries.length;
        return group;
      });
    }
  } catch (error) {
    console.error('处理PDO映射时出错:', error);
  }
  
  return mapping;
}

function extractPDOs(processData: any): { 
  rxPdos: PDO[], 
  txPdos: PDO[], 
  rxPdoIndices: string[], 
  txPdoIndices: string[],
  pdoMapping: PDOMapping,
  exclude: { [key: string]: string[] }
} {
  const rxPdos: PDO[] = [];
  const txPdos: PDO[] = [];
  const rxPdoIndices: string[] = [];
  const txPdoIndices: string[] = [];
  const exclude: { [key: string]: string[] } = {};

  try {
    if (processData.RxPdo) {
      const rxPdoArray = Array.isArray(processData.RxPdo) ? 
        processData.RxPdo : [processData.RxPdo];

      rxPdoArray.forEach((pdo: any) => {
        if (pdo && pdo.Index) {
          const pdoIndex = formatHexValue(pdo.Index[0]);
          rxPdoIndices.push(pdoIndex);
          rxPdos.push(...extractPDOEntries(pdo));

          if (pdo.Exclude) {
            const excludeArray = Array.isArray(pdo.Exclude) ? 
              pdo.Exclude : [pdo.Exclude];
            exclude[pdoIndex] = excludeArray.map((ex: any) => {
              const excludeValue = typeof ex === 'string' ? ex : ex._;
              return formatHexValue(excludeValue);
            });
          }
        }
      });
    }

    if (processData.TxPdo) {
      const txPdoArray = Array.isArray(processData.TxPdo) ? 
        processData.TxPdo : [processData.TxPdo];

      txPdoArray.forEach((pdo: any) => {
        if (pdo && pdo.Index) {
          txPdoIndices.push(formatHexValue(pdo.Index[0]));
          txPdos.push(...extractPDOEntries(pdo));
        }
      });
    }
  } catch (error) {
    console.error('处理PDO映射时出错:', error);
  }

  const pdoMapping = extractPDOMappings(processData);

  return { rxPdos, txPdos, rxPdoIndices, txPdoIndices, pdoMapping, exclude };
}

function extractSyncs(processData: any): Sync[] {
  const syncs: Sync[] = [];
  
  syncs.push(
    {
      index: 0,
      direction: "OUTPUT",
      pdos: [],
      watchdog: "DISABLE"
    },
    {
      index: 1,
      direction: "INPUT",
      pdos: [],
      watchdog: "DISABLE"
    }
  );

  if (processData.RxPdo) {
    const rxPdoIndices = processData.RxPdo.map((pdo: any) => formatHexValue(pdo.Index[0]));
    syncs.push({
      index: 2,
      direction: "OUTPUT",
      pdos: rxPdoIndices,
      watchdog: "ENABLE"
    });
  }

  if (processData.TxPdo) {
    const txPdoIndices = processData.TxPdo.map((pdo: any) => formatHexValue(pdo.Index[0]));
    syncs.push({
      index: 3,
      direction: "INPUT",
      pdos: txPdoIndices,
      watchdog: "DISABLE"
    });
  }

  return syncs;
}

export async function parseENI(xmlContent: string): Promise<SlaveConfig> {
  return new Promise((resolve, reject) => {
    parseString(xmlContent, { explicitArray: true }, (err, result) => {
      if (err) {
        reject(err);
        return;
      }

      const slaves: Slave[] = [];
      const config = result.EtherCATConfig.Config[0];

      if (config.Slave) {
        config.Slave.forEach((slave: any, index: number) => {
          const info = slave.Info[0];
          const processData = slave.ProcessData[0];
          const { rxPdos, txPdos, rxPdoIndices, txPdoIndices, pdoMapping, exclude } = extractPDOs(processData);
          const sdos = extractSDOs(slave.Mailbox && slave.Mailbox[0]);
          const syncs = extractSyncs(processData);

          const name = info.Name[0].replace(/\[\[CDATA\[(.*?)\]\]\]/, '$1');

          slaves.push({
            index: index.toString(),
            name: name,
            vid: formatHexValue(info.VendorId[0]),
            pid: formatHexValue(info.ProductCode[0]),
            rx_pdo: rxPdoIndices.join(', '),
            tx_pdo: txPdoIndices.join(', '),
            sdos: sdos,
            rx_pdos: rxPdos,
            tx_pdos: txPdos,
            pdo_mapping: pdoMapping,
            syncs: syncs,
            exclude: exclude
          });
        });
      }

      resolve({ slaves });
    });
  });
}

export async function getSlaveConfig(xmlContent: string): Promise<SlaveConfig> {
  return parseENI(xmlContent);
}

// 1. 获取过滤后的 rx_pdos
export function getFilteredRxPdos(json: any, slaveIndex: number, rxPdo: string[]): PDO[] {
  try {
    const slave = json.slaves[slaveIndex];
    if (!slave || !slave.pdo_mapping || !slave.pdo_mapping.rx_pdos) {
      return [];
    }

    const filteredPdos: PDO[] = [];
    slave.pdo_mapping.rx_pdos
      .filter((pdo: PDOGroup) => rxPdo.includes(pdo.index))
      .forEach((pdo: PDOGroup) => {
        pdo.entries.forEach((entry: PDOEntry) => {
          filteredPdos.push({
            name: entry.name,
            index: entry.index,
            subindex: entry.subindex,
            type: entry.type
          });
        });
      });

    console.log(`从站 ${slaveIndex} 过滤后的 RX PDOs:`, JSON.stringify(filteredPdos, null, 2));
    return filteredPdos;
  } catch (error) {
    console.error('获取过滤后的 rx_pdos 失败:', error);
    return [];
  }
}

// 2. 获取过滤后的 tx_pdos
export function getFilteredTxPdos(json: any, slaveIndex: number, txPdo: string[]): PDO[] {
  try {
    const slave = json.slaves[slaveIndex];
    if (!slave || !slave.pdo_mapping || !slave.pdo_mapping.tx_pdos) {
      return [];
    }

    const filteredPdos: PDO[] = [];
    slave.pdo_mapping.tx_pdos
      .filter((pdo: PDOGroup) => txPdo.includes(pdo.index))
      .forEach((pdo: PDOGroup) => {
        pdo.entries.forEach((entry: PDOEntry) => {
          filteredPdos.push({
            name: entry.name,
            index: entry.index,
            subindex: entry.subindex,
            type: entry.type
          });
        });
      });

    console.log(`从站 ${slaveIndex} 过滤后的 TX PDOs:`, JSON.stringify(filteredPdos, null, 2));
    return filteredPdos;
  } catch (error) {
    console.error('获取过滤后的 tx_pdos 失败:', error);
    return [];
  }
}

// 3. 构建 pdo_mapping
export function buildPdoMapping(
  json: any,
  slaveIndex: number,
  rxPdo: string[],
  txPdo: string[]
): PDOMapping {
  try {
    const slave = json.slaves[slaveIndex];
    if (!slave || !slave.pdo_mapping) {
      console.log(`从站 ${slaveIndex} 没有 pdo_mapping 数据`);
      return { rx_pdos: [], tx_pdos: [] };
    }

    // 先获取所有符合条件的 rx_pdos
    const matchedRxPdos = slave.pdo_mapping.rx_pdos
      .filter((pdo: PDOGroup) => rxPdo.includes(pdo.index));

    // 计算 rx_pdos 的 entryOffset
    const filteredRxPdos = matchedRxPdos.map((pdo: PDOGroup, index: number) => {
      const previousEntriesCount = matchedRxPdos
        .slice(0, index)
        .reduce((count: number, p: PDOGroup) => count + p.entries.length, 0);
      
      return {
        ...pdo,
        entryOffset: previousEntriesCount
      };
    });

    // 先获取所有符合条件的 tx_pdos
    const matchedTxPdos = slave.pdo_mapping.tx_pdos
      .filter((pdo: PDOGroup) => txPdo.includes(pdo.index));

    // 计算所有 rx entries 的总数
    const totalRxEntries = filteredRxPdos.reduce(
      (count: number, p: PDOGroup) => count + p.entries.length, 
      0
    );

    // 计算 tx_pdos 的 entryOffset
    const filteredTxPdos = matchedTxPdos.map((pdo: PDOGroup, index: number) => {
      const previousTxEntriesCount = matchedTxPdos
        .slice(0, index)
        .reduce((count: number, p: PDOGroup) => count + p.entries.length, 0);
      
      return {
        ...pdo,
        entryOffset: totalRxEntries + previousTxEntriesCount
      };
    });

    const mapping: PDOMapping = {
      rx_pdos: filteredRxPdos,
      tx_pdos: filteredTxPdos
    };

    console.log(`从站 ${slaveIndex} 过滤后的 PDO Mapping:`, JSON.stringify(mapping, null, 2));
    return mapping;
  } catch (error) {
    console.error('构建 pdo_mapping 失败:', error);
    return { rx_pdos: [], tx_pdos: [] };
  }
}

// 4. 获取过滤后的 sdos
export function getFilteredSdos(json: any, slaveIndex: number, sdos: string[]): SDO[] {
  try {
    const slave = json.slaves[slaveIndex];
    if (!slave || !slave.sdos) {
      return [];
    }

    const filteredSdos = slave.sdos.filter((sdo: SDO) => sdos.includes(sdo.index));
    console.log(`从站 ${slaveIndex} 过滤后的 SDOs:`, JSON.stringify(filteredSdos, null, 2));
    return filteredSdos;
  } catch (error) {
    console.error('获取过滤后的 sdos 失败:', error);
    return [];
  }
}

// 5. 更新从站配置
export function updateSlaveConfig(
  json: any, 
  slaveIndex: number,
  rx_pdo: string[],
  tx_pdo: string[],
  sdos: string[],
  rx_pdos: PDO[],
  tx_pdos: PDO[],
  pdo_mapping: PDOMapping
): any {
  try {
    const updatedJson = JSON.parse(JSON.stringify(json));
    if (!updatedJson.slaves[slaveIndex]) {
      throw new Error(`从站索引 ${slaveIndex} 不存在`);
    }

    const slave = updatedJson.slaves[slaveIndex];
    slave.rx_pdo = rx_pdo.join(', ');
    slave.tx_pdo = tx_pdo.join(', ');
    slave.sdos = getFilteredSdos(json, slaveIndex, sdos);
    slave.rx_pdos = rx_pdos;
    slave.tx_pdos = tx_pdos;
    slave.pdo_mapping = pdo_mapping;

    console.log(`从站 ${slaveIndex} 更新后的配置:`, JSON.stringify(slave, null, 2));
    return updatedJson;
  } catch (error) {
    console.error('更新从站配置失败:', error);
    return json;
  }
}

// 6. 处理 ENI 配置
export async function processEniConfig(xmlContent: string, eniConfig: any): Promise<any> {
  try {
    console.log('开始处理 ENI 配置...');

    // 1. 解析 XML 获取模板 JSON
    const templateJson = await parseENI(xmlContent);
    console.log('解析 XML 获取的模板 JSON:', JSON.stringify(templateJson, null, 2));

    let updatedJson = JSON.parse(JSON.stringify(templateJson));

    // 2. 处理每个从站的配置
    for (const slave of eniConfig.slaves) {
      console.log(`\n开始处理从站 ${slave.slaveIndex} 的配置...`);
      const { slaveIndex, rxPdo, txPdo, sdos } = slave;

      // 获取过滤后的 PDOs
      const rx_pdos = getFilteredRxPdos(templateJson, slaveIndex, rxPdo);
      const tx_pdos = getFilteredTxPdos(templateJson, slaveIndex, txPdo);

      // 构建 PDO mapping
      const pdo_mapping = buildPdoMapping(templateJson, slaveIndex, rxPdo, txPdo);

      // 更新从站配置
      updatedJson = updateSlaveConfig(
        updatedJson,
        slaveIndex,
        rxPdo,
        txPdo,
        sdos,
        rx_pdos,
        tx_pdos,
        pdo_mapping
      );
    }

    console.log('\nENI 配置处理完成！');
    return updatedJson;
  } catch (error) {
    console.error('处理 ENI 配置失败:', error);
    throw error;
  }
} 