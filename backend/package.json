{"name": "ethercat-webui-server", "version": "1.0.0", "private": true, "type": "module", "scripts": {"start": "node dist/server.mjs", "dev": "cross-env NODE_ENV=development tsx watch src/server.ts", "build": "tsc", "esbuild": "node build.mjs"}, "dependencies": {"@types/adm-zip": "^0.5.6", "@types/decompress": "^4.2.7", "@types/multer": "^1.4.12", "adm-zip": "^0.5.16", "archiver": "^7.0.1", "cors": "^2.8.5", "dbus-native": "^0.4.0", "decompress": "^4.2.1", "dotenv": "^16.0.3", "esbuild": "^0.25.4", "express": "^4.18.2", "express-fileupload": "^1.5.1", "moment-timezone": "^0.6.0", "multer": "^1.4.5-lts.2", "pocketbase": "^0.21.5", "sequelize": "^6.37.6", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.13.0", "xml2js": "^0.6.2"}, "devDependencies": {"@types/archiver": "^6.0.3", "@types/cors": "^2.8.17", "@types/express": "^4.17.17", "@types/express-fileupload": "^1.5.1", "@types/node": "^18.19.80", "@types/ws": "^8.5.4", "@types/xml2js": "^0.4.14", "cross-env": "^7.0.3", "tsx": "^4.7.0", "typescript": "^4.9.5"}}