#!/bin/bash

# 日志目录
LOG_DIR="/home/<USER>/logs/monitor"
# 创建日志目录（如果不存在）
mkdir -p "$LOG_DIR"

# 获取当前时间，格式 YYYYMMDDHHMM
LOG_FILE="$LOG_DIR/$(date +%Y%m%d%H%M).log"

# 执行次数
COUNT=3

echo "=== 以 $(date '+%Y-%m-%d %H:%M:%S') 作为启动检测时间 ===" >> "$LOG_FILE"

for i in $(seq 1 $COUNT); do
    echo "---- 第 $i 次执行 ethercat slaves 命令 ----" >> "$LOG_FILE"
    OUTPUT=$(ethercat slaves 2>&1)
    echo "$OUTPUT" >> "$LOG_FILE"
    if echo "$OUTPUT" | grep -q "Master"; then
        echo "结果: 通过 (包含Master)" >> "$LOG_FILE"
    else
        echo "结果: 不通过 (未包含Master)" >> "$LOG_FILE"
    fi
    echo "-----------------------------------------" >> "$LOG_FILE"
    # 你可以根据需求加sleep，比如sleep 1
done
