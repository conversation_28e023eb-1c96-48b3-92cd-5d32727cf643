<template>
  <div>
    <!-- 重启加载遮罩 -->
    <div v-if="restarting" class="restart-mask">
      <div class="restart-content">
        <a-spin size="large" />
        <div class="restart-text">正在重启平台，请稍候...</div>
        <div class="restart-tip">{{ restartCountdown }}秒后自动跳转到登录页</div>
      </div>
    </div>
    <a-layout class="app-container">
      <Sidebar 
        v-model:collapsed="collapsed" 
      />
      <a-layout>
        <a-layout-header class="header">
          <a-button
            class="trigger"
            :type="collapsed ? 'primary' : 'default'"
            @click="toggleCollapsed"
          >
            <MenuFoldOutlined v-if="!collapsed" />
            <MenuUnfoldOutlined v-else />
          </a-button>
          <div class="header-right">
            <a-button type="primary" class="header-btn" @click="showPlatformLogs">
              查看平台日志
            </a-button>
            <a-button type="primary" class="header-btn" @click="showUpdateModal">
              更新平台
            </a-button>
            <a-button type="primary" class="header-btn" @click="restartPlatform" :loading="restarting">
              重启平台
            </a-button>
            <a-dropdown>
              <a class="user-dropdown" @click.prevent>
                <UserOutlined />
                <span class="username">{{ username }}</span>
              </a>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="showChangePassword">
                    <LockOutlined />
                    <span>修改密码</span>
                  </a-menu-item>
                  <a-menu-item @click="handleLogout">
                    <LogoutOutlined />
                    <span>退出登录</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </a-layout-header>
        <a-layout-content class="content">
          <div class="main-content">
            <router-view></router-view>
          </div>
        </a-layout-content>
      </a-layout>
    </a-layout>
    <a-modal
      v-model:visible="passwordModalVisible"
      title="修改密码"
      @ok="handleChangePassword"
      :confirmLoading="changing"
    >
      <a-form :model="passwordForm" layout="vertical">
        <a-form-item 
          label="当前密码" 
          required
          :validateStatus="passwordForm.oldPasswordError ? 'error' : ''"
          :help="passwordForm.oldPasswordError"
        >
          <a-input-password v-model:value="passwordForm.oldPassword" />
        </a-form-item>
        <a-form-item 
          label="新密码" 
          required
          :validateStatus="passwordForm.passwordError ? 'error' : ''"
          :help="passwordForm.passwordError"
        >
          <a-input-password v-model:value="passwordForm.password" />
        </a-form-item>
        <a-form-item 
          label="确认新密码" 
          required
          :validateStatus="passwordForm.passwordConfirmError ? 'error' : ''"
          :help="passwordForm.passwordConfirmError"
        >
          <a-input-password v-model:value="passwordForm.passwordConfirm" />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal
      v-model:visible="logsModalVisible"
      title="平台日志"
      width="800px"
      :footer="null"
    >
      <div class="logs-container">
        <div class="logs-header">
          <a-button type="primary" @click="refreshLogs">刷新</a-button>
        </div>
        <a-textarea
          v-model:value="platformLogs"
          :rows="20"
          readonly
          class="logs-content"
        />
      </div>
    </a-modal>
    <!-- 添加更新平台对话框 -->
    <a-modal
      v-model:visible="updateModalVisible"
      title="更新平台"
      @ok="handleUpdate"
      :confirmLoading="updating"
      :maskClosable="false"
    >
      <div class="update-content">
        <a-upload
          :file-list="updateFileList"
          :before-upload="beforeUpdateUpload"
          @remove="handleUpdateRemove"
          :multiple="false"
          accept=".tar.gz"
        >
          <a-button>
            <template #icon><UploadOutlined /></template>
            选择更新包
          </a-button>
        </a-upload>
        <div class="upload-tip">请上传 .tar.gz 格式的更新包</div>
        <a-progress 
          v-if="updateProgress > 0 && updateProgress < 100"
          :percent="updateProgress" 
          :status="updateProgress < 100 ? 'active' : 'success'"
        />
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { 
  MenuFoldOutlined, 
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
  LockOutlined,
  UploadOutlined
} from '@ant-design/icons-vue';
import Sidebar from '@/components/Sidebar.vue';
import { Modal, message } from 'ant-design-vue';
import api from '@/services/api';
import type { AxiosProgressEvent } from 'axios';

interface PasswordForm {
  oldPassword: string;
  password: string;
  passwordConfirm: string;
  oldPasswordError?: string;
  passwordError?: string;
  passwordConfirmError?: string;
}

export default defineComponent({
  name: 'DefaultLayout',
  
  components: {
    Sidebar,
    MenuFoldOutlined,
    MenuUnfoldOutlined,
    UserOutlined,
    LogoutOutlined,
    LockOutlined,
    UploadOutlined
  },
  
  setup() {
    const store = useStore();
    const router = useRouter();
    const collapsed = ref(false);
    const passwordModalVisible = ref(false);
    const logsModalVisible = ref(false);
    const platformLogs = ref('');
    const changing = ref(false);
    const restarting = ref(false);
    const restartCountdown = ref(5);
    
    const username = computed(() => store.state.auth.user?.username || '');
    
    const toggleCollapsed = () => {
      collapsed.value = !collapsed.value;
    };
    
    const handleLogout = async () => {
      try {
        await store.dispatch('auth/logout');
        message.success('退出成功');
        router.push('/login');
      } catch (error) {
        message.error('退出失败');
        console.error(error);
      }
    };
    
    const passwordForm = reactive<PasswordForm>({
      oldPassword: '',
      password: '',
      passwordConfirm: '',
    });

    const showChangePassword = () => {
      passwordForm.oldPassword = '';
      passwordForm.password = '';
      passwordForm.passwordConfirm = '';
      passwordForm.oldPasswordError = '';
      passwordForm.passwordError = '';
      passwordForm.passwordConfirmError = '';
      passwordModalVisible.value = true;
    };

    const validatePasswordForm = () => {
      let isValid = true;
      
      if (!passwordForm.oldPassword) {
        passwordForm.oldPasswordError = '请输入当前密码';
        isValid = false;
      }
      
      if (!passwordForm.password) {
        passwordForm.passwordError = '请输入新密码';
        isValid = false;
      }
      
      if (!passwordForm.passwordConfirm) {
        passwordForm.passwordConfirmError = '请确认新密码';
        isValid = false;
      } else if (passwordForm.password !== passwordForm.passwordConfirm) {
        passwordForm.passwordConfirmError = '两次输入的密码不一致';
        isValid = false;
      }
      
      return isValid;
    };

    const handleChangePassword = async () => {
      if (!validatePasswordForm()) return;
      
      try {
        changing.value = true;
        await api.post('/auth/change-password', {
          oldPassword: passwordForm.oldPassword,
          newPassword: passwordForm.password,
          confirmPassword: passwordForm.passwordConfirm
        });
        
        message.success('密码修改成功，请重新登录');
        passwordModalVisible.value = false;

        // 退出登录并跳转
        await store.dispatch('auth/logout');
        router.push('/login');
      } catch (error) {
        console.error('Failed to change password:', error);
        message.error('密码修改失败');
      } finally {
        changing.value = false;
      }
    };
    
    const showPlatformLogs = async () => {
      try {
        console.log('Fetching platform logs...');
        const response = await api.get('/system/platform/logs');
        console.log('Response received:', response);
        if (response.data.success) {
          platformLogs.value = response.data.logs;
          logsModalVisible.value = true;
        } else {
          console.error('Failed to get logs:', response.data);
          message.error('获取日志失败');
        }
      } catch (error) {
        console.error('Error fetching logs:', error);
        message.error('获取日志失败');
      }
    };

    const refreshLogs = () => {
      showPlatformLogs();
    };

    const restartPlatform = async () => {
      try {
        Modal.confirm({
          title: '确认重启',
          content: '确定要重启平台吗？重启后将返回登录页面。',
          okText: '确定',
          cancelText: '取消',
          onOk: async () => {
            if (restarting.value) return;
            
            try {
              restarting.value = true;
              
              // 发送重启命令
              api.post('/system/platform/restart').catch(() => {
                // 忽略所有错误，因为服务会断开连接
              });

              // 开始倒计时
              const countdownInterval = setInterval(() => {
                restartCountdown.value--;
                if (restartCountdown.value <= 0) {
                  clearInterval(countdownInterval);
                  // 清除登录状态
                  store.commit('auth/clearAuth');
                  store.commit('auth/setSkipAuthCheck', true);
                  // 跳转到登录页
                  window.location.pathname = '/login';
                }
              }, 1000);
            } catch (error) {
              console.error('Error restarting platform:', error);
              message.error('平台重启失败');
              restarting.value = false;
            }
          }
        });
      } catch (error) {
        console.error('Error showing confirmation:', error);
        message.error('平台重启失败');
      }
    };

    const updateModalVisible = ref(false);
    const updating = ref(false);
    const updateFileList = ref<any[]>([]);
    const updateProgress = ref(0);
    const updateFile = ref<File | null>(null);

    // 显示更新对话框
    const showUpdateModal = () => {
      updateModalVisible.value = true;
      updateFileList.value = [];
      updateFile.value = null;
      updateProgress.value = 0;
    };

    // 处理文件上传前的验证
    const beforeUpdateUpload = (file: File) => {
      // 检查文件类型是否为 .tar.gz
      const isTarGz = file.name.endsWith('.tar.gz');
      if (!isTarGz) {
        message.error('请上传 .tar.gz 格式的更新包');
        return false;
      }
      updateFile.value = file;
      updateFileList.value = [file];
      return false;
    };

    // 处理文件移除
    const handleUpdateRemove = () => {
      updateFile.value = null;
      updateFileList.value = [];
    };

    // 处理更新
    const handleUpdate = async () => {
      if (!updateFile.value) {
        message.error('请选择更新包');
        return;
      }

      try {
        updating.value = true;
        updateProgress.value = 0;

        const formData = new FormData();
        formData.append('package', updateFile.value);

        await api.post('/system/platform/update', formData, {
          onUploadProgress: (progressEvent: AxiosProgressEvent) => {
            if (progressEvent.total) {
              updateProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            }
          }
        });

        message.success('更新成功，系统将在 5 秒后重启');
        updateModalVisible.value = false;

        // 等待 5 秒后重启平台
        setTimeout(() => {
          restartPlatform();
        }, 5000);

      } catch (error: any) {
        console.error('Update failed:', error);
        message.error(error.response?.data?.message || '更新失败');
      } finally {
        updating.value = false;
      }
    };

    return {
      collapsed,
      passwordModalVisible,
      logsModalVisible,
      platformLogs,
      changing,
      restarting,
      restartCountdown,
      passwordForm,
      username,
      toggleCollapsed,
      showChangePassword,
      handleChangePassword,
      handleLogout,
      showPlatformLogs,
      refreshLogs,
      restartPlatform,
      updateModalVisible,
      updating,
      updateFileList,
      updateProgress,
      showUpdateModal,
      beforeUpdateUpload,
      handleUpdateRemove,
      handleUpdate,
    };
  }
});
</script>

<style lang="less" scoped>
@import '../styles/variables.less';

.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #F6F8FC 0%, #E9EEF6 100%);
  
  .header {
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    margin: 16px 16px 0;
    border-radius: 16px;
    height: 64px;
    
    .trigger {
      font-size: 18px;
      cursor: pointer;
      transition: color 0.3s;
    }
    
    .header-right {
      float: right;
      height: 100%;
      display: flex;
      align-items: center;
      
      .header-btn {
        margin-right: 16px;
      }
      
      .user-dropdown {
        color: @text-color;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 8px;
        transition: all 0.3s;
        
        &:hover {
          background: rgba(0, 0, 0, 0.04);
        }
        
        .username {
          margin-left: 8px;
        }
      }
    }
  }
  
  .content {
    overflow-y: auto;
    padding: 24px;
    
    .main-content {
      min-height: calc(100vh - 112px);
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(20px);
      border-radius: 16px;
      padding: 24px;
    }
  }
}

.logs-container {
  .logs-header {
    margin-bottom: 16px;
    text-align: right;
  }

  .logs-content {
    font-family: monospace;
    white-space: pre-wrap;
  }
}

.restart-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.65);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.restart-content {
  text-align: center;
  color: white;
}

.restart-text {
  margin-top: 16px;
  font-size: 16px;
}

.restart-tip {
  margin-top: 8px;
  font-size: 14px;
  opacity: 0.8;
}

.update-content {
  .upload-tip {
    color: #666;
    font-size: 12px;
    margin-top: 4px;
  }
}
</style>