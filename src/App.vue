<template>
  <router-view></router-view>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue';

export default defineComponent({
  name: 'App'
});

onMounted(() => {
  const version = process.env.VUE_APP_VERSION || 'dev';
  document.title = `边缘控制器平台 ${version} - 飞仙软件`;
});
</script>

<style lang="less">
@import './styles/variables.less';
@import './styles/global.less';

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: auto;
}

#app {
  min-height: 100vh;
  overflow-y: auto;
  background: linear-gradient(135deg, #F6F8FC 0%, #E9EEF6 100%);
}

// 美化滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  
  &:hover {
    background: rgba(0, 0, 0, 0.3);
  }
}
</style> 