import { Request, Response, NextFunction } from 'express';

export class ApiError extends <PERSON>rror {
  constructor(
    public statusCode: number,
    message: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export function errorHandler(err: Error, req: Request, res: Response, next: NextFunction) {
  if (err instanceof ApiError) {
    res.status(err.statusCode).json({
      status: err.statusCode,
      message: err.message
    });
    return;
  }

  console.error(err);
  res.status(500).json({
    status: 500,
    message: '服务器内部错误'
  });
} 