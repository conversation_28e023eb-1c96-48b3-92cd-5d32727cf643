<template>
  <div class="page-loading">
    <div class="loading-content">
      <a-spin size="large" />
      <div class="loading-text">{{ text }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'PageLoading',
  
  props: {
    text: {
      type: String,
      default: '加载中...'
    }
  }
});
</script>

<style lang="less" scoped>
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .loading-content {
    text-align: center;
    
    .loading-text {
      margin-top: 16px;
      color: @text-color-secondary;
    }
  }
}
</style> 