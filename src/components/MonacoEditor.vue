<template>
  <div ref="editorContainer" class="monaco-editor-container"></div>
</template>

<script lang="ts">
import { defineComponent, onMounted, onBeforeUnmount, watch, ref } from 'vue';
import * as monaco from 'monaco-editor';

export default defineComponent({
  name: 'MonacoEditor',
  
  props: {
    value: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'javascript'
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },

  emits: ['update:value'],

  setup(props, { emit }) {
    const editorContainer = ref<HTMLElement | null>(null);
    let editor: monaco.editor.IStandaloneCodeEditor | null = null;

    onMounted(() => {
      if (editorContainer.value) {
        editor = monaco.editor.create(editorContainer.value, {
          value: props.value,
          language: props.language,
          theme: 'vs',
          ...props.options
        });

        editor.onDidChangeModelContent(() => {
          const value = editor?.getValue();
          emit('update:value', value);
        });
      }
    });

    watch(() => props.value, (newValue) => {
      if (editor && newValue !== editor.getValue()) {
        editor.setValue(newValue);
      }
    });

    onBeforeUnmount(() => {
      if (editor) {
        editor.dispose();
      }
    });

    return {
      editorContainer
    };
  }
});
</script>

<style scoped>
.monaco-editor-container {
  width: 100%;
  height: 100%;
}
</style> 