<template>
  <div class="error-message">
    <div class="error-content">
      <WarningOutlined class="error-icon" />
      <h2>{{ title }}</h2>
      <p>{{ message }}</p>
      <a-button type="primary" @click="retry">
        重试
      </a-button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { WarningOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'ErrorMessage',
  
  components: {
    WarningOutlined
  },
  
  props: {
    title: {
      type: String,
      default: '出错了'
    },
    message: {
      type: String,
      default: '加载失败，请稍后重试'
    }
  },
  
  emits: ['retry'],
  
  setup(props, { emit }) {
    const retry = () => {
      emit('retry');
    };
    
    return {
      retry
    };
  }
});
</script>

<style lang="less" scoped>
.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  
  .error-content {
    text-align: center;
    
    .error-icon {
      font-size: 48px;
      color: @warning-color;
    }
    
    h2 {
      margin: 16px 0;
      color: @heading-color;
    }
    
    p {
      color: @text-color-secondary;
      margin-bottom: 24px;
    }
  }
}
</style> 