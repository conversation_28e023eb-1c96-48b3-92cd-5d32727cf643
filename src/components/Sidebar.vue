<template>
  <a-layout-sider
    :collapsed="collapsed"
    class="sidebar"
    :trigger="null"
    collapsible
  >
    <div class="logo">
      <span :class="{ 'logo-text': true, 'collapsed': collapsed }">
  <img src="@/assets/logo.png" alt="logo" />
  <span v-if="!collapsed">边缘控制器</span>
</span>
    </div>
    
    <div class="menu-container">
      <a-menu
        v-model:selectedKeys="selectedKeys"
        theme="light"
        mode="inline"
        class="custom-menu"
      >
        <a-menu-item v-if="menuSettings.dashboard" key="dashboard" @click="navigate('/')">
          <template #icon><DashboardOutlined /></template>
          <span>仪表盘</span>
        </a-menu-item>
        
        <a-menu-item v-if="menuSettings.ethercat" key="ethercat" @click="navigate('/ethercat')">
          <template #icon><ApiOutlined /></template>
          <span>EtherCAT状态</span>
        </a-menu-item>
        
        <a-menu-item v-if="menuSettings.programs" key="programs" @click="navigate('/programs')">
          <template #icon><CodeOutlined /></template>
          <span>程序管理</span>
        </a-menu-item>
        
        <a-menu-item v-if="menuSettings.users" key="users" @click="navigate('/users')">
          <template #icon><UserOutlined /></template>
          <span>用户管理</span>
        </a-menu-item>
        
        <a-menu-item v-if="menuSettings.settings" key="settings" @click="navigate('/settings')">
          <template #icon><SettingOutlined /></template>
          <span>系统设置</span>
        </a-menu-item>
      </a-menu>
    </div>

    <div class="settings-button">
      <a-button
        type="text"
        @click="showSettingsModal"
        :class="{ collapsed }"
      >
        <!-- <template #icon><SettingOutlined /></template> -->
        <span v-if="!collapsed"></span>
      </a-button>
    </div>

    <a-modal
      v-model:visible="settingsVisible"
      title="菜单设置"
      @ok="saveSettings"
      :confirmLoading="saving"
    >
      <a-form :model="tempSettings" layout="vertical">
        <a-form-item label="显示菜单项">
          <a-checkbox-group v-model:value="selectedMenus">
            <a-checkbox value="dashboard">仪表盘</a-checkbox>
            <a-checkbox value="ethercat">EtherCAT状态</a-checkbox>
            <a-checkbox value="programs">程序管理</a-checkbox>
            <a-checkbox value="users">用户管理</a-checkbox>
            <a-checkbox value="settings">系统设置</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <div class="version-info">
      <span>{{ version }}</span>
    </div>
  </a-layout-sider>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  DashboardOutlined,
  ApiOutlined,
  CodeOutlined,
  UserOutlined,
  SettingOutlined
} from '@ant-design/icons-vue';
import { settingsApi, MenuSettings } from '@/services/api';

export default defineComponent({
  name: 'SideNavigation',
  
  components: {
    DashboardOutlined,
    ApiOutlined,
    CodeOutlined,
    UserOutlined,
    SettingOutlined
  },
  
  props: {
    collapsed: {
      type: Boolean,
      required: true
    }
  },
  
  setup() {
    const router = useRouter();
    const route = useRoute();
    const selectedKeys = ref<string[]>(['dashboard']);
    const settingsVisible = ref(false);
    const saving = ref(false);
    const menuSettings = ref<MenuSettings>({
      dashboard: true,
      ethercat: true,
      programs: true,
      users: true,
      settings: true
    });
    const selectedMenus = ref<string[]>([]);
    
    // 根据路由路径获取对应的菜单 key
    const getMenuKey = (path: string) => {
      const pathMap: Record<string, string> = {
        '/': 'dashboard',
        '/ethercat': 'ethercat',
        '/programs': 'programs',
        '/users': 'users',
        '/settings': 'settings'
      };
      return pathMap[path] || 'dashboard';
    };

    // 监听路由变化
    watch(
      () => route.path,
      (path) => {
        selectedKeys.value = [getMenuKey(path)];
      },
      { immediate: true }  // 立即执行一次
    );
    
    const navigate = (path: string) => {
      router.push(path);
    };
    
    // 加载设置
    const loadSettings = async () => {
      try {
        const settings = await settingsApi.getMenuSettings();
        menuSettings.value = settings;
        selectedMenus.value = Object.entries(settings)
          .filter(([_, value]) => value)
          .map(([key]) => key);
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    };

    // 显示设置对话框
    const showSettingsModal = () => {
      selectedMenus.value = Object.entries(menuSettings.value)
        .filter(([_, value]) => value)
        .map(([key]) => key);
      settingsVisible.value = true;
    };

    // 保存设置
    const saveSettings = async () => {
      try {
        saving.value = true;
        const newSettings: MenuSettings = {
          dashboard: selectedMenus.value.includes('dashboard'),
          ethercat: selectedMenus.value.includes('ethercat'),
          programs: selectedMenus.value.includes('programs'),
          users: selectedMenus.value.includes('users'),
          settings: selectedMenus.value.includes('settings')
        };

        await settingsApi.updateMenuSettings(newSettings);
        menuSettings.value = newSettings;
        settingsVisible.value = false;
        message.success('设置已保存');
      } catch (error) {
        console.error('Failed to save settings:', error);
        message.error('保存设置失败');
      } finally {
        saving.value = false;
      }
    };

    // 添加版本信息
    const version = ref(`${process.env.VUE_APP_VERSION || 'dev'}-${process.env.VUE_APP_GIT_SHA || 'unknown'}`);

    // 初始加载设置
    loadSettings();

    return {
      selectedKeys,
      navigate,
      settingsVisible,
      saving,
      menuSettings,
      selectedMenus,
      showSettingsModal,
      saveSettings,
      version,
    };
  }
});
</script>

<style lang="less" scoped>
@import '../styles/variables.less';

.sidebar {
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  margin: 16px 0 16px 16px;
  border-radius: 16px !important;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
  
  .logo {
    height: 64px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    img {
      height: 32px;
      width: auto;
    }
  }
  
  .menu-container {
    padding: 0 8px;
  }
  
  :deep(.ant-menu) {
    background: transparent !important;
    border-right: none;
    
    .ant-menu-item {
      margin: 8px 0;
      height: 44px;
      line-height: 44px;
      border-radius: 12px;
      transition: all 0.3s;
      
      &:hover {
        background: rgba(0, 0, 0, 0.04);
      }
      
      &.ant-menu-item-selected {
        background: rgba(24, 144, 255, 0.1);
        backdrop-filter: blur(10px);
        color: #1890ff;
        
        &::after {
          display: none;
        }
        
        .anticon {
          color: #1890ff;
        }
      }
      
      .anticon {
        font-size: 18px;
      }
    }
  }
}

// 修改滚动条样式
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  
  &:hover {
    background: rgba(0, 0, 0, 0.2);
  }
}

.settings-button {
  position: absolute;
  bottom: 16px;
  left: 0;
  right: 0;
  padding: 0 16px;
  
  .ant-btn {
    width: 100%;
    text-align: left;
    height: 44px;
    border-radius: 12px;
    
    &.collapsed {
      text-align: center;
    }
    
    &:hover {
      background: rgba(0, 0, 0, 0.04);
    }
  }
}

.version-info {
  position: absolute;
  bottom: 8px;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  padding: 8px;
}
</style> 