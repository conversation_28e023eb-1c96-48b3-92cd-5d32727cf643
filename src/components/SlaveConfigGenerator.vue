<template>
  <a-modal :visible="visible" title="从站JSON模板生成器" width="90%" :footer="null"
    @update:visible="$emit('update:visible', $event)">
    <div class="generator-container">
      <!-- 左侧：当前配置 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>当前从站配置</h3>
          <a-space>
            <a-dropdown :trigger="['click']" :visible="dcDropdownVisible"
              @visibleChange="handleDcDropdownVisibleChange">
              <a-button>
                <template #icon>
                  <SettingOutlined />
                </template>
                全局DC配置
              </a-button>
              <template #overlay>
                <a-menu @click.stop>
                  <a-menu-item @click.stop>
                    <div class="dc-config-menu" @click.stop>
                      <div class="dc-switch">
                        <a-switch v-model:checked="globalDcEnabled" checked-children="DC全局启用"
                          un-checked-children="DC全局禁用" @change="toggleGlobalDc" />
                      </div>
                      <template v-if="globalDcEnabled">
                        <a-form layout="vertical" @click.stop>
                          <a-form-item label="Assign/Activate">
                            <a-input v-model:value="globalDcConfig.assign_activate" placeholder="0x0300"
                              @change="updateGlobalDcConfig" />
                          </a-form-item>
                          <a-form-item label="Sync0 Cycle">
                            <a-input v-model:value="globalDcConfig.sync0_cycle" placeholder="留空使用 PERIOD_NS"
                              @change="updateGlobalDcConfig" />
                          </a-form-item>
                          <a-form-item label="Sync0 Shift">
                            <a-input v-model:value="globalDcConfig.sync0_shift" placeholder="留空使用 PERIOD_NS/2"
                              @change="updateGlobalDcConfig" />
                          </a-form-item>
                          <a-form-item label="Sync1 Cycle">
                            <a-input v-model:value="globalDcConfig.sync1_cycle" placeholder="留空使用 0"
                              @change="updateGlobalDcConfig" />
                          </a-form-item>
                          <a-form-item label="Sync1 Shift">
                            <a-input v-model:value="globalDcConfig.sync1_shift" placeholder="留空使用 0"
                              @change="updateGlobalDcConfig" />
                          </a-form-item>
                        </a-form>
                      </template>
                    </div>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <!-- 添加工具箱按钮 -->
            <a-button @click="showToolboxModal" v-show="false">
              <template #icon>
                <ToolOutlined />
              </template>
              工具箱
            </a-button>
            <a-button type="primary" @click="addNewSlave">添加从站</a-button>
            <a-button @click="previewTopology">预览拓扑图</a-button>
            <a-button @click="deduplicateSlaves">从站去重</a-button>
            <a-button @click="syncAllPdos">一键同步模板和名字注释</a-button>
            <a-upload :before-upload="handleENIUpload" :show-upload-list="false">
              <a-button :loading="uploading">
                <template #icon><upload-outlined /></template>
                上传ENI文件
              </a-button>
            </a-upload>
            <a-switch v-model:checked="isJsonView" checked-children="JSON" un-checked-children="列表" />
          </a-space>
        </div>
        <div v-if="isJsonView" class="json-editor">
          <MonacoEditor v-model:value="currentConfig" language="json" :options="editorOptions" />
        </div>
        <div v-else class="list-view">
          <div v-for="(slave, slaveIndex) in currentSlaves" :key="`slave_${slaveIndex}`" class="slave-section">
            <a-collapse v-model:activeKey="activeSlaveKeys">
              <a-collapse-panel :key="`slave_${slaveIndex}`">
                <template #header>
                  <div class="slave-header">
                    <span>从站 {{ slaveIndex }}: {{ slave.name }}</span>
                    <a-space>
                      <a-button type="link" @click.stop="cloneSlave(slaveIndex)">
                        克隆
                      </a-button>
                      <a-button type="link" @click.stop="moveSlave(slaveIndex, 'up')" :disabled="slaveIndex === 0">
                        上移
                      </a-button>
                      <a-button type="link" @click.stop="moveSlave(slaveIndex, 'down')"
                        :disabled="slaveIndex === currentSlaves.length - 1">
                        下移
                      </a-button>
                      <a-button type="link" danger @click.stop="removeSlave(slaveIndex)">
                        删除
                      </a-button>
                    </a-space>
                  </div>
                </template>
                <a-form layout="vertical">
                  <a-form-item label="名称">
                    <a-input v-model:value="slave.name" @change="updateJson" />
                  </a-form-item>
                  <a-form-item label="Vendor ID">
                    <a-input v-model:value="slave.vid" @change="updateJson" />
                  </a-form-item>
                  <a-form-item label="Product ID">
                    <a-input v-model:value="slave.pid" @change="updateJson" />
                  </a-form-item>
                  <a-form-item label="RxPDO Index">
                    <a-input v-model:value="slave.rx_pdo" @change="updateJson" />
                  </a-form-item>
                  <a-form-item label="TxPDO Index">
                    <a-input v-model:value="slave.tx_pdo" @change="updateJson" />
                  </a-form-item>
                  <a-form-item label="分布式时钟(DC)">
                    <a-switch :checked="isDcEnabled(slaveIndex)" checked-children="启用" un-checked-children="禁用"
                      @change="(checked) => toggleDcConfig(slaveIndex, checked)" />
                  </a-form-item>
                  <template v-if="isDcEnabled(slaveIndex)">
                    <a-form-item label="Assign/Activate">
                      <a-input v-model:value="slave.dc_config.assign_activate" placeholder="0x0300"
                        @change="updateJson" />
                    </a-form-item>
                    <a-form-item label="Sync0 Cycle">
                      <a-input v-model:value="slave.dc_config.sync0_cycle" placeholder="留空使用 PERIOD_NS"
                        @change="updateJson" />
                    </a-form-item>
                    <a-form-item label="Sync0 Shift">
                      <a-input v-model:value="slave.dc_config.sync0_shift" placeholder="留空使用 PERIOD_NS/2"
                        @change="updateJson" />
                    </a-form-item>
                    <a-form-item label="Sync1 Cycle">
                      <a-input v-model:value="slave.dc_config.sync1_cycle" placeholder="留空使用 0" @change="updateJson" />
                    </a-form-item>
                    <a-form-item label="Sync1 Shift">
                      <a-input v-model:value="slave.dc_config.sync1_shift" placeholder="留空使用 0" @change="updateJson" />
                    </a-form-item>
                  </template>
                </a-form>
              </a-collapse-panel>
            </a-collapse>
            
            <div class="pdo-lists">
              <div class="pdo-section">
                <a-collapse :default-active-key="['1']">
                  <a-collapse-panel key="1">
                    <template #header>
                      <div class="pdo-section-header">
                        <span>RxPDOs</span>
                        <span class="pdo-count">({{ slave.rx_pdos?.length || 0 }})</span>
                        <a-button type="link" size="small" @click.stop="clearPdos(slaveIndex, 'rx')">
                          清空
                        </a-button>
                        <a-button type="link" size="small" @click.stop="addPdoEntry(slaveIndex, 'rx')">
                          添加
                        </a-button>
                      </div>
                    </template>
                    <a-collapse>
                      <a-collapse-panel v-for="(pdo, pdoIndex) in slave.rx_pdos" :key="`rx_${pdoIndex}`">
                        <template #header>
                          <div class="pdo-header">
                            <span class="pdo-name">{{ pdo.name }}</span>
                            <a-tag color="blue">{{ pdo.index }}:{{ pdo.subindex }}</a-tag>
                            <a-tag color="green">{{ pdo.comment || '无注释' }}</a-tag>
                            <a-button type="link" size="small"
                              @click.stop="syncPdoInfo(slaveIndex, 'rx', pdoIndex, pdo)" title="从模板同步名称和注释">
                              <SyncOutlined />
                            </a-button>
                          </div>
                        </template>
                        <a-form layout="vertical">
                          <a-form-item label="名称">
                            <a-input v-model:value="pdo.name" @change="updateJson" />
                          </a-form-item>
                          <a-form-item label="Index">
                            <a-input v-model:value="pdo.index" @change="updateJson" />
                          </a-form-item>
                          <a-form-item label="SubIndex">
                            <a-input v-model:value="pdo.subindex" @change="updateJson" />
                          </a-form-item>
                          <a-form-item label="类型">
                            <a-select v-model:value="pdo.type" @change="updateJson">
                              <a-select-option value="bool">bool</a-select-option>
                              <a-select-option value="uint8">uint8</a-select-option>
                              <a-select-option value="int8">int8</a-select-option>
                              <a-select-option value="uint16">uint16</a-select-option>
                              <a-select-option value="int16">int16</a-select-option>
                              <a-select-option value="uint32">uint32</a-select-option>
                              <a-select-option value="int32">int32</a-select-option>
                              <a-select-option value="uint64">uint64</a-select-option>
                              <a-select-option value="int64">int64</a-select-option>
                              <a-select-option value="double">double</a-select-option>
                            </a-select>
                          </a-form-item>
                          <a-form-item label="BitLen">
                            <a-input-number v-model:value="pdo.bitlen" @change="updateJson" />
                          </a-form-item>
                          <a-form-item label="注释">
                            <a-input v-model:value="pdo.comment" @change="updateJson" />
                          </a-form-item>
                        </a-form>
                        <template #extra>
                          <a-button type="link" danger @click.stop="removePdo(slaveIndex, 'rx', pdoIndex)">
                            删除
                          </a-button>
                        </template>
                      </a-collapse-panel>
                    </a-collapse>
                  </a-collapse-panel>
                </a-collapse>
              </div>
              
              <div class="pdo-section">
                <a-collapse :default-active-key="['1']">
                  <a-collapse-panel key="1">
                    <template #header>
                      <div class="pdo-section-header">
                        <span>TxPDOs</span>
                        <span class="pdo-count">({{ slave.tx_pdos?.length || 0 }})</span>
                        <a-button type="link" size="small" @click.stop="clearPdos(slaveIndex, 'tx')">
                          清空
                        </a-button>
                        <a-button type="link" size="small" @click.stop="addPdoEntry(slaveIndex, 'tx')">
                          添加
                        </a-button>
                      </div>
                    </template>
                    <a-collapse>
                      <a-collapse-panel v-for="(pdo, pdoIndex) in slave.tx_pdos" :key="`tx_${pdoIndex}`">
                        <template #header>
                          <div class="pdo-header">
                            <span class="pdo-name">{{ pdo.name }}</span>
                            <a-tag color="blue">{{ pdo.index }}:{{ pdo.subindex }}</a-tag>
                            <a-tag color="green">{{ pdo.comment || '无注释' }}</a-tag>
                            <a-button type="link" size="small"
                              @click.stop="syncPdoInfo(slaveIndex, 'tx', pdoIndex, pdo)" title="从模板同步名称和注释">
                              <SyncOutlined />
                            </a-button>
                          </div>
                        </template>
                        <a-form layout="vertical">
                          <a-form-item label="名称">
                            <a-input v-model:value="pdo.name" @change="updateJson" />
                          </a-form-item>
                          <a-form-item label="Index">
                            <a-input v-model:value="pdo.index" @change="updateJson" />
                          </a-form-item>
                          <a-form-item label="SubIndex">
                            <a-input v-model:value="pdo.subindex" @change="updateJson" />
                          </a-form-item>
                          <a-form-item label="类型">
                            <a-select v-model:value="pdo.type" @change="updateJson">
                              <a-select-option value="bool">bool</a-select-option>
                              <a-select-option value="uint8">uint8</a-select-option>
                              <a-select-option value="int8">int8</a-select-option>
                              <a-select-option value="uint16">uint16</a-select-option>
                              <a-select-option value="int16">int16</a-select-option>
                              <a-select-option value="uint32">uint32</a-select-option>
                              <a-select-option value="int32">int32</a-select-option>
                              <a-select-option value="uint64">uint64</a-select-option>
                              <a-select-option value="int64">int64</a-select-option>
                              <a-select-option value="double">double</a-select-option>
                            </a-select>
                          </a-form-item>
                          <a-form-item label="BitLen">
                            <a-input-number v-model:value="pdo.bitlen" @change="updateJson" />
                          </a-form-item>
                          <a-form-item label="注释">
                            <a-input v-model:value="pdo.comment" @change="updateJson" />
                          </a-form-item>
                        </a-form>
                        <template #extra>
                          <a-button type="link" danger @click.stop="removePdo(slaveIndex, 'tx', pdoIndex)">
                            删除
                          </a-button>
                        </template>
                      </a-collapse-panel>
                    </a-collapse>
                  </a-collapse-panel>
                </a-collapse>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：模板配置 -->
      <div class="right-panel">
        <div class="panel-header">
          <h3>模板配置</h3>
          <a-input-search v-model:value="searchText" placeholder="搜索 PDO..." style="width: 100%" @search="onSearch" />
        </div>
        <div class="template-list">
          <div v-for="(slave, slaveIndex) in filteredTemplateConfig" :key="`template_${slaveIndex}`"
            class="template-section">
            <h4>{{ slave.name }}</h4>
            <div class="pdo-group">
              <h5>RxPDOs</h5>
              <a-list size="small">
                <a-list-item v-for="pdo in slave.rx_pdos" :key="pdo.index" class="compact-list-item">
                  <div class="pdo-item">
                    <div class="pdo-info">
                      <span class="pdo-name">{{ pdo.name }}</span>
                      <div class="pdo-tags">
                        <a-tag color="blue">{{ pdo.index }}:{{ pdo.subindex }}</a-tag>
                        <a-tooltip :title="pdo.comment">
                          <a-tag color="green" class="truncate-tag">{{ pdo.comment }}</a-tag>
                        </a-tooltip>
                      </div>
                    </div>
                    <div class="pdo-actions">
                      <a-tooltip :title="`类型: ${pdo.type}, 位长度: ${pdo.bitlen}`">
                        <InfoCircleOutlined />
                      </a-tooltip>
                      <a-select size="small" style="width: 120px; margin-left: 8px" placeholder="添加到.."
                        @change="(target: string | number) => addPdo(pdo, target)">
                        <a-select-option value="all">添加到所有从站</a-select-option>
                        <a-select-option v-for="(slave, idx) in currentSlaves" :key="idx" :value="idx">
                          从站 {{ idx }}
                        </a-select-option>
                      </a-select>
                    </div>
                  </div>
                </a-list-item>
              </a-list>
            </div>
            <div class="pdo-group">
              <h5>TxPDOs</h5>
              <a-list size="small">
                <a-list-item v-for="pdo in slave.tx_pdos" :key="pdo.index" class="compact-list-item">
                  <div class="pdo-item">
                    <div class="pdo-info">
                      <span class="pdo-name">{{ pdo.name }}</span>
                      <div class="pdo-tags">
                        <a-tag color="blue">{{ pdo.index }}:{{ pdo.subindex }}</a-tag>
                        <a-tooltip :title="pdo.comment">
                          <a-tag color="green" class="truncate-tag">{{ pdo.comment }}</a-tag>
                        </a-tooltip>
                      </div>
                    </div>
                    <div class="pdo-actions">
                      <a-tooltip :title="`类型: ${pdo.type}, 位长度: ${pdo.bitlen}`">
                        <InfoCircleOutlined />
                      </a-tooltip>
                      <a-select size="small" style="width: 120px; margin-left: 8px" placeholder="选择从站"
                        @change="(target: string | number) => addPdo(pdo, target)">
                        <a-select-option value="all">添加到所有从站</a-select-option>
                        <a-select-option v-for="(slave, idx) in currentSlaves" :key="idx" :value="idx">
                          从站 {{ idx }}
                        </a-select-option>
                      </a-select>
                    </div>
                  </div>
                </a-list-item>
              </a-list>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <a-button type="primary" @click="downloadConfig">下载JSON配置</a-button>
      <a-button type="primary" @click="copyConfig">复制JSON配置</a-button>
    </div>

    <!-- 从站位置选择对话框 -->
    <a-modal v-model:visible="insertPositionVisible" title="选择从站插入位置" @ok="handleInsertPosition" okText="确定"
      cancelText="取消">
      <a-form layout="vertical">
        <a-form-item label="插入位置">
          <a-input-number v-model:value="insertPosition" :min="0" :max="currentSlaves.length" style="width: 100%" />
        </a-form-item>
        <div class="position-hint">
          <p>当前从站数量: {{ currentSlaves.length }}</p>
          <p>位置范围: 0 ~ {{ currentSlaves.length }}</p>
          <p>说明: 新从站将被插入到指定位置，原位置及之后的从站索引将自动更新</p>
        </div>
      </a-form>
    </a-modal>

    <!-- 克隆从站对话框 -->
    <a-modal
      v-model:visible="cloneModalVisible"
      title="克隆从站"
      @ok="handleCloneConfirm"
      okTest="确定"
      cancleText="取消"
    >
      <a-form layout="vertical">
        <a-form-item label="克隆数量">
          <a-input-number v-model:value="cloneCount" :min="1" :max="100" style="width: 100%" />
        </a-form-item>
        
        <div class="clone-hint">
          <p>请输入要创建的克隆数量</p>
          <p>克隆的从站将被添加到原从站之后</p>
          <p>克隆的从站名称将自动添加后缀</p>
        </div>
      </a-form>
    </a-modal>

    <!-- 添加编辑对话框 -->
    <a-modal v-model:visible="editModalVisible" title="编辑从站配置" width="1200px" @ok="handleEditOk"
      @cancel="handleEditCancel">
      <div class="edit-container">
        <!-- 左侧从站树 -->
        <div class="slave-tree">
          <a-tree v-model:selectedKeys="selectedSlaveKeys" :treeData="slaveTreeData" @select="handleSlaveSelect" />
        </div>

        <!-- 中间表格区域 -->
        <div class="table-container">
          <!-- RX PDO 表格 -->
          <div class="table-section">
            <div class="table-header">
              <h3>RX PDO 配置</h3>
              <a-button size="small" @click="handleRxPdoSelectAll">
                {{ isAllRxPdoSelected ? '取消全选' : '全选' }}
              </a-button>
            </div>
            <a-table :dataSource="rxPdoTableData" :columns="rxPdoColumns" size="small" :pagination="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'select'">
                  <a-checkbox v-model:checked="record.selected" :disabled="isRxPdoDisabled(record)"
                    @change="() => handleRxPdoChange(record)" />
                </template>
                <template v-else-if="column.key === 'info'">
                  <a-tooltip 
                    placement="right" 
                    :overlayStyle="{ maxWidth: '500px' }" 
                    :overlayInnerStyle="{ padding: '12px 16px' }"
                    overlayClassName="pdo-tooltip-container"
                  >
                    <template #title>
                      <div v-html="getPdoDetailInfo(record.index, 'rx')"></div>
                    </template>
                    <InfoCircleOutlined style="cursor: pointer; color: #1890ff;" />
                  </a-tooltip>
                </template>
              </template>
            </a-table>
          </div>

          <!-- TX PDO 表格 -->
          <div class="table-section">
            <div class="table-header">
              <h3>TX PDO 配置</h3>
              <a-button size="small" @click="handleTxPdoSelectAll">
                {{ isAllTxPdoSelected ? '取消全选' : '全选' }}
              </a-button>
            </div>
            <a-table :dataSource="txPdoTableData" :columns="txPdoColumns" size="small" :pagination="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'select'">
                  <a-checkbox v-model:checked="record.selected" @change="() => handleTxPdoChange(record)" />
                </template>
                <template v-else-if="column.key === 'info'">
                  <a-tooltip 
                    placement="right" 
                    :overlayStyle="{ maxWidth: '500px' }" 
                    :overlayInnerStyle="{ padding: '12px 16px' }"
                    overlayClassName="pdo-tooltip-container"
                  >
                    <template #title>
                      <div v-html="getPdoDetailInfo(record.index, 'tx')"></div>
                    </template>
                    <InfoCircleOutlined style="cursor: pointer; color: #1890ff;" />
                  </a-tooltip>
                </template>
              </template>
            </a-table>
          </div>

          <!-- SDOs 表格 -->
          <div class="table-section">
            <div class="table-header">
              <h3>SDO 配置</h3>
              <a-button size="small" @click="handleSdoSelectAll">
                {{ isAllSdoSelected ? '取消全选' : '全选' }}
              </a-button>
            </div>
            <a-table :dataSource="sdosTableData" :columns="sdosColumns" size="small" :pagination="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'select'">
                  <a-checkbox v-model:checked="record.selected" @change="() => handleSdoChange(record)" />
                </template>
              </template>
            </a-table>
          </div>
        </div>

      </div>
    </a-modal>

    <!-- 工具箱对话框 -->
    <a-modal v-model:visible="toolboxModalVisible" title="工具箱" @ok="handleToolboxOk" @cancel="handleToolboxCancel">
      <a-form layout="vertical">
        <a-form-item label="克隆从站数量">
          <a-input-number v-model:value="toolboxSlaveCount" :min="1" :max="100" style="width: 100%" />
        </a-form-item>
        <a-button type="primary" block @click="handleQuickServoConfig">
          快速配置（伺服）
        </a-button>
      </a-form>
      <div class="toolbox-hint">
        <p>快速配置（伺服）将执行以下操作：</p>
        <ol>
          <li>触发从站去重</li>
          <li>清空剩余从站配置的RxPDOs和TxPDOs</li>
          <li>添加常用控制位到所有从站</li>
          <li>启用全局DC</li>
          <li>克隆出指定数量的从站配置</li>
        </ol>
      </div>
    </a-modal>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, onMounted, h } from 'vue';
import { message, Modal, Upload } from 'ant-design-vue';
import type { UploadChangeParam } from 'ant-design-vue';
import { InfoCircleOutlined, SyncOutlined, SettingOutlined, UploadOutlined, ToolOutlined } from '@ant-design/icons-vue';
import MonacoEditor from '@/components/MonacoEditor.vue';
import { ethercatApi } from '@/services/api';
import axios from 'axios';
import { instance } from '@viz-js/viz';
import { EniConfig } from '@/types';

interface Slave {
  index: number;
  name: string;
  vid: string;
  pid: string;
  rx_pdo: string;
  tx_pdo: string;
  rx_count: number;
  tx_count: number;
}

interface PDO {
  name: string;
  index: string;
  subindex: string;
  type: string;
  bitlen: number;
  comment?: string;
}

interface TableRecord {
  index: string;
  name: string;
  selected: boolean;
  value?: string;
}

interface SlaveConfig {
  slaves: Array<{
    index: string;
    name: string;
    rx_pdo: string;
    tx_pdo: string;
    sdos: Array<{
      name: string;
      index: string;
      value: string;
    }>;
    pdo_mapping: {
      rx_pdos: any[];
      tx_pdos: any[];
    };
    exclude?: Record<string, string[]>;
  }>;
}

export default defineComponent({
  name: 'SlaveConfigGenerator',
  components: {
    MonacoEditor,
    InfoCircleOutlined,
    SyncOutlined,
    SettingOutlined,
    UploadOutlined,
    ToolOutlined
  },
  props: {
    visible: {
      type: Boolean,
      required: true
    }
  },
  emits: ['update:visible'],
  setup(props) {
    const isJsonView = ref(false);
    const uploading = ref(false);
    const currentConfig = ref<string>('{}');
    const templateConfig = ref<any>(null);
    const searchText = ref('');
    const activeSlaveKeys = ref<string[]>([]);
    const globalDcEnabled = ref(false);
    const insertPositionVisible = ref(false);
    const insertPosition = ref<number>(0);
    const pendingSlaveConfig = ref<any>(null);
    const globalDcConfig = ref({
      assign_activate: '0x0300',
      sync0_cycle: '',
      sync0_shift: '',
      sync1_cycle: '',
      sync1_shift: ''
    });

    // 从站克隆
    const cloneModalVisible = ref(false);
    const cloneCount = ref(1);
    const cloneSourceIndex = ref(-1);
    
    const currentSlaves = computed(() => {
      try {
        const config = JSON.parse(currentConfig.value);
        return config.slaves || [];
      } catch (error) {
        console.error('Failed to parse current config:', error);
        return [];
      }
    });

    const editorOptions = {
      minimap: { enabled: false },
      automaticLayout: true,
      theme: 'vs-light'
    };

    const fetchCurrentConfig = async () => {
      try {
        const config = await ethercatApi.getSlaveConfig();
        currentConfig.value = JSON.stringify(config, null, 2);
        console.log('Current config loaded:', config);
      } catch (error) {
        console.error('Failed to get slave config:', error);
        message.error('获取从站配置失败');
      }
    };

    const fetchTemplateConfig = async () => {
      try {
        const template = await ethercatApi.getTemplateConfig();
        templateConfig.value = template;
        console.log('Template config loaded:', template);
      } catch (error) {
        console.error('Failed to get template config:', error);
        message.error('获取模板配置失败');
      }
    };

    watch(() => props.visible, (newValue) => {
      if (newValue) {
        console.log('Modal opened, fetching data...');
        fetchCurrentConfig();
        fetchTemplateConfig();
      }
    });

    onMounted(() => {
      if (props.visible) {
        console.log('Component mounted, fetching data...');
        fetchCurrentConfig();
        fetchTemplateConfig();
      }
    });

    const removePdo = (slaveIndex: number, type: 'rx' | 'tx', pdoIndex: number) => {
      try {
        const config = JSON.parse(currentConfig.value);
        const pdoList = type === 'rx' ? config.slaves[slaveIndex].rx_pdos : config.slaves[slaveIndex].tx_pdos;
        pdoList.splice(pdoIndex, 1);
        currentConfig.value = JSON.stringify(config, null, 2);
      } catch (error) {
        console.error('Failed to remove PDO:', error);
        message.error('删除PDO失败');
      }
    };

    const filteredTemplateConfig = computed(() => {
      if (!templateConfig.value?.template || !searchText.value) {
        return templateConfig.value?.template || [];
      }
      
      const search = searchText.value.toLowerCase();
      return templateConfig.value.template.map((slave: any) => ({
        ...slave,
        rx_pdos: slave.rx_pdos.filter((pdo: any) => 
          pdo.name.toLowerCase().includes(search) ||
          pdo.comment.toLowerCase().includes(search) ||
          pdo.index.toLowerCase().includes(search)
        ),
        tx_pdos: slave.tx_pdos.filter((pdo: any) => 
          pdo.name.toLowerCase().includes(search) ||
          pdo.comment.toLowerCase().includes(search) ||
          pdo.index.toLowerCase().includes(search)
        )
      })).filter((slave: any) => slave.rx_pdos.length > 0 || slave.tx_pdos.length > 0);
    });

    const addPdo = (pdo: PDO, target: string | number) => {
      try {
        const config = JSON.parse(currentConfig.value);
        
        if (target === 'all') {
          let addedCount = 0;
          config.slaves.forEach((slave: any, index: number) => {
            const isRx = pdo.type === 'rx' || 
              templateConfig.value?.template.some((slave: any) => 
                slave.rx_pdos?.some((p: any) => 
                  p.index.toLowerCase() === pdo.index.toLowerCase() && 
                  p.subindex === pdo.subindex
                )
              );

            const pdoList = isRx ? slave.rx_pdos : slave.tx_pdos;
            
            if (!pdoList.some((p: any) => 
              p.index.toLowerCase() === pdo.index.toLowerCase() && 
              p.subindex === pdo.subindex
            )) {
              pdoList.push({ ...pdo });
              addedCount++;
            }
          });

          currentConfig.value = JSON.stringify(config, null, 2);
          if (addedCount > 0) {
            message.success(`成功添加到 ${addedCount} 个从站`);
          } else {
            message.warning('所有从站都已包含该PDO');
          }
          return;
        }

        const slaveIndex = Number(target);
        if (!config.slaves[slaveIndex]) {
          message.error('目标从站不存在');
          return;
        }

        const isRx = pdo.type === 'rx' || 
          templateConfig.value?.template.some((slave: any) => 
            slave.rx_pdos?.some((p: any) => 
              p.index.toLowerCase() === pdo.index.toLowerCase() && 
              p.subindex === pdo.subindex
            )
          );

        const pdoList = isRx ? config.slaves[slaveIndex].rx_pdos : config.slaves[slaveIndex].tx_pdos;
        
        if (pdoList.some((p: any) => 
          p.index.toLowerCase() === pdo.index.toLowerCase() && 
          p.subindex === pdo.subindex
        )) {
          message.warning('该PDO已存在');
          return;
        }
        
        pdoList.push({ ...pdo });
        currentConfig.value = JSON.stringify(config, null, 2);
        message.success(`添加到${isRx ? 'RxPDOs' : 'TxPDOs'}成功`);
      } catch (error) {
        console.error('Failed to add PDO:', error);
        message.error('添加PDO失败');
      }
    };

    const downloadConfig = () => {
      try {
        const blob = new Blob([currentConfig.value], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'slave_config.json';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error('Failed to download config:', error);
        message.error('下载失败');
      }
    };

    const addNewSlave = () => {
      try {
        const config = JSON.parse(currentConfig.value);
        if (!config.slaves) {
          config.slaves = [];
        }
        
        const newSlaveIndex = config.slaves.length;
        
        config.slaves.push({
          name: '新从站',
          index: newSlaveIndex.toString(),
          vid: '0x00000000',
          pid: '0x00000000',
          rx_pdo: '0x1600',
          tx_pdo: '0x1a00',
          rx_pdos: [],
          tx_pdos: []
        });
        
        currentConfig.value = JSON.stringify(config, null, 2);
        activeSlaveKeys.value = [`slave_${newSlaveIndex}`];
        message.success('添加从站成功');
      } catch (error) {
        console.error('Failed to add slave:', error);
        message.error('添加从站失败');
      }
    };

    const removeSlave = (slaveIndex: number) => {
      try {
        const config = JSON.parse(currentConfig.value);
        config.slaves.splice(slaveIndex, 1);
        
        config.slaves.forEach((slave: any, index: number) => {
          slave.index = index.toString();
        });
        
        currentConfig.value = JSON.stringify(config, null, 2);
        message.success('删除从站成功');
      } catch (error) {
        console.error('Failed to remove slave:', error);
        message.error('删除从站失败');
      }
    };

    const onSearch = (value: string) => {
      searchText.value = value;
    };

    const cloneSlave = (slaveIndex: number) => {
      showCloneModal(slaveIndex);
      // try {
      //   const config = JSON.parse(currentConfig.value);
      //   const clonedSlave = JSON.parse(JSON.stringify(config.slaves[slaveIndex]));
        
      //   config.slaves.splice(slaveIndex + 1, 0, clonedSlave);
        
      //   config.slaves.forEach((slave: any, index: number) => {
      //     slave.index = index.toString();
      //   });
        
      //   currentConfig.value = JSON.stringify(config, null, 2);
      //   message.success('克隆从站成功');
      // } catch (error) {
      //   console.error('Failed to clone slave:', error);
      //   message.error('克隆从站失败');
      // }
    };

    // 显示克隆模态框
    const showCloneModal = (slaveIndex: number) => {
      cloneSourceIndex.value = slaveIndex;
      cloneCount.value = 1;
      cloneModalVisible.value = true;
    }

    // 处理克隆确认
    const handleCloneConfirm = () => {
      if (cloneCount.value < 1) {
        message.error('克隆数量必须大于0');
        return;
      }

      // 执行克隆操作
      cloneMultipleSlaves(cloneSourceIndex.value, cloneCount.value);
      // 关闭模态框
      cloneModalVisible.value = false;
    }

    // 克隆多个从站
    const cloneMultipleSlaves = (sourceIndex: number, count: number) => {
      try {
        // 解析当前完整JSON配置
        const config = JSON.parse(currentConfig.value);
        // 从完整从站配置中获取指定索引从站配置（深拷贝）
        const sourceSlave = JSON.parse(JSON.stringify(config.slaves[sourceIndex]));
        // 获取源从站名
        const originalName = sourceSlave.name;

        // 创建并插入指定数量的克隆
        for (let i = 1; i < count; i++ ) {
          // 获取克隆从站配置
          const cloneSlave = JSON.parse(JSON.stringify(sourceSlave));
          // 设置克隆从站名
          cloneSlave.name = `${originalName}_克隆${i}`;
          // 插入克隆到源从站后
          config.slaves.splice(sourceIndex + i, 0, cloneSlave);
        }

        // 更新从站索引
        config.slaves.forEach((slave: any, index: number) => {
          slave.index = index.toString();
        });
        
        // 更新完整JSON
        currentConfig.value = JSON.stringify(config, null, 2);
        message.success(`成功克隆${count}个从站`);

      } catch (error) {
        console.log("克隆从站失败: ", error);
        message.error("克隆从站失败");
      }
    }

    const moveSlave = (slaveIndex: number, direction: 'up' | 'down') => {
      try {
        const config = JSON.parse(currentConfig.value);
        const newIndex = direction === 'up' ? slaveIndex - 1 : slaveIndex + 1;
        
        if (newIndex < 0 || newIndex >= config.slaves.length) {
          return;
        }

        const slave = config.slaves[slaveIndex];
        config.slaves.splice(slaveIndex, 1);
        config.slaves.splice(newIndex, 0, slave);

        config.slaves.forEach((slave: any, index: number) => {
          slave.index = index.toString();
        });

        currentConfig.value = JSON.stringify(config, null, 2);
      } catch (error) {
        console.error('Failed to move slave:', error);
        message.error('移动从站失败');
      }
    };

    const sortSlaves = () => {
      try {
        const config = JSON.parse(currentConfig.value);
        config.slaves.sort((a: any, b: any) => a.name.localeCompare(b.name));
        currentConfig.value = JSON.stringify(config, null, 2);
        message.success('从站排序成功');
      } catch (error) {
        console.error('Failed to sort slaves:', error);
        message.error('从站排序失败');
      }
    };

    const syncPdoInfo = (slaveIndex: number, type: 'rx' | 'tx', pdoIndex: number, pdo: any) => {
      try {
        const config = JSON.parse(currentConfig.value);
        const templatePdo = templateConfig.value?.template
          .flatMap((slave: any) => [...(slave.rx_pdos || []), ...(slave.tx_pdos || [])])
          .find((p: any) => 
            p.index.toLowerCase() === pdo.index.toLowerCase() && 
            p.subindex === pdo.subindex
          );

        if (!templatePdo) {
          message.warning('模板中未找到对应的PDO');
          return;
        }

        const pdoList = type === 'rx' ? config.slaves[slaveIndex].rx_pdos : config.slaves[slaveIndex].tx_pdos;
        pdoList[pdoIndex] = {
          ...pdoList[pdoIndex],
          name: templatePdo.name,
          comment: templatePdo.comment
        };

        currentConfig.value = JSON.stringify(config, null, 2);
        message.success('同步成功');
      } catch (error) {
        console.error('Failed to sync PDO info:', error);
        message.error('同步失败');
      }
    };

    const syncAllPdos = () => {
      try {
        const config = JSON.parse(currentConfig.value);
        const templatePdos = templateConfig.value?.template
          .flatMap((slave: any) => [...(slave.rx_pdos || []), ...(slave.tx_pdos || [])]);

        if (!templatePdos?.length) {
          message.warning('模板中未找到PDO');
          return;
        }

        let syncCount = 0;
        config.slaves.forEach((slave: any) => {
          ['rx_pdos', 'tx_pdos'].forEach((type: string) => {
            slave[type].forEach((pdo: any) => {
              const templatePdo = templatePdos.find((p: any) => 
                p.index.toLowerCase() === pdo.index.toLowerCase() && 
                p.subindex === pdo.subindex
              );
              if (templatePdo) {
                pdo.name = templatePdo.name;
                pdo.comment = templatePdo.comment;
                syncCount++;
              }
            });
          });
        });

        currentConfig.value = JSON.stringify(config, null, 2);
        message.success(`同步完成，共更新 ${syncCount} 个PDO`);
      } catch (error) {
        console.error('Failed to sync all PDOs:', error);
        message.error('同步失败');
      }
    };

    const previewTopology = async () => {
      try {
        const config = JSON.parse(currentConfig.value);
        const slaves = config.slaves || [];
        
        if (slaves.length === 0) {
          message.warning('配置中没有从站数据');
          return;
        }
        
        // 生成 DOT 语言描述
        let dotContent = `
/* EtherCAT bus graph. Generated from slave configuration. */

strict graph bus {
    rankdir="LR"
    ranksep=0.8
    nodesep=0.8
    node [fontname="Helvetica"]
    edge [fontname="Helvetica",fontsize="10"]
    
    // 根据从站数量调整图形大小
    size="${Math.max(8, Math.min(20, slaves.length * 1.2))},${Math.max(5, Math.min(10, slaves.length * 0.5))}"
    ratio="fill"
    
    // 设置节点大小和形状
    node [shape="box", style="rounded,filled", fillcolor="#e6f7ff", width=2.5, height=0.8]
    
    master [label="EtherCAT\\nMaster", fillcolor="#f6ffed"]
`;

        // 添加从站节点
        slaves.forEach((slave: { name: string; vid: string; pid: string }, index: number) => {
          dotContent += `    slave${index} [label="${slave.name}\\nPos:${index}\\nVID:${slave.vid}\\nPID:${slave.pid}"]\n`;
        });
        
        // 添加连接关系
        dotContent += `    master -- slave0\n`;
        for (let i = 0; i < slaves.length - 1; i++) {
          dotContent += `    slave${i} -- slave${i + 1}\n`;
        }
        
        // 结束 DOT 图
        dotContent += '}\n';
        
        try {
          // 使用 viz.js 在前端渲染 DOT 为 SVG
          const viz = await instance();
          const svgElement = await viz.renderSVGElement(dotContent);
          
          // 将 SVG 元素转换为字符串
          const svgString = new XMLSerializer().serializeToString(svgElement);
          
          // 显示拓扑图
          Modal.confirm({
            title: 'EtherCAT 从站拓扑图',
            width: 900,
            content: h('div', { 
              class: 'topology-image-container',
              innerHTML: svgString  // 直接插入 SVG 字符串
            }),
            okText: '下载 SVG',
            cancelText: '关闭',
            onOk: () => {
              const container = document.querySelector('.topology-image-container');
              if (container) {
                const svg = container.querySelector('svg');
                if (svg) {
                  // 下载 SVG
                  const blob = new Blob([svgString], { type: 'image/svg+xml' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = 'ethercat-topology.svg';
                  a.click();
                  URL.revokeObjectURL(url);
                }
              }
            },
            class: 'topology-modal'
          });
        } catch (error) {
          console.error('Failed to render topology with viz.js:', error);
          
          // 如果渲染失败,回退到简单的文本拓扑图
          Modal.info({
            title: 'EtherCAT 从站拓扑图 (文本模式)',
            width: 800,
            content: h('div', { class: 'topology-preview' }, [
              h('div', { class: 'topology-chain' }, 
                [
                  h('div', { class: 'topology-node master-node' }, 'Master'),
                  h('div', { class: 'topology-arrow' }, '→'),
                  ...slaves.map((slave: any, index: number) => [
                    h('div', { class: 'topology-node' }, 
                      `从站 ${index}: ${slave.name}`
                    ),
                    // 如果不是最后一个从站添加箭头
                    index < slaves.length - 1 ? h('div', { class: 'topology-arrow' }, '→') : null
                  ]).flat() // 展平数组,去除 null
                ]
              ),
              h('div', { class: 'topology-dot-code' }, [
                h('pre', dotContent)
              ])
            ]),
            class: 'topology-modal'
          });
        }
      } catch (error) {
        console.error('Failed to preview topology:', error);
        message.error('预览拓扑图失败: ' + (error instanceof Error ? error.message : String(error)));
      }
    };

    const updateJson = () => {
      try {
        const config = { slaves: currentSlaves.value };
        currentConfig.value = JSON.stringify(config, null, 2);
      } catch (error) {
        console.error('Failed to update JSON:', error);
      }
    };

    watch(currentSlaves, updateJson, { deep: true });

    const clearPdos = (slaveIndex: number, type: 'rx' | 'tx') => {
      try {
        const config = JSON.parse(currentConfig.value);
        if (type === 'rx') {
          config.slaves[slaveIndex].rx_pdos = [];
        } else {
          config.slaves[slaveIndex].tx_pdos = [];
        }
        currentConfig.value = JSON.stringify(config, null, 2);
        message.success(`${type.toUpperCase()}PDOs 已清空`);
      } catch (error) {
        console.error('Failed to clear PDOs:', error);
        message.error('清空PDOs失败');
      }
    };

    const copyConfig = () => {
      try {
        // 创建临时文本区域
        const textArea = document.createElement('textarea');
        textArea.value = currentConfig.value;
        
        // 确保文本区域在视口之外
        textArea.style.position = 'fixed';
        textArea.style.left = '-9999px';
        textArea.style.top = '-9999px';
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        // 执行复制命令
        const successful = document.execCommand('copy');
        
        // 清理
        document.body.removeChild(textArea);
        
        if (successful) {
          message.success('JSON 配置已复制到剪贴板');
        } else {
          throw new Error('复制失败');
        }
      } catch (error) {
        console.error('Failed to copy config:', error);
        message.error('复制失败');
      }
    };

    const addPdoEntry = (slaveIndex: number, type: 'rx' | 'tx') => {
      try {
        const config = JSON.parse(currentConfig.value);
        const pdoList = type === 'rx' ? config.slaves[slaveIndex].rx_pdos : config.slaves[slaveIndex].tx_pdos;
        
        const newPdo = {
          name: '新PDO',
          index: '0x0000',
          subindex: '0',
          type: 'uint8',
          bitlen: 8,
          comment: '新添加的PDO'
        };
        
        pdoList.push(newPdo);
        currentConfig.value = JSON.stringify(config, null, 2);
        message.success(`${type.toUpperCase()}PDO 添加成功`);
      } catch (error) {
        console.error('Failed to add PDO:', error);
        message.error('添加PDO失败');
      }
    };

    const isDcEnabled = (slaveIndex: number) => {
      try {
        const config = JSON.parse(currentConfig.value);
        return config.slaves[slaveIndex].dc_config !== undefined;
      } catch (error) {
        return false;
      }
    };

    const toggleDcConfig = (slaveIndex: number, checked: boolean) => {
      try {
        const config = JSON.parse(currentConfig.value);
        if (checked) {
          // 启用单个从站的DC
          config.slaves[slaveIndex].dc_config = {
            assign_activate: '0x0300',
            sync0_cycle: '',
            sync0_shift: '',
            sync1_cycle: '',
            sync1_shift: ''
          };
        } else {
          // 禁用单个从站的DC
          delete config.slaves[slaveIndex].dc_config;
          
          // 检查是否还有启用DC的从站
          const hasEnabledDc = config.slaves.some((slave: any) => slave.dc_config);
          if (!hasEnabledDc) {
            // 如果没有启用DC的从站了，关闭全局开关
            globalDcEnabled.value = false;
          }
        }
        currentConfig.value = JSON.stringify(config, null, 2);
      } catch (error) {
        console.error('Failed to toggle DC config:', error);
        message.error('切换DC配置失败');
      }
    };

    // 监听配置变化，更新全局DC状态
    watch(currentConfig, (newValue) => {
      try {
        const config = JSON.parse(newValue);
        const hasEnabledDc = config.slaves.some((slave: any) => slave.dc_config);
        globalDcEnabled.value = hasEnabledDc;
      } catch (error) {
        console.error('Failed to update global DC status:', error);
      }
    });

    // 切换全局DC状态
    const toggleGlobalDc = (checked: boolean) => {
      try {
        const config = JSON.parse(currentConfig.value || '{}');
        
        if (checked) {
          config.slaves.forEach((slave: any) => {
            slave.dc_config = { ...globalDcConfig.value };
          });
        } else {
          config.slaves.forEach((slave: any) => {
            delete slave.dc_config;
          });
        }
        
        currentConfig.value = JSON.stringify(config, null, 2);
        message.success(`已${checked ? '启用' : '禁用'}所有从站的DC`);
      } catch (error) {
        console.error('Failed to toggle global DC:', error);
        message.error('切换全局DC状态失败');
      }
    };

    // 更新全局DC配置
    const updateGlobalDcConfig = () => {
      try {
        const config = JSON.parse(currentConfig.value);
        
        // 将全局配置应用到所有从站
        config.slaves.forEach((slave: any) => {
          if (slave.dc_config) {
            slave.dc_config = { ...globalDcConfig.value };
          }
        });
        
        currentConfig.value = JSON.stringify(config, null, 2);
        message.success('已更新所有从站的DC配置');
      } catch (error) {
        console.error('Failed to update global DC config:', error);
        message.error('更新全局DC配置失败');
      }
    };

    const dcDropdownVisible = ref(false);

    const handleDcDropdownVisibleChange = (visible: boolean) => {
      // 只有点击按钮时才改变可见性
      if (!visible || !dcDropdownVisible.value) {
        dcDropdownVisible.value = visible;
      }
    };

    const deduplicateSlaves = () => {
      try {
        const config = JSON.parse(currentConfig.value);
        const uniqueSlaves = new Map();
        const duplicates: number[] = [];

        // 遍历所有从站，找出重复的
        config.slaves.forEach((slave: any, index: number) => {
          // 创建不包含索引的从站信息的哈希键
          const slaveKey = JSON.stringify({
            name: slave.name,
            vid: slave.vid,
            pid: slave.pid,
            rx_pdo: slave.rx_pdo,
            tx_pdo: slave.tx_pdo,
            rx_pdos: slave.rx_pdos,
            tx_pdos: slave.tx_pdos,
            dc_config: slave.dc_config
          });

          if (!uniqueSlaves.has(slaveKey)) {
            // 保存第一个出现的从站
            uniqueSlaves.set(slaveKey, index);
          } else {
            // 记录重复的从站索引
            duplicates.push(index);
          }
        });

        if (duplicates.length === 0) {
          message.info('没有发现重复的从站');
          return;
        }

        // 从后往前删除重复的从站（从后往前删除不会影响前面的索引）
        duplicates.reverse().forEach(index => {
          config.slaves.splice(index, 1);
        });

        currentConfig.value = JSON.stringify(config, null, 2);
        message.success(`已删除 ${duplicates.length} 个重复的从站`);

      } catch (error) {
        console.error('Failed to deduplicate slaves:', error);
        message.error('从站去重失败');
      }
    };

    const uploadedXmlContent = ref<string>('');

    const handleENIUpload = async (file: File): Promise<boolean> => {
      try {
        // 检查文件类型
        const isENI = file.type === 'text/xml' || file.name.toLowerCase().endsWith('.xml');
        if (!isENI) {
          message.error('只支持上传 ENI 文件（XML 格式）');
          return false;
        }

        // 检查文件大小限制 (10MB)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
          message.error(`文件大小不能超过 10MB，当前文件大小: ${(file.size / 1024 / 1024).toFixed(2)}MB`);
          return false;
        }

        uploading.value = true;
        
        // 读取文件内容
        const reader = new FileReader();
        const xmlContent = await new Promise<string>((resolve, reject) => {
          reader.onload = (e) => {
            if (e.target?.result) {
              resolve(e.target.result as string);
            } else {
              reject(new Error('文件内容读取失败'));
            }
          };
          reader.onerror = (e) => reject(new Error('文件读取错误'));
          reader.readAsText(file);
        });
        
        // 保存XML内容
        uploadedXmlContent.value = xmlContent;

        // 解析XML内容
        const config = await ethercatApi.parseENIContent(xmlContent);
        
        // 检查返回的配置是否有效
        if (!config || !config.slaves || !Array.isArray(config.slaves)) {
          throw new Error('配置解析失败：无效的配置格式');
        }

        // 创建编辑用的配置副本，而不是覆盖模板配置
        editingConfig.value = JSON.parse(JSON.stringify(config));

        // 设置默认选中第一个从站
        if (config.slaves.length > 0) {
          selectedSlaveKeys.value = ['0'];
          currentSlaveIndex.value = 0;
        }

        // 初始化表格数据
        initializeTableData(0);

        // 显示编辑对话框
        editModalVisible.value = true;
        
        return false; // 阻止自动上传
      } catch (error) {
        console.error('ENI 文件处理失败:', error);
        message.error(error instanceof Error ? error.message : 'ENI 文件处理失败');
        return false;
      } finally {
        uploading.value = false;
      }
    };

    const handleInsertPosition = async () => {
      try {
        if (!pendingSlaveConfig.value) {
          message.error('没有可插入的配置');
          return;
        }

        const position = insertPosition.value;
        
        if (!currentConfig.value) {
          currentConfig.value = JSON.stringify({ slaves: [] });
        } else if (typeof currentConfig.value === 'string') {
          const config = JSON.parse(currentConfig.value);
          config.slaves.splice(position, 0, ...pendingSlaveConfig.value.slaves);
          config.slaves = config.slaves.map((slave: any, index: number) => ({
            ...slave,
            index: index.toString()
          }));
          currentConfig.value = JSON.stringify(config, null, 2);
        }

        message.success('从站配置已插入');
        insertPositionVisible.value = false;
      } catch (error) {
        console.error('插入从站配置失败:', error);
        message.error('插入从站配置失败，请重试');
      }
    };

    const editModalVisible = ref(false);
    const editingConfig = ref<any>(null);
    const rxPdoTableData = ref<any[]>([]);
    const txPdoTableData = ref<any[]>([]);
    const sdosTableData = ref<any[]>([]);

    const rxPdoColumns = [
      { title: '选择', key: 'select', width: 60 },
      { title: '索引', dataIndex: 'index', key: 'index' },
      { title: '条目数', dataIndex: 'entriesCount', key: 'entriesCount', width: 80 },
      { title: '信息', key: 'info', width: 60 }
    ];

    const txPdoColumns = [
      { title: '选择', key: 'select', width: 60 },
      { title: '索引', dataIndex: 'index', key: 'index' },
      { title: '条目数', dataIndex: 'entriesCount', key: 'entriesCount', width: 80 },
      { title: '信息', key: 'info', width: 60 }
    ];

    const sdosColumns = [
      { title: '选择', key: 'select', width: 60 },
      { title: '索引', dataIndex: 'index', key: 'index' },
      { title: '名称', dataIndex: 'name', key: 'name' },
      { title: '值', dataIndex: 'value', key: 'value' },
    ];

    const jsonConfig = ref<any>(null);
    const insertModalVisible = ref(false);

    const handleFileChange = async (info: UploadChangeParam) => {
      try {
        if (info.file.status === 'done') {
          const response = info.file.response;
          if (response && response.success) {
            jsonConfig.value = response.data;
            editingConfig.value = JSON.parse(JSON.stringify(jsonConfig.value));
            initializeTableData();
            editModalVisible.value = true;
          }
        }
      } catch (error) {
        console.error('Failed to handle file change:', error);
        message.error('文件处理失败');
      }
    };

    const handleRxPdoChange = (record: any) => {
      const slave = editingConfig.value?.slaves?.[currentSlaveIndex.value];
      if (!slave) return;
      
      // 保存选择状态
      if (!slaveSelections.value[currentSlaveIndex.value]) {
        slaveSelections.value[currentSlaveIndex.value] = {
          rx_pdos: {},
          tx_pdos: {},
          sdos: {}
        };
      }
      slaveSelections.value[currentSlaveIndex.value].rx_pdos[record.index] = record.selected;

      // 处理互斥关系
      if (record.selected) {
        const excludeList = slave.exclude?.[record.index];
        if (excludeList) {
          rxPdoTableData.value.forEach(item => {
            if (excludeList.includes(item.index)) {
              item.selected = false;
              slaveSelections.value[currentSlaveIndex.value].rx_pdos[item.index] = false;
            }
          });
        }
      }

      updateConfigFromSelection();
    };

    const handleTxPdoChange = (record: any) => {
      // 保存选择状态
      if (!slaveSelections.value[currentSlaveIndex.value]) {
        slaveSelections.value[currentSlaveIndex.value] = {
          rx_pdos: {},
          tx_pdos: {},
          sdos: {}
        };
      }
      slaveSelections.value[currentSlaveIndex.value].tx_pdos[record.index] = record.selected;
      updateConfigFromSelection();
    };

    const handleSdoChange = (record: any) => {
      // 保存选择状态
      if (!slaveSelections.value[currentSlaveIndex.value]) {
        slaveSelections.value[currentSlaveIndex.value] = {
          rx_pdos: {},
          tx_pdos: {},
          sdos: {}
        };
      }
      slaveSelections.value[currentSlaveIndex.value].sdos[record.index] = record.selected;
      updateConfigFromSelection();
    };

    const updateConfigFromSelection = () => {
      const slave = editingConfig.value?.slaves?.[currentSlaveIndex.value];
      if (!slave) return;
      
      // 更新 RX PDOs 选中状态，但保留所有 PDO
      const selectedRxPdos = rxPdoTableData.value
        .filter(item => item.selected)
        .map(item => item.index);
      
      // 只更新选中的 PDO 字符串，不修改原始 PDO 数据
      slave.rx_pdo = selectedRxPdos.join(',');
      
      // 更新 TX PDOs 选中状态，但保留所有 PDO
      const selectedTxPdos = txPdoTableData.value
        .filter(item => item.selected)
        .map(item => item.index);
      
      // 只更新选中的 PDO 字符串，不修改原始 PDO 数据
      slave.tx_pdo = selectedTxPdos.join(',');
      
      // 更新 PDO mapping 的选中状态，但保留所有映射
      if (slave.pdo_mapping) {
        // 为每个 PDO 添加选中状态标记，但不删除任何 PDO
        slave.pdo_mapping.rx_pdos.forEach((pdo: any) => {
          pdo._selected = selectedRxPdos.includes(pdo.index);
        });
        
        slave.pdo_mapping.tx_pdos.forEach((pdo: any) => {
          pdo._selected = selectedTxPdos.includes(pdo.index);
        });
      }
      
      // 更新 SDOs 选中状态
      if (slave.sdos && sdosTableData.value) {
        slave.sdos.forEach((sdo: any, index: number) => {
          sdo._selected = sdosTableData.value[index].selected;
        });
      }

      // 保存当前选择状态到 slaveSelections
      if (!slaveSelections.value[currentSlaveIndex.value]) {
        slaveSelections.value[currentSlaveIndex.value] = {
          rx_pdos: {},
          tx_pdos: {},
          sdos: {}
        };
      }

      // 保存所有 PDO 的选择状态，包括未选中的
      rxPdoTableData.value.forEach(item => {
        slaveSelections.value[currentSlaveIndex.value].rx_pdos[item.index] = item.selected;
      });

      txPdoTableData.value.forEach(item => {
        slaveSelections.value[currentSlaveIndex.value].tx_pdos[item.index] = item.selected;
      });

      sdosTableData.value.forEach(item => {
        slaveSelections.value[currentSlaveIndex.value].sdos[item.index] = item.selected;
      });
    };

    const handleGenerateJson = async () => {
      try {
        if (!uploadedXmlContent.value) {
          message.error('请先上传 ENI 文件');
          return;
        }

        // 构建配置数据
        const selectedSlaves = selectedSlaveKeys.value.map(key => {
          const slaveIndex = parseInt(key);
          return {
            slaveIndex,
            rxPdo: getSelectedRxPdos(slaveIndex),
            txPdo: getSelectedTxPdos(slaveIndex),
            sdos: getSelectedSdos(slaveIndex)
          };
        });

        const eniConfig = {
          xmlContent: uploadedXmlContent.value,
          slaves: selectedSlaves
        };

        // 打印要发送到后端的数据
        console.log('发送到后端的配置数据:', eniConfig);

        // 调用后端 API 生成配置
        const response = await ethercatApi.generateSlaveConfig(eniConfig);
        
        // 保存生成的配置
        jsonConfig.value = response;
        pendingSlaveConfig.value = response;

        message.success('配置生成成功');
      } catch (error) {
        console.error('生成配置失败:', error);
        message.error('生成配置失败，请重试');
        throw error; // 向上传递错误，让 handleEditOk 捕获
      }
    };

    // 添加缺失的状态声明
    const selectedSlaveKeys = ref<string[]>([]);
    const currentSlaveIndex = ref(0);

    // 添加从站树数据计算属性
    const slaveTreeData = computed(() => {
      if (!editingConfig.value?.slaves) return [];

      return editingConfig.value.slaves.map((slave: any, index: number) => ({
        key: index.toString(),
        title: `${slave.name} (${index})`,
        isLeaf: true
      }));
    });

    // 添加临时存储对象
    const slaveSelections = ref<Record<string, {
      rx_pdos: Record<string, boolean>,
      tx_pdos: Record<string, boolean>,
      sdos: Record<string, boolean>
    }>>({});

    // 修改初始化表格数据函数
    const initializeTableData = (slaveIndex = 0) => {
      const slave = editingConfig.value?.slaves?.[slaveIndex];
      if (!slave) {
        console.error('No slave data available');
        return;
      }

      // 获取该从站的已保存选择状态
      const savedSelection = slaveSelections.value[slaveIndex] || {
        rx_pdos: {},
        tx_pdos: {},
        sdos: {}
      };

      // 初始化 RX PDO 表格数据，使用原始的 PDO 列表
      if (slave.pdo_mapping?.rx_pdos) {
        rxPdoTableData.value = slave.pdo_mapping.rx_pdos.map((pdo: any) => ({
          index: pdo.index,
          entriesCount: pdo.entries?.length || 0,
          selected: savedSelection.rx_pdos[pdo.index] ?? false
        }));
      } else if (slave.rx_pdo) {
        // 兼容没有 pdo_mapping 的情况
        rxPdoTableData.value = slave.rx_pdo.split(',')
          .map((pdo: string) => pdo.trim())
          .filter((pdo: string) => pdo)
          .map((pdo: string) => ({
            index: pdo,
            entriesCount: 0, // 没有 pdo_mapping 时，默认为0
            selected: savedSelection.rx_pdos[pdo] ?? false
          }));
      } else {
        rxPdoTableData.value = [];
      }

      // 初始化 TX PDO 表格数据，使用原始的 PDO 列表
      if (slave.pdo_mapping?.tx_pdos) {
        txPdoTableData.value = slave.pdo_mapping.tx_pdos.map((pdo: any) => ({
          index: pdo.index,
          entriesCount: pdo.entries?.length || 0,
          selected: savedSelection.tx_pdos[pdo.index] ?? false
        }));
      } else if (slave.tx_pdo) {
        // 兼容没有 pdo_mapping 的情况
        txPdoTableData.value = slave.tx_pdo.split(',')
          .map((pdo: string) => pdo.trim())
          .filter((pdo: string) => pdo)
          .map((pdo: string) => ({
            index: pdo,
            entriesCount: 0, // 没有 pdo_mapping 时，默认为0
            selected: savedSelection.tx_pdos[pdo] ?? false
          }));
      } else {
        txPdoTableData.value = [];
      }

      // 初始化 SDOs 表格数据
      if (slave.sdos) {
        sdosTableData.value = slave.sdos.map((sdo: any) => ({
          index: sdo.index.startsWith('0x') ? sdo.index : `0x${sdo.index}`,
          name: sdo.name,
          value: sdo.value,
          selected: savedSelection.sdos[sdo.index] ?? false
        }));
      } else {
        sdosTableData.value = [];
      }
    };

    // 添加 PDO 名称获取函数
    const getPdoName = (pdoIndex: string, slave: any): string => {
      const mapping = slave.pdo_mapping;
      if (!mapping) return pdoIndex;

      const rxPdoMap = mapping.rx_pdos?.find((p: any) => p.index === pdoIndex);
      const txPdoMap = mapping.tx_pdos?.find((p: any) => p.index === pdoIndex);

      if (rxPdoMap?.entries?.[0]?.name) {
        return rxPdoMap.entries[0].name;
      }
      if (txPdoMap?.entries?.[0]?.name) {
        return txPdoMap.entries[0].name;
      }

      return pdoIndex;
    };

    // 修改从站选择处理函数
    const handleSlaveSelect = (selectedKeys: string[]) => {
      if (selectedKeys.length > 0) {
        // 保存当前从站的选择状态
        const currentSlave = editingConfig.value?.slaves?.[currentSlaveIndex.value];
        if (currentSlave) {
          if (!slaveSelections.value[currentSlaveIndex.value]) {
            slaveSelections.value[currentSlaveIndex.value] = {
              rx_pdos: {},
              tx_pdos: {},
              sdos: {}
            };
          }
          
          // 保存当前选择状态
          rxPdoTableData.value.forEach(item => {
            slaveSelections.value[currentSlaveIndex.value].rx_pdos[item.index] = item.selected;
          });
          txPdoTableData.value.forEach(item => {
            slaveSelections.value[currentSlaveIndex.value].tx_pdos[item.index] = item.selected;
          });
          sdosTableData.value.forEach(item => {
            slaveSelections.value[currentSlaveIndex.value].sdos[item.index] = item.selected;
          });
        }

        // 更新当前从站索引并初始化表格数据
        currentSlaveIndex.value = parseInt(selectedKeys[0]);
        initializeTableData(currentSlaveIndex.value);
      }
    };

    // 添加编辑对话框处理函数
    const handleEditOk = async () => {
      try {
        // 调用 handleGenerateJson 生成配置
        await handleGenerateJson();
        
        // 关闭编辑对话框
        editModalVisible.value = false;
        
        // 打开插入位置对话框
        insertPositionVisible.value = true;
      } catch (error) {
        console.error('处理编辑确认时出错:', error);
        message.error('生成配置失败，请重试');
      }
    };

    const handleEditCancel = () => {
      editModalVisible.value = false;
    };

    // 添加 RX PDO 禁用检查函数
    const isRxPdoDisabled = (record: any): boolean => {
      const slave = editingConfig.value?.slaves?.[currentSlaveIndex.value];
      if (!slave?.exclude) return false;

      // 遍历所有已选中的 PDO
      for (const selectedPdo of rxPdoTableData.value) {
        // 只检查已选中的 PDO
        if (selectedPdo.selected) {
          // 获取该 PDO 的互斥列表
          const excludeList = slave.exclude[selectedPdo.index];
          // 如果当前记录在互斥列表中，则禁用
          if (Array.isArray(excludeList) && excludeList.includes(record.index)) {
            return true;
          }
        }
      }
      return false;
    };

    const getSelectedRxPdos = (slaveIndex: number): string[] => {
      return rxPdoTableData.value
        .filter(item => item.selected)
        .map(item => item.index);
    };

    const getSelectedTxPdos = (slaveIndex: number): string[] => {
      return txPdoTableData.value
        .filter(item => item.selected)
        .map(item => item.index);
    };

    const getSelectedSdos = (slaveIndex: number): string[] => {
      return sdosTableData.value
        .filter(item => item.selected)
        .map(item => item.index);
    };

    // 添加计算属性
    const isAllRxPdoSelected = computed(() => {
      return rxPdoTableData.value.length > 0 && 
        rxPdoTableData.value.every(item => item.selected || isRxPdoDisabled(item));
    });

    const isAllTxPdoSelected = computed(() => {
      return txPdoTableData.value.length > 0 && 
        txPdoTableData.value.every(item => item.selected);
    });

    const isAllSdoSelected = computed(() => {
      return sdosTableData.value.length > 0 && 
        sdosTableData.value.every(item => item.selected);
    });

    // 添加处理函数
    const handleRxPdoSelectAll = async () => {
      const slave = editingConfig.value?.slaves?.[currentSlaveIndex.value];
      if (!slave) return;

      if (!isAllRxPdoSelected.value) {
        // 检查是否存在互斥关系
        const hasExclusions = slave.exclude && Object.keys(slave.exclude).length > 0;
        if (hasExclusions) {
          const confirmed = await new Promise(resolve => {
            Modal.confirm({
              title: '互斥关系提示',
              content: '当前从站存在PDO互斥关系，请手动选择需要的PDO。',
              okText: '确定',
              cancelText: '取消',
              onOk: () => resolve(false)
            });
          });
          return;
        }

        // 全选未禁用的项
        rxPdoTableData.value.forEach(item => {
          if (!isRxPdoDisabled(item)) {
            item.selected = true;
            handleRxPdoChange(item);
          }
        });
      } else {
        // 取消全选
        rxPdoTableData.value.forEach(item => {
          item.selected = false;
          handleRxPdoChange(item);
        });
      }
    };

    const handleTxPdoSelectAll = () => {
      if (!isAllTxPdoSelected.value) {
        // 全选
        txPdoTableData.value.forEach(item => {
          item.selected = true;
          handleTxPdoChange(item);
        });
      } else {
        // 取消全选
        txPdoTableData.value.forEach(item => {
          item.selected = false;
          handleTxPdoChange(item);
        });
      }
    };

    const handleSdoSelectAll = () => {
      if (!isAllSdoSelected.value) {
        // 全选
        sdosTableData.value.forEach(item => {
          item.selected = true;
          handleSdoChange(item);
        });
      } else {
        // 取消全选
        sdosTableData.value.forEach(item => {
          item.selected = false;
          handleSdoChange(item);
        });
      }
    };

    // 获取PDO详细信息函数
    const getPdoDetailInfo = (pdoIndex: string, type: 'rx' | 'tx'): string => {
      const slave = editingConfig.value?.slaves?.[currentSlaveIndex.value];
      if (!slave || !slave.pdo_mapping) return '无详细信息';

      const pdoList = type === 'rx' ? slave.pdo_mapping.rx_pdos : slave.pdo_mapping.tx_pdos;
      const pdo = pdoList?.find((p: any) => p.index === pdoIndex);
      
      if (!pdo || !pdo.entries || pdo.entries.length === 0) {
        return '无条目信息';
      }
      
      // 使用HTML格式实现更好的悬停提示样式
      return `
        <div class="pdo-tooltip">
          <div class="pdo-title">${pdo.name || pdoIndex} 包含以下条目:</div>
          ${pdo.entries.map((entry: any, idx: number) => `
            <div class="pdo-entry">
              <div class="pdo-entry-header">${idx + 1}. ${entry.name || '未命名'}</div>
              <div class="pdo-entry-detail">
                <span class="pdo-label">索引:</span>
                <span class="pdo-value">${entry.index}:${entry.subindex}</span>
              </div>
            </div>
          `).join('')}
        </div>
      `;
    };

    const toolboxModalVisible = ref(false);
    const toolboxSlaveCount = ref(1);

    const showToolboxModal = () => {
      toolboxModalVisible.value = true;
    };

    const handleToolboxOk = () => {
      toolboxModalVisible.value = false;
    };

    const handleToolboxCancel = () => {
      toolboxModalVisible.value = false;
    };

    const handleQuickServoConfig = async () => {
      try {
        // 1. 触发从站去重
        const originalCount = currentSlaves.value.length;
        deduplicateSlaves();
        
        // 如果去重后剩余超过1个从站配置，弹出提示框
        const remainingCount = currentSlaves.value.length;
        if (remainingCount > 1) {
          Modal.warning({
            title: '从站去重提示',
            content: `从站去重后仍有 ${remainingCount} 个不同的从站配置，将继续执行后续操作。`,
            okText: '确定'
          });
        }

        // 2. 清空所有从站的RxPDOs和TxPDOs
        const config = JSON.parse(currentConfig.value);
        config.slaves.forEach((slave: any) => {
          slave.rx_pdos = [];
          slave.tx_pdos = [];
        });

        // 3. 添加常用控制位到所有从站
        const commonPdos = [
          { name: '操作模式', index: '0x6060', subindex: '0', type: 'int8', bitlen: 8, comment: '操作模式' },
          { name: '目标速度', index: '0x60FF', subindex: '0', type: 'int32', bitlen: 32, comment: '目标速度' },
          { name: '控制字', index: '0x6040', subindex: '0', type: 'uint16', bitlen: 16, comment: '控制字' },
          { name: '状态字', index: '0x6041', subindex: '0', type: 'uint16', bitlen: 16, comment: '状态字' },
          { name: '错误代码', index: '0x603F', subindex: '0', type: 'uint16', bitlen: 16, comment: '错误代码' },
          { name: '实际反馈速度', index: '0x606C', subindex: '0', type: 'int32', bitlen: 32, comment: '实际反馈速度' }
        ];

        config.slaves.forEach((slave: any) => {
          // 前三个是RxPDOs（写入），后三个是TxPDOs（读取）
          slave.rx_pdos = commonPdos.slice(0, 3).map(pdo => ({ ...pdo }));
          slave.tx_pdos = commonPdos.slice(3).map(pdo => ({ ...pdo }));
        });

        // 4. 启用全局DC
        globalDcEnabled.value = true;
        config.slaves.forEach((slave: any) => {
          slave.dc_config = { ...globalDcConfig.value };
        });

        // 更新配置
        currentConfig.value = JSON.stringify(config, null, 2);

        // 5. 克隆从站
        if (toolboxSlaveCount.value > 1) {
          const sourceIndex = 0; // 使用第一个从站作为源
          cloneMultipleSlaves(sourceIndex, toolboxSlaveCount.value);
        }

        message.success('快速配置（伺服）完成');
        toolboxModalVisible.value = false;

      } catch (error) {
        console.error('快速配置失败:', error);
        message.error('快速配置失败，请重试');
      }
    };

    return {
      isJsonView,
      currentConfig,
      templateConfig,
      searchText,
      activeSlaveKeys,
      globalDcEnabled,
      globalDcConfig,
      currentSlaves,
      editorOptions,
      removePdo,
      downloadConfig,
      fetchCurrentConfig,
      fetchTemplateConfig,
      addNewSlave,
      removeSlave,
      filteredTemplateConfig,
      addPdo,
      onSearch,
      cloneSlave,
      cloneModalVisible,
      cloneCount,
      showCloneModal,
      handleCloneConfirm,
      cloneMultipleSlaves,
      moveSlave,
      sortSlaves,
      syncPdoInfo,
      syncAllPdos,
      previewTopology,
      updateJson,
      clearPdos,
      copyConfig,
      addPdoEntry,
      isDcEnabled,
      toggleDcConfig,
      toggleGlobalDc,
      updateGlobalDcConfig,
      dcDropdownVisible,
      handleDcDropdownVisibleChange,
      deduplicateSlaves,
      uploading,
      handleENIUpload,
      insertPositionVisible,
      insertPosition,
      handleInsertPosition,
      editModalVisible,
      editingConfig,
      rxPdoTableData,
      txPdoTableData,
      sdosTableData,
      rxPdoColumns,
      txPdoColumns,
      sdosColumns,
      handleEditOk,
      handleEditCancel,
      jsonConfig,
      insertModalVisible,
      handleFileChange,
      handleRxPdoChange,
      handleTxPdoChange,
      handleSdoChange,
      updateConfigFromSelection,
      handleGenerateJson,
      selectedSlaveKeys,
      currentSlaveIndex,
      slaveTreeData,
      initializeTableData,
      handleSlaveSelect,
      isRxPdoDisabled,
      getPdoName,
      uploadedXmlContent,
      slaveSelections,
      isAllRxPdoSelected,
      isAllTxPdoSelected,
      isAllSdoSelected,
      handleRxPdoSelectAll,
      handleTxPdoSelectAll,
      handleSdoSelectAll,
      getPdoDetailInfo,
      toolboxModalVisible,
      toolboxSlaveCount,
      showToolboxModal,
      handleToolboxOk,
      handleToolboxCancel,
      handleQuickServoConfig,
    };
  }
});
</script>

<style scoped lang="less">
.generator-container {
  display: flex;
  height: 600px;
  gap: 16px;

  .left-panel {
    flex: 2;
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    overflow-y: auto;
    background: #fff;
  }

  .right-panel {
    flex: 1;
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    overflow-y: auto;
    background: #fff;
    min-width: 380px;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .ant-space {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .json-editor {
    height: calc(100% - 40px);
    border: 1px solid #f0f0f0;
  }

  .list-view {
    height: calc(100% - 40px);
    overflow-y: auto;
  }

  .slave-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #fafafa;
    border-radius: 4px;
    border: 1px solid #f0f0f0;

    .ant-collapse {
      background: transparent;
      border: none;
    }

    .ant-collapse-content {
      background: #fff;
    }
  }

  .pdo-lists {
    display: flex;
    gap: 16px;

    .pdo-section {
      flex: 1;
      background: #fff;
      padding: 16px;
      border-radius: 4px;
      border: 1px solid #f0f0f0;

      .pdo-section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;

        .pdo-count {
          color: #8c8c8c;
          font-weight: normal;
        }
      }

      .ant-collapse {
        background: transparent;
        border: none;

        .ant-collapse-content {
          background: #fff;
        }

        // 嵌套的折叠面板样式
        .ant-collapse {
          margin-top: 8px;
          
          .ant-collapse-item {
            border-radius: 4px;
            border: 1px solid #f0f0f0;
            margin-bottom: 8px;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }

  .pdo-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    gap: 8px;

    .pdo-info {
      flex: 1;
      min-width: 0;
      
      .pdo-name {
        font-weight: 500;
        display: block;
        margin-bottom: 4px;
      }

      .pdo-tags {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
      }
    }

    .pdo-actions {
      display: flex;
      align-items: center;
      white-space: nowrap;
      gap: 8px;
    }
  }

  .pdo-group {
    margin-bottom: 16px;
    
    h5 {
      margin-bottom: 8px;
      padding-left: 8px;
      border-left: 3px solid #1890ff;
    }
  }

  .template-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #fafafa;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
  }
}

.footer {
  text-align: right;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.slave-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .ant-space {
    .ant-btn {
      padding: 0 4px;
    }
  }
}

.pdo-header {
  display: flex;
  align-items: center;
  gap: 8px;

  .pdo-name {
    font-weight: 500;
  }

  .ant-tag {
    margin: 0;
  }
}

.compact-list-item {
  padding: 4px 8px !important;
}

.truncate-tag {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.topology-preview {
  display: flex;
  flex-direction: column;
  gap: 20px;
  
  .topology-image-container {
    display: flex;
    justify-content: center;
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    overflow: auto;
    max-height: 60vh;
    
    :deep(svg) {
      max-width: 100%;
      height: auto;
      min-height: 200px;
    }
  }
  
  .topology-controls {
    display: flex;
    justify-content: center;
    margin-top: 16px;
    gap: 8px;
    
    .ant-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      min-width: 100px;
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  
  .ant-btn {
    min-width: 120px;
  }
}

:deep(.topology-modal) {
  .ant-modal-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 0;
  }
  
  .ant-modal-footer {
    text-align: center;
    
    .ant-btn {
      min-width: 120px;
    }
  }
}

.dc-config-menu {
  padding: 16px;
  min-width: 300px;

  .dc-switch {
    margin-bottom: 16px;
    text-align: center;
  }

  :deep(.ant-form-item) {
    margin-bottom: 8px;
  }

  // 防止点击事件冒泡
  :deep(.ant-input), 
  :deep(.ant-form-item),
  :deep(.ant-form) {
    cursor: auto;
    
    &:hover {
      background: none;
    }
  }
}

.ant-upload {
  display: inline-block;
  margin-right: 8px;
}

.position-hint {
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;

  p {
    margin-bottom: 8px;
    color: #666;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.edit-container {
  display: flex;
  gap: 16px;
}

.slave-tree {
  width: 200px;
  border-right: 1px solid #f0f0f0;
  padding-right: 16px;
}

.table-container {
  flex: 2;
  min-width: 0;
}

.table-section {
  margin-bottom: 24px;

  h3 {
    margin-bottom: 16px;
    color: #1890ff;
    font-weight: 500;
  }
}

.action-bar {
  margin: 16px 0;
  text-align: right;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
  }
}

.clone-hint {
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;

  p {
    margin-bottom: 8px;
    color: #666;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.pdo-tooltip {
  position: relative;
  display: inline-block;
}

.pdo-title {
  background-color: #333;
  color: #fff;
  padding: 5px 10px;
  border-radius: 5px 5px 0 0;
}

.pdo-entry {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 0 0 5px 5px;
  position: absolute;
  z-index: 1;
  width: 200px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: none;
}

.pdo-entry-header {
  font-weight: bold;
  margin-bottom: 5px;
}

.pdo-entry-detail {
  display: flex;
  align-items: center;
}

.pdo-label {
  font-weight: bold;
  margin-right: 5px;
}

.pdo-value {
  margin-left: 5px;
}

:deep(.pdo-tooltip-container) {
  .pdo-tooltip {
    min-width: 300px;

    .pdo-title {
      font-weight: 500;
      margin-bottom: 12px;
      color: #fff;
      font-size: 14px;
    }

    .pdo-entry {
      padding-bottom: 10px;
      margin-bottom: 10px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      &:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }

      .pdo-entry-header {
        color: #fff;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .pdo-entry-detail {
        display: flex;
        justify-content: space-between;
        
        .pdo-label {
          color: rgba(255, 255, 255, 0.85);
        }
        
        .pdo-value {
          color: #fff;
          font-family: monospace;
        }
      }
    }
  }
}

.toolbox-hint {
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;

  p {
    margin-bottom: 8px;
    color: #666;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>