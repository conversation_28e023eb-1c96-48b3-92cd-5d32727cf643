export interface EthercatState {
  ethercatStatus: boolean;
  slaveCount: number;
  currentProgram: string;
  slaves: any[];
}

export interface Slave {
  master: number;
  index: number;
  name: string;
  state: string;
  vendorId: string;
  productCode: string;
  revision: string;
  serial: string;
  group: string;
  orderNumber: string;
  deviceName: string;
  currentConsumption: number;
  fmmuBitOperation: boolean;
  distributedClocks: string;
  dcSystemTimeDelay: number;
  ports: {
    index: number;
    type: string;
    link: string;
    loop: string;
    signal: string;
    nextSlave: string;
    rxTime: number;
    diff: number;
    nextDc: number;
  }[];
  rxPdos?: PDO[];
  txPdos?: PDO[];
  syncManagers?: SyncManager[];
}

export interface SlaveGroup {
  master: number;
  slaves: Slave[];
}

export interface PDOEntry {
  index: string;
  subIndex: string;
  bits: number;
  name: string;
  value?: any;
}

export interface PDO {
  index: string;
  name: string;
  entries: PDOEntry[];
  type?: string;
  value?: any;
}

export interface SyncManager {
  index: number;
  physAddr: string;
  defaultSize: number;
  controlRegister: string;
  enable: boolean;
  pdos: PDO[];
}

export interface SlaveDetail extends Slave {
  rxPdos?: PDO[];
  txPdos?: PDO[];
  syncManagers?: SyncManager[];
}

export interface EthercatStatus {
  running: boolean;
  slaveCount: number;
  currentProgram?: string;
}

export interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}

export interface SystemMetrics {
  timestamp: string;
  cpu_usage: number;
  memory_usage: number;
  network_tx: number;
  network_rx: number;
}

export interface SystemLog {
  id: string;
  time: string;
  level: 'info' | 'warning' | 'error';
  message: string;
}

export interface SlaveStatusCount {
  running: number;
  preop: number;
  init: number;
  error: number;
}

export interface Program {
  id: string;
  name: string;
  middlewareStatus: 'active' | 'inactive' | 'failed';
  programStatus: 'active' | 'inactive' | 'failed';
  uploadTime: string;
  masterIndex: number;
  taskFrequency: number;
  ethercatDir: string;
  config: any;
  filePath: string;
  middlewareService?: string;
  programService?: string;
  startup?: string;
  loading?: boolean;
}

export interface ProgramConfig {
  masterIndex: number;
  taskFrequency: number;
  config: any;
}

export interface UploadForm {
  name: string;
  masterIndex: number;
  taskFrequency: number;
  ethercatDir: string;
  file: File | null;
  configFile: File | null;
}

export interface ConfigForm {
  masterIndex: number;
  taskFrequency: number;
}

export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
  status: boolean;
  created: string;
}

export interface UserForm {
  username: string;
  email: string;
  role: string;
  status: boolean;
}

export interface PasswordForm {
  password: string;
  confirmPassword: string;
}

export interface MenuItem {
  key: string;
  title: string;
  icon?: any;
  path: string;
  children?: MenuItem[];
}

export interface ChartData {
  timestamp: number;
  value: number;
}

export interface ChartOption {
  title?: string;
  data: ChartData[];
  color?: string;
  areaColor?: string[];
}

export interface DashboardStatus {
  ethercatStatus: boolean;
  slaveCount: number;
  currentProgram: string | null;
  programUptime: string | null;
  slaveStatusCount: SlaveStatusCount;
  metrics: SystemMetrics;
}

export interface VariableValue {
  name: string;
  value: any;
  type: 'int' | 'float' | 'bool' | 'string';
}

export interface EtherCATLog {
  time: string;
  level: 'ERROR' | 'WARNING';
  message: string;
}

// 添加 SystemInfo 类型定义
export interface SystemInfo {
  cpuCount: number;      // CPU核心数
  cpuModel: string;      // CPU型号
  totalMemory: number;   // 总内存(GB)
  freeMemory: number;    // 可用内存(GB)
  platform: string;      // 操作系统平台
  arch: string;         // CPU架构
  hostname: string;     // 主机名
  cpuDetails?: {        // CPU详细信息（仅Linux）
    processors: Array<{
      processor: number; // CPU核心编号
      model: string;    // 型号
      mhz: number;      // 频率
      cacheSize: string; // 缓存大小
    }>;
  };
}

// 添加更多类型...

// 单个从站的配置接口
interface SlaveConfig {
  slaveIndex: number;
  rxPdo: string[];
  txPdo: string[];
  sdos: string[];
}

// 整体配置接口
export interface EniConfig {
  slaves: SlaveConfig[];  // 支持多个从站配置
  xmlContent: string;     // ENI文件的XML内容
} 