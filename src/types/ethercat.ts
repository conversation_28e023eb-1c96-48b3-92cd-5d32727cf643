export interface Master {
  index: number;
  phase: string;
  active: boolean;
  slaveCount: number;
}

export interface EthercatStatus {
  running: boolean;
  masters: Master[];
  slaves: any[];
  slaveCount: number;
  lastUpdateTime: string;
}

export interface Slave {
  master: number;
  index: number;
  name: string;
  state: string;
  vendorId: string;
  productCode: string;
  revision: string;
  serial: string;
  group: string;
  orderNumber: string;
  deviceName: string;
  currentConsumption: number;
  fmmuBitOperation: boolean;
  distributedClocks: string;
  dcSystemTimeDelay: number;
  ports: {
    index: number;
    type: string;
    link: string;
    loop: string;
    signal: string;
    nextSlave: string;
    rxTime: number;
    diff: number;
    nextDc: number;
  }[];
  rxPdos?: PDO[];
  txPdos?: PDO[];
  syncManagers?: SyncManager[];
}

export interface PDO {
  index: string;
  name: string;
  entries: PDOEntry[];
  type?: string;
  value?: any;
}

export interface PDOEntry {
  index: string;
  subIndex: string;
  bits: number;
  name: string;
  value?: any;
}

export interface SyncManager {
  index: number;
  physAddr: string;
  defaultSize: number;
  controlRegister: string;
  enable: boolean;
  pdos: PDO[];
}
