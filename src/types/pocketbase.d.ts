declare module 'pocketbase' {
  export default class PocketBase {
    constructor(url: string);
    authStore: {
      clear(): void;
      save(token: string, model: any): void;
      isValid: boolean;
    };
    collection(name: string): {
      authWithPassword(username: string, password: string): Promise<{
        token: string;
        record: any;
      }>;
      requestPasswordReset(email: string): Promise<void>;
      confirmPasswordReset(
        token: string,
        password: string,
        passwordConfirm: string
      ): Promise<void>;
    };
  }
} 