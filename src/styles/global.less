@import 'ant-design-vue/dist/antd.less';

// 自定义主题变量
@primary-color: #007AFF;  // iOS 风格的蓝色
@link-color: #007AFF;
@success-color: #34C759;  // iOS 风格的绿色
@warning-color: #FF9500;  // iOS 风格的橙色
@error-color: #FF3B30;    // iOS 风格的红色
@heading-color: #1C1C1E;
@text-color: #3A3A3C;
@text-color-secondary: #8E8E93;
@disabled-color: #C7C7CC;
@border-radius-base: 12px;  // 统一使用大圆角
@border-color-base: #E5E5EA;
@box-shadow-base: 0 4px 16px rgba(0, 0, 0, 0.08);
@background-color-base: #F2F2F7;

// 全局样式
body {
  background-color: @background-color-base;
}

// 卡片样式统一
.ant-card {
  border-radius: @border-radius-base;
  border: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04), 
              0 4px 8px rgba(0, 0, 0, 0.04), 
              0 8px 16px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.9);
  
  .ant-card-head {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  }
}

// 按钮样式统一
.ant-btn {
  border-radius: 10px;
  height: 40px;
  padding: 0 20px;
  font-weight: 500;
  border: none;
  box-shadow: none;
  
  &.ant-btn-primary {
    background: @primary-color;
    
    &:hover {
      background: darken(@primary-color, 5%);
    }
  }
  
  &.ant-btn-default {
    background: rgba(0, 0, 0, 0.05);
    color: @text-color;
    
    &:hover {
      background: rgba(0, 0, 0, 0.08);
    }
  }
}

// 输入框样式统一
.ant-input, .ant-select-selector {
  border-radius: 10px !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

// 表格样式优化
.ant-table {
  background: transparent;
  
  .ant-table-content {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: @border-radius-base;
  }
  
  .ant-table-thead > tr > th {
    background: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  }
}

// 标签样式统一
.ant-tag {
  border-radius: 6px;
  padding: 4px 12px;
  border: none;
  
  &.ant-tag-success {
    background: fade(@success-color, 10%);
    color: @success-color;
  }
  
  &.ant-tag-error {
    background: fade(@error-color, 10%);
    color: @error-color;
  }
}

// 菜单样式优化
.ant-menu {
  background: transparent;
  
  .ant-menu-item {
    border-radius: 10px;
    margin: 4px 8px;
    
    &:hover {
      background: rgba(0, 0, 0, 0.04);
    }
    
    &.ant-menu-item-selected {
      background: rgba(0, 122, 255, 0.1);
      color: @primary-color;
    }
  }
}

// 模态框样式
.ant-modal {
  .ant-modal-content {
    border-radius: @border-radius-base;
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
  }
}

// 下拉菜单样式
.ant-dropdown-menu {
  border-radius: 10px;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  
  .ant-dropdown-menu-item {
    border-radius: 8px;
  }
}

// 修复布局问题
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: hidden;
}

#app {
  height: 100%;
}

// 内容区域的滚动条样式美化
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
  
  &:hover {
    background: #999;
  }
}

// 卡片样式优化
.ant-card {
  height: 100%;  // 让卡片填充容器高度
}

// G2 圆角样式
.g2-radius {
  &-lg {
    border-radius: 16px;
    overflow: hidden;
  }

  &-md {
    border-radius: 12px;
    overflow: hidden;
  }

  &-base {
    border-radius: 8px;
    overflow: hidden;
  }

  &-sm {
    border-radius: 6px;
    overflow: hidden;
  }
}

// 全局组件样式覆盖
.ant-card {
  border-radius: 16px !important;
  overflow: hidden;
}

.ant-modal-content {
  border-radius: 16px !important;
  overflow: hidden;
}

.ant-table {
  .ant-table-container {
    border-radius: 12px !important;
    overflow: hidden;
  }
}

.ant-btn {
  border-radius: 8px !important;

  &.ant-btn-sm {
    border-radius: 6px !important;
  }
}

.ant-tag {
  border-radius: 6px !important;
}

.ant-select-selector {
  border-radius: 8px !important;
}

.ant-input,
.ant-input-password {
  border-radius: 8px !important;
}

.ant-tabs-card {
  .ant-tabs-nav {
    .ant-tabs-tab {
      border-radius: 8px 8px 0 0 !important;
    }
  }
}

.ant-descriptions {
  border-radius: 12px !important;
  overflow: hidden;
}

.ant-form-item-control-input-content {
  .ant-input-number {
    border-radius: 8px !important;
  }
}

.ant-dropdown-menu {
  border-radius: 12px !important;
  overflow: hidden;
}

.ant-tooltip {
  .ant-tooltip-inner {
    border-radius: 8px !important;
  }
}

.ant-message-notice-content {
  border-radius: 8px !important;
}

.ant-notification-notice {
  border-radius: 12px !important;
} 