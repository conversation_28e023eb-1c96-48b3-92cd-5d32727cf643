// iOS 风格的颜色变量
@primary-color: #007AFF;
@link-color: #007AFF;
@success-color: #34C759;
@warning-color: #FF9500;
@error-color: #FF3B30;
@heading-color: #1C1C1E;
@text-color: #3A3A3C;
@text-color-secondary: #8E8E93;
@disabled-color: #C7C7CC;
@border-radius-base: 12px;
@border-color-base: #E5E5EA;
@box-shadow-base: 0 4px 16px rgba(0, 0, 0, 0.08);
@background-color-base: #F2F2F7;

// 布局相关
@layout-header-height: 64px;
@layout-header-padding: 0 24px;
@layout-sider-width: 240px;
@layout-content-padding: 24px;

// 卡片相关
@card-padding-base: 24px;
@card-radius: @border-radius-base;
@card-shadow: 0 1px 2px rgba(0, 0, 0, 0.04),
              0 4px 8px rgba(0, 0, 0, 0.04),
              0 8px 16px rgba(0, 0, 0, 0.04);

// 动画相关
@animation-duration-base: 0.2s;
@animation-duration-slow: 0.3s;
@animation-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);