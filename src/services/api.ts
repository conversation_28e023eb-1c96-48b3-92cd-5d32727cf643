import axios, { AxiosProgressEvent } from 'axios';
import store from '@/store';
import router from '@/router';
import { message } from 'ant-design-vue';
import type { EthercatStatus, SlaveDetail, ApiResponse, SystemMetrics, SystemLog, SlaveStatusCount, Program, ProgramConfig, User, UserForm, DashboardStatus, VariableValue, EtherCATLog, EniConfig } from '@/types';
import SparkMD5 from 'spark-md5';

export interface EtherCATConfig {
  masterDevices: { [key: number]: string | null };
}

const api = axios.create({
  baseURL: '/api',
  timeout: 300000
});

// 添加请求拦截器
api.interceptors.request.use(
  config => {
    const token = store.state.auth.token;
    console.log('[Request Interceptor] Current token:', token?.substring(0, 20) + '...');
    console.log('[Request Interceptor] Request URL:', config.url);
    console.log('[Request Interceptor] Request Method:', config.method);
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('[Request Interceptor] Added Authorization header');
    }
    return config;
  },
  error => {
    console.error('[Request Interceptor] Error:', error);
    return Promise.reject(error);
  }
);

// 添加响应拦截器
api.interceptors.response.use(
  response => {
    console.log('[Response Interceptor] Success:', response.config.url);
    return response;
  },
  async error => {
    console.log('[Response Interceptor] Error status:', error.response?.status);
    console.log('[Response Interceptor] Error message:', error.response?.data?.message);
    
    const originalRequest = error.config;
    console.log('[Response Interceptor] Original request URL:', originalRequest.url);

    // 从错误消息中提取错误码
    const errorMessage = error.response?.data?.message || '';
    const errorCodeMatch = errorMessage.match(/^\[(\d+)\]/);
    const actualErrorCode = errorCodeMatch ? parseInt(errorCodeMatch[1]) : error.response?.status;
    
    console.log('[Response Interceptor] Actual error code:', actualErrorCode);

    // 只处理400错误码（认证错误）
    if (actualErrorCode === 400) {
      console.log('[Auth Error] Authentication error detected');
      
      // 显示错误消息时去掉错误码前缀
      const cleanMessage = errorMessage.replace(/^\[\d+\]\s*/, '');
      message.error(cleanMessage || '登录已过期，请重新登录');

      // 登出并跳转到登录页
      await store.dispatch('auth/logout');
      if (router.currentRoute.value.path !== '/login') {
        console.log('[Auth Error] Redirecting to login page');
        router.push('/login');
      }

      return Promise.reject(error);
    }

    // 对于其他错误，返回干净的错误消息（去掉错误码前缀）
    const cleanMessage = errorMessage.replace(/^\[\d+\]\s*/, '');
    error.response.data.message = cleanMessage;
    return Promise.reject(error);
  }
);

export const ethercatApi = {
  async getStatus() {
    const response = await api.get<ApiResponse<EthercatStatus>>('/ethercat/status');
    return response.data.data;
  },
  
  async startService() {
    const response = await api.post<ApiResponse<void>>('/ethercat/start');
    return response.data;
  },
  
  async stopService() {
    const response = await api.post<ApiResponse<void>>('/ethercat/stop');
    return response.data;
  },
  
  async getSlaves() {
    const response = await api.get<ApiResponse<SlaveDetail[]>>('/ethercat/slaves');
    return response.data.data;
  },
  
  async getSlaveXml(master: number, slave: number): Promise<string> {
    const response = await api.get(`/ethercat/xml/${master}/${slave}`);
    return response.data.data;
  },
  
  async getTopology(master: number): Promise<string> {
    const response = await api.get(`/ethercat/topology/${master}`, {
      responseType: 'text'
    });
    return response.data;
  },
  
  async getAvailableMasters(): Promise<number[]> {
    const response = await api.get('/ethercat/masters');
    return response.data.data;
  },
  
  async getNetworkInterfaces(): Promise<string[]> {
    const response = await api.get('/ethercat/network-interfaces');
    return response.data.data;
  },
  
  async getEtherCATConfig(): Promise<EtherCATConfig> {
    const response = await api.get('/ethercat/config');
    return response.data.data;
  },
  
  async updateEtherCATConfig(config: EtherCATConfig): Promise<void> {
    await api.post('/ethercat/config', config);
  },
  
  async restartService(): Promise<void> {
    await api.post('/ethercat/restart');
  },
  
  async getSlaveConfig(): Promise<any> {
    const response = await api.get('/ethercat/slave-config');
    return response.data.data;
  },
  
  async getTemplateConfig(): Promise<any> {
    const response = await api.get('/ethercat/template-config');
    return response.data.data;
  },
  
  async getBusTopology(): Promise<string> {
    const response = await api.get('/ethercat/bus-topology');
    return response.data.data;
  },
  
  async parseENIContent(xmlContent: string): Promise<any> {
    const response = await api.post('/ethercat/parse-eni', { xml: xmlContent });
    return response.data.data;
  },
  
  async generateSlaveConfig(config: EniConfig): Promise<any> {
    const response = await api.post('/ethercat/generate-config', config);
    return response.data.data;
  }
};

export const systemApi = {
  async getMetrics() {
    const response = await api.get<ApiResponse<SystemMetrics>>('/system/metrics');
    return response.data.data;
  },
  
  async getLogs(limit = 100) {
    const response = await api.get<ApiResponse<SystemLog[]>>(`/system/logs?limit=${limit}`);
    return response.data.data;
  },
  
  async getSlaveStatusCount() {
    const response = await api.get<ApiResponse<SlaveStatusCount>>('/ethercat/slave-status-count');
    return response.data.data;
  }
};

const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB per chunk

interface ChunkInfo {
  chunk: Blob;
  index: number;
  hash: string;
}

const uploadApi = axios.create({
  baseURL: '/api',
  timeout: 300000,
  maxContentLength: Infinity,
  maxBodyLength: Infinity
});

export const programApi = {
  // 计算文件hash
  async calculateHash(file: File): Promise<string> {
    return new Promise((resolve) => {
      const spark = new SparkMD5.ArrayBuffer();
      const reader = new FileReader();
      reader.readAsArrayBuffer(file);
      reader.onload = (e) => {
        spark.append(e.target?.result as ArrayBuffer);
        resolve(spark.end());
      };
    });
  },
  
  // 将文件分片
  createFileChunks(file: File): ChunkInfo[] {
    const chunks: ChunkInfo[] = [];
    let cur = 0;
    while (cur < file.size) {
      chunks.push({
        chunk: file.slice(cur, cur + CHUNK_SIZE),
        index: chunks.length,
        hash: ''
      });
      cur += CHUNK_SIZE;
    }
    return chunks;
  },
  
  // 上传单个分片
  async uploadChunk(chunk: ChunkInfo, fileName: string, fileHash: string): Promise<void> {
    const formData = new FormData();
    formData.append('chunk', chunk.chunk);
    formData.append('hash', fileHash);
    formData.append('filename', fileName);
    formData.append('index', chunk.index.toString());

    await uploadApi.post('/programs/chunk', formData);
  },
  
  // 上传程序
  async uploadProgram(formData: FormData, onProgress?: (progress: number) => void): Promise<any> {
    // 发送请求给后端
    const response = await api.post('/programs', formData, {
      // 上传进度回调
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const progress = progressEvent.loaded / progressEvent.total;
          onProgress(progress);
        }
      }
    });
    return response.data;
  },
  
  async getPrograms() {
    const response = await api.get<ApiResponse<Program[]>>('/programs');
    return response.data.data;
  },
  
  async startProgram(id: string) {
    const response = await api.post<ApiResponse<void>>(`/programs/${id}/start`);
    return response.data;
  },
  
  async stopProgram(id: string) {
    const response = await api.post<ApiResponse<void>>(`/programs/${id}/stop`);
    return response.data;
  },
  
  async deleteProgram(id: string) {
    const response = await api.delete<ApiResponse<void>>(`/programs/${id}`);
    return response.data;
  },
  
  async updateConfig(id: string, config: ProgramConfig) {
    const response = await api.put<ApiResponse<Program>>(`/programs/${id}/config`, config);
    return response.data.data;
  },
  
  async downloadTemplate(id: string): Promise<void> {
    const response = await api.get(`/programs/${id}/template`, {
      responseType: 'blob'
    });
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.download = `ethercat_${id}.c`;
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    
    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },
  
  async replaceProgram(id: string, formData: FormData, onProgress?: (progress: number) => void): Promise<any> {
    const response = await api.post(`/programs/${id}/replace`, formData, {
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const progress = progressEvent.loaded / progressEvent.total;
          onProgress(progress);
        }
      }
    });
    return response.data;
  },
  
  async generateCode(config: any, language: string): Promise<string> {
    const response = await api.post('/generator/generate', { config, language });
    return response.data.data;
  },
  
  async downloadFullPackage(id: string): Promise<ArrayBuffer> {
    const response = await api.get(`/programs/${id}/package`, {
      responseType: 'arraybuffer'
    });
    return response.data;
  },
  
  // 获取程序日志
  async getProgramLogs(id: string): Promise<string[]> {
    const response = await api.get<ApiResponse<string[]>>(`/programs/${id}/logs`);
    return response.data.data;
  },
  
  async downloadMiddlewareCode(id: string): Promise<Blob> {
    const response = await api.get(`/programs/${id}/middleware-code`, {
      responseType: 'blob'
    });
    return response.data;
  },
  
  // 启动中间层
  startMiddleware: async (id: string) => {
    const response = await axios.post(`/api/programs/${id}/start-middleware`);
    return response.data;
  },
  
  // 停止中间层
  stopMiddleware: async (id: string) => {
    const response = await axios.post(`/api/programs/${id}/stop-middleware`);
    return response.data;
  },
  
  // 更新程序自启动状态
  async updateStartup(id: string, startup: string, masterIndex: string) {
    const response = await api.put(`/programs/${id}/startup`, { startup, masterIndex });
    return response.data;
  },
  
  // 添加更新CPU绑定的方法
  async updateCpuBinding(id: string, programCpu: string, middlewareCpu: string): Promise<void> {
    const response = await api.put(`/programs/${id}/cpu-binding`, {
      programCpu,
      middlewareCpu
    });
    return response.data;
  },
  
  // 获取系统信息
  async getSystemInfo(): Promise<SystemInfo> {
    const response = await api.get('/programs/system-info');
    return response.data.data;
  },

  // 获取程序的 CPU 绑定信息
  async getCpuBinding(id: string): Promise<{ programCpu: string; middlewareCpu: string }> {
    const response = await api.get(`/programs/${id}/cpu-binding`);
    return response.data.data;
  },
  
  // 更新调试模式
  async updateDebugMode(isDebug: boolean): Promise<boolean> {
    const response = await api.post('/programs/debug-mode', { debug: isDebug });
    return response.data.data.debug;
  },
  
  // 获取调试模式状态
  async getDebugMode(): Promise<boolean> {
    const response = await api.get('/programs/debug-mode');
    return response.data.data.debug;
  }
};

// 更新类型定义
export interface SystemInfo {
  cpuCount: number;      // CPU核心数
  cpuModel: string;      // CPU型号
  totalMemory: number;   // 总内存(GB)
  freeMemory: number;    // 可用内存(GB)
  platform: string;      // 操作系统平台
  arch: string;         // CPU架构
  hostname: string;     // 主机名
  cpuDetails?: {        // CPU详细信息（仅Linux）
    processors: Array<{
      processor: number; // CPU核心编号
      model: string;    // 型号
      mhz: number;      // 频率
      cacheSize: string; // 缓存大小
    }>;
  };
}

export const authApi = {
  async login(credentials: { username: string; password: string }) {
    const response = await api.post<ApiResponse<{ token: string; user: any }>>('/auth/login', credentials);
    return response.data.data;
  },
  
  async logout() {
    const response = await api.post<ApiResponse<void>>('/auth/logout');
    return response.data;
  },
  
  async forgotPassword(email: string) {
    const response = await api.post<ApiResponse<void>>('/auth/forgot-password', { email });
    return response.data;
  },
  
  async changePassword(userId: string, oldPassword: string, password: string, passwordConfirm: string): Promise<void> {
    await api.post(`/auth/change-password/${userId}`, {
      oldPassword,
      password,
      passwordConfirm
    });
  },

  async refreshToken() {
    const response = await api.post<ApiResponse<{ token: string; user: any }>>('/auth/refresh');
    return response.data.data;
  }
};

export const userApi = {
  async getUsers() {
    const response = await api.get<ApiResponse<User[]>>('/users');
    return response.data.data;
  },
  
  async createUser(user: UserForm) {
    const response = await api.post<ApiResponse<User>>('/users', user);
    return response.data.data;
  },
  
  async updateUser(id: string, user: Partial<UserForm>) {
    const response = await api.put<ApiResponse<User>>(`/users/${id}`, user);
    return response.data.data;
  },
  
  async deleteUser(id: string) {
    const response = await api.delete<ApiResponse<void>>(`/users/${id}`);
    return response.data;
  },
  
  async resetPassword(id: string, password: string) {
    const response = await api.post<ApiResponse<void>>(`/users/${id}/reset-password`, { password });
    return response.data;
  }
};

export const templateApi = {
  async generateFromProgram(programId: string): Promise<string> {
    const response = await api.get(`/programs/${programId}/template`, {
      responseType: 'text'
    });
    return response.data;
  },
  
  async generateTemplate(config: any): Promise<string> {
    const response = await api.post('/template/generate', config);
    return response.data.data;
  },
  
  // 下载模板文件
  async downloadTemplate(config: any, filename?: string) {
    const response = await api.post('/template/download', { config, filename }, {
      responseType: 'blob'
    });
    return response.data;
  },
  
  // 从从站生成配置
  async generateConfig(master: number, slave: number) {
    const response = await api.get(`/template/config/${master}/${slave}`);
    return response.data.data;
  },
  
  // 保存置
  async saveConfig(config: any) {
    const response = await api.post('/template/config', config);
    return response.data.data;
  },
  
  // 获取配置列表
  async getConfigs() {
    const response = await api.get('/template/configs');
    return response.data.data;
  },
  
  // 获取指定配置
  async getConfig(id: string) {
    const response = await api.get(`/template/config/${id}`);
    return response.data.data;
  },
  
  // 删除配置
  async deleteConfig(id: string) {
    const response = await api.delete(`/template/config/${id}`);
    return response.data.data;
  }
};

export const dashboardApi = {
  async getStatus(): Promise<DashboardStatus> {
    const response = await api.get('/dashboard/status');
    return response.data.data;
  },
  
  async getVariableValues(programId: string): Promise<VariableValue[]> {
    const response = await api.get(`/dashboard/variables/${programId}`);
    return response.data.data;
  },
  
  async getEtherCATLogs(): Promise<EtherCATLog[]> {
    const response = await api.get('/dashboard/logs');
    return response.data.data;
  }
};

export interface MenuSettings {
  dashboard: boolean;
  ethercat: boolean;
  programs: boolean;
  users: boolean;
  settings: boolean;
}

export interface NetworkConfig {
  content: string;
}

export const settingsApi = {
  async getMenuSettings(): Promise<MenuSettings> {
    const response = await api.get('/settings/menu');
    return response.data.data;
  },
  
  async updateMenuSettings(settings: MenuSettings): Promise<void> {
    await api.post('/settings/menu', settings);
  },
  
  async getNetworkConfig(): Promise<NetworkConfig> {
    const response = await api.get('/settings/network');
    return response.data.data;
  },
  
  async updateNetworkConfig(config: NetworkConfig): Promise<void> {
    await api.post('/settings/network', config);
  }
};

export default api;