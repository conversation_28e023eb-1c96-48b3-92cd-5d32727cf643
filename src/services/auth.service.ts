import PocketBase from 'pocketbase';
import { ApiError } from '../utils/errors';

const pb = new PocketBase('http://127.0.0.1:8090');

export class AuthService {
  static async login(username: string, password: string) {
    try {
      const authData = await pb.collection('users').authWithPassword(
        username,
        password
      );
      
      return {
        token: authData.token,
        user: {
          id: authData.record.id,
          username: authData.record.username,
          email: authData.record.email,
          role: authData.record.role
        }
      };
    } catch (error) {
      throw new ApiError(401, '用户名或密码错误');
    }
  }

  static async logout(token: string) {
    try {
      await pb.authStore.clear();
      return true;
    } catch (error) {
      throw new ApiError(500, '退出登录失败');
    }
  }

  static async forgotPassword(email: string) {
    try {
      await pb.collection('users').requestPasswordReset(email);
      return true;
    } catch (error) {
      throw new ApiError(400, '发送重置密码邮件失败');
    }
  }

  static async resetPassword(token: string, password: string) {
    try {
      await pb.collection('users').confirmPasswordReset(
        token,
        password,
        password
      );
      return true;
    } catch (error) {
      throw new ApiError(400, '重置密码失败');
    }
  }

  static async verifyToken(token: string) {
    try {
      pb.authStore.save(token, null);
      const isValid = await pb.authStore.isValid;
      return isValid;
    } catch (error) {
      return false;
    }
  }
} 