import { Modu<PERSON> } from 'vuex';
import { RootState } from '../types';
import { authApi } from '@/services/api';

export interface AuthState {
  token: string | null;
  user: any | null;
  isAuthenticated: boolean;
  refreshing: boolean;
}

const auth: Module<AuthState, RootState> = {
  namespaced: true,

  state: {
    token: localStorage.getItem('token'),
    user: JSON.parse(localStorage.getItem('user') || 'null'),
    isAuthenticated: !!localStorage.getItem('token'),
    refreshing: false
  },

  mutations: {
    SET_TOKEN(state, token: string | null) {
      state.token = token;
      if (token) {
        localStorage.setItem('token', token);
      } else {
        localStorage.removeItem('token');
      }
    },

    SET_USER(state, user: any | null) {
      state.user = user;
      if (user) {
        localStorage.setItem('user', JSON.stringify(user));
      } else {
        localStorage.removeItem('user');
      }
    },

    SET_AUTH(state, isAuthenticated: boolean) {
      state.isAuthenticated = isAuthenticated;
    },

    SET_REFRESHING(state, refreshing: boolean) {
      state.refreshing = refreshing;
    },

    CLEAR_AUTH(state) {
      state.token = null;
      state.user = null;
      state.isAuthenticated = false;
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    }
  },

  actions: {
    async login({ commit }, credentials) {
      try {
        const response = await authApi.login(credentials);
        commit('SET_TOKEN', response.token);
        commit('SET_USER', response.user);
        commit('SET_AUTH', true);
        return response;
      } catch (error) {
        console.error('Login error:', error);
        throw error;
      }
    },

    async logout({ commit }) {
      try {
        await authApi.logout();
      } catch (error) {
        console.error('Logout error:', error);
      } finally {
        commit('CLEAR_AUTH');
      }
    },

    async refreshToken({ commit, state }) {
      // 如果已经在刷新中，则不重复刷新
      if (state.refreshing) {
        return;
      }

      try {
        commit('SET_REFRESHING', true);
        const response = await authApi.refreshToken();
        commit('SET_TOKEN', response.token);
        commit('SET_USER', response.user);
        commit('SET_AUTH', true);
        return response;
      } catch (error) {
        console.error('Token refresh error:', error);
        commit('CLEAR_AUTH');
        throw error;
      } finally {
        commit('SET_REFRESHING', false);
      }
    }
  },

  getters: {
    isAuthenticated: state => state.isAuthenticated,
    user: state => state.user,
    token: state => state.token,
    refreshing: state => state.refreshing
  }
};

export default auth;