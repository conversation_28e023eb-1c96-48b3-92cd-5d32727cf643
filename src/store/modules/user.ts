import { Module } from 'vuex';
import { RootState } from '../types';
import { userApi } from '@/services/api';
import type { User, UserForm } from '@/types';

export interface UserState {
  users: User[];
  loading: boolean;
}

const user: Module<UserState, RootState> = {
  namespaced: true,
  
  state: {
    users: [],
    loading: false
  },
  
  mutations: {
    SET_USERS(state, users: User[]) {
      state.users = users;
    },
    SET_LOADING(state, loading: boolean) {
      state.loading = loading;
    }
  },
  
  actions: {
    async fetchUsers({ commit }) {
      try {
        commit('SET_LOADING', true);
        const users = await userApi.getUsers();
        commit('SET_USERS', users);
      } finally {
        commit('SET_LOADING', false);
      }
    },
    
    async createUser({ dispatch }, user: UserForm) {
      await userApi.createUser(user);
      await dispatch('fetchUsers');
    },
    
    async updateUser({ dispatch }, { id, user }: { id: string; user: Partial<UserForm> }) {
      await userApi.updateUser(id, user);
      await dispatch('fetchUsers');
    },
    
    async deleteUser({ dispatch }, id: string) {
      await userApi.deleteUser(id);
      await dispatch('fetchUsers');
    },
    
    async resetPassword({ dispatch }, { id, password }: { id: string; password: string }) {
      await userApi.resetPassword(id, password);
    }
  }
};

export default user; 