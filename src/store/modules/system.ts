import { Module } from 'vuex';
import { RootState } from '../types';
import { systemApi } from '@/services/api';
import type { SystemMetrics, SystemLog } from '@/types';

export interface SystemState {
  metrics: SystemMetrics[];
  logs: SystemLog[];
  loading: boolean;
}

const system: Module<SystemState, RootState> = {
  namespaced: true,

  state: {
    metrics: [],
    logs: [],
    loading: false
  },

  mutations: {
    ADD_METRIC(state, metric: SystemMetrics) {
      state.metrics.push(metric);
      if (state.metrics.length > 100) {
        state.metrics.shift();
      }
    },
    SET_LOGS(state, logs: SystemLog[]) {
      state.logs = logs;
    },
    ADD_LOG(state, log: SystemLog) {
      state.logs.unshift(log);
      if (state.logs.length > 1000) {
        state.logs.pop();
      }
    },
    CLEAR_LOGS(state) {
      state.logs = [];
    },
    SET_LOADING(state, loading: boolean) {
      state.loading = loading;
    }
  },

  actions: {
    async fetchLogs({ commit }) {
      try {
        commit('SET_LOADING', true);
        const logs = await systemApi.getLogs();
        commit('SET_LOGS', logs);
      } finally {
        commit('SET_LOADING', false);
      }
    },

    updateMetric({ commit }, metric: SystemMetrics) {
      commit('ADD_METRIC', metric);
    }
  },

  getters: {
    latestMetric: state => state.metrics[state.metrics.length - 1],
    cpuUsage: state => state.metrics.map(m => [new Date(m.timestamp).getTime(), m.cpu_usage]),
    memoryUsage: state => state.metrics.map(m => [new Date(m.timestamp).getTime(), m.memory_usage])
  }
};

export default system; 