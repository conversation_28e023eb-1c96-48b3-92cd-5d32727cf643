import { Modu<PERSON> } from 'vuex';
import { RootState } from '../types';
import { ethercatApi } from '@/services/api';
import type { EthercatStatus, SlaveDetail } from '@/types';

export interface EthercatState {
  status: EthercatStatus | null;
  slaves: SlaveDetail[];
  loading: boolean;
  error: string | null;
}

const ethercat: Module<EthercatState, RootState> = {
  namespaced: true,

  state: {
    status: null,
    slaves: [],
    loading: false,
    error: null
  },

  mutations: {
    SET_STATUS(state, status: EthercatStatus) {
      state.status = status;
    },
    SET_SLAVES(state, slaves: SlaveDetail[]) {
      state.slaves = slaves;
    },
    SET_LOADING(state, loading: boolean) {
      state.loading = loading;
    },
    SET_ERROR(state, error: string | null) {
      state.error = error;
    }
  },

  actions: {
    async fetchStatus({ commit }) {
      try {
        commit('SET_LOADING', true);
        commit('SET_ERROR', null);
        const status = await ethercatApi.getStatus();
        commit('SET_STATUS', status);
        if (status.running) {
          const slaves = await ethercatApi.getSlaves();
          commit('SET_SLAVES', slaves);
        }
      } catch (error) {
        console.error('Failed to fetch EtherCAT status:', error);
        commit('SET_ERROR', '获取 EtherCAT 状态失败');
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async startService({ commit, dispatch }) {
      try {
        commit('SET_LOADING', true);
        commit('SET_ERROR', null);
        await ethercatApi.startService();
        await dispatch('fetchStatus');
      } catch (error) {
        console.error('Failed to start EtherCAT service:', error);
        commit('SET_ERROR', '启动 EtherCAT 服务失败');
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async stopService({ commit, dispatch }) {
      try {
        commit('SET_LOADING', true);
        commit('SET_ERROR', null);
        await ethercatApi.stopService();
        await dispatch('fetchStatus');
      } catch (error) {
        console.error('Failed to stop EtherCAT service:', error);
        commit('SET_ERROR', '停止 EtherCAT 服务失败');
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    }
  },

  getters: {
    isRunning: state => state.status?.running || false,
    slaveCount: state => state.slaves.length,
    runningSlaves: state => state.slaves.filter(s => s.state === 'OP').length,
    currentProgram: state => state.status?.currentProgram,
    hasError: state => !!state.error
  }
};

export default ethercat; 