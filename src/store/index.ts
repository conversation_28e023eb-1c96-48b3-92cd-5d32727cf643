import { createStore } from 'vuex';
import auth from './modules/auth';
import ethercat from './modules/ethercat';
import system from './modules/system';
import user from './modules/user';
import type { RootState } from './types';

const store = createStore<RootState>({
  modules: {
    auth,
    ethercat,
    system,
    user
  },
  state: () => ({} as RootState),
  mutations: {},
  actions: {},
  getters: {}
});

export default store; 