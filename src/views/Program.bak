<template>
  <!-- ... 其他代码保持不变 ... -->
  <a-table
    :columns="columns"
    :data-source="programs"
    :loading="loading"
  >
    <!-- ... 其他列保持不变 ... -->
    <template #action="{ record }">
      <a-space>
        <!-- 添加下载模板按钮 -->
        <a-button type="link" @click="downloadTemplate(record)">
          <template #icon>
            <CodeOutlined />
          </template>
          下载模板
        </a-button>
        <a-button type="link" @click="showConfig(record)">
          <template #icon>
            <SettingOutlined />
          </template>
          配置
        </a-button>
        <a-button 
          type="link" 
          :disabled="record.status === 'running'"
          @click="deleteProgram(record)"
        >
          <template #icon>
            <DeleteOutlined />
          </template>
          删除
        </a-button>
      </a-space>
    </template>
  </a-table>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { 
  UploadOutlined, 
  SettingOutlined, 
  DeleteOutlined,
  CodeOutlined  // 添加这个
} from '@ant-design/icons-vue';
import { programApi, templateApi } from '@/services/api';

export default defineComponent({
  components: {
    UploadOutlined,
    SettingOutlined,
    DeleteOutlined,
    CodeOutlined  // 添加这个
  },

  setup() {
    // ... 其他代码保持不变 ...

    const downloadTemplate = async (record: any) => {
      try {
        // 显示加载状态
        message.loading({ content: '正在生成模板...', key: 'template' });
        
        // 生成模板
        const template = await templateApi.generateFromProgram(record.id);
        
        // 创建下载
        const blob = new Blob([template], { type: 'text/x-c' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${record.name}_template.c`;
        
        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        // 显示成功消息
        message.success({ content: '模板生成成功', key: 'template' });
      } catch (error) {
        console.error('Failed to download template:', error);
        message.error({ content: '模板生成失败', key: 'template' });
      }
    };

    return {
      // ... 其他返回值保持不变 ...
      downloadTemplate
    };
  }
});
</script>

<style lang="less" scoped>
// ... 样式保持不变 ...
</style> 