<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <img src="@/assets/logo.png" alt="logo" />
        </div>
        <h2 class="title">边缘控制器</h2>
      </div>
      <a-form
        :model="formState"
        @finish="handleSubmit"
        layout="vertical"
        class="login-form"
      >
        <a-form-item
          label="用户名"
          name="username"
          :rules="[{ required: true, message: '请输入用户名' }]"
        >
          <a-input v-model:value="formState.username" size="large" />
        </a-form-item>

        <a-form-item
          label="密码"
          name="password"
          :rules="[{ required: true, message: '请输入密码' }]"
        >
          <a-input-password v-model:value="formState.password" size="large" />
        </a-form-item>

        <a-form-item>
          <a-button type="primary" html-type="submit" :loading="loading" size="large" block>
            登录
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

export default defineComponent({
  name: 'LoginPage',

  setup() {
    const store = useStore();
    const router = useRouter();
    const loading = ref(false);

    const formState = reactive({
      username: '',
      password: ''
    });

    const handleSubmit = async () => {
      try {
        loading.value = true;
        await store.dispatch('auth/login', formState);
        message.success('登录成功');
        router.push('/');
      } catch (error) {
        message.error('登录失败');
        console.error(error);
      } finally {
        loading.value = false;
      }
    };

    return {
      formState,
      loading,
      handleSubmit
    };
  }
});
</script>

<style lang="less" scoped>
@import '../styles/variables.less';

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #F6F8FC 0%, #E9EEF6 100%);
}

.login-card {
  width: 400px;
  padding: 32px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 
    0 4px 24px rgba(0, 0, 0, 0.08),
    0 1px 2px rgba(0, 0, 0, 0.04);
}

.login-header {
  text-align: center;
  margin-bottom: 36px;

  .logo {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;

    img {
      width: 100%;
      height: auto;
    }
  }

  .title {
    font-size: 24px;
    font-weight: 600;
    color: @heading-color;
    margin: 0;
  }
}

.login-form {
  :deep(.ant-form-item-label) {
    label {
      color: @text-color;
      font-weight: 500;
      font-size: 14px;
    }
  }

  :deep(.ant-input), :deep(.ant-input-password) {
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    background: rgba(255, 255, 255, 0.9);
    height: 44px;
    padding: 0 16px;
    transition: all 0.3s ease;
    
    &:hover, &:focus {
      border-color: @primary-color;
      background: #fff;
      box-shadow: 0 0 0 2px fade(@primary-color, 10%);
    }

    input {
      height: 42px;
      font-size: 14px;
    }
  }

  :deep(.ant-input-password) {
    .ant-input {
      height: 42px;
      background: transparent;
      border: none;
      padding: 0;
      box-shadow: none;
    }
  }

  :deep(.ant-btn) {
    height: 44px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    box-shadow: 0 2px 8px fade(@primary-color, 25%);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px fade(@primary-color, 35%);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px fade(@primary-color, 20%);
    }
  }
}
</style> 