<template>
  <div class="settings">
    <a-tabs>
      <a-tab-pane key="network" tab="网络设置">
        <div class="network-settings">
          <div class="editor-container">
            <div class="editor-header">
              <h3>网络配置编辑</h3>
              <p class="description">
                左侧编辑配置模板，右侧显示当前系统配置。点击同步按钮将左侧配置应用到系统。
              </p>
            </div>
            
            <div class="editor-panels">
              <!-- 左侧编辑面板 -->
              <div class="panel">
                <div class="panel-header">
                  <h4>配置模板</h4>
                  <a-button 
                    type="link" 
                    size="small"
                    @click="useDefaultTemplate"
                  >
                    使用默认模板
                  </a-button>
                </div>
                <a-textarea
                  v-model:value="templateContent"
                  :rows="20"
                  :spellcheck="false"
                  class="config-editor"
                  @input="handleTemplateChange"
                />
              </div>

              <!-- 右侧只读面板 -->
              <div class="panel">
                <div class="panel-header">
                  <h4>当前系统配置</h4>
                  <a-button 
                    type="link" 
                    size="small"
                    @click="copyToTemplate"
                  >
                    复制到左侧
                  </a-button>
                </div>
                <a-textarea
                  v-model:value="systemContent"
                  :rows="20"
                  :spellcheck="false"
                  class="config-editor"
                  :disabled="true"
                />
              </div>
            </div>

            <div class="validation-message" v-if="validationError">
              <a-alert
                :message="validationError"
                type="error"
                show-icon
              />
            </div>

            <!-- 添加进度提示 -->
            <div class="progress-message" v-if="syncing">
              <a-alert
                type="info"
                show-icon
              >
                <template #message>
                  <div class="progress-content">
                    <span>正在重启网络服务</span>
                  </div>
                </template>
                <template #description>
                  <p>请耐心等待，重启过程中可能暂时无法访问网络</p>
                  <p v-if="hasDhcp">DHCP配置可能需要较长时间等待IP地址分配</p>
                </template>
              </a-alert>
            </div>

            <div class="actions">
              <a-space>
                <a-button 
                  type="primary" 
                  @click="syncConfig" 
                  :loading="syncing"
                  :disabled="loading || syncing || !hasConfigChanged"
                >
                  {{ syncing ? '正在同步' : '同步到系统' }}
                </a-button>
                <a-tooltip :title="hasConfigChanged ? '放弃修改并刷新' : '刷新当前配置'">
                  <a-button 
                    @click="refreshConfig" 
                    :loading="loading"
                    :disabled="syncing"
                  >
                    刷新配置
                  </a-button>
                </a-tooltip>
              </a-space>
            </div>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- 同步确认对话框 -->
    <a-modal
      v-model:visible="confirmVisible"
      title="确认更新网络配置"
      @ok="confirmSync"
      :okButtonProps="{ danger: true }"
      okText="确认更新"
      cancelText="取消"
    >
      <p>确定要将左侧配置同步到系统吗？</p>
      <p>注意：</p>
      <ul>
        <li>错误的配置可能导致系统无法连接到网络</li>
        <li>配置更新后，网络服务将自动重启</li>
        <li>如果新的配置中修改了当前连接的网口，可能会导致当前连接断开，请注意</li>
      </ul>
    </a-modal>

    <!-- 添加同步过程遮罩层 -->
    <a-modal
      :visible="syncing"
      :closable="false"
      :maskClosable="false"
      :footer="null"
      :width="400"
      class="sync-modal"
    >
      <div class="sync-progress">
        <a-spin size="large" />
        <div class="sync-status">
          <h3>正在同步网络配置</h3>
          <p class="sync-tip">{{ getSyncTip }}</p>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { settingsApi } from '@/services/api';
import { h } from 'vue'; // Added h import

// 默认网络配置模板
const DEFAULT_TEMPLATE = `auto lo
iface lo inet loopback

allow-hotplug eth0
iface eth0 inet dhcp
#address ************
#netmask *************

allow-hotplug eth1
iface eth1 inet static
address *************
netmask *************

allow-hotplug eth2
iface eth2 inet static
address ***************
netmask *************`;

export default defineComponent({
  name: 'SystemSettings',
  setup() {
    const templateContent = ref(DEFAULT_TEMPLATE);
    const systemContent = ref('');
    const validationError = ref('');
    const confirmVisible = ref(false);
    const loading = ref(false);
    const syncing = ref(false);
    // 移除 templateChanged，改用 initialized 标记是否完成初始化
    const initialized = ref(false);
    const hasDhcp = ref(false);

    // 比较配置是否有变化（忽略空格和换行符的差异）
    const normalizeConfig = (config: string) => {
      return config.split('\n')
        .map(line => line.trim())
        .filter(line => line && !line.startsWith('#'))
        .join('\n');
    };

    const hasConfigChanged = computed(() => {
      const normalizedTemplate = normalizeConfig(templateContent.value);
      const normalizedSystem = normalizeConfig(systemContent.value);
      return normalizedTemplate !== normalizedSystem;
    });

    const refreshConfig = async () => {
      loading.value = true;
      try {
        const config = await settingsApi.getNetworkConfig();
        systemContent.value = config.content;
        
        // 只在第一次初始化时更新模板内容
        if (!initialized.value) {
          templateContent.value = config.content;
          initialized.value = true;
        }
        
        validationError.value = '';
        message.success('配置已刷新');

        // 如果有未保存的修改，提醒用户
        if (hasConfigChanged.value) {
          message.warning('当前有未同步的修改');
        }
      } catch (error: any) {
        message.error('获取网络配置失败');
      } finally {
        loading.value = false;
      }
    };

    const handleTemplateChange = () => {
      validationError.value = '';
    };

    const useDefaultTemplate = () => {
      templateContent.value = DEFAULT_TEMPLATE;
    };

    const copyToTemplate = () => {
      templateContent.value = systemContent.value;
    };

    const hasDhcpConfig = (content: string): boolean => {
      return content.split('\n').some(line => 
        line.trim().toLowerCase().includes('inet dhcp')
      );
    };

    const syncConfig = () => {
      if (!hasConfigChanged.value) {
        message.info('配置未发生变化，无需同步');
        return;
      }

      if (hasDhcpConfig(templateContent.value)) {
        hasDhcp.value = true;
        Modal.confirm({
          title: 'DHCP 配置提醒',
          content: h('div', {}, [
            h('p', '检测到配置中包含 DHCP 设置，请注意：'),
            h('ul', [
              h('li', '使用 DHCP 可能需要较长时间等待 IP 地址分配'),
              h('li', '网络服务重启时间可能会超过 30 秒'),
              h('li', '如果 DHCP 服务器无响应，可能导致网络配置失败')
            ]),
            h('p', { style: { marginTop: '16px' } }, '是否继续应用配置？')
          ]),
          okText: '继续应用',
          cancelText: '取消',
          onOk: () => {
            confirmVisible.value = true;
          }
        });
      } else {
        hasDhcp.value = false;
        confirmVisible.value = true;
      }
    };

    const confirmSync = async () => {
      if (!hasConfigChanged.value) {
        message.info('配置未发生变化，无需同步');
        return;
      }

      syncing.value = true;
      try {
        await settingsApi.updateNetworkConfig({
          content: templateContent.value
        });
        
        confirmVisible.value = false;
        message.success('网络配置已更新');
        
        // 立即检查配置
        try {
          await refreshConfig();
          syncing.value = false;
          message.success('网络配置已更新完成');
        } catch (error) {
          // 如果第一次请求失败，开始轮询重试
          const checkConfig = async () => {
            try {
              await refreshConfig();
              syncing.value = false;
              message.success('网络配置已更新完成');
            } catch (error) {
              // 如果请求失败（可能是网络断开），继续尝试
              setTimeout(checkConfig, 3000);
            }
          };
          // 开始轮询检查
          setTimeout(checkConfig, 3000);
        }

      } catch (error: any) {
        syncing.value = false;
        validationError.value = error.response?.data?.message || '配置更新失败';
        message.error(validationError.value);
      }
    };

    onMounted(() => {
      refreshConfig();
    });

    // 添加同步提示文本
    const getSyncTip = computed(() => {
      if (hasDhcp.value) {
        return '正在等待 DHCP 分配 IP 地址，这可能需要较长时间...';
      }
      return '请耐心等待，重启过程中可能暂时无法访问网络';
    });

    return {
      templateContent,
      systemContent,
      validationError,
      confirmVisible,
      loading,
      syncing,
      hasConfigChanged,
      refreshConfig,
      handleTemplateChange,
      useDefaultTemplate,
      copyToTemplate,
      syncConfig,
      confirmSync,
      hasDhcp,
      getSyncTip
    };
  }
});
</script>

<style lang="less" scoped>
.settings {
  .network-settings {
    padding: 24px;

    .editor-container {
      max-width: 1200px;
      margin: 0 auto;

      .editor-header {
        margin-bottom: 16px;

        h3 {
          margin-bottom: 8px;
        }

        .description {
          color: #666;
          margin-bottom: 0;
        }
      }

      .editor-panels {
        display: flex;
        gap: 24px;
        margin-bottom: 16px;

        .panel {
          flex: 1;
          
          .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            h4 {
              margin: 0;
              font-size: 16px;
              font-weight: 600;
            }
          }

          .config-editor {
            font-family: "Consolas", "Monaco", monospace;
            font-size: 15px;
            font-weight: 500;
            line-height: 1.6;
            letter-spacing: 0.3px;

            // 暗色主题样式
            :deep(.ant-input) {
              padding: 12px 16px;
              color: #e8e8e8;
              background-color: #1e1e1e;
              border-color: #333;
              
              &::placeholder {
                color: #666;
              }

              &:hover, &:focus {
                border-color: #177ddc;
                box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
              }
            }

            &[disabled] {
              :deep(.ant-input) {
                background-color: #2d2d2d;
                cursor: not-allowed;
                color: #d0d0d0;
                font-weight: 500;
                opacity: 0.9;

                &:hover {
                  border-color: #333;
                }
              }
            }
          }
        }
      }

      .validation-message {
        margin-bottom: 16px;
      }

      .progress-message {
        margin-bottom: 16px;

        .progress-content {
          display: flex;
          align-items: center;
          gap: 16px;

          .countdown {
            min-width: 100px;
          }

          :deep(.ant-progress) {
            flex: 1;
            margin: 0;
          }
        }
      }

      .actions {
        text-align: right;
      }
    }
  }

  // 添加同步遮罩层样式
  :deep(.sync-modal) {
    .ant-modal-content {
      background: rgba(0, 0, 0, 0.75);
      border: 1px solid #333;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
      
      .ant-modal-body {
        padding: 32px 24px;
      }
    }
  }

  .sync-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #fff;
    text-align: center;

    .ant-spin {
      margin-bottom: 24px;

      :deep(.ant-spin-dot-item) {
        background-color: #177ddc;
      }
    }

    .sync-status {
      width: 100%;

      h3 {
        color: #fff;
        margin-bottom: 16px;
      }

      p {
        margin-bottom: 12px;
        color: rgba(255, 255, 255, 0.85);
      }

      .sync-tip {
        margin-top: 16px;
        color: rgba(255, 255, 255, 0.65);
        font-size: 13px;
      }

      :deep(.ant-progress-bg) {
        background-color: #177ddc;
      }
    }
  }
}
</style> 