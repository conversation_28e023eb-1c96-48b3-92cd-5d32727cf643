<template>
  <div class="dashboard">
    <a-row :gutter="[16, 16]">
      <a-col :span="8">
        <a-card class="stat-card">
          <template #title>
            <div class="card-header">
              <ApiOutlined />
              <span>EtherCAT状态</span>
            </div>
          </template>
          <div class="card-content">
            <a-tag :color="ethercatStatus ? 'success' : 'error'">
              {{ ethercatStatus ? '运行中' : '已停止' }}
            </a-tag>
            <div class="update-time">
              最后更新: {{ lastUpdateTime }}
            </div>
          </div>
        </a-card>
      </a-col>
      
      <a-col :span="8">
        <a-card class="stat-card">
          <template #title>
            <div class="card-header">
              <ClusterOutlined />
              <span>从站数量</span>
            </div>
          </template>
          <div class="card-content">
            <span class="stat-number">{{ slaveCount }}</span>
            <span class="stat-label">个从站</span>
          </div>
        </a-card>
      </a-col>
      
      <a-col :span="8">
        <a-card class="stat-card">
          <template #title>
            <div class="card-header">
              <CodeOutlined />
              <span>运行程序</span>
            </div>
          </template>
          <div class="card-content">
            <template v-if="currentProgram">
              <div class="program-info">
                <span class="program-name">{{ currentProgram }}</span>
                <a-tag color="processing">运行中</a-tag>
              </div>
              <div class="program-uptime">运行时长: {{ programUptime }}</div>
            </template>
            <template v-else>
              <a-empty description="暂无运行程序" />
            </template>
          </div>
        </a-card>
      </a-col>
    </a-row>
    
    <a-row :gutter="[16, 16]" style="margin-top: 16px">
      <a-col :span="24">
        <a-card class="monitor-card" title="实时变量监控">
          <template #extra>
            <a-button type="primary" size="small" @click="refreshVariables">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </template>
          <div class="variables-grid">
            <div v-for="variable in monitorVariables" :key="variable.name" class="variable-item">
              <div class="variable-header">
                <span class="variable-name">{{ variable.name }}</span>
                <a-badge :status="variable.value ? 'success' : 'default'" />
              </div>
              <div class="variable-value" :class="{ active: variable.value }">
                {{ formatValue(variable.value, variable.type) }}
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
    
    <a-row :gutter="[16, 16]" style="margin-top: 16px">
      <a-col :span="24">
        <a-card class="log-card" title="EtherCAT 日志">
          <template #extra>
            <a-space>
              <a-select
                v-model:value="logLevel"
                style="width: 100px"
                size="small"
              >
                <a-select-option value="all">全部</a-select-option>
                <a-select-option value="error">错误</a-select-option>
                <a-select-option value="warning">警告</a-select-option>
              </a-select>
              <a-button type="primary" size="small" @click="refreshLogs">
                <template #icon><ReloadOutlined /></template>
                刷新
              </a-button>
            </a-space>
          </template>
          <div class="log-content">
            <div
              v-for="log in filteredLogs"
              :key="log.time"
              :class="['log-item', `log-${log.level.toLowerCase()}`]"
            >
              <span class="log-time">{{ log.time }}</span>
              <span class="log-level">{{ log.level }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted, computed } from 'vue';
import { 
  ApiOutlined, 
  ClusterOutlined, 
  CodeOutlined,
  ReloadOutlined 
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useStore } from 'vuex';
import { dashboardApi } from '@/services/api';
import type { VariableValue, EtherCATLog } from '@/types';

export default defineComponent({
  name: 'DashboardView',
  
  components: {
    ApiOutlined,
    ClusterOutlined,
    CodeOutlined,
    ReloadOutlined
  },
  
  setup() {
    const store = useStore();
    const ethercatStatus = ref(false);
    const slaveCount = ref(0);
    const currentProgram = ref('');
    const lastUpdateTime = ref('');
    const programUptime = ref('');
    
    // 变量监控相关
    const monitorVariables = ref<VariableValue[]>([]);
    const logLevel = ref('all');
    const etherCATLogs = ref<EtherCATLog[]>([]);

    const formatValue = (value: any, type: string) => {
      switch (type) {
        case 'float':
          return Number(value).toFixed(2);
        case 'bool':
          return value ? 'ON' : 'OFF';
        default:
          return value;
      }
    };

    const filteredLogs = computed(() => {
      if (logLevel.value === 'all') return etherCATLogs.value;
      return etherCATLogs.value.filter(log => log.level.toLowerCase() === logLevel.value);
    });

    const refreshVariables = async () => {
      if (!currentProgram.value) return;
      
      try {
        const variables = await dashboardApi.getVariableValues(currentProgram.value);
        monitorVariables.value = variables;
      } catch (error) {
        console.error('Failed to refresh variables:', error);
        message.error('刷新变量失败');
      }
    };

    const refreshLogs = async () => {
      try {
        const logs = await dashboardApi.getEtherCATLogs();
        etherCATLogs.value = logs;
      } catch (error) {
        console.error('Failed to refresh logs:', error);
        message.error('刷新日志失败');
      }
    };

    // 定时刷新
    let refreshTimer: number;

    const fetchData = async () => {
      try {
        const status = await store.dispatch('fetchEthercatStatus');
        ethercatStatus.value = status.running;
        slaveCount.value = status.slaveCount;
        currentProgram.value = status.currentProgram;
        lastUpdateTime.value = dayjs().format('HH:mm:ss');
        
        if (status.running) {
          await refreshVariables();
          await refreshLogs();
        }
      } catch (error) {
        console.error(error);
      }
    };

    onMounted(() => {
      fetchData();
      refreshTimer = window.setInterval(fetchData, 1000);
    });

    onUnmounted(() => {
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
    });

    return {
      ethercatStatus,
      slaveCount,
      currentProgram,
      lastUpdateTime,
      programUptime,
      monitorVariables,
      logLevel,
      filteredLogs,
      formatValue,
      refreshVariables,
      refreshLogs
    };
  }
});
</script>

<style lang="less" scoped>
@import 'ant-design-vue/lib/style/themes/default.less';

.dashboard {
  .stat-card {
    height: 180px;
    
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .anticon {
        font-size: 18px;
      }
    }
    
    .card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: calc(100% - 50px);
      
      .stat-number {
        font-size: 36px;
        font-weight: bold;
        color: @heading-color;
      }
      
      .stat-label {
        margin-top: 8px;
        color: @text-color-secondary;
      }
      
      .update-time {
        margin-top: 16px;
        font-size: 12px;
        color: @text-color-secondary;
      }
      
      .program-info {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .program-name {
          font-size: 16px;
          font-weight: 500;
        }
      }
      
      .program-uptime {
        margin-top: 8px;
        font-size: 12px;
        color: @text-color-secondary;
      }
    }
  }
  
  .chart-card {
    margin-top: 16px;
  }
  
  .log-card {
    .log-content {
      height: 300px;
      overflow-y: auto;
      
      .log-item {
        padding: 8px;
        border-bottom: 1px solid @border-color-base;
        font-family: monospace;
        
        &:last-child {
          border-bottom: none;
        }
        
        .log-time {
          color: @text-color-secondary;
          margin-right: 8px;
        }
        
        .log-level {
          padding: 2px 6px;
          border-radius: 4px;
          margin-right: 8px;
          font-size: 12px;
        }
        
        &.log-info .log-level {
          background: fade(@primary-color, 10%);
          color: @primary-color;
        }
        
        &.log-warning .log-level {
          background: fade(@warning-color, 10%);
          color: @warning-color;
        }
        
        &.log-error .log-level {
          background: fade(@error-color, 10%);
          color: @error-color;
        }
      }
    }
  }
}

.variables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 8px;
}

.variable-item {
  padding: 12px;
  border: 1px solid @border-color-base;
  border-radius: 4px;
  background: @component-background;

  .variable-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .variable-name {
      font-weight: 500;
      color: @heading-color;
    }
  }

  .variable-value {
    font-family: monospace;
    font-size: 16px;
    color: @text-color-secondary;
    
    &.active {
      color: @success-color;
    }
  }
}
</style> 