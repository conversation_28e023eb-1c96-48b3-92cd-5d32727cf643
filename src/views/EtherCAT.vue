<template>
  <div class="ethercat">
    <!-- 状态卡片 -->
    <a-row :gutter="[16, 16]" class="status-cards">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="服务状态"
            :value="loading ? '加载中' : (serviceStatus ? '运行中' : '已停止')"
            :value-style="{ 
              color: loading ? '#8E8E93' : (serviceStatus ? '#34C759' : '#FF3B30') 
            }"
          >
            <template #prefix>
              <PoweroffOutlined :spin="loading" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="主站总数"
            :value="serviceStatus ? masters.length : '--'"
          >
            <template #prefix>
              <ApiOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="活动主站"
            :value="serviceStatus ? activeMasters : '--'"
            :value-style="{ color: serviceStatus ? '#34C759' : '#8E8E93' }"
          >
            <template #prefix>
              <CheckCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="从站总数"
            :value="serviceStatus ? totalSlaves : '--'"
          >
            <template #prefix>
              <ClusterOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 控制面板 -->
    <a-card class="control-panel">
      <template #title>
        <div class="panel-title">
          <ApiOutlined />
          <span>EtherCAT 控制面板</span>
        </div>
      </template>
      
      <template #extra>
        <a-space>
          <a-select
            v-model:value="refreshInterval"
            style="width: 120px"
            size="small"
          >
            <a-select-option :value="0">手动刷新</a-select-option>
            <a-select-option :value="1000">1 秒</a-select-option>
            <a-select-option :value="3000">3 秒</a-select-option>
            <a-select-option :value="5000">5 秒</a-select-option>
            <a-select-option :value="10000">10 秒</a-select-option>
          </a-select>
          
          <a-button 
            type="primary" 
            @click="startService" 
            :loading="loading"
            :disabled="serviceStatus"
          >
            启动服务
          </a-button>
          <a-button 
            danger 
            @click="stopService" 
            :loading="loading"
            :disabled="!serviceStatus"
          >
            停止服务
          </a-button>
          <a-button @click="refreshStatus" :loading="refreshing">
            <template #icon><ReloadOutlined /></template>
            刷新状态
          </a-button>
          <a-button type="primary" @click="showNetworkConfig">
            <template #icon><SettingOutlined /></template>
            主站配置
          </a-button>
          <a-button type="primary" @click="downloadConfig">
            <template #icon><DownloadOutlined /></template>
            下载从站配置
          </a-button>
        </a-space>
      </template>

      <!-- 主站信息表格 -->
      <div class="section-title">主站信息</div>
      <a-table
        v-if="masters.length"
        :columns="masterColumns"
        :data-source="masters"
        :loading="loading"
        rowKey="index"
        size="small"
      >
        <template #phase="{ text }">
          <a-tag :color="getPhaseColor(text)">
            {{ getPhaseText(text) }}
          </a-tag>
        </template>
        
        <template #active="{ text }">
          <a-tag :color="text ? 'success' : 'default'">
            {{ text ? '是' : '否' }}
          </a-tag>
        </template>

        <template #link="{ text }">
          <a-tag :color="text?.toUpperCase() === 'UP' ? 'success' : 'error'">
            {{ text?.toUpperCase() === 'UP' ? '已连接' : '已断开' }}
          </a-tag>
        </template>

        <template #stats="{ record }">
          <a-tooltip>
            <template #title>
              <div class="stats-tooltip">
                <div class="stats-group">
                  <div class="stats-title">传输统计</div>
                  <div class="stats-item">
                    <span class="label">发送帧数:</span>
                    <span class="value">{{ record.txFrames }}</span>
                  </div>
                  <div class="stats-item">
                    <span class="label">接收帧数:</span>
                    <span class="value">{{ record.rxFrames }}</span>
                  </div>
                  <div class="stats-item">
                    <span class="label">发送字:</span>
                    <span class="value">{{ formatBytes(record.txBytes) }}</span>
                  </div>
                  <div class="stats-item">
                    <span class="label">接收字节:</span>
                    <span class="value">{{ formatBytes(record.rxBytes) }}</span>
                  </div>
                  <div class="stats-item">
                    <span class="label">丢失帧数:</span>
                    <span class="value">{{ record.lostFrames }}</span>
                  </div>
                </div>
                <div class="stats-group">
                  <div class="stats-title">速率统计</div>
                  <div class="stats-item">
                    <span class="label">发送帧率:</span>
                    <span class="value">{{ record.txFrameRate.join('/') }} fps</span>
                  </div>
                  <div class="stats-item">
                    <span class="label">发送速率:</span>
                    <span class="value">{{ record.txRate.join('/') }} KB/s</span>
                  </div>
                  <div class="stats-item">
                    <span class="label">接收帧率:</span>
                    <span class="value">{{ record.rxFrameRate.join('/') }} fps</span>
                  </div>
                  <div class="stats-item">
                    <span class="label">接收速率:</span>
                    <span class="value">{{ record.rxRate.join('/') }} KB/s</span>
                  </div>
                  <div class="stats-item">
                    <span class="label">丢失率:</span>
                    <span class="value">{{ record.lossRate.join('/') }} fps</span>
                  </div>
                  <div class="stats-item">
                    <span class="label">帧丢失率:</span>
                    <span class="value">{{ record.frameLoss.join('/') }}%</span>
                  </div>
                </div>
              </div>
            </template>
            <InfoCircleOutlined />
          </a-tooltip>
        </template>
        <template #action="{ record }">
          <a-button type="link" @click="showTopology(record)">
            <NodeIndexOutlined />
            总线拓扑图
          </a-button>
        </template>
      </a-table>
      <a-empty 
        v-else
        :description="!serviceStatus ? '服务未运行，无法获取主站信息' : 'No Data'"
        :image="!serviceStatus ? Empty.PRESENTED_IMAGE_SIMPLE : Empty.PRESENTED_IMAGE_DEFAULT"
      />

      <!-- 从站信息表格 -->
      <div class="section-title">
        从站信息
        <a-space>
          <a-button type="primary" @click="showConfigGenerator">
            <template #icon><PlusOutlined /></template>
            从站JSON模板生成器
          </a-button>
        </a-space>
      </div>
      <a-table
        v-if="slaveGroups.length"
        :columns="slaveColumns"
        :data-source="slaveGroups"
        :loading="loading"
        rowKey="master"
        size="small"
        :expandedRowKeys="expandedKeys"
        @expandedRowsChange="handleExpandChange"
      >
        <template #expandedRowRender="{ record }">
          <a-table
            :columns="expandedColumns"
            :data-source="record.slaves"
            :pagination="false"
            rowKey="index"
            size="small"
          >
            <template #state="{ text }">
              <a-tag :color="getStateColor(text)">
                {{ getStateText(text) }}
              </a-tag>
            </template>
            <template #action="{ record }">
              <a-space>
                <a-button type="link" @click="showSlaveDetail(record)">
                  <EyeOutlined />
                  详情
                </a-button>
                <a-button type="link" @click="downloadXml(record)">
                  <DownloadOutlined />
                  下载XML
                </a-button>
              </a-space>
            </template>
          </a-table>
        </template>
      </a-table>
      <a-empty 
        v-else
        :description="!serviceStatus ? '服务未运行，无法获取从站信息' : 'No Data'"
        :image="!serviceStatus ? Empty.PRESENTED_IMAGE_SIMPLE : Empty.PRESENTED_IMAGE_DEFAULT"
      />
    </a-card>

    <!-- 从站详情对话框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="`从站详情 - ${currentSlave?.deviceName || ''}`"
      width="60%"
      :footer="null"
      :bodyStyle="{ maxHeight: '85vh', overflow: 'auto', padding: '12px 24px' }"
      :style="{ top: '20px' }"
    >
      <a-tabs v-model:activeKey="activeTab" class="slave-detail-tabs">
        <a-tab-pane key="basic" tab="基本信息">
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="从站索引">
              {{ currentSlave?.index }}
            </a-descriptions-item>
            <a-descriptions-item label="Device">
              {{ currentSlave?.name }}
            </a-descriptions-item>
            <a-descriptions-item label="Device name">
              {{ currentSlave?.deviceName }}
            </a-descriptions-item>
            <a-descriptions-item label="Vendor Id">
              {{ currentSlave?.vendorId }}
            </a-descriptions-item>
            <a-descriptions-item label="Product code">
              {{ currentSlave?.productCode }}
            </a-descriptions-item>
            <a-descriptions-item label="Revision number">
              {{ currentSlave?.revision }}
            </a-descriptions-item>
            <a-descriptions-item label="Serial number">
              {{ currentSlave?.serial }}
            </a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag :color="getStateColor(currentSlave?.state)">
                {{ getStateText(currentSlave?.state) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="Group">
              {{ currentSlave?.group }}
            </a-descriptions-item>
            <a-descriptions-item label="Order number">
              {{ currentSlave?.orderNumber }}
            </a-descriptions-item>
            <a-descriptions-item label="Current consumption">
              {{ currentSlave?.currentConsumption }} mA
            </a-descriptions-item>
          </a-descriptions>

          <div class="section-title">DL Information</div>
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="FMMU bit operation">
              {{ currentSlave?.fmmuBitOperation ? 'yes' : 'no' }}
            </a-descriptions-item>
            <a-descriptions-item label="Distributed clocks">
              {{ currentSlave?.distributedClocks }}
            </a-descriptions-item>
            <a-descriptions-item label="DC system time delay">
              {{ currentSlave?.dcSystemTimeDelay }} ns
            </a-descriptions-item>
          </a-descriptions>

          <div class="section-title">Port Information</div>
          <a-table
            :columns="portColumns"
            :dataSource="currentSlave?.ports"
            :pagination="false"
            size="small"
            :scroll="{ x: 1200 }"
            bordered
          >
            <template #link="{ text }">
              <a-tag :color="text === 'up' ? 'success' : 'default'">
                {{ text }}
              </a-tag>
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="pdo" tab="PDO配置">
          <a-collapse v-model:activeKey="activeSM">
            <a-collapse-panel 
              v-for="sm in currentSlave?.syncManagers" 
              :key="sm.index"
            >
              <template #header>
                <span class="sm-header">
                  SM{{ sm.index }}: PhysAddr {{ sm.physAddr }}, DefaultSize {{ sm.defaultSize }}, 
                  ControlRegister {{ sm.controlRegister }}, Enable {{ sm.enable ? 1 : 0 }}
                  <a-tag v-if="sm.pdos?.length > 0" class="data-tag" color="processing">
                    有数据
                  </a-tag>
                </span>
              </template>
              
              <template v-for="pdo in sm.pdos" :key="pdo.index">
                <div class="pdo-header">
                  <a-tag :color="pdo.type === 'RxPDO' ? 'error' : 'success'" style="margin-right: 8px">
                    {{ pdo.type }}
                  </a-tag>
                  {{ pdo.index }} "{{ pdo.name }}"
                </div>
                <a-table
                  :columns="pdoColumns"
                  :data-source="pdo.entries"
                  :pagination="false"
                  size="small"
                  :class="{ 'rx-pdo-table': pdo.type === 'RxPDO', 'tx-pdo-table': pdo.type === 'TxPDO' }"
                >
                  <template #index="{ record }">
                    {{ record.index }}:{{ record.subIndex }}
                  </template>
                  <template #bits="{ text }">
                    {{ text }} bit
                  </template>
                </a-table>
              </template>
            </a-collapse-panel>
          </a-collapse>
        </a-tab-pane>
        
        <a-tab-pane key="xml" tab="XML配置">
          <div class="editor-container">
            <monaco-editor
              v-model:value="slaveXml"
              language="xml"
              :options="editorOptions"
              class="xml-editor"
            />
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-modal>

    <!-- 网口配置对话框 -->
    <a-modal
      v-model:visible="networkConfigVisible"
      title="EtherCAT 主站配置"
      @ok="handleNetworkConfigSave"
      :confirmLoading="configSaving"
    >
      <a-alert
        v-if="Object.keys(networkConfig.masterDevices).length > 0"
        message="更换网口后，请确保将网线接入新的网口位置"
        type="warning"
        show-icon
        style="margin-bottom: 16px"
      />
      
      <div v-if="networkInterfaces.length === 0" class="empty-interfaces">
        <a-empty description="未检测到可用的网口" />
      </div>
      
      <template v-else>
        <div class="master-configs">
          <!-- 现有主站配置 -->
          <div 
            v-for="(device, index) in networkConfig.masterDevices" 
            :key="index" 
            class="master-config-item"
          >
            <a-space align="baseline">
              <a-form-item 
                :label="`MASTER${index}_DEVICE`"
                :required="index === 0"
              >
                <a-select
                  v-model:value="networkConfig.masterDevices[index]"
                  :options="getAvailableInterfaces(index).map(iface => ({
                    label: iface.label,
                    value: iface.value
                  }))"
                  style="width: 200px"
                  placeholder="请选择网口"
                />
              </a-form-item>
              
              <a-button 
                v-if="index > 0"
                type="link" 
                danger
                @click="() => removeMaster(index)"
              >
                <DeleteOutlined />
                删除
              </a-button>
            </a-space>
          </div>

          <!-- 添加主站按钮 -->
          <a-button 
            type="dashed" 
            block 
            @click="addMaster"
            :disabled="Object.keys(networkConfig.masterDevices).length >= networkInterfaces.length"
          >
            <PlusOutlined /> 添加主站
          </a-button>
        </div>
      </template>
    </a-modal>

    <!-- 添加模板生成器组件 -->
    <SlaveConfigGenerator
      v-if="configGeneratorVisible"
      v-model:visible="configGeneratorVisible"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted, computed, watch, h } from 'vue';
import { message, Empty, Modal } from 'ant-design-vue';
import { 
  ApiOutlined,
  ReloadOutlined,
  EyeOutlined,
  DownloadOutlined,
  ClusterOutlined,
  CheckCircleOutlined,
  PoweroffOutlined,
  InfoCircleOutlined,
  NodeIndexOutlined,
  SettingOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import type { TableColumnType } from 'ant-design-vue';
import type { Slave, PDO, PDOEntry, SyncManager } from '@/types';
import { ethercatApi } from '@/services/api';
import MonacoEditor from '@/components/MonacoEditor.vue';
import { useStore } from 'vuex';
import dayjs from 'dayjs';
import { instance } from '@viz-js/viz';
import SlaveConfigGenerator from '@/components/SlaveConfigGenerator.vue';

interface SlaveDetail extends Slave {
  rxPdos?: PDO[];
  txPdos?: PDO[];
  syncManagers?: SyncManager[];
}

interface Master {
  index: number;
  phase: string;
  active: boolean;
  slaveCount: number;
  mac: string;
  interface: string;
  link: string;
  txFrames: number;
  rxFrames: number;
  txBytes: number;
  rxBytes: number;
  lostFrames: number;
  txFrameRate: number[];
  txRate: number[];
  rxFrameRate: number[];
  rxRate: number[];
  lossRate: number[];
  frameLoss: number[];
}

interface SlaveGroup {
  master: number;
  slaves: Slave[];
}

interface NetworkConfig {
  masterDevices: { [key: number]: string | null };
}

interface CurrentConfig {
  masterDevices: { [key: number]: string | null };
}

interface NetworkInterface {
  label: string;
  value: string;
}

export default defineComponent({
  name: 'EtherCATView',
  
  components: {
    ApiOutlined,
    ReloadOutlined,
    EyeOutlined,
    DownloadOutlined,
    MonacoEditor,
    ClusterOutlined,
    CheckCircleOutlined,
    PoweroffOutlined,
    InfoCircleOutlined,
    NodeIndexOutlined,
    SettingOutlined,
    PlusOutlined,
    DeleteOutlined,
    SlaveConfigGenerator
  },

  setup() {
    const store = useStore();
    const loading = ref(false);
    const refreshing = ref(false);
    const serviceStatus = ref(false);
    const slaves = ref<SlaveDetail[]>([]);
    const masters = ref<Master[]>([]);
    const modalVisible = ref(false);
    const activeTab = ref('basic');
    const currentSlave = ref<SlaveDetail | null>(null);
    const slaveXml = ref('');
    const refreshInterval = ref(parseInt(localStorage.getItem('refreshInterval') || '5000'));
    const lastUpdateTime = ref('');
    const tableKey = ref(0);
    const expandedKeys = ref<number[]>([]);
    const isFirstLoad = ref(true);
    const modalWidth = ref('60%');
    const topologyVisible = ref(false);
    const topologyContainer = ref<HTMLElement | null>(null);
    const vizInstance = ref<any>(null);
    const currentMasterIndex = ref<number>(0);
    const currentSvg = ref<string>('');
    const networkConfigVisible = ref(false);
    const configSaving = ref(false);
    const networkInterfaces = ref<NetworkInterface[]>([]);
    const networkConfig = ref<NetworkConfig>({
      masterDevices: {}
    });
    const currentConfig = ref<CurrentConfig>({
      masterDevices: {}
    });

    const columns: TableColumnType[] = [
      { title: '从站序号', dataIndex: 'index', width: 80 },
      { title: '设备名', dataIndex: 'deviceName' },
      { title: 'Vendor Id', dataIndex: 'vendorId' },
      { title: 'Product code', dataIndex: 'productCode' },
      { title: '状态', dataIndex: 'state', slots: { customRender: 'state' } },
      { title: '操作', slots: { customRender: 'action' }, width: 200 }
    ];
    
    const pdoColumns = [
      { 
        title: '索引:子索引', 
        dataIndex: 'index',
        slots: { customRender: 'index' }
      },
      { 
        title: '位宽', 
        dataIndex: 'bits',
        slots: { customRender: 'bits' }
      },
      { 
        title: '名称', 
        dataIndex: 'name',
        ellipsis: true
      }
    ];
    
    const editorOptions = {
      readOnly: true,
      minimap: { enabled: false },
      lineNumbers: 'on',
      scrollBeyondLastLine: false,
      automaticLayout: true,
      wordWrap: 'on',
      fontSize: 12
    };

    const getStateColor = (state?: string) => {
      switch (state) {
        case 'OP': return 'success';
        case 'PREOP': return 'warning';
        case 'INIT': return 'default';
        default: return 'error';
      }
    };

    const getStateText = (state?: string) => {
      const stateMap: Record<string, string> = {
        'OP': '运行',
        'PREOP': '预运行',
        'INIT': '初始化',
        'BOOT': '启动',
        'SAFE_OP': '安全运行'
      };
      return stateMap[state || ''] || '未知';
    };

    const startService = async () => {
      try {
        loading.value = true;
        await store.dispatch('ethercat/startService');
        message.success('服务启动成功');
        await refreshStatus(true);
      } catch (error) {
        message.error('启动服务失败');
        console.error(error);
      } finally {
        loading.value = false;
      }
    };

    const stopService = async () => {
      try {
        loading.value = true;
        await store.dispatch('ethercat/stopService');
        message.success('服务停止成功');
        await refreshStatus(true);
      } catch (error) {
        message.error('停止服务失败');
        console.error(error);
      } finally {
        loading.value = false;
      }
    };

    const refreshStatus = async (showLoading = false) => {
      try {
        if (showLoading) {
          loading.value = true;
        }

        await store.dispatch('ethercat/fetchStatus');
        serviceStatus.value = store.state.ethercat.status?.running || false;
        
        if (serviceStatus.value) {
          slaves.value = store.state.ethercat.slaves || [];
          masters.value = store.state.ethercat.status?.masters || [];
          tableKey.value += 1;
        } else {
          slaves.value = [];
          masters.value = [];
        }
        
        lastUpdateTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
      } catch (error) {
        message.error('刷新状态失败');
        console.error('Refresh error:', error);
        serviceStatus.value = false;
        slaves.value = [];
        masters.value = [];
      } finally {
        if (showLoading) {
          loading.value = false;
        }
      }
    };

    // 监听服务状态变化
    watch(serviceStatus, (newStatus) => {
      if (!newStatus) {
        slaves.value = [];
        masters.value = [];
      }
    });

    const showSlaveDetail = async (slave: SlaveDetail) => {
      currentSlave.value = slave;
      modalVisible.value = true;
      activeTab.value = 'basic';
      
      try {
        const xml = await ethercatApi.getSlaveXml(slave.master, slave.index);
        slaveXml.value = xml;
      } catch (error) {
        message.error('获取从站XML配置失败');
        console.error(error);
        slaveXml.value = '';
      }
    };

    const downloadXml = async (slave: SlaveDetail) => {
      try {
        const xml = await ethercatApi.getSlaveXml(slave.master, slave.index);
        
        const blob = new Blob([xml], { type: 'text/xml' });
        
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `slave_m${slave.master}_p${slave.index}_${slave.deviceName || 'config'}.xml`;
        
        document.body.appendChild(link);
        link.click();
        
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        message.error('下载XML配置失败');
        console.error(error);
      }
    };

    let refreshTimer: number | null = null;

    watch(refreshInterval, (newValue) => {
      localStorage.setItem('refreshInterval', newValue.toString());
      
      if (refreshTimer) {
        clearInterval(refreshTimer);
        refreshTimer = null;
      }
      
      if (newValue > 0) {
        refreshTimer = window.setInterval(() => refreshStatus(false), newValue);
      }
    });

    onMounted(() => {
      refreshStatus(true);
      if (refreshInterval.value > 0) {
        refreshTimer = window.setInterval(() => refreshStatus(false), refreshInterval.value);
      }
    });

    onUnmounted(() => {
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
    });

    const updatePdoValue = (record: PDO, val: string | number | boolean) => {
      record.value = val;
    };

    const masterColumns: TableColumnType[] = [
      { title: '主站序号', dataIndex: 'index' },
      { title: '状态', dataIndex: 'phase', slots: { customRender: 'phase' } },
      { title: '从站数量', dataIndex: 'slaveCount' },
      { title: 'MAC地址', dataIndex: 'mac' },
      { title: '网口', dataIndex: 'interface' },
      { title: '连接状态', dataIndex: 'link', slots: { customRender: 'link' } },
      { title: '统计信息', slots: { customRender: 'stats' } }
    ];

    const getPhaseColor = (phase: string) => {
      switch (phase) {
        case 'OPERATIONAL': return 'success';
        case 'INIT': return 'default';
        case 'PREOP': return 'warning';
        default: return 'default';
      }
    };

    const getPhaseText = (phase: string) => {
      const phaseMap: Record<string, string> = {
        'OPERATIONAL': '运行',
        'INIT': '初始化',
        'PREOP': '预运行',
        'SAFEOP': '安全运行'
      };
      return phaseMap[phase] || phase;
    };

    // 计活动主站数量
    const activeMasters = computed(() => 
      serviceStatus.value ? masters.value.filter(m => m.active).length : '--'
    );

    // 计算总从站数量
    const totalSlaves = computed(() => 
      serviceStatus.value ? masters.value.reduce((sum, m) => sum + m.slaveCount, 0) : '--'
    );

    const formatBytes = (bytes: number) => {
      if (bytes < 1024) return `${bytes} B`;
      if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    };

    const portColumns: TableColumnType[] = [
      { title: '端口', dataIndex: 'index', width: 80 },
      { title: '类型', dataIndex: 'type', width: 100 },
      { title: '链接', dataIndex: 'link', slots: { customRender: 'link' }, width: 100 },
      { title: '循环', dataIndex: 'loop', width: 100 },
      { title: '信号', dataIndex: 'signal', width: 100 },
      { title: '下一从站', dataIndex: 'nextSlave', width: 120 },
      { 
        title: 'RxTime [ns]', 
        dataIndex: 'rxTime',
        width: 120,
        align: 'right'
      },
      { 
        title: 'Diff [ns]', 
        dataIndex: 'diff',
        width: 120,
        align: 'right'
      },
      { 
        title: 'NextDc [ns]', 
        dataIndex: 'nextDc',
        width: 120,
        align: 'right'
      }
    ];

    // 计算属性：运行中的从站数量
    const runningSlaves = computed(() => 
      slaves.value.filter(s => s.state === 'OP').length
    );

    // 表格列定义
    const slaveColumns = [
      { title: '主站序号', dataIndex: 'master', width: 100 },
      { title: '从站数量', customRender: ({ record }: { record: SlaveGroup }) => record.slaves.length },
      { title: '运行状态', customRender: ({ record }: { record: SlaveGroup }) => {
        const runningSlaves = record.slaves.filter(s => s.state === 'OP').length;
        return `${runningSlaves}/${record.slaves.length}`;
      }}
    ];

    // 子表格列定义
    const expandedColumns = [
      { title: '从站序号', dataIndex: 'index', width: 80 },
      { title: '设备名', dataIndex: 'deviceName' },
      { title: 'Vendor Id', dataIndex: 'vendorId' },
      { title: 'Product code', dataIndex: 'productCode' },
      { 
        title: '状态', 
        dataIndex: 'state',
        slots: { customRender: 'state' }
      },
      { 
        title: '操作',
        width: 200,
        slots: { customRender: 'action' }
      }
    ];

    // 计算分组后的从站数据
    const slaveGroups = computed<SlaveGroup[]>(() => {
      const groups = new Map<number, SlaveGroup>();
      
      slaves.value.forEach(slave => {
        const group = groups.get(slave.master) || { master: slave.master, slaves: [] };
        group.slaves.push(slave);
        groups.set(slave.master, group);
      });

      return Array.from(groups.values()).sort((a, b) => a.master - b.master);
    });

    // 监听数据变化，只在第一次加载时展开所有行
    watch(slaveGroups, (newGroups) => {
      if (isFirstLoad.value && newGroups.length > 0) {
        expandedKeys.value = newGroups.map(group => group.master);
        isFirstLoad.value = false;
      }
    });

    // 处理手动展开/折叠
    const handleExpandChange = (keys: number[]) => {
      expandedKeys.value = keys;
    };

    const activeSM = ref<string[]>([]);

    onMounted(async () => {
      vizInstance.value = await instance();
    });

    const adjustModalSize = (svg: SVGElement) => {
      const viewBox = svg.getAttribute('viewBox')?.split(' ').map(Number) || [];
      if (viewBox.length === 4) {
        const [, , width, height] = viewBox;
        const ratio = width / height;

        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const maxWidth = viewportWidth * 0.8;
        const maxHeight = viewportHeight * 0.8;

        let targetWidth = width;
        let targetHeight = height;

        if (width > maxWidth) {
          targetWidth = maxWidth;
          targetHeight = targetWidth / ratio;
        }

        if (targetHeight > maxHeight) {
          targetHeight = maxHeight;
          targetWidth = targetHeight * ratio;
        }

        modalWidth.value = `${Math.min(Math.max(targetWidth + 48, 400), maxWidth)}px`;

        svg.style.width = '100%';
        svg.style.height = 'auto';
        svg.style.maxHeight = `${maxHeight - 120}px`;
      }
    };

    const showTopology = async (master: Master) => {
      try {
        topologyVisible.value = true;
        currentMasterIndex.value = master.index;
        const dot = await ethercatApi.getTopology(master.index);
        
        if (vizInstance.value && topologyContainer.value) {
          const svg = await vizInstance.value.renderSVGElement(dot);
          topologyContainer.value.innerHTML = '';
          topologyContainer.value.appendChild(svg);
          
          currentSvg.value = svg.outerHTML;
          
          adjustModalSize(svg);
        }
      } catch (error) {
        message.error('获取拓扑图失败');
        console.error(error);
      }
    };

    const downloadTopology = () => {
      try {
        const blob = new Blob([currentSvg.value], { type: 'image/svg+xml' });
        
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `topology_master${currentMasterIndex.value}.svg`;
        
        document.body.appendChild(link);
        link.click();
        
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        message.error('下载拓扑图失败');
        console.error(error);
      }
    };

    // 计算可用的网口（排除已选择的）
    const getAvailableInterfaces = (currentIndex: number) => {
      // 获取所有已选择的网口值
      const selectedInterfaces = Object.entries(networkConfig.value.masterDevices)
        .filter(([index, device]) => device && parseInt(index) !== currentIndex)
        .map(([_, device]) => device);

      // 返回未被选择的网口
      return networkInterfaces.value.filter(iface => !selectedInterfaces.includes(iface.value));
    };

    // 计算是否显示网口更换警告
    const showNetworkChangeWarning = computed(() => {
      const currentDevices = currentConfig.value.masterDevices;
      const newDevices = networkConfig.value.masterDevices;
      return Object.keys(currentDevices).some(key => 
        currentDevices[Number(key)] !== newDevices[Number(key)]
      );
    });

    // 获取网口列表
    const fetchNetworkInterfaces = async () => {
      try {
        const interfaces = await ethercatApi.getNetworkInterfaces();
        networkInterfaces.value = interfaces.map(iface => ({
          label: iface,
          value: iface
        }));
      } catch (error) {
        console.error('Failed to get network interfaces:', error);
        message.error('获取网口列表失败');
      }
    };

    // 获取当前配置
    const fetchCurrentConfig = async () => {
      try {
        const config = await ethercatApi.getEtherCATConfig();
        currentConfig.value = {
          masterDevices: config.masterDevices
        };
        networkConfig.value = {
          masterDevices: { ...config.masterDevices }
        };
      } catch (error) {
        console.error('Failed to get current config:', error);
        message.error('获取当前配置失败');
      }
    };

    // 显示配置对话框
    const showNetworkConfig = async () => {
      try {
        message.loading({ content: '加载配置...', key: 'config' });
        
        // 获取可用网口
        await fetchNetworkInterfaces();
        
        // 获取当前配置
        const config = await ethercatApi.getEtherCATConfig();
        networkConfig.value = {
          masterDevices: config.masterDevices || {}
        };

        message.success({ content: '加载完成', key: 'config' });
        networkConfigVisible.value = true;
      } catch (error) {
        console.error('Failed to show network config:', error);
        message.error({ content: '加载配置失败', key: 'config' });
      }
    };

    // 保存配置
    const handleNetworkConfigSave = async () => {
      // 确保主站0存在且已配置
      if (!networkConfig.value.masterDevices[0]) {
        message.error('主站0必须配置网口');
        return;
      }

      try {
        configSaving.value = true;
        await ethercatApi.updateEtherCATConfig(networkConfig.value);

        message.success('配置已更新');
        // await ethercatApi.restartService();
        // message.success('服务重启成功');
        networkConfigVisible.value = false;
        
        // 刷新状态
        await refreshStatus(true);
      } catch (error: any) {
        console.error('Failed to save config:', error);
        // 显示后端返回的具体错误信息
        const errorMessage = error.response?.data?.message || '保存配置失败';
        message.error(errorMessage);
      } finally {
        configSaving.value = false;
      }
    };

    const downloadConfig = async () => {
      try {
        message.loading({ content: '正在生成配置...', key: 'download' });
        const config = await ethercatApi.getSlaveConfig();
        
        // 创建载
        const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'slave_config.json';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        message.success({ content: '配置已下载', key: 'download' });
      } catch (error) {
        console.error('Failed to download config:', error);
        message.error({ content: '下载失败', key: 'download' });
      }
    };

    const configGeneratorVisible = ref(false);

    const showConfigGenerator = () => {
      configGeneratorVisible.value = true;
    };

    const addMaster = () => {
      const existingIndexes = Object.keys(networkConfig.value.masterDevices).map(Number);
      const newIndex = existingIndexes.length > 0 ? Math.max(...existingIndexes) + 1 : 0;
      
      // 获取当前可用的网口
      const availableInterfaces = getAvailableInterfaces(newIndex);
      
      // 尝试匹配对应索引的网口（比如 Master0 匹配 eth0）
      const defaultInterface = availableInterfaces.find(iface => 
        iface.value.endsWith(newIndex.toString())
      );
      
      // 如果找不到对应索引的网口，就使用第一个可用的网口
      networkConfig.value.masterDevices[newIndex] = defaultInterface ? 
        defaultInterface.value : 
        (availableInterfaces[0]?.value || null);
    };

    const removeMaster = (index: number) => {
      // 不允许删除主站0
      if (index === 0) {
        message.error('不允许删除主站0');
        return;
      }
      const { [index]: removed, ...rest } = networkConfig.value.masterDevices;
      networkConfig.value.masterDevices = rest;
    };

    return {
      loading,
      refreshing,
      serviceStatus,
      slaves,
      modalVisible,
      activeTab,
      currentSlave,
      slaveXml,
      columns,
      pdoColumns,
      editorOptions,
      getStateColor,
      getStateText,
      startService,
      stopService,
      refreshStatus,
      showSlaveDetail,
      downloadXml,
      runningSlaves,
      lastUpdateTime,
      updatePdoValue,
      masterColumns,
      getPhaseColor,
      getPhaseText,
      activeMasters,
      totalSlaves,
      masters,
      formatBytes,
      portColumns,
      refreshInterval,
      Empty,
      slaveColumns,
      expandedColumns,
      slaveGroups,
      tableKey,
      expandedKeys,
      handleExpandChange,
      activeSM,
      topologyVisible,
      topologyContainer,
      showTopology,
      modalWidth,
      currentMasterIndex,
      currentSvg,
      downloadTopology,
      networkConfigVisible,
      configSaving,
      networkInterfaces,
      networkConfig,
      getAvailableInterfaces,
      showNetworkChangeWarning,
      showNetworkConfig,
      handleNetworkConfigSave,
      downloadConfig,
      configGeneratorVisible,
      showConfigGenerator,
      addMaster,
      removeMaster
    };
  }
});
</script>

<style lang="less" scoped>
@import '../styles/variables.less';

.ethercat {
  .status-cards {
    margin-bottom: 24px;
    
    .ant-card {
      border-radius: 16px;
      overflow: hidden;
      
      .ant-statistic {
        .ant-statistic-title {
          color: @text-color-secondary;
          margin-bottom: 8px;
        }
        
        .ant-statistic-content {
          .anticon {
            margin-right: 8px;
            font-size: 24px;
          }
        }
      }
    }
  }
  
  .control-panel {
    border-radius: 16px;
    overflow: hidden;
    
    .panel-title {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .anticon {
        font-size: 18px;
      }
    }
  }
  
  .xml-editor {
    height: 400px;
    border: 1px solid @border-color-base;
    border-radius: 8px;
  }
  
  :deep(.ant-descriptions) {
    background: rgba(255, 255, 255, 0.6);
    border-radius: 12px;
    overflow: hidden;
    
    .ant-descriptions-item-label {
      background: rgba(0, 0, 0, 0.02);
      width: 120px;
    }
  }

  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin: 24px 0 16px;
    color: @heading-color;
    
    &:first-child {
      margin-top: 0;
    }
  }

  :deep(.ant-table) {
    .ant-table-container {
      border-radius: 12px;
      overflow: hidden;
    }

    .ant-table-thead > tr > th {
      background-color: #fafafa;
      padding: 12px 16px;
      white-space: nowrap;

      &:first-child {
        border-top-left-radius: 12px !important;
      }

      &:last-child {
        border-top-right-radius: 12px !important;
      }
    }
    
    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      white-space: nowrap;
    }

    .ant-table-tbody > tr:last-child > td {
      &:first-child {
        border-bottom-left-radius: 12px !important;
      }

      &:last-child {
        border-bottom-right-radius: 12px !important;
      }
    }
  }

  :deep(.ant-btn) {
    border-radius: 8px;

    &.ant-btn-sm {
      border-radius: 6px;
    }
  }

  :deep(.ant-tag) {
    border-radius: 6px;
  }

  :deep(.ant-select-selector) {
    border-radius: 8px !important;
  }

  :deep(.ant-modal-content) {
    border-radius: 16px;
    overflow: hidden;
  }

  :deep(.ant-tabs-card) {
    .ant-tabs-nav {
      .ant-tabs-tab {
        border-radius: 8px 8px 0 0;
      }
    }
  }

  .stats-tooltip {
    min-width: 300px;
    
    .stats-group {
      &:not(:last-child) {
        margin-bottom: 12px;
        padding-bottom: 12px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }
      
      .stats-title {
        font-weight: 500;
        margin-bottom: 8px;
        color: #fff;
      }
      
      .stats-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        
        .label {
          color: rgba(255, 255, 255, 0.85);
        }
        
        .value {
          color: #fff;
          font-family: monospace;
        }
      }
    }
  }

  :deep(.ant-table-expanded-row) {
    .ant-table-wrapper {
      margin: 0 !important;
    }
    
    .ant-table {
      margin: 0 !important;
      
      td {
        background: rgba(0, 0, 0, 0.02);
      }
    }
  }

  .pdo-header {
    font-family: monospace;
    padding: 8px 12px;
    color: @text-color;
    font-weight: 500;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
  }

  :deep(.rx-pdo-table) {
    .ant-table-thead > tr > th {
      background-color: #fff1f0;
      color: @error-color;
    }
  }

  :deep(.tx-pdo-table) {
    .ant-table-thead > tr > th {
      background-color: #f6ffed;
      color: @success-color;
    }
  }

  :deep(.ant-collapse) {
    .ant-collapse-header {
      font-family: monospace;
      
      .anticon {
        font-size: 14px;
        transition: color 0.3s;
      }
    }
  }

  :deep(.ant-table-small) {
    font-family: monospace;
    
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      padding: 8px 12px;
    }
    
    .ant-table-tbody > tr > td {
      padding: 8px 12px;
    }
  }
}

:deep(.slave-detail-modal) {
  .ant-modal {
    height: 90vh;
    padding-bottom: 0;
    top: 5vh;
  }

  .ant-modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .ant-modal-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .ant-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    .ant-tabs-content-holder {
      flex: 1;
      overflow: hidden;
    }

    .ant-tabs-content {
      height: 100%;

      .ant-tabs-tabpane {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .xml-container {
    flex: 1;
    min-height: 500px;
    display: flex;
    flex-direction: column;

    .xml-editor {
      flex: 1;
      border: 1px solid @border-color-base;
      border-radius: 8px;
    }
  }
}

.editor-container {
  height: 800px;
  margin: 16px 0;
}

.xml-editor {
  height: 100% !important;
  border: 1px solid @border-color-base;
  border-radius: 8px;
}

:deep(.ant-modal-body) {
  padding: 24px;
}

:deep(.ant-tabs-content) {
  height: auto;
}

.sm-header {
  font-family: monospace;
  display: flex;
  align-items: center;
  
  .data-tag {
    margin-left: 8px;
    font-size: 12px;
  }
}

:deep(.ant-collapse) {
  .ant-collapse-header {
    font-family: monospace;
  }
}

:deep(.ant-tabs) {
  .ant-tabs-nav {
    margin-bottom: 8px !important;
  }
}

:deep(.slave-detail-tabs) {
  .ant-tabs-nav {
    margin: 0;
  }
}

.topology-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  
  :deep(svg) {
    transition: all 0.3s ease;
  }
}

:deep(.ant-modal-footer) {
  text-align: center;
  
  .ant-btn {
    min-width: 120px;
  }
}

.modal-footer {
  text-align: center;
  
  .ant-btn {
    min-width: 120px;
  }
}

.master-configs {
  .master-config-item {
    margin-bottom: 16px;
  }
}

.empty-interfaces {
  padding: 24px 0;
}
</style> 