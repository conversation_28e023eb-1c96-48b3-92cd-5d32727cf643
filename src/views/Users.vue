<template>
  <div class="user-management">
    <a-card class="user-list">
      <template #title>用户管理</template>
      <template #extra>
        <a-button type="primary" @click="showCreateModal">
          <template #icon><UserAddOutlined /></template>
          新建用户
        </a-button>
      </template>

      <a-table
        :columns="columns"
        :data-source="users"
        :loading="loading"
        rowKey="id"
      >
        <template #status="{ text }">
          <a-tag :color="text ? 'success' : 'error'">
            {{ text ? '启用' : '禁用' }}
          </a-tag>
        </template>
        
        <template #role="{ text }">
          <a-tag :color="text === 'admin' ? 'blue' : 'default'">
            {{ getRoleName(text) }}
          </a-tag>
        </template>
        
        <template #action="{ record }">
          <a-space>
            <a-button 
              type="link" 
              size="small"
              @click="showEditModal(record)"
            >
              编辑
            </a-button>
            <a-button 
              type="link" 
              size="small"
              @click="showResetPasswordModal(record)"
            >
              重置密码
            </a-button>
            <a-button 
              type="link" 
              danger
              size="small"
              @click="confirmDelete(record)"
            >
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑用户对话框 -->
    <a-modal
      v-model:visible="userModalVisible"
      :title="isEdit ? '编辑用户' : '新建用户'"
      @ok="handleUserSubmit"
      :confirmLoading="submitting"
    >
      <a-form :model="userForm" layout="vertical">
        <a-form-item 
          label="用户名" 
          required
          :rules="[{ required: true, message: '请输入用户名' }]"
        >
          <a-input 
            v-model:value="userForm.username" 
            placeholder="请输入用户名"
            :disabled="isEdit"
          />
        </a-form-item>
        
        <a-form-item 
          label="邮箱"
          required
          :rules="[
            { required: true, message: '请输入邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' }
          ]"
        >
          <a-input v-model:value="userForm.email" placeholder="请输入邮箱" />
        </a-form-item>
        
        <a-form-item label="角色" required>
          <a-select v-model:value="userForm.role">
            <a-select-option value="admin">管理员</a-select-option>
            <a-select-option value="user">普通用户</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="状态">
          <a-switch v-model:checked="userForm.status" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 重置密码对话框 -->
    <a-modal
      v-model:visible="resetPasswordVisible"
      title="重置密码"
      @ok="handleResetPassword"
      :confirmLoading="submitting"
    >
      <a-form :model="passwordForm" layout="vertical">
        <a-form-item 
          label="新密码" 
          required
          :rules="[
            { required: true, message: '请输入新密码' },
            { min: 6, message: '密码长度不能小于6位' }
          ]"
        >
          <a-input-password 
            v-model:value="passwordForm.password" 
            placeholder="请输入新密码" 
          />
        </a-form-item>
        
        <a-form-item 
          label="确认密码"
          required
          :rules="[
            { required: true, message: '请确认密码' },
            { validator: validateConfirmPassword }
          ]"
        >
          <a-input-password 
            v-model:value="passwordForm.confirmPassword" 
            placeholder="请确认密码" 
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { UserAddOutlined } from '@ant-design/icons-vue';
import type { TableColumnType } from 'ant-design-vue';
import { useStore } from 'vuex';
import type { User, UserForm } from '@/types';

export default defineComponent({
  name: 'UserManagement',
  
  components: {
    UserAddOutlined
  },

  setup() {
    const store = useStore();
    const loading = ref(false);
    const users = ref<User[]>([]);
    const userModalVisible = ref(false);
    const resetPasswordVisible = ref(false);
    const submitting = ref(false);
    const isEdit = ref(false);
    
    const userForm = reactive<UserForm>({
      username: '',
      email: '',
      role: 'user',
      status: true
    });
    
    const passwordForm = reactive({
      password: '',
      confirmPassword: ''
    });

    const columns: TableColumnType[] = [
      { title: '用户名', dataIndex: 'username' },
      { title: '邮箱', dataIndex: 'email' },
      { title: '角色', dataIndex: 'role', slots: { customRender: 'role' } },
      { title: '状态', dataIndex: 'status', slots: { customRender: 'status' } },
      { title: '创建时间', dataIndex: 'created' },
      { title: '操作', slots: { customRender: 'action' }, width: 250 }
    ];

    const getRoleName = (role: string) => {
      const roleMap: Record<string, string> = {
        admin: '管理员',
        user: '普通用户'
      };
      return roleMap[role] || role;
    };

    const fetchUsers = async () => {
      try {
        loading.value = true;
        users.value = await store.dispatch('user/fetchUsers');
      } catch (error) {
        message.error('获取用户列表失败');
        console.error(error);
      } finally {
        loading.value = false;
      }
    };

    const showCreateModal = () => {
      isEdit.value = false;
      userForm.username = '';
      userForm.email = '';
      userForm.role = 'user';
      userForm.status = true;
      userModalVisible.value = true;
    };

    const showEditModal = (user: User) => {
      isEdit.value = true;
      userForm.username = user.username;
      userForm.email = user.email;
      userForm.role = user.role;
      userForm.status = user.status;
      userModalVisible.value = true;
    };

    const handleUserSubmit = async () => {
      try {
        submitting.value = true;
        if (isEdit.value) {
          await store.dispatch('user/updateUser', { id: userForm.username, user: userForm });
          message.success('用户更新成功');
        } else {
          await store.dispatch('user/createUser', userForm);
          message.success('用户创建成功');
        }
        userModalVisible.value = false;
        fetchUsers();
      } catch (error) {
        message.error('操作失败');
        console.error(error);
      } finally {
        submitting.value = false;
      }
    };

    const showResetPasswordModal = (user: User) => {
      passwordForm.password = '';
      passwordForm.confirmPassword = '';
      resetPasswordVisible.value = true;
    };

    const handleResetPassword = async () => {
      try {
        submitting.value = true;
        await store.dispatch('user/resetPassword', { id: userForm.username, password: passwordForm.password });
        message.success('密码重置成功');
        resetPasswordVisible.value = false;
      } catch (error) {
        message.error('密码重置失败');
        console.error(error);
      } finally {
        submitting.value = false;
      }
    };

    const confirmDelete = (user: User) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除用户 "${user.username}" 吗？`,
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        async onOk() {
          try {
            await store.dispatch('user/deleteUser', user.id);
            message.success('删除成功');
            fetchUsers();
          } catch (error) {
            message.error('删除失败');
            console.error(error);
          }
        }
      });
    };

    onMounted(() => {
      fetchUsers();
    });

    return {
      loading,
      users,
      columns,
      userModalVisible,
      resetPasswordVisible,
      submitting,
      isEdit,
      userForm,
      passwordForm,
      getRoleName,
      fetchUsers,
      showCreateModal,
      showEditModal,
      handleUserSubmit,
      showResetPasswordModal,
      handleResetPassword,
      confirmDelete
    };
  }
});
</script>

<style lang="less" scoped>
.user-management {
  .user-list {
    .ant-table {
      margin-top: 16px;
    }
  }
}
</style> 