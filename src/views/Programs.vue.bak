<template>
  <div class="program-management">
    <a-card class="program-list">
      <template #title>程序管理</template>
      <template #extra>
        <a-space>
          <a-button @click="showStartupModal">
            <template #icon><SettingOutlined /></template>
            程序自启
          </a-button>
          <a-button @click="refreshPrograms" :loading="refreshing">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="showUploadModal">
            <template #icon><UploadOutlined /></template>
            上传程序
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="programs"
        :loading="loading"
        rowKey="id"
        :scroll="{ x: 1200 }"
      >
        <template #status="{ text }">
          <a-tag :color="text === 'running' ? 'success' : text === 'stopped' ? 'default' : 'warning'">
            {{ getStatusText(text) }}
          </a-tag>
        </template>
        
        <template #middlewareStatus="{ text }">
          <a-tag :color="getStatusColor(text)">
            {{ getStatusText(text) }}
          </a-tag>
        </template>
        
        <template #programStatus="{ text }">
          <a-tag :color="getStatusColor(text)">
            {{ getStatusText(text) }}
          </a-tag>
        </template>
        
        <template #action="{ record }">
          <a-space>
            <a-button 
              type="primary"
              size="small"
              :disabled="record.middlewareStatus === 'active'"
              :loading="record.loading"
              @click="startMiddleware(record)"
            >
              启动中间层
            </a-button>
            <a-button 
              danger
              size="small"
              :disabled="record.middlewareStatus !== 'active'"
              :loading="record.loading"
              @click="stopMiddleware(record)"
            >
              停止中间层
            </a-button>
            <a-button 
              type="primary"
              size="small"
              :disabled="record.programStatus === 'active'"
              :loading="record.loading"
              @click="startProgram(record)"
            >
              启动程序
            </a-button>
            <a-button 
              danger
              size="small"
              :disabled="record.programStatus !== 'active'"
              :loading="record.loading"
              @click="stopProgram(record)"
            >
              停止程序
            </a-button>
            <a-button 
              type="link" 
              size="small"
              :disabled="record.status === 'running' || record.loading || anyRowLoading"
              @click="showBasicConfig(record)"
            >
              配置
            </a-button>
            <a-dropdown>
              <a-button type="link">
                <template #icon><DownloadOutlined /></template>
                下载
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="downloadTemplate(record)">
                    下载中间层代码
                  </a-menu-item>
                  <a-menu-item @click="downloadFullPackage(record)">
                    下载完整程序包
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <a-button 
              type="link" 
              size="small"
              :disabled="record.status !== 'running'"
              @click="showLogs(record)"
            >
              <template #icon>
                <FileTextOutlined />
              </template>
              日志
            </a-button>
            <a-button 
              type="link" 
              danger
              size="small"
              :disabled="record.status === 'running' || record.loading || anyRowLoading"
              @click="confirmDelete(record)"
            >
              <template #icon>
                <DeleteOutlined />
              </template>
              删除
            </a-button>
          </a-space>
        </template>

        <template #config="{ record }">
          <a-tooltip placement="top" title="点击查看/编辑配置">
            <FileTextOutlined 
              class="config-icon" 
              @click="showJsonConfig(record)"
            />
          </a-tooltip>
        </template>

        <template #taskFrequency="{ text }">
          {{ text }} Hz ({{ Math.round(1000000 / text) }} μs)
        </template>
      </a-table>
    </a-card>

    <!-- 上传程序对话框 -->
    <a-modal
      v-model:visible="uploadModalVisible"
      title="上传程序"
      @ok="handleUpload"
      :confirmLoading="uploading"
    >

      <a-form :model="uploadForm" layout="vertical">
        <a-form-item label="程序名称" required>
          <a-input v-model:value="uploadForm.name" placeholder="请输入程序名称" />
        </a-form-item>

        <a-form-item label="主站索引" required>
          <a-select
            v-model:value="uploadForm.masterIndex"
            :options="availableMasters.map(m => ({ value: m, label: `Master ${m}` }))"
            placeholder="请选择主站"
          />
        </a-form-item>

        <a-form-item label="任务频率 (Hz)" required>
          <a-space>
            <a-input-number 
              v-model:value="uploadForm.taskFrequency" 
              :min="1000"
              :max="120000"
              :default-value="4000"
              :step="4000" 
              @change="updateUploadFrequency"
            />
            <span>({{ uploadFrequencyUs }} μs)</span>
          </a-space>
        </a-form-item>

        <a-form-item label="程序压缩包" required>
          <a-upload
            :file-list="fileList"
            :before-upload="beforeUpload"
            @remove="handleRemove"
            :multiple="false"
            :showUploadList="true"
            accept=".zip"
            :maxSize="200 * 1024 * 1024"
          >
            <a-button>
              <template #icon><UploadOutlined /></template>
              选择程序压缩包
            </a-button>
          </a-upload>
          <div class="upload-tip">请上传包含主程序及其依赖文件的ZIP压缩包</div>
          <a-progress 
            v-if="uploadProgress > 0 && uploadProgress < 100"
            :percent="uploadProgress" 
            :status="uploadProgress < 100 ? 'active' : 'success'"
          />
        </a-form-item>

        <a-form-item label="配置文件 (JSON)" required>
          <a-upload
            :file-list="configFileList"
            :before-upload="beforeUploadConfig"
            @remove="handleRemoveConfig"
            :multiple="false"
            :showUploadList="true"
            accept=".json"
          >
            <a-button>
              <template #icon><UploadOutlined /></template>
              选择配置文件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 配置查看/编辑对话框 -->
    <a-modal
      v-model:visible="configModalVisible"
      title="程序配置"
      width="60%"
      :style="{ top: '20px' }"
      @ok="handleConfigSave"
    >
      <a-tabs v-model:activeKey="activeConfigTab">
        <a-tab-pane key="basic" tab="基本配置">
          <a-form :model="configForm" layout="vertical">
            <a-form-item label="主站索引">
              <a-select
                v-model:value="configForm.masterIndex"
                :options="availableMasters.map(m => ({ value: m, label: `Master ${m}` }))"
                placeholder="请选择主站"
              />
            </a-form-item>
            <a-form-item label="任务频率 (Hz)">
              <a-space>
                <a-input-number 
                  v-model:value="configForm.taskFrequency" 
                  :min="1000"
                  :max="120000"
                  :default-value="4000"
                  :step="4000" 
                  @change="updateConfigFrequency"
                />
                <span>({{ configFrequencyUs }} μs)</span>
              </a-space>
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="json" tab="从站JSON配置">
          <monaco-editor
            v-model:value="configJson"
            language="json"
            :options="editorOptions"
            class="json-editor"
          />
        </a-tab-pane>
        <a-tab-pane key="replace" tab="替换程序">
          <a-form layout="vertical">
            <a-form-item label="程序文件">
              <a-upload
                :file-list="replaceFileList"
                :before-upload="beforeReplaceUpload"
                @remove="handleReplaceRemove"
                :multiple="false"
                :showUploadList="true"
                accept=".zip"
                :maxSize="200 * 1024 * 1024"
              >
                <a-button>
                  <template #icon><UploadOutlined /></template>
                  选择新程序压缩包(zip, 最大200MB)
                </a-button>
              </a-upload>
              <a-progress 
                v-if="replaceProgress > 0 && replaceProgress < 100"
                :percent="replaceProgress" 
                :status="replaceProgress < 100 ? 'active' : 'success'"
              />
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="binCPU" tab="绑定CPU">
          <div class="system-info" v-if="systemInfo">
            <a-descriptions title="系统信息" bordered>
              <a-descriptions-item label="CPU核心数">
                {{ systemInfo.cpuCount }}
              </a-descriptions-item>
              <a-descriptions-item label="CPU型号">
                {{ systemInfo.cpuModel }}
              </a-descriptions-item>
              <a-descriptions-item label="总内存">
                {{ systemInfo.totalMemory }} GB
              </a-descriptions-item>
              <a-descriptions-item label="可用内存">
                {{ systemInfo.freeMemory }} GB
              </a-descriptions-item>
              <a-descriptions-item label="操作系统">
                {{ systemInfo.platform }}
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <a-form :model="cpuBindForm" layout="vertical">
            <a-form-item label="程序绑定CPU核">
              <a-input-number 
                v-model:value="cpuBindForm.programCpu" 
                :min="0"
                :max="(systemInfo?.cpuCount || 1) - 1"
                placeholder="请输入CPU核编号"
              />
            </a-form-item>
            <a-form-item label="中间层绑定CPU核">
              <a-input-number 
                v-model:value="cpuBindForm.middlewareCpu" 
                :min="0"
                :max="(systemInfo?.cpuCount || 1) - 1"
                placeholder="请输入CPU核编号"
              />
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>

      <template #footer>
        <a-space>
          <a-button key="cancel" @click="configModalVisible = false">取消</a-button>
          <template v-if="activeConfigTab === 'replace'">
            <a-button 
              key="replace" 
              type="primary" 
              :disabled="!replaceFile || replacing"
              :loading="replacing"
              @click="handleReplace"
            >
              {{ replacing ? '替换中...' : '替换程序' }}
            </a-button>
          </template>
          <template v-else>
            <a-button key="save" type="primary" @click="handleConfigSave">保存配置</a-button>
          </template>
        </a-space>
      </template>
    </a-modal>

    <!-- 日志查看对话框 -->
    <a-modal
      v-model:visible="logModalVisible"
      title="程序日志"
      :width="800"
      :footer="null"
    >
      <div class="log-content">
        <div class="log-header">
          <span>{{ currentProgram?.name }}</span>
          <a-button type="primary" @click="refreshLogs" :loading="logsLoading">
            刷新
          </a-button>
        </div>
        <a-spin :spinning="logsLoading">
          <div class="log-list">
            <div v-if="logs?.length === 0" class="no-logs">
              暂无日志
            </div>
            <div v-else class="log-item" v-for="(log, index) in logs" :key="index">
              {{ log }}
            </div>
          </div>
        </a-spin>
      </div>
    </a-modal>

    <!-- 代码生成器部分 -->
    <div class="code-generator">
      <div class="section-title">代码生成器</div>
      <div class="generator-container">
        <!-- 左侧 JSON 编辑器 -->
        <div class="editor-wrapper">
          <div class="editor-header">
            <h4>配置 JSON</h4>
          </div>
          <div class="editor-content">
            <MonacoEditor
              v-model:value="generatorJson"
              language="json"
              :options="jsonEditorOptions"
              class="json-editor"
            />
          </div>
        </div>

        <!-- 中间控制区 -->
        <div class="control-panel">
          <a-space direction="vertical">
            <a-select
              v-model:value="selectedLanguage"
              style="width: 120px"
              placeholder="选择语言"
            >
              <a-select-option value="cs">C#</a-select-option>
              <a-select-option value="c">C</a-select-option>
              <a-select-option value="cpp">C++</a-select-option>
              <a-select-option value="python">Python</a-select-option>
            </a-select>
            <a-button 
              type="primary" 
              :disabled="!generatorJson || !selectedLanguage"
              :loading="generating"
              @click="generateCode"
            >
              生成代码
              <template #icon><RightOutlined /></template>
            </a-button>
          </a-space>
        </div>

        <!-- 右侧代码预览 -->
        <div class="editor-wrapper">
          <div class="editor-header">
            <h4>生成结果</h4>
            <a-space>
              <a-button 
                type="link" 
                :disabled="!generatedCode"
                @click="copyGeneratedCode"
              >
                <template #icon><CopyOutlined /></template>
                复制代码
              </a-button>
              <a-button 
                type="link" 
                :disabled="!generatedCode"
                @click="downloadGeneratedCode"
              >
                <template #icon><DownloadOutlined /></template>
                下载代码
              </a-button>
            </a-space>
          </div>
          <div class="editor-content">
            <MonacoEditor
              v-model:value="generatedCode"
              :language="selectedLanguage === 'cs' ? 'csharp' : selectedLanguage"
              :options="getEditorOptions(selectedLanguage)"
              height="400px"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 添加自启动设置对话框 -->
    <a-modal
      v-model:visible="startupModalVisible"
      title="程序自启动设置"
      @ok="handleStartupSave"
      :confirmLoading="startupSaving"
    >
      <div class="startup-content">
        <div class="current-startup" v-if="startupProgram">
          <div class="section-title">当前自启动程序</div>
          <a-alert
            :message="startupProgram.name"
            type="info"
            show-icon
            style="margin-bottom: 16px"
          />
        </div>
        <div class="select-startup">
          <div class="section-title">设置自启动程序</div>
          <a-select
            v-model:value="selectedStartupId"
            style="width: 100%"
            placeholder="请选择要自启动的程序"
          >
            <a-select-option value="none">无</a-select-option>
            <a-select-option
              v-for="program in programs"
              :key="program.id"
              :value="program.id"
            >
              {{ program.name }}
            </a-select-option>
          </a-select>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted, computed, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { UploadOutlined, FileTextOutlined, ReloadOutlined, DownloadOutlined, DeleteOutlined, RightOutlined, CopyOutlined, SettingOutlined } from '@ant-design/icons-vue';
import type { UploadProps, TableColumnType } from 'ant-design-vue';
import MonacoEditor from '@/components/MonacoEditor.vue';
import { programApi } from '@/services/api';
import type { Program, ProgramConfig, UploadForm, ConfigForm, SystemInfo } from '@/types';
import { templateApi } from '@/services/api';
import { ethercatApi } from '@/services/api';
import { AxiosError } from 'axios';
// import { ErrorCode } from '@/utils/error-codes';
import { ErrorCode } from '../utils/error-codes';

interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
}

export default defineComponent({
  name: 'ProgramManagement',
  
  components: {
    UploadOutlined,
    FileTextOutlined,
    ReloadOutlined,
    MonacoEditor,
    DownloadOutlined,
    DeleteOutlined,
    RightOutlined,
    CopyOutlined,
    SettingOutlined,
  },
  
  setup() {
    const loading = ref(false);
    const programs = ref<Program[]>([]);
    const uploadModalVisible = ref(false);
    const configModalVisible = ref(false);
    const logModalVisible = ref(false);
    const uploading = ref(false);
    const logsLoading = ref(false);
    const logs = ref<string[]>([]);
    const replaceFile = ref<File | null>(null);
    const replaceFileList = ref<any[]>([]);
    const replaceProgress = ref(0);
    const replacing = ref(false);
    // 响应式表单对象，默认值-name: 程序名称,file: 程序文件,configFile: 配置文件
    const uploadForm = reactive<UploadForm>({
      name: '',
      masterIndex: 0,
      taskFrequency: 4000,
      ethercatDir: '/dev/shm/ethercat',
      file: null,
      configFile: null
    });
    const configForm = reactive<ConfigForm>({
      masterIndex: 0,
      taskFrequency: 4000
    });
    const fileList = ref<any[]>([]);
    const configFileList = ref<any[]>([]);
    const activeConfigTab = ref('basic');
    const configJson = ref('');
    const editorOptions = {
      readOnly: false,
      minimap: { enabled: false },
      lineNumbers: 'on',
      scrollBeyondLastLine: false,
      automaticLayout: true
    };
    const currentProgram = ref<Program | null>(null);
    const refreshing = ref(false);
    const uploadProgress = ref(0);
    const configLoading = ref(false);

    const cpuBindForm = reactive({
      programCpu: 2,
      middlewareCpu: 3
    });

    const columns: TableColumnType[] = [
      { 
        title: '程序名称', 
        dataIndex: 'name',
        ellipsis: true,
        width: '15%'
      },
      { 
        title: '主站索引', 
        dataIndex: 'masterIndex',
        width: '10%'
      },
      { 
        title: '任务频率', 
        dataIndex: 'taskFrequency',
        slots: { customRender: 'taskFrequency' },
        width: '15%'
      },
      { 
        title: '从站JSON', 
        dataIndex: 'config', 
        slots: { customRender: 'config' },
        width: '8%',
        align: 'center'
      },
      { 
        title: '上传时间', 
        dataIndex: 'uploadTime',
        width: '15%',
        ellipsis: true,
        customRender: ({ text }) => {
          if (!text) return '';
          // 将 UTC 时间转换为 CST
          const date = new Date(text);
          return date.toLocaleString('zh-CN', { 
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
          });
        }
      },
      { 
        title: '中间层状态',
        dataIndex: 'middlewareStatus',
        slots: { customRender: 'middlewareStatus' },
        width: '120px'
      },
      { 
        title: '程序状态',
        dataIndex: 'programStatus',
        slots: { customRender: 'programStatus' },
        width: '120px'
      },
      { 
        title: '操作', 
        slots: { customRender: 'action' }, 
        width: '27%',
        fixed: 'right',
        className: 'operation-column'
      }
    ];

    const getStatusText = (status: string) => {
      const statusMap: Record<string, string> = {
        active: '运行中',
        inactive: '已停止',
        failed: '错误'
      };
      return statusMap[status] || status;
    };

    const fetchPrograms = async () => {
      try {
        loading.value = true;
        const data = await programApi.getPrograms();
        programs.value = data.map(program => ({
          ...program,
          loading: false
        }));
      } catch (error: any) {
        console.error(error);
        const errorCode = error.response?.data?.status || 500;
        const errorMsg = error.response?.data?.message || '未知错误';
        message.error(`[${errorCode}] ${errorMsg}`);
      } finally {
        loading.value = false;
      }
    };

    const showUploadModal = () => {
      uploadForm.name = '';
      uploadForm.masterIndex = 0;
      uploadForm.taskFrequency = 4000;
      uploadForm.ethercatDir = '/dev/shm/ethercat';
      uploadForm.file = null;
      uploadForm.configFile = null;
      fileList.value = [];
      configFileList.value = [];
      uploadModalVisible.value = true;
    };

    // 处理上传逻辑
    const handleUpload = async () => {
      if (!uploadForm.name || !uploadForm.file || !uploadForm.configFile) {
        message.error('请填写完整信息');
        return;
      }

      try {
        uploading.value = true;
        uploadProgress.value = 0;

        // 创建表单对象
        const formData = new FormData();
        formData.append('name', uploadForm.name);
        formData.append('masterIndex', uploadForm.masterIndex.toString());
        formData.append('taskFrequency', uploadForm.taskFrequency.toString());
        formData.append('program', uploadForm.file);
        formData.append('config', uploadForm.configFile);

        // 发送上传请求
        await programApi.uploadProgram(formData, (progress: number) => {
          uploadProgress.value = Math.round(progress * 100);
        });

        message.success('上传成功');
        uploadModalVisible.value = false;
        fetchPrograms();
      } catch (error: any) {
        console.error('Upload failed:', error);
        // 显示具体的错误信息
        if (error.response?.data?.message) {
          message.error(error.response.data.message);
        } else {
          message.error('上传失败');
        }
      } finally {
        uploading.value = false;
        uploadProgress.value = 0;
      }
    };

    const beforeUpload = (file: File) => {
      uploadForm.file = file;
      fileList.value = [file];
      return false;
    };

    const beforeUploadConfig = (file: File) => {
      uploadForm.configFile = file;
      configFileList.value = [file];
      return false;
    };

    const stopProgram = async (program: Program) => {
      if (program.loading || anyRowLoading.value) return;
      try {
        program.loading = true;
        await programApi.stopProgram(program.id);
        message.success('停止成功');
        await fetchPrograms();
      } catch (error: any) {
        console.error(error);
        const errorCode = error.response?.data?.status || 500;
        const errorMsg = error.response?.data?.message || '未知错误';
        message.error(`[${errorCode}] ${errorMsg}`);
      } finally {
        program.loading = false;
      }
    };

    const startProgram = async (record: Program) => {
      try {
        record.loading = true;
        await programApi.startProgram(record.id);
        message.success('程序启动成功');
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        await refreshPrograms();
      } catch (error: any) {
        // console.error('Failed to start program:', error);
        // message.error(error.response?.data?.message || '启动失败');
        // 显示具体的错误信息
        const errorMsg = error.response?.data?.message || '启动失败';
        if (error.response?.data?.status === ErrorCode.SLAVE_COUNT_MISMATCH ||
            error.response?.data?.status === ErrorCode.SLAVE_VID_PID_MISMATCH) {
          message.error(errorMsg);
        } else {
          message.error('启动失败：' + errorMsg);
        }
      } finally {
        try {
          const updatedPrograms = await programApi.getPrograms();
          const updatedRecord = updatedPrograms.find(p => p.id === record.id);
          if (updatedRecord) {
            Object.assign(record, updatedRecord);
          }
        } catch (error) {
          console.error('Failed to refresh program status:', error);
        }
        record.loading = false;
      }
    };

    const deleteProgram = async (program: Program) => {
      if (program.loading || anyRowLoading.value) return;
      try {
        program.loading = true;
        await programApi.deleteProgram(program.id);
        message.success('删除成功');
        await fetchPrograms();
      } catch (error: any) {
        console.error(error);
        const errorCode = error.response?.data?.status || 500;
        const errorMsg = error.response?.data?.message || '未知错误';
        message.error(`[${errorCode}] ${errorMsg}`);
      } finally {
        program.loading = false;
      }
    };

    const confirmDelete = (program: Program) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除程序 "${program.name}" 吗？`,
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        async onOk() {
          await deleteProgram(program);
        }
      });
    };

    const showBasicConfig = async (program: Program) => {
      currentProgram.value = program;
      configForm.masterIndex = program.masterIndex;
      configForm.taskFrequency = program.taskFrequency;
      configJson.value = JSON.stringify(program.config, null, 2);
      activeConfigTab.value = 'basic';
      configModalVisible.value = true;
      
      // 获取系统信息
      await fetchSystemInfo();
      
      // 获取 CPU 绑定信息
      try {
        const cpuBinding = await programApi.getCpuBinding(program.id);
        cpuBindForm.programCpu = parseInt(cpuBinding.programCpu);
        cpuBindForm.middlewareCpu = parseInt(cpuBinding.middlewareCpu);
      } catch (error) {
        console.error('Failed to fetch CPU binding:', error);
        // 使用默认值
        cpuBindForm.programCpu = 2;
        cpuBindForm.middlewareCpu = 3;
      }
    };

    const showJsonConfig = async (program: Program) => {
      currentProgram.value = program;
      configForm.masterIndex = program.masterIndex;
      configForm.taskFrequency = program.taskFrequency;
      configJson.value = JSON.stringify(program.config, null, 2);
      activeConfigTab.value = 'json';
      configModalVisible.value = true;
      
      // 获取系统信息
      await fetchSystemInfo();
      
      // 获取 CPU 绑定信息
      try {
        const cpuBinding = await programApi.getCpuBinding(program.id);
        cpuBindForm.programCpu = parseInt(cpuBinding.programCpu);
        cpuBindForm.middlewareCpu = parseInt(cpuBinding.middlewareCpu);
      } catch (error) {
        console.error('Failed to fetch CPU binding:', error);
        // 使用默认值
        cpuBindForm.programCpu = 2;
        cpuBindForm.middlewareCpu = 3;
      }
    };

    const showLogs = async (program: Program) => {
      currentProgram.value = program;
      logModalVisible.value = true;
      await refreshLogs();
    };

    const handleConfigSave = async () => {
      if (!currentProgram.value) return;

      try {
        configLoading.value = true;
        
        if (activeConfigTab.value === 'binCPU') {
          // 调用绑定CPU的API
          await programApi.updateCpuBinding(
            currentProgram.value.id,
            cpuBindForm.programCpu.toString(),
            cpuBindForm.middlewareCpu.toString()
          );
          message.success('CPU绑定更新成功');
          configModalVisible.value = false;
          return;
        }

        let configToUpdate;

        if (activeConfigTab.value === 'json') {
          // 如果是 JSON 配置标签页，使用解析后的 JSON 作为从站配置
          const parsedConfig = JSON.parse(configJson.value);
          configToUpdate = {
            masterIndex: currentProgram.value.masterIndex,
            taskFrequency: currentProgram.value.taskFrequency,
            config: parsedConfig  // 使用新的从站配置
          };
        } else {
          // 如果是基本配置标签页，使用表单值
          configToUpdate = {
            masterIndex: configForm.masterIndex,
            taskFrequency: configForm.taskFrequency,
            config: currentProgram.value.config  // 保持原有的从站配置
          };
        }

        await programApi.updateConfig(currentProgram.value.id, configToUpdate);
        message.success('配置更新成功');
        configModalVisible.value = false;
        fetchPrograms();
      } catch (error: any) {
        console.error(error);
        const errorCode = error.response?.data?.status || 500;
        const errorMsg = error.response?.data?.message || '未知错误';
        message.error(`[${errorCode}] ${errorMsg}`);
      } finally {
        configLoading.value = false;
      }
    };

    // 文件上传相关方法
    const handleRemove = () => {
      uploadForm.file = null;
      fileList.value = [];
    };

    const handleRemoveConfig = () => {
      uploadForm.configFile = null;
      configFileList.value = [];
    };

    const formatJson = (json: any) => {
      return JSON.stringify(json, null, 2);
    };

    // 判断程序是否可以启动
    const isStartDisabled = (program: Program) => {
      // 如果中间层或程序任一正在运行，则禁用启动按钮
      if (program.middlewareStatus === 'active' || program.programStatus === 'active') {
        return true;
      }

      // 检查是否有相同主站索引的程序正在运行
      return programs.value.some(p => 
        p.masterIndex === program.masterIndex && 
        (p.middlewareStatus === 'active' || p.programStatus === 'active') &&
        p.id !== program.id
      );
    };

    // 新程序列表
    const refreshPrograms = async () => {
      try {
        refreshing.value = true;
        await fetchPrograms();
        message.success('刷新成功');
      } catch (error: any) {
        console.error(error);
        const errorCode = error.response?.data?.status || 500;
        const errorMsg = error.response?.data?.message || '未知错误';
        message.error(`[${errorCode}] ${errorMsg}`);
      } finally {
        refreshing.value = false;
      }
    };

    // 添加计算属性，检查是否有任何行正在加载
    const anyRowLoading = computed(() => 
      programs.value.some(program => program.loading)
    );

    const downloadTemplate = async (record: Program) => {
      try {
        const blob = await programApi.downloadMiddlewareCode(record.id);
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${record.name}_middleware.c`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error: any) {
        console.error('下载中间层代码败');
        message.error(error.response?.data?.message || '下载中间层代码失败');
      }
    };

    // const replaceFileList = ref<any[]>([]);
    // const replaceFile = ref<File | null>(null);
    // const replacing = ref(false);
    // const replaceProgress = ref(0);

    const beforeReplaceUpload = (file: File) => {
      // 检查文件类型
      const isZip = file.type === 'application/zip' || file.name.endsWith('.zip');
      if (!isZip) {
        message.error('请上传 ZIP 格式的程序压缩包');
        return false;
      }

      // 检查文件大小 (200MB)
      const maxSize = 200 * 1024 * 1024;
      if (file.size > maxSize) {
        message.error('文件大小不能超过200MB');
        return false;
      }

      replaceFile.value = file;
      replaceFileList.value = [file];
      return false;
    };

    const handleReplaceRemove = () => {
      replaceFile.value = null;
      replaceFileList.value = [];
    };

   // 处理替换
const handleReplace = async () => {
  if (!replaceFile.value || !currentProgram.value) {
    message.error('请选择替换文件');
    return;
  }

  try {
    replacing.value = true;
    replaceProgress.value = 0;

    const formData = new FormData();
    formData.append('program', replaceFile.value);

    await programApi.replaceProgram(currentProgram.value.id, formData, (progress) => {
      replaceProgress.value = Math.round(progress * 100);
    });

    message.success('程序替换成功');
    configModalVisible.value = false;
    await refreshPrograms();
  } catch (error: any) {
    console.error('Failed to replace program:', error);
    message.error(error.response?.data?.message || '程序替换失败');
  } finally {
    replacing.value = false;
    replaceProgress.value = 0;
  }
};

    const availableMasters = ref<number[]>([]);
    
    const uploadFrequencyUs = ref(Math.round(1000000 / 4000));
    const configFrequencyUs = ref(Math.round(1000000 / 4000));

    const updateUploadFrequency = (value: number) => {
      uploadFrequencyUs.value = Math.round(1000000 / value);
    };

    const updateConfigFrequency = (value: number) => {
      configFrequencyUs.value = Math.round(1000000 / value);
    };

    const handleConfigModalClose = () => {
      // 关闭对话框时清除状态
      replaceFileList.value = [];
      replaceFile.value = null;
      replaceProgress.value = 0;
      activeConfigTab.value = 'basic';  // 重置为基本配置标签页
    };

    const clearReplaceState = () => {
      // 清除替换状
      replaceFileList.value = [];
      replaceFile.value = null;
      replaceProgress.value = 0;
    };

    // 监听配置对话框可见性变化
    watch(configModalVisible, (visible) => {
      if (!visible) {
        handleConfigModalClose();
      }
    });

    // 监听标签页切换
    watch(activeConfigTab, (newTab) => {
      if (newTab !== 'replace') {
        clearReplaceState();
      }
    });

    // 代码生成器相
    const generatorJson = ref('');
    const selectedLanguage = ref('c');
    const generatedCode = ref('');
    const generating = ref(false);

    const jsonEditorOptions = {
      minimap: { enabled: false },
      automaticLayout: true,
      formatOnPaste: true,
      formatOnType: true,
    };

    const getEditorOptions = (language: string) => {
      const baseOptions = {
        minimap: { enabled: false },
        automaticLayout: true,
        readOnly: true,
        theme: 'vs-light',
      };

      const languageMap: Record<string, string> = {
        'cs': 'csharp',
        'cpp': 'cpp',
        'c': 'c',
        'python': 'python'
      };

      return {
        ...baseOptions,
        language: languageMap[language] || 'plaintext'
      };
    };

    const generateCode = async () => {
      if (!generatorJson.value || !selectedLanguage.value) return;

      try {
        generating.value = true;
        const config = JSON.parse(generatorJson.value);
        const result = await programApi.generateCode(config, selectedLanguage.value);
        generatedCode.value = result;
      } catch (error: any) {
        console.error('Failed to generate code:', error);
        message.error(error.response?.data?.message || '代码生成失败');
      } finally {
        generating.value = false;
      }
    };

    const downloadGeneratedCode = () => {
      if (!generatedCode.value) return;

      const extensions: Record<string, string> = {
        c: '.c',
        cpp: '.cpp',
        cs: '.cs',
        python: '.py'
      };

      const extension = extensions[selectedLanguage.value] || '.txt';
      const blob = new Blob([generatedCode.value], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `generated_code${extension}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    };

    const copyGeneratedCode = async () => {
      if (!generatedCode.value) return;
      
      try {
        await navigator.clipboard.writeText(generatedCode.value);
        message.success('代码已复制到剪贴板');
      } catch (error: any) {
        console.error('Failed to copy code:', error);
        message.error(error.response?.data?.message || '复制失败');
      }
    };

    const downloadFullPackage = async (record: any) => {
      try {
        message.loading({ 
          content: '正在打包程序...', 
          key: 'package', 
          duration: 0
        });
        
        const packageData = await programApi.downloadFullPackage(record.id);
        
        const blob = new Blob([packageData], { type: 'application/zip' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${record.name}_package.zip`;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        message.success({ 
          content: '程序包下载成功', 
          key: 'package' 
        });
      } catch (error: any) {
        console.error('Failed to download package:', error);
        message.error({ 
          content: error.response?.data?.message || '程序包下载失败', 
          key: 'package',
          duration: 5
        });
      }
    };

    const refreshLogs = async () => {
      if (!currentProgram.value) return;
      
      try {
        logsLoading.value = true;
        const response = await programApi.getProgramLogs(currentProgram.value.id);
        logs.value = response;
      } catch (error: any) {
        console.error('Failed to fetch logs:', error);
        message.error(error.response?.data?.message || '获取日志失败');
      } finally {
        logsLoading.value = false;
      }
    };

    // 获取可用主站列表
    const fetchAvailableMasters = async () => {
      try {
        const masters = await ethercatApi.getAvailableMasters();
        availableMasters.value = masters;
      } catch (error) {
        console.error('Failed to get available masters:', error);
        availableMasters.value = [0];
      }
    };

    // 在组件挂载时获取主站列表
    onMounted(async () => {
      await fetchAvailableMasters();
      await fetchPrograms();
    });

    const getStatusColor = (status: string) => {
      const colorMap: Record<string, string> = {
        active: 'success',
        inactive: 'default',
        failed: 'error'
      };
      return colorMap[status] || 'default';
    };

    const startMiddleware = async (record: Program) => {
      try {
        record.loading = true;
        await programApi.startMiddleware(record.id);
        message.success('中间层启动成功');
        await refreshPrograms();
      } catch (error: any) {
        message.error(error.response?.data?.message || '启动失败');
      } finally {
        record.loading = false;
      }
    };

    const stopMiddleware = async (record: Program) => {
      try {
        record.loading = true;
        await programApi.stopMiddleware(record.id);
        message.success('中间层停止成功');
        await refreshPrograms();
      } catch (error: any) {
        message.error(error.response?.data?.message || '停止失败');
      } finally {
        record.loading = false;
      }
    };

    // 自启动相关的状态
    const startupModalVisible = ref(false);
    const selectedStartupId = ref('');
    const startupSaving = ref(false);

    // 计算当前的自启动程序
    const startupProgram = computed(() => 
      programs.value.find(p => (p as Program).startup === '1')
    );

    // 显示自启动设置对话框
    const showStartupModal = () => {
      startupModalVisible.value = true;
      selectedStartupId.value = startupProgram.value?.id || '';
    };

    // 保存自启动设置
    const handleStartupSave = async () => {
      try {
        startupSaving.value = true;
        
        // 先将所有程序的 startup 设置为 0
        await Promise.all(
          programs.value.map(program => 
            (program as Program).startup === '1' 
              ? programApi.updateStartup(program.id, '0')
              : Promise.resolve()
          )
        );

        // 将选中的程序设置为自启动
        if (selectedStartupId.value) {
          await programApi.updateStartup(selectedStartupId.value, '1');
        }

        message.success('自启动设置已保存');
        startupModalVisible.value = false;
        await refreshPrograms();
      } catch (error: any) {
        message.error(error.response?.data?.message || '保存失败');
      } finally {
        startupSaving.value = false;
      }
    };

    const systemInfo = ref<SystemInfo | null>(null);

    // 获取系统信息
    const fetchSystemInfo = async () => {
      try {
        systemInfo.value = await programApi.getSystemInfo();
      } catch (error) {
        message.error('获取系统信息失败');
      }
    };

    return {
      loading,
      programs,
      columns,
      uploadModalVisible,
      configModalVisible,
      logModalVisible,
      logsLoading,
      logs,
      uploading,
      uploadForm,
      configForm,
      fileList,
      configFileList,
      activeConfigTab,
      configJson,
      editorOptions,
      currentProgram,
      refreshing,
      uploadProgress,
      getStatusText,
      showUploadModal,
      handleUpload,
      beforeUpload,
      beforeUploadConfig,
      startProgram,
      stopProgram,
      deleteProgram,
      confirmDelete,
      showBasicConfig,
      showJsonConfig,
      handleConfigSave,
      refreshPrograms,
      generateCode,
      showLogs,
      refreshLogs,
      anyRowLoading,
      isStartDisabled,
      downloadTemplate,
      downloadFullPackage,
      getEditorOptions,
      jsonEditorOptions,
      generatorJson,
      selectedLanguage,
      generatedCode,
      generating,
      downloadGeneratedCode,
      copyGeneratedCode,
      availableMasters,
      uploadFrequencyUs,
      configFrequencyUs,
      replaceFileList,
      replaceProgress,
      updateUploadFrequency,
      updateConfigFrequency,
      handleRemove,
      handleRemoveConfig,
      beforeReplaceUpload,
      handleReplaceRemove,
      getStatusColor,
      startMiddleware,
      stopMiddleware,
      startupModalVisible,
      selectedStartupId,
      startupSaving,
      startupProgram,
      showStartupModal,
      handleStartupSave,
      replaceFile,
      cpuBindForm,
      handleReplace,
      systemInfo,
      replacing,
    };
  }
});
</script>

<style lang="less" scoped>
.upload-tip {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}
.program-management {
  .monaco-editor {
    height: 400px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
  }

  :deep(.json-editor) {
    height: 600px !important;
    min-height: 600px !important;
  }

  .config-icon {
    font-size: 18px;
    color: @primary-color;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      color: darken(@primary-color, 10%);
      transform: scale(1.1);
    }
  }
}

.preview-icon {
  cursor: pointer;
  color: @primary-color;
  font-size: 16px;
  
  &:hover {
    color: darken(@primary-color, 10%);
  }
}

:deep(.ant-modal-body) {
  max-height: 80vh;
  overflow-y: auto;
  padding: 24px;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #f0f0f0;
  padding: 16px 24px;
}

:deep(.ant-tabs-content) {
  height: calc(70vh - 120px);
  overflow: auto;
}

.code-generator {
  margin-top: 24px;
  background: #fff;
  padding: 24px;
  border-radius: 2px;  // 修改圆角大小与表格一致
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 
             0 3px 6px 0 rgba(0, 0, 0, 0.12), 
             0 5px 12px 4px rgba(0, 0, 0, 0.09);  // 改阴效果与表格一致
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
  }
  
  .generator-container {
    display: flex;
    gap: 16px;
    min-height: 600px;

    .editor-wrapper {
      flex: 3;
      background: #fff;
      border: 1px solid #f0f0f0;
      border-radius: 2px;
      overflow: hidden;
      box-shadow: none;
      display: flex;
      flex-direction: column;

      .editor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        background: #fafafa;
        flex-shrink: 0;

        h4 {
          margin: 0;
          font-size: 14px;
          font-weight: 500;
        }

        .ant-btn-link {
          padding: 4px 8px;
          height: auto;
        }
      }

      .editor-content {
        flex: 1;
        padding: 16px;
        height: 100%;
        min-height: 500px;
        
        :deep(.monaco-editor) {
          height: 100% !important;
        }
      }
    }

    .control-panel {
      flex: 0.5;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 8px;
    }
  }
}

.log-content {
  position: relative;
  height: 60vh;
  display: flex;
  flex-direction: column;

  .log-header {
    position: sticky;
    top: 0;
    z-index: 1;
    padding: 12px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  :deep(.ant-spin-nested-loading) {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  :deep(.ant-spin-container) {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .log-list {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    background: #fafafa;
    font-family: Consolas, monospace;
    white-space: pre-wrap;
    word-break: break-all;

    .log-item {
      line-height: 1.5;
      margin-bottom: 4px;

      &:hover {
        background: #f0f0f0;
      }
    }

    .no-logs {
      text-align: center;
      color: #999;
      padding: 24px 0;
    }
  }
}
.program-management {
  .monaco-editor {
    height: 400px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
  }

  :deep(.json-editor) {
    height: 600px !important;
    min-height: 600px !important;
  }

  .config-icon {
    font-size: 18px;
    color: @primary-color;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      color: darken(@primary-color, 10%);
      transform: scale(1.1);
    }
  }

  .log-content {
    .log-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      span {
        font-size: 14px;
        color: #666;
      }
    }
    
    .log-list {
      height: calc(70vh - 180px);
      overflow-y: auto;
      background: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      font-family: "Consolas", monospace;
      
      .log-item {
        padding: 4px 8px;
        font-size: 13px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-break: break-all;
        
        &:hover {
          background: rgba(0, 0, 0, 0.02);
        }
      }
      
      .no-logs {
        text-align: center;
        color: #999;
        padding: 32px 0;
      }
    }
  }
}

// 修改操作列的样式
:deep(.operation-column) {
  position: relative;
  z-index: 2;  // 确保操作列显示在其他列上面
  background: #fff;  // 添加背景色，避免透明

  // 确保表头也显示在最上层
  &.ant-table-cell-fix-right {
    background: #fff;
    z-index: 3;  // 表头的z-index要比内容高
  }

  // 表头的hover效果
  &.ant-table-cell-fix-right:hover {
    background: #fafafa;
  }
}

// 确保下拉菜单显示在最上层
:deep(.ant-dropdown) {
  z-index: 1000;
}

// 确保表格的固定列阴影效果正确
:deep(.ant-table-cell-fix-right-first::after) {
  box-shadow: inset -6px 0 6px -6px rgba(0,0,0,0.15);
}

.startup-content {
  .section-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #666;
  }

  .current-startup {
    margin-bottom: 24px;
  }
}

.system-info {
  margin-bottom: 24px;
  
  :deep(.ant-descriptions) {
    background: #fafafa;
    padding: 16px;
    border-radius: 4px;
  }
}
</style> 