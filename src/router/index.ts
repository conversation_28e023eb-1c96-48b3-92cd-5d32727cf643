import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import { useStore } from 'vuex';
import { settingsApi } from '@/services/api';
import SystemSettings from '@/views/SystemSettings.vue';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { 
      public: true,
      layout: 'blank'  // 使用空白布局
    }
  },
  {
    path: '/',
    component: () => import('../layouts/DefaultLayout.vue'),
    meta: { requiresAuth: true },  // 需要认证
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('../views/Dashboard.vue')
      },
      {
        path: 'ethercat',
        name: 'EtherCAT',
        component: () => import('../views/EtherCAT.vue')
      },
      {
        path: 'programs',
        name: 'Programs',
        component: () => import('../views/Programs.vue')
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('../views/Users.vue')
      },
      {
        path: '/settings',
        name: 'SystemSettings',
        component: SystemSettings,
        meta: { requiresAuth: true }
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const store = useStore();
  const isAuthenticated = store.getters['auth/isAuthenticated'];
  
  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login');
  } else if (to.path === '/login' && isAuthenticated) {
    next('/');
  } else if (to.path === '/' && isAuthenticated) {
    try {
      // 获取菜单设置
      const menuSettings = await settingsApi.getMenuSettings();
      // 如果 dashboard 被禁用，重定向到第一个可用的菜单
      if (!menuSettings.dashboard) {
        if (menuSettings.ethercat) next('/ethercat');
        else if (menuSettings.programs) next('/programs');
        else if (menuSettings.users) next('/users');
        else next('/login');  // 如果没有可用菜单，退回登录页
      } else {
        next();
      }
    } catch (error) {
      console.error('Failed to get menu settings:', error);
      next();
    }
  } else {
    const version = process.env.VUE_APP_VERSION || 'dev';
    // 可以根据路由设置不同的标题
    document.title = `${to.meta.title || '边缘控制器平台'} ${version} - 飞仙软件`;
    next();
  }
});

export default router; 